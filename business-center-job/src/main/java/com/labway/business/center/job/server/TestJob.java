package com.labway.business.center.job.server;

import com.labway.business.center.compare.service.CompareMonitorService;
import com.labway.business.center.compare.service.WebserviceMonitorService;
import com.labway.business.center.core.util.NotifyUtil;
import com.labway.business.center.finance.service.FinanceMonitorService;
import com.labway.business.center.mdm.api.reagent.service.MdmMonitorService;
import com.swak.frame.dto.Response;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * TestJob 服务调用监测job
 *
 * <AUTHOR>
 * @version 2023/04/24 11:06
 **/
@Slf4j
@Component
public class TestJob {

    @Resource
    private NotifyUtil notifyUtil;
    // 业务中台
    @DubboReference
    private CompareMonitorService compareMonitorService;
    // 业务中台-webservice
    @DubboReference
    private WebserviceMonitorService webserviceMonitorService;
    // 主数据
    @DubboReference
    private MdmMonitorService mdmMonitorService;
    // 费控
    @DubboReference
    private FinanceMonitorService financeMonitorService;


    @XxlJob("serviceHeartbeatMonitor")
    public ReturnT<String> serviceHeartbeatMonitor() {
        String jobParam = XxlJobHelper.getJobParam();
        log.info("开始监测服务心跳，任务执行入参：{}", jobParam);

        Boolean flag = Boolean.TRUE;

        try {
            // compare监测
            Response<?> compareResponse = compareMonitorService.serviceMonitor();
            log.info("compare服务心跳监测结果：{}", compareResponse.isSuccess());
        } catch (Exception e) {
            flag = Boolean.FALSE;
            log.error("compare服务心跳监测失败，异常信息：", e);
            notifyUtil.sendBusinessRobot("compare服务心跳监测失败,请及时核实compare服务运行状态！！！");
        }

        try {
            // webservice监测
            Response<?> webserviceResponse = webserviceMonitorService.serviceMonitor();
            log.info("webservice服务心跳监测结果：{}", webserviceResponse.isSuccess());
        } catch (Exception e) {
            flag = Boolean.FALSE;
            log.error("webservice服务心跳监测失败，异常信息：", e);
            notifyUtil.sendBusinessRobot("webservice服务心跳监测失败,请及时核实webservice服务运行状态！！！");
        }

        try {
            // mdm监测
            Response<?> mdmResponse = mdmMonitorService.serviceMonitor();
            log.info("mdm服务心跳监测结果：{}", mdmResponse.isSuccess());
        } catch (Exception e) {
            flag = Boolean.FALSE;
            log.error("mdm服务心跳监测失败，异常信息：", e);
            notifyUtil.sendBusinessRobot("mdm服务心跳监测失败,请及时核实mdm服务运行状态！！！");
        }

        try {
            // finance监测
            Response<?> financeResponse = financeMonitorService.serviceMonitor();
            log.info("finance服务心跳监测结果：{}", financeResponse.isSuccess());
        } catch (Exception e) {
            flag = Boolean.FALSE;
            log.error("finance服务心跳监测失败，异常信息：", e);
            notifyUtil.sendBusinessRobot("finance服务心跳监测失败,请及时核实finance服务运行状态！！！");
        }


        if (flag) {
            return ReturnT.SUCCESS;
        }

        notifyUtil.sendBusinessRobot("业务系统服务心跳监测异常，请尽快检查服务调用链，十万火急，刻不容缓！！！");
        throw new RuntimeException("服务心跳监测失败!!!!");
    }


}