package com.labway.business.center.job.server.finance;

import com.alibaba.fastjson.JSONObject;
import com.labway.business.center.compare.request.QueryAdditionalSampleInfoRequest;
import com.labway.business.center.finance.request.CleanNoExistRequest;
import com.labway.business.center.finance.service.*;
import com.swak.frame.dto.Response;
import com.swak.frame.util.UUIDHexGenerator;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

/**
 * FinanceJob 费控-定时任务
 *
 * <AUTHOR>
 * @version 2023/04/27 09:18
 **/
@Slf4j
@Component
public class FinanceJob {
    @DubboReference
    private CleanExpensesFormService cleanExpensesFormService;
    @DubboReference
    private AccassService accassService;
    @DubboReference
    private AccountingBookService accountingBookService;
    @DubboReference
    private BankService bankService;
    @DubboReference
    private BudgetUnitService budgetUnitService;
    @DubboReference
    private CarNoService carNoService;
    @DubboReference
    private CashflowService cashflowService;
    @DubboReference
    private EmployeeService employeeService;
    @DubboReference
    private FeeTypeService feeTypeService;
    @DubboReference
    private LedgerAccountService ledgerAccountService;
    @DubboReference
    private ManagementOrgsService managementOrgsService;
    @DubboReference
    private NccDeptService nccDeptService;
    @DubboReference
    private OaDeptService oaDeptService;
    @DubboReference
    private OrgMappingService orgMappingService;
    @DubboReference
    private ProjectService projectService;
    @DubboReference
    private InvoiceInfoService invoiceInfoService;


    /**
     * 同步NCC研发项目数据
     *
     * @return
     */
    @XxlJob("washNccProjectToCenterDatabase")
    public void washNccProjectToCenterDatabase() {
        XxlJobHelper.log("同步NCC研发项目数据");
        log.info("同步NCC研发项目数据开始");
        try {
            projectService.washNccProjectToCenterDatabase();
            log.info("同步NCC研发项目数据结束");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    /**
     * 同步NCC业务单元数据
     *
     * @return
     */
    @XxlJob("washNccOrgsToCenterDatabase")
    public void washNccOrgsToCenterDatabase() {
        XxlJobHelper.log("同步NCC业务单元数据");
        log.info("同步NCC业务单元数据开始");
        try {
            orgMappingService.washNccOrgsToCenterDatabase();
            log.info("同步NCC业务单元数据结束");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    /**
     * 同步OA部门数据
     *
     * @return
     */
    @XxlJob("syncOaDeptByMdm")
    public void syncOaDeptByMdm() {
        XxlJobHelper.log("同步OA部门数据");
        log.info("同步OA部门数据开始");
        try {
            oaDeptService.syncOaDeptByMdm();
            log.info("同步OA部门数据结束");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    /**
     * 同步NCC部门数据
     *
     * @return
     */
    @XxlJob("washNccDeptToCenterDatabase")
    public void washNccDeptToCenterDatabase() {
        XxlJobHelper.log("同步NCC部门数据");
        log.info("同步NCC部门数据开始");
        try {
            nccDeptService.washNccDeptToCenterDatabase();
            log.info("同步NCC部门数据结束");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    /**
     * 同步NCC科目数据
     *
     * @return
     */
    @XxlJob("washBdAccasoaToCenterDatabase")
    public void washBdAccasoaToCenterDatabase() {
        XxlJobHelper.log("同步NCC科目数据");
        log.info("同步NCC科目数据开始");
        try {
            ledgerAccountService.washBdAccasoaToCenterDatabase();
            log.info("同步NCC科目数据结束");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    /**
     * 同步NCC预算单元数据
     *
     * @return
     */
    @XxlJob("washNccManagementOrgsToCenterDatabase")
    public void washNccManagementOrgsToCenterDatabase() {
        XxlJobHelper.log("同步NCC预算单元数据");
        log.info("同步NCC预算单元数据开始");
        try {
            managementOrgsService.washNccManagementOrgsToCenterDatabase();
            log.info("同步NCC预算单元数据结束");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    /**
     * 同步NCC费用类别数据
     *
     * @return
     */
    @XxlJob("washFeeTypeToCenterDatabase")
    public void washFeeTypeToCenterDatabase() {
        XxlJobHelper.log("同步NCC费用类别数据");
        log.info("同步NCC费用类别数据开始");
        try {
            feeTypeService.washFeeTypeToCenterDatabase();
            log.info("同步NCC费用类别数据结束");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    /**
     * 同步NCC人员数据
     *
     * @return
     */
    @XxlJob("washNccEmployeeToCenterDatabase")
    public void washNccEmployeeToCenterDatabase() {
        XxlJobHelper.log("同步NCC人员数据");
        log.info("同步NCC人员数据开始");
        try {
            employeeService.washNccEmployeeToCenterDatabase();
            log.info("同步NCC人员数据结束");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    /**
     * 同步车号数据
     *
     * @return
     */
    @XxlJob("washNccCarNoToCenterDatabase")
    public void washNccCarNoToCenterDatabase() {
        XxlJobHelper.log("同步车号数据");
        log.info("同步车号数据开始");
        try {
            carNoService.washNccCarNoToCenterDatabase();
            log.info("同步车号数据结束");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    /**
     * 同步现金流数据
     *
     * @return
     */
    @XxlJob("washCashflow2Database")
    public void washCashflow2Database() {
        XxlJobHelper.log("同步现金流数据");
        log.info("同步现金流数据开始");
        try {
            cashflowService.washCashflow2Database();
            log.info("同步现金流数据结束");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    /**
     * 同步预算单元数据
     *
     * @return
     */
    @XxlJob("washBudgetUnitToCenterDatabase")
    public void washBudgetUnitToCenterDatabase() {
        XxlJobHelper.log("同步预算单元数据");
        log.info("同步预算单元数据开始");
        try {
            budgetUnitService.washBudgetUnitToCenterDatabase();
            log.info("同步预算单元数据结束");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    /**
     * 同步银行账户数据
     *
     * @return
     */
    @XxlJob("washNccBankToCenterDatabase")
    public void washNccBankToCenterDatabase() {
        XxlJobHelper.log("同步银行账户数据");
        log.info("同步银行账户数据开始");
        try {
            bankService.washNccBankToCenterDatabase();
            log.info("同步银行账户数据结束");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    /**
     * 同步核算账簿
     *
     * @return
     */
    @XxlJob("washAccountingBookToCenterDatabase")
    public void washAccountingBookToCenterDatabase() {
        XxlJobHelper.log("同步辅助核算账簿数据");
        log.info("同步辅助核算账簿数据开始");
        try {
            accountingBookService.washAccountingBookToCenterDatabase();
            log.info("同步辅助核算账簿数据结束");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    /**
     * 同步辅助核算
     *
     * @return
     */
    @XxlJob("washAccassToCenterDatabase")
    public void washAccassToCenterDatabase() {
        XxlJobHelper.log("同步辅助核算数据");
        log.info("同步辅助核算数据开始");
        try {
            accassService.washAccassToCenterDatabase();
            log.info("同步辅助核算数据完成");
        }catch (Exception e){
            log.error(e.getMessage(), e);
        }
    }

    /**
     * 同步凭证数据
     *
     * @return
     */
    @XxlJob("washExpensesForm")
    public void querySampleResult() {
        String requestId = UUIDHexGenerator.generator();
        String params = XxlJobHelper.getJobParam();
        JSONObject param = JSONObject.parseObject(params);
        Long time = param.getLong("time");
        XxlJobHelper.log("同步凭证数据，请求id={},任务时间限制：{}s",requestId,time);
        log.info("同步凭证数据开始");
        try {
            cleanExpensesFormService.washExpensesData(requestId,time);
            log.info("同步凭证数据成功");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    /**
     * 清洗凭证中辅助核算为空的数据
     *
     * @return
     */
    @XxlJob("cleanExpensesByAccass")
    public void cleanExpensesByAccass() {
        XxlJobHelper.log("清洗凭证中辅助核算为空的数据");
        log.info("清洗凭证中辅助核算为空的数据开始");
        try {
            cleanExpensesFormService.cleanExpensesByAccass();
            log.info("清洗凭证中辅助核算为空的数据成功");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    /**
     * 清洗本地库中遗漏的数据
     * 费用报销单
     *
     * @return
     */
    @XxlJob("washNoExistClaimsForm")
    public void washNoExistClaimsForm() {
        XxlJobHelper.log("清洗本地库中遗漏的数据-费用报销单");
        log.info("清洗本地库中遗漏的数据-费用报销单");
        try {
            String jobParam = XxlJobHelper.getJobParam();
            log.info("清洗本地库中遗漏的数据-费用报销单，任务执行入参：{}",jobParam);
            CleanNoExistRequest cleanNoExistRequest = JSONObject.parseObject(jobParam, CleanNoExistRequest.class);
            cleanExpensesFormService.washNoExistClaimsForm(cleanNoExistRequest);
            log.info("清洗本地库中遗漏的数据-费用报销单成功");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    /**
     * 清洗本地库中遗漏的数据
     * 差旅报销单
     *
     * @return
     */
    @XxlJob("washTravelNoExistClaimsForm")
    public void washTravelNoExistClaimsForm() {
        XxlJobHelper.log("清洗本地库中遗漏的数据-差旅报销单");
        log.info("清洗本地库中遗漏的数据-差旅报销单");
        try {
            String jobParam = XxlJobHelper.getJobParam();
            log.info("清洗本地库中遗漏的数据-差旅报销单，任务执行入参：{}",jobParam);
            CleanNoExistRequest cleanNoExistRequest = JSONObject.parseObject(jobParam, CleanNoExistRequest.class);
            cleanExpensesFormService.washTravelNoExistClaimsForm(cleanNoExistRequest);
            log.info("清洗本地库中遗漏的数据-差旅报销单");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    /**
     * 清洗本地库中遗漏的数据
     * 科目为空
     *
     * @return
     */
    @XxlJob("washAccassoIsNull")
    public void washAccassoIsNull() {
        XxlJobHelper.log("清洗科目为空的数据");
        log.info("清洗科目为空的数据");
        try {
            cleanExpensesFormService.washAccassoIsNull();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    /**
     * 同步发票
     *
     * @return
     */
    @XxlJob("syncMakeVoucherInvoices")
    public void syncMakeVoucherInvoices() {
        XxlJobHelper.log("同步已制单的发票数据");
        log.info("同步已制单的发票数据开始");
        try {
            invoiceInfoService.syncMakeVoucherInvoices();
            log.info("同步已制单的发票数据成功");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }
}