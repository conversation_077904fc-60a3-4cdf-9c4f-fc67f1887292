package com.labway.business.center.job.config;

import com.github.jaemon.dinger.constant.DingerConstant;
import com.github.jaemon.dinger.core.DingerConfigurerAdapter;
import com.github.jaemon.dinger.core.DingerManagerBuilder;
import com.github.jaemon.dinger.core.DingerRobot;
import com.github.jaemon.dinger.core.entity.DingerProperties;
import com.github.jaemon.dinger.core.session.DingerSessionFactory;
import com.github.jaemon.dinger.core.spring.DingerSessionFactoryBean;
import com.github.jaemon.dinger.exception.ConfigurationException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * DingerAutoConfiguration
 *
 * <AUTHOR>
 * @since 1.2
 */
@Configuration
@EnableConfigurationProperties(DingerProperties.class)
public class DingerAutoConfiguration implements InitializingBean {

    @Bean
    public DingerSessionFactory dingerSessionFactory(DingerConfigurerAdapter dingerConfigurerAdapter) throws Exception {
        DingerSessionFactoryBean factory = new DingerSessionFactoryBean();

        factory.setConfiguration(
                com.github.jaemon.dinger.core.session.Configuration.of(dingerProperties(), dingerSender(dingerConfigurerAdapter))
        );

        return factory.getObject();
    }

    @Override
    public void afterPropertiesSet() {
    }

//===============================================默认通知配置============================================================
    @Primary
    @Bean(name = "dingerProperties")
    @ConfigurationProperties(prefix = DingerConstant.DINGER_PROP_PREFIX)
    public DingerProperties dingerProperties() {
        return new DingerProperties();
    }

    @Bean(name = "dingerManagerBuilder")
    public DingerManagerBuilder dingerManagerBuilder() {
        return new DingerManagerBuilder();
    }

    @Bean(name = "dingerRobot")
    public DingerRobot dingerSender(DingerConfigurerAdapter dingerConfigurerAdapter){
        try {
            dingerConfigurerAdapter.configure(dingerManagerBuilder());
        } catch (Exception ex) {
            throw new ConfigurationException(ex);
        }
        return new DingerRobot(dingerProperties(), dingerManagerBuilder());
    }

//===============================================备用通知配置=============================================================
    @Bean(name = "dinger2Properties")
    @ConfigurationProperties(prefix = "spring.dinger2")
    public DingerProperties dinger2Properties() {
        return new DingerProperties();
    }

    @Bean(name = "dinger2ManagerBuilder")
    public DingerManagerBuilder dinger2ManagerBuilder() {
        return new DingerManagerBuilder();
    }

    @Bean(name = "dinger2Robot")
    public DingerRobot dinger2Sender(DingerConfigurerAdapter dingerConfigurerAdapter){
        try {
            dingerConfigurerAdapter.configure(dinger2ManagerBuilder());
        } catch (Exception ex) {
            throw new ConfigurationException(ex);
        }
        return new DingerRobot(dinger2Properties(), dinger2ManagerBuilder());
    }

//===============================================业务中台通知配置==========================================================
    @Bean(name = "dingerBusinessRobot")
    public DingerRobot dingerBusinessRobot(DingerConfigurerAdapter dingerConfigurerAdapter) throws Exception {
        dingerConfigurerAdapter.configure(dinger2ManagerBuilder());
        return new DingerRobot(dingerBusinessProperties(), dingerBusinessManagerBuilder());
    }

    @Bean(name = "dingerBusinessProperties")
    @ConfigurationProperties(prefix = "spring.dinger-business")
    public DingerProperties dingerBusinessProperties() {
        return new DingerProperties();
    }

    @Bean(name = "dingerBusinessManagerBuilder")
    public DingerManagerBuilder dingerBusinessManagerBuilder() {
        return new DingerManagerBuilder();
    }


//===============================================常州监控通知配置==========================================================
    @Bean(name = "dingerChangzhouRobot")
    public DingerRobot dingerChangzhouRobot(DingerConfigurerAdapter dingerConfigurerAdapter) throws Exception {
        dingerConfigurerAdapter.configure(dinger2ManagerBuilder());
        return new DingerRobot(dingerChangzhouProperties(), dingerChangzhouManagerBuilder());
    }

    @Bean(name = "dingerChangzhouProperties")
    @ConfigurationProperties(prefix = "spring.dinger-changzhou")
    public DingerProperties dingerChangzhouProperties() {
        return new DingerProperties();
    }

    @Bean(name = "dingerChangzhouManagerBuilder")
    public DingerManagerBuilder dingerChangzhouManagerBuilder() {
        return new DingerManagerBuilder();
    }

//===============================================广州监控通知配置==========================================================

    @Bean(name = "dingerGuangzhouRobot")
    public DingerRobot dingerGuangzhouRobot(DingerConfigurerAdapter dingerConfigurerAdapter) throws Exception {
        dingerConfigurerAdapter.configure(dinger2ManagerBuilder());
        return new DingerRobot(dingerGuangzhouProperties(), dingerGuangzhouManagerBuilder());
    }

    @Bean(name = "dingerGuangzhouProperties")
    @ConfigurationProperties(prefix = "spring.dinger-guangzhou")
    public DingerProperties dingerGuangzhouProperties() {
        return new DingerProperties();
    }

    @Bean(name = "dingerGuangzhouManagerBuilder")
    public DingerManagerBuilder dingerGuangzhouManagerBuilder() {
        return new DingerManagerBuilder();
    }

}