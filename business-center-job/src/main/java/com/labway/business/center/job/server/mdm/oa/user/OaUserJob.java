package com.labway.business.center.job.server.mdm.oa.user;

import com.labway.business.center.mdm.api.user.service.UserServerBaseService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description 部门信息同步定时任务
 * @Date 2023/3/9 11:38
 */
@Slf4j
@Component
public class OaUserJob {
    @DubboReference
    private UserServerBaseService userServerService;

    /**
     * 主数据 从 OA 全量同步所有的部门数据
     */
    @XxlJob("syncAllUserInfo")
    public void syncAllUserInfo() {
        XxlJobHelper.log("自动执行定时任务,主数据 从 OA 全量同步所有的人员数据:syncAllUserInfo");
        log.info("自动执行定时任务,主数据 从 OA 全量同步所有的人员数据:syncAllUserInfo");
        userServerService.syncAllUser();
    }

    /**
     * 主数据 从 OA 增量同步人员数据
     */
    @XxlJob("syncUserInfo")
    public void syncUserInfo() {
        XxlJobHelper.log("自动执行定时任务,主数据 从 OA 增量同步所有的人员数据:syncUserInfo");
        log.info("自动执行定时任务,主数据 从 OA 增量同步人员数据:syncUserInfo");
        userServerService.syncOaUser();
    }
}
