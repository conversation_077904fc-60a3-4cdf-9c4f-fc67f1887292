package com.labway.business.center.core.file;

import org.springframework.core.io.ByteArrayResource;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;

/**
 * <AUTHOR>
 */
public class CustomMultipartFile implements MultipartFile {
    private final ByteArrayResource resource;
    private final String name;
    private String originalFilename;
    private String contentType;

    public CustomMultipartFile(ByteArrayResource resource) {
        this.resource = resource;
        this.name = resource.getFilename();
    }

    @Override
    public String getName() {
        return name;
    }

    @Override
    public String getOriginalFilename() {
        return originalFilename;
    }

    public void setOriginalFilename(String originalFilename) {
        this.originalFilename = originalFilename;
    }

    @Override
    public String getContentType() {
        return contentType;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType;
    }

    @Override
    public boolean isEmpty() {
        return (resource == null || resource.contentLength() == 0);
    }

    @Override
    public long getSize() {
        return resource.contentLength();
    }

    @Override
    public byte[] getBytes() throws IOException {
        return resource.getByteArray();
    }

    @Override
    public InputStream getInputStream() throws IOException {
        return resource.getInputStream();
    }

    @Override
    public void transferTo(java.io.File dest) throws IOException, IllegalStateException {
        throw new UnsupportedOperationException("Not supported");
    }
}