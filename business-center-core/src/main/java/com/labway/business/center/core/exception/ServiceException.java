package com.labway.business.center.core.exception;

import lombok.Getter;

import java.util.Objects;

public class ServiceException extends RuntimeException{
    @Getter
    private final Integer code;


    public ServiceException(Integer code, String message) {
        super(message);
        this.code = Objects.requireNonNull(code, "code is null");
    }

    public ServiceException(Integer code, String message, Throwable cause) {
        super(message, cause);
        this.code = Objects.requireNonNull(code, "code is null");
    }

    public ServiceException(String message) {
        this(-1,message);
    }
}
