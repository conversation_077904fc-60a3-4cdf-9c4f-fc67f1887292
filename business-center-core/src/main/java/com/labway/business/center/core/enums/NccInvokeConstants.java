package com.labway.business.center.core.enums;

import lombok.Getter;
import lombok.experimental.UtilityClass;

/**
 * NccInvokeConstants ncc调用
 *
 * <AUTHOR>
 * @version 2023/04/07 11:51
 **/
@Getter
@UtilityClass
public class NccInvokeConstants {
    /**
     * 执行结果
     */
    public static final  String INVOKE_RESULT = "success";
    /**
     * message
     */
    public static final  String INVOKE_RESULT_MESSAGE = "message";
    /**
     * data
     */
    public static final  String INVOKE_RESULT_DATA = "data";
    /**
     * 新增人员成功标识
     */
    public static final  String SUCCESSFUL = "Y";
}