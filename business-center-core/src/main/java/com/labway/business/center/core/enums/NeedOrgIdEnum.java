package com.labway.business.center.core.enums;

import lombok.Getter;

/**
 * NeedOrgIdEnum 是否需要业务单元
 *
 * <AUTHOR>
 * @version 2023/04/27 18:28
 **/
@Getter
public enum NeedOrgIdEnum {
    NEED(0,"需要业务单元参数"),
    NOT_NEED(1,"不需要业务单元参数"),
    NEED_FINANCE(2,"需要金融组织参数"),
    IGNORE(-1,"不需要验证"),
    ;
    private Integer status;
    private String msg;
    
    NeedOrgIdEnum(Integer status,String msg) {
        this.status = status;
        this.msg = msg;
    }
}