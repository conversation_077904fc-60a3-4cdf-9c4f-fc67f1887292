package com.labway.business.center.core.util;

import java.util.function.IntUnaryOperator;

public class VersionGenerator {

	// 预设常用配置
	public static final VersionConfig STANDARD = new VersionConfig();
	public static final VersionConfig SIMPLE = new VersionConfig().setSegments(2).setIncrementRule(i -> (i > 99) ? 0 : i);
	public static final VersionConfig EXTENDED = new VersionConfig().setSegments(4).setAutoExtend(true);

	public static class VersionConfig {
		/**
		 * 版本号段数
		 */
		private int segments = 3;

		/**
		 * 设置初始主版本号
		 */
		private int initialVersion = 1;

		/**
		 * 自定义递增规则
		 */
		private IntUnaryOperator incrementRule = i -> (i > 99) ? 0 : i;

		/**
		 * 自动扩展位数
		 */
		private boolean autoExtend = false;

		// 链式配置方法
		public VersionConfig setSegments(int segments) {
			this.segments = segments;
			return this;
		}

		public VersionConfig setInitialVersion(int initialVersion) {
			this.initialVersion = initialVersion;
			return this;
		}

		/**
		 * 设置自定义递增规则
		 * @param rule 输入当前值，返回递增后的值（返回0时触发进位）
		 * @return 当前配置对象
		 */
		public VersionConfig setIncrementRule(IntUnaryOperator rule) {
			this.incrementRule = rule;
			return this;
		}

		/**
		 * 启用自动扩展位数
		 * @param autoExtend true时允许输入位数小于配置位数
		 * @return 当前配置对象
		 */
		public VersionConfig setAutoExtend(boolean autoExtend) {
			this.autoExtend = autoExtend;
			return this;
		}
	}

	/**
	 * 生成下一个版本号（使用默认配置）
	 *
	 * @param currentVersion 当前版本号（null或空字符串时返回初始版本）
	 * @return 新版本号
	 * @throws InvalidVersionException 版本号格式非法时抛出
	 */
	public static String generateNextVersion(String currentVersion) throws InvalidVersionException {
		return generateNextVersion(currentVersion, STANDARD);
	}

	/**
	 * 生成下一个版本号（自定义配置）
	 *
	 * @param currentVersion 当前版本号
	 * @param config 版本规则配置（可null，使用默认配置）
	 * @return 新版本号
	 * @throws InvalidVersionException 版本号格式非法时抛出
	 */
	public static String generateNextVersion(String currentVersion, VersionConfig config) throws InvalidVersionException {
		if (config == null) config = new VersionConfig();

		if (currentVersion == null || currentVersion.trim().isEmpty()) {
			return createInitialVersion(config);
		}

		String[] parts = currentVersion.split("\\.");
		validateVersionParts(parts, config);

		int[] versionNumbers = parseVersionNumbers(parts, config);
		incrementVersion(versionNumbers, config);

		return formatVersion(versionNumbers);
	}

	private static String createInitialVersion(VersionConfig config) {
		int[] numbers = new int[config.segments];
		numbers[0] = config.initialVersion;
		return formatVersion(numbers);
	}

	private static void validateVersionParts(String[] parts, VersionConfig config) throws InvalidVersionException {
		if (parts.length != config.segments && !config.autoExtend) {
			throw new InvalidVersionException(String.format("需要 %d 位版本号，实际收到 %d 位", config.segments, parts.length));
		}

		for (String part : parts) {
			if (!part.matches("^\\d+$")) {
				throw new InvalidVersionException("非法字符: " + part);
			}
		}
	}

	private static int[] parseVersionNumbers(String[] parts, VersionConfig config) {
		int[] numbers = new int[config.segments];
		for (int i = 0; i < numbers.length; i++) {
			if (i < parts.length) {
				numbers[i] = Integer.parseInt(parts[i]);
			} else {
				numbers[i] = 0; // 自动补齐缺失位
			}
		}
		return numbers;
	}

	private static void incrementVersion(int[] numbers, VersionConfig config) {
		int carry = 1;
		for (int i = numbers.length - 1; i >= 0 && carry > 0; i--) {
			int newValue = numbers[i] + carry;
			int result = config.incrementRule.applyAsInt(newValue);

			if (result == 0) {
				carry = 1;
				numbers[i] = 0;
			} else {
				carry = 0;
				numbers[i] = result;
			}
		}

		if (carry > 0) {
			numbers[0] += carry; // 最高位溢出保持增长
		}
	}

	private static String formatVersion(int[] numbers) {
		StringBuilder sb = new StringBuilder();
		for (int i = 0; i < numbers.length; i++) {
			sb.append(numbers[i]);
			if (i < numbers.length - 1) sb.append(".");
		}
		return sb.toString();
	}

	public static class InvalidVersionException extends Exception {
		public InvalidVersionException(String message) {
			super(message);
		}
	}

	public static void main(String[] args) {
		// 标准三段式版本（默认配置）
		VersionConfig standardConfig = new VersionConfig();
		testVersion("", standardConfig);              // → 1.0.0
		testVersion("1.0.99", standardConfig);        // → 1.1.0
		testVersion("2.99.99", standardConfig);       // → 3.0.0

		// 两位版本号，9进位
		VersionConfig simpleConfig = new VersionConfig().setSegments(2).setIncrementRule(i -> (i >= 9) ? 0 : i + 1);
		testVersion("", simpleConfig);                // → 1.0
		testVersion("1.9", simpleConfig);             // → 2.0

		// 四位版本号，自动扩展位数
		VersionConfig extendConfig = new VersionConfig().setSegments(4).setAutoExtend(true);
		testVersion("1.2", extendConfig);             // → 1.2.0.1
		testVersion("1.99.99.99", extendConfig);      // → 2.0.0.0

		// 自定义进位规则（16进制样式）
		VersionConfig hexConfig = new VersionConfig().setSegments(3).setIncrementRule(i -> (i >= 15) ? 0 : i + 1);
		testVersion("0.F.15", hexConfig);             // → 1.0.0 (假设输入为16进制)
	}

	private static void testVersion(String version, VersionConfig config) {
		try {
			String result = generateNextVersion(version, config);
			System.out.printf("%-10s (%d位) → %s\n", version.isEmpty() ? "null" : version, config.segments, result);
		} catch (InvalidVersionException e) {
			System.out.println("错误: " + e.getMessage());
		}
	}
}