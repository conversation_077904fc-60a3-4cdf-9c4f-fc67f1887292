 package com.labway.business.center.core.enums;

 public enum DefaultAbleEnum {
     
     NO_DEFAULT(0,"非默认选项"),DEFAULT(1,"默认选项");
     
     private int code;
     
     private String msg;

    private DefaultAbleEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }
    
    public static boolean validate(int status) {
        for(DefaultAbleEnum ableEnum:values()) {
            if (status==ableEnum.code) {
               return true; 
            }
        }
        return false;
    }

    public int getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
     
     

}
