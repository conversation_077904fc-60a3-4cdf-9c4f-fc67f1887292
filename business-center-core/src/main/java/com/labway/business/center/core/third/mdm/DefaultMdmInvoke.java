package com.labway.business.center.core.third.mdm;

import cn.hutool.http.HttpRequest;
import lombok.extern.slf4j.Slf4j;


/**
 * DefaultMdmInvoke 默认的主数据调用
 *
 * <AUTHOR>
 * @version 2023/05/09 11:37
 **/
@Slf4j
public class DefaultMdmInvoke implements AbstractMdmInvoker {
    
    @Override
    public String doInvoke(MdmConfig config, String path, String body) {
        String url = config.getBaseUrl() + path;
        HttpRequest httpRequest = HttpRequest.post(url).header("appKey",config.getAppKey())
                .header("appSecret",config.getAppSecret()).body(body);
        try {
            log.info("开始调用主数据，url：{},请求参数：{}",url,body);
            String response = httpRequest.execute().body();
            return response;
        } catch (Exception e) {
            log.info("调用主数据获取信息发生异常，异常信息", e);
            throw new MdmInvokeException(102, "调用主数据数据失败");
        }
    }
}