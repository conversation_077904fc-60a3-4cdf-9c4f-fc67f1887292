package com.labway.business.center.core.aop.param;

import lombok.Data;

import java.util.Date;

/**
 * OptRecordDTO
 *
 * <AUTHOR>
 * @version 2023/04/11 17:52
 **/
@Data
public class OptRecordDTO {
    
    /**
     * 操作记录id
     */
    private String recordId;
    /**
     * 记录类型
     */
    private Integer recordType;
    /**
     * 操作记录内容
     */
    private String recordContent;
    /**
     * 执行方法名
     */
    private String optMethod;
    /**
     * 请求参数
     */
    private String optRequest;
    /**
     * 响应参数
     */
    private String optResponse;
    /**
     * 操作类型 1增2删3改
     */
    private String optType;
    /**
     * 操作结果 0成功1失败
     */
    private Integer optStatus;
    /**
     * 系统编号
     */
    private String sysCode;
    /**
     * 创建人
     */
    private String createdBy;
    /**
     * 创建时间
     */
    private Date createdTime;
    /**
     * 操作人id
     */
    private String optUserId;
    /**
     * 操作人名称
     */
    private String optUserName;
    /**
     * 操作模块
     */
    private String optModule;
}