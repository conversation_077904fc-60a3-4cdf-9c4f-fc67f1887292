package com.labway.business.center.core.enums;


/**
 * 性别
 */
public enum PersonSexEnum {

    MAN(1,"男"),
    WOMAN(2,"女"),
    ;

    private Integer value;
    private String desc;

   public static PersonSexEnum getPersonSex(int value){
        for (PersonSexEnum applySampleStatus : PersonSexEnum.values()) {
            if (applySampleStatus.value.equals(value)){
                return applySampleStatus;
            }
        }
        return null;
    }


    PersonSexEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
