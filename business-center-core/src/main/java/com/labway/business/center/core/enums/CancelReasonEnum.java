package com.labway.business.center.core.enums;

/**
 * 取消外送原因
 */
public enum CancelReasonEnum {

    DEFAULT_REASON(0, "默认"),
    TMS_NOT_TAKE_SAMPLE(1, "物流未取样");

    private Integer value;

    private String desc;

    CancelReasonEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public int getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }
}
