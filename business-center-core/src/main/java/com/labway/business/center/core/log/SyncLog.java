package com.labway.business.center.core.log;

import com.labway.business.center.core.enums.SyncDataFrom;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * SyncLog 同步日志
 *
 * <AUTHOR>
 * @version 2023/04/27 11:30
 **/
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface SyncLog {
    // 数据来源
    SyncDataFrom dataFrom() default SyncDataFrom.NCC;
    // 任务名称
    String taskName();
}