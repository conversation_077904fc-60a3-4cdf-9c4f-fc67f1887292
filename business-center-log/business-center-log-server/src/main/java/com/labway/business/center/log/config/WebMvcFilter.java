package com.labway.business.center.log.config;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.labway.business.center.core.enums.ResultCode;
import com.labway.business.center.log.constants.LogConstant;
import com.labway.sso.core.conf.Constant;
import com.swak.frame.dto.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.*;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @version 2023/05/09 16:30
 **/
@Slf4j
@Component
@WebFilter(urlPatterns = "/*", filterName = "LoginUserInfoFilter")
@Order(2)//控制过滤器的级别
public class WebMvcFilter implements Filter {
    @Resource
    private ParamConfig paramConfig;
    private List<String> ignorePath = Lists.newArrayList("/log/list","/log/getAllType","/module/list","/system/list");
    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        log.info(">>>>>>>>>>> 日志认证过滤器初始化");
        Filter.super.init(filterConfig);
    }
    
    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
        HttpServletRequest request = (HttpServletRequest) servletRequest;
        String requestURI = request.getRequestURI();
        log.info("请求路径：" + requestURI);
        if (ignorePath.contains(requestURI)) {
            // 放行
            filterChain.doFilter(request, servletResponse);
            return;
        }
        // 判断请求是否合法
        String systemCode = request.getHeader(LogConstant.HAND_SYSTEM_CODE);
        String systemKey = request.getHeader(LogConstant.HAND_SYSTEM_KEY);
        if (StringUtils.isNotBlank(systemCode) && StringUtils.isNotBlank(systemKey)) {
            if (systemKey.equals(paramConfig.getSystemKey(systemCode))) {
                filterChain.doFilter(request, servletResponse);
                return;
            }
        }
        returnJson(servletResponse, JSON.toJSONString(Response.fail(ResultCode.REQUEST_HAND_ERROR)));
    }
    
    private void returnJson(ServletResponse response, String json) {
        PrintWriter writer = null;
        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/json; charset=utf-8");
        try {
            writer = response.getWriter();
            writer.print(json);
        } catch (IOException e) {
            log.error("response error", e);
        } finally {
            if (writer != null) {
                writer.close();
            }
        }
    }

    
}