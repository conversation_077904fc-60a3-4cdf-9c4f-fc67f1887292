package com.labway.business.center.log.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.labway.business.center.log.persistence.entity.OperationSystemModule;
import com.labway.business.center.log.persistence.mapper.OperationSystemModuleMapper;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2023/06/16 13:23
 **/
@Repository
public class OperationSystemModuleRepository {
    @Resource
    private OperationSystemModuleMapper operationSystemModuleMapper;

    public List<OperationSystemModule> getAllSystemModule() {
        return operationSystemModuleMapper.selectList(null);
    }

    public int saveBatch(List<OperationSystemModule> insertList) {
        return operationSystemModuleMapper.saveBatch(insertList);
    }

    public int insert(OperationSystemModule operationSystemModule) {
        return operationSystemModuleMapper.insert(operationSystemModule);
    }

    /**
     * 获取系统下的所有模块
     * @param systemKey
     * @return
     */
    public List<OperationSystemModule> getSystemModuleBySystemKey(String systemKey) {
        LambdaQueryWrapper<OperationSystemModule> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OperationSystemModule::getSystemKey,systemKey);
        return operationSystemModuleMapper.selectList(queryWrapper);
    }
}