package com.labway.business.center.log.service.impl;

import com.labway.center.log.api.service.LogMonitorService;
import com.swak.frame.dto.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.validation.annotation.Validated;

/**
 * <AUTHOR>
 */
@Slf4j
@DubboService
@RefreshScope
public class LogMonitorServiceImpl implements LogMonitorService {
    @Override
    public Response<?> serviceMonitor() {
        return Response.success();
    }

    @Override
    public Response<?> getServiceHeartbeat() {
        return Response.success();
    }
}
