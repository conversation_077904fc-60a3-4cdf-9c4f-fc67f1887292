package com.labway.business.center.log.persistence.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;

/**
 * 
 * @TableName tb_operation_system_module
 */
@TableName(value ="tb_operation_system_module")
@Data
public class OperationSystemModule implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 系统key
     */
    @TableField(value = "system_key")
    private String systemKey;

    /**
     * 系统模块名称
     */
    @TableField(value = "system_module_name")
    private String systemModuleName;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}