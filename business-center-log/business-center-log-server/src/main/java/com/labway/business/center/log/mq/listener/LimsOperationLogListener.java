package com.labway.business.center.log.mq.listener;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.labway.business.center.core.util.RedisUtils;
import com.labway.business.center.log.constants.RabbitConstant;
import com.labway.business.center.core.log.param.MessageDTO;
import com.labway.business.center.core.log.param.OperationLogRequest;
import com.labway.center.log.api.service.OperationLogService;
import com.rabbitmq.client.Channel;
import com.swak.frame.dto.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @since 2023/3/2 15:56
 */
@Slf4j
@Component
public class LimsOperationLogListener implements ChannelAwareMessageListener {
    @Resource
    private RedisUtils redisUtils;
    @Resource
    private OperationLogService operationLogService;
    
    @Override
    @RabbitListener(queues = {RabbitConstant.LIMS_QUEUES_NAME})
    public void onMessage(Message message, Channel channel) throws Exception {
        long deliveryTag = message.getMessageProperties().getDeliveryTag();
        String body = new String(message.getBody(), "UTF-8");
        log.info("--------------监听到上报的日志内容：{}",body);
        MessageDTO messagedDto = JSON.parseObject(body, MessageDTO.class);
        if (StringUtils.isBlank(messagedDto.getMessageId())) {
            channel.basicNack(deliveryTag, true, true);
            return;
        }
        if (!redisUtils.lock(RabbitConstant.REDIS_MQ_KEY + messagedDto.getMessageId(),StringUtils.EMPTY,1,TimeUnit.HOURS)) {
            channel.basicAck(deliveryTag, true);
            return;
        }
        try {
            /**
             * 确认消息，参数说明：
             * long deliveryTag：唯一标识 ID。
             * boolean multiple：是否批处理，当该参数为 true 时，
             * 则可以一次性确认 deliveryTag 小于等于传入值的所有消息。
             */
            String operationLog = messagedDto.getMessage();
            OperationLogRequest operationLogRequest = JSON.parseObject(operationLog, OperationLogRequest.class);
            Response<String> response = operationLogService.saveOperationLog(operationLogRequest);
            if (response.isSuccess()) {
                channel.basicAck(deliveryTag, true);
            }else {
                log.error("日志入库失败，响应内容：{}", JSONUtil.toJsonStr(response));
                channel.basicNack(deliveryTag, true, true);
            }
            /**
             * 否定消息，参数说明：
             * long deliveryTag：唯一标识 ID。
             * boolean multiple：是否批处理，当该参数为 true 时，
             * 则可以一次性确认 deliveryTag 小于等于传入值的所有消息。
             * boolean requeue：如果 requeue 参数设置为 true，
             * 则 RabbitMQ 会重新将这条消息存入队列，以便发送给下一个订阅的消费者；
             * 如果 requeue 参数设置为 false，则 RabbitMQ 立即会还把消息从队列中移除，
             * 而不会把它发送给新的消费者。
             */
        }
        catch (Exception e) {
            log.error("mq消费出现异常",e);
            /**
             * 拒绝消息，参数说明：
             * long deliveryTag：唯一标识 ID。
             * boolean requeue：如果 requeue 参数设置为 true，
             * 则 RabbitMQ 会重新将这条消息存入队列，以便发送给下一个订阅的消费者；
             * 如果 requeue 参数设置为 false，则 RabbitMQ 立即会还把消息从队列中移除，
             * 而不会把它发送给新的消费者。
             */
            channel.basicReject(deliveryTag, true);
        }
    }
}
