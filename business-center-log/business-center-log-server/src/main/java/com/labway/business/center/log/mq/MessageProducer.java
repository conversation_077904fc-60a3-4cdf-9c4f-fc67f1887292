package com.labway.business.center.log.mq;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.labway.business.center.log.constants.RabbitConstant;
import com.labway.business.center.core.log.param.MessageDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * mq消息发送方
 * <AUTHOR>
 * @version 2023/06/08 10:08
 **/
@Slf4j
@Component
public class MessageProducer {
    @Resource
    private RabbitTemplate rabbitTemplate;
    @Async
    public void sendMessage(String messageInfo) {
        MessageDTO messageDTO = MessageDTO.builder().messageId(IdUtil.simpleUUID()).sendTime(System.currentTimeMillis()).message(messageInfo).build();

        // 发送消息
        rabbitTemplate.convertAndSend(RabbitConstant.OPERATION_OUT_EXCHANGE,"", JSONUtil.toJsonStr(messageDTO));
    }
}