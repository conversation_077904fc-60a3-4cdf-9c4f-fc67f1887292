package com.labway.business.center.log.config;


import com.labway.business.center.log.constants.RabbitConstant;
import org.springframework.amqp.core.FanoutExchange;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * RabbitmqConfig
 *
 */
@Configuration
public class RabbitmqConfig {


    /**
     * 创建广播式交换机
     * @return
     */
    @Bean
    public FanoutExchange logFanoutExchange() {
        return new FanoutExchange(RabbitConstant.OPERATION_OUT_EXCHANGE);
    }

}
