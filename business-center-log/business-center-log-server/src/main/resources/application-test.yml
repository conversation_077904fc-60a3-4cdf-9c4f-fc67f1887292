# 不分库,分表

spring:
  shardingsphere:
    mode:
      type: Memory # Sharding-jdbc 运行模式
    datasource:
      names: ds1  # 数据源别名
      ds1:
        type: com.zaxxer.hikari.HikariDataSource  # 数据源连接池连接类型,支持 Durid,Hikari 等常见数据库连接池
        driver-class-name: com.mysql.cj.jdbc.Driver  # 数据库连接驱动
        jdbc-url: **********************************************************************************************************************************************************************************************************************
        username: root
        password: <PERSON><PERSON>ei@1234!
    rules:  # 分库分表规则配置
      sharding:
        tables:
          tb_operation_log:  # 数据库表名前缀
            actual-data-nodes: ds1.tb_operation_log_${['labway_lus','business_finance','labway_mdm','business_compare','business_check','learning_platform','labway_lims']}  # 实际表名
            table-strategy:  # 分表策略
              standard:
                sharding-column: system_key  # 按照分表的列
                sharding-algorithm-name: tb_operation_log  # 分表算法名称(使用 yml 配置不能包含下划线,否则不生效)
            key-generate-strategy:  # 主键生成策略
              column: id  # 主键列
              key-generator-name: snowflake  # 策略算法名称(推荐使用雪花算法)
        sharding-algorithms:
          tb_operation_log:
            type: INLINE
            props:
              algorithm-expression: tb_operation_log_$->{system_key}  # 分表算法(取余)
        key-generators:
          snowflake:
            type: SNOWFLAKE #主键类型
    props:
      sql-show: true  # 展示执行 SQL
  redis:
    host: ************
    password: hDKSVg5v75%wzdGn
  rabbitmq:
    host: ************
    port: 5672
    username: root
    password: Huawei@1234!
    virtual-host: vhost
    # 消息确认（ACK）
    publisher-confirm-type: correlated #确认消息已发送到交换机(Exchange)
    publisher-returns: true #确认消息已发送到队列(Queue)
mybatis-plus:
  mapper-locations: classpath:/mapper/*.xml
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
dubbo:
  application:
    name: business-center-log-server
  protocol:
    name: dubbo
  #    port: -1
  registry:
    address: nacos://${nacos.address}?namespace=${nacos.discovery-namespace}
  provider:
    timeout: 10000
  consumer:
    check: false
    loadbalance: roundrobin
# huawei ods
huawei:
  obs:
    ak: L2NO0C3UJDB7SFYQKQIY
    sk: lSc1G2f52iTndVB0cpdDEU89ZjfcxjiSErOMlRDN
    endPoint: obs.cn-east-3.myhuaweicloud.com
    bucketName: labway-obs
labway:
  sso:
    redis-address: redis://************:6379
    redis-password: hDKSVg5v75%wzdGn
    redis-expire: 1440
    logout: /logout
    server: http://localhost:9980/sso-server
    excluded-paths: /**