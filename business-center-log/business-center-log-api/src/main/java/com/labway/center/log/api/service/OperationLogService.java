package com.labway.center.log.api.service;


import com.labway.business.center.core.log.param.OperationLogRequest;
import com.labway.center.log.api.request.OperationLogSearchRequest;
import com.swak.frame.dto.Response;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;

/**
* <AUTHOR>
* @description 针对表【tb_operation_log(操作日志记录表)】的数据库操作Service
* @createDate 2023-06-15 11:10:05
*/
@Validated
public interface OperationLogService {

    /**
     * 保存操作日志
     * @param operationLogRequest
     * @return
     */
    Response<String> saveOperationLog(@Valid OperationLogRequest operationLogRequest);

    Response getList(OperationLogSearchRequest operationLogSearchRequest);

    /**
     * mq发送日志
     * @param operationLogRequest
     * @return
     */
    Response<String> sendMqTest(OperationLogRequest operationLogRequest);
}
