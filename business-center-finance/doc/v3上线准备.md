# 三期上线 25.6.12
1. 菜单调整
2. 表结构导入
3. 基础表数据初始化（预算部门、预算类别）
4. 新建SSO api用户
5. 配置文件修改
6. 更新SSO服务
```yaml
labway:
  sso:
    user-url: /api/user/hasSystem
    system-token: d9729feb74992cc3482b350163a1a010
finance:
  exclusion-path:
    - "/v3/budget/use" # 预算使用
  exclusion-budget-form-type:
    - "人力成本表"
```
``` 需要新增的表
tb_budget
tb_budget_add
tb_budget_available
tb_budget_control_policy
tb_budget_dept
tb_budget_fee_type_mapping
tb_budget_flow
tb_budget_type
tb_budget_unit_user
tb_budget_version
tb_revenue
tb_fee_budget_form_type
tb_budget_release
```
```表结构修改
alter table tb_voucher
    modify status tinyint null comment '凭证状态(0-待制单，1，以制单，2 ，不制单, 3作废单据)';

```
7. 接口地址信息
```
穿透：https://center.labway.cn/FeeBudgetPage/#/fee
SSO：https://center.labway.cn/sso-server
费控：https://center.labway.cn/BussinessCenterFeeControl
```
8. 对OA提供的接口要去除加解密
9. 定时任务（同步预算部门）

## 第二次更新
```sql
alter table tb_budget_control_policy
    add has_revenue tinyint(1) default 0 null comment '部门是否有收入0-没有，1-有' after budget_dept_id;

```