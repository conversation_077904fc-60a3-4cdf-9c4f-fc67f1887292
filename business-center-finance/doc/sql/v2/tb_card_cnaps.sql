/*
 Navicat Premium Data Transfer

 Source Server         : 测试环境
 Source Server Type    : MySQL
 Source Server Version : 80025
 Source Host           : ***********:3306
 Source Schema         : business-finance

 Target Server Type    : MySQL
 Target Server Version : 80025
 File Encoding         : 65001

 Date: 30/11/2023 14:28:02
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for tb_card_cnaps
-- ----------------------------
DROP TABLE IF EXISTS `tb_card_cnaps`;
CREATE TABLE `tb_card_cnaps`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `card_no` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '银行卡号',
  `cnaps` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '联行号',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uniq_card`(`card_no` ASC) USING BTREE COMMENT '唯一索引'
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '卡号对应的联行号' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of tb_card_cnaps
-- ----------------------------
INSERT INTO `tb_card_cnaps` VALUES (1, '6226620603728320', '303290000018');

SET FOREIGN_KEY_CHECKS = 1;
