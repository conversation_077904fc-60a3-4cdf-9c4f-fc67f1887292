/*
 Navicat Premium Data Transfer

 Source Server         : 测试环境
 Source Server Type    : MySQL
 Source Server Version : 80025
 Source Host           : ***********:3306
 Source Schema         : business-finance

 Target Server Type    : MySQL
 Target Server Version : 80025
 File Encoding         : 65001

 Date: 30/11/2023 14:30:17
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for tb_finance_bill_diff
-- ----------------------------
DROP TABLE IF EXISTS `tb_finance_bill_diff`;
CREATE TABLE `tb_finance_bill_diff`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `org_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '业务单元',
  `status` tinyint NULL DEFAULT NULL COMMENT '处理状态(0-未处理，1，已处理)',
  `batch_id` int NULL DEFAULT NULL COMMENT '批次号',
  `serial` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '序列号',
  `result_flag` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '响应码',
  `push_time` datetime NULL DEFAULT NULL COMMENT '推送日期',
  `receive_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '收款人',
  `receive_bank_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '收款银行名称',
  `receive_bank_account` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '收款银行账号',
  `pay_bank_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '付款银行名称',
  `pay_bank_account` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '付款银行账号',
  `pay_remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '付款事由',
  `pay_time` datetime NULL DEFAULT NULL COMMENT '付款时间',
  `pay_money` decimal(20, 2) NULL DEFAULT NULL COMMENT '支付金额',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '资金管理平台差池账单表' ROW_FORMAT = DYNAMIC;

SET FOREIGN_KEY_CHECKS = 1;
