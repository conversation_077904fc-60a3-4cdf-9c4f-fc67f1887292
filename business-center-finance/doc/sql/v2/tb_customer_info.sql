/*
 Navicat Premium Data Transfer

 Source Server         : 测试环境
 Source Server Type    : MySQL
 Source Server Version : 80025
 Source Host           : ***********:3306
 Source Schema         : business-finance

 Target Server Type    : MySQL
 Target Server Version : 80025
 File Encoding         : 65001

 Date: 30/11/2023 14:28:34
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for tb_customer_info
-- ----------------------------
DROP TABLE IF EXISTS `tb_customer_info`;
CREATE TABLE `tb_customer_info`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `customer_code` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '客商编码',
  `customer_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '客商名称',
  `enabled` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否有效 0无效 1有效',
  `custclass` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '客户基本分类',
  `areacl` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '地区分类',
  `status` tinyint(1) NULL DEFAULT 0 COMMENT '客商状态0-开启，1-关闭',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1727898012136509626 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '客商信息表' ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
