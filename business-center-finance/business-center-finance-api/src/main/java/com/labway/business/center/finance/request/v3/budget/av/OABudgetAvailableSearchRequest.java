package com.labway.business.center.finance.request.v3.budget.av;

import cn.hutool.core.date.DateUtil;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;
import java.util.Set;

/**
 * 可用预算搜索
 *
 * <AUTHOR> on 2025/4/18.
 */
@Getter
@Setter
public class BudgetAvailableListSearchRequest implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 默认为当年
	 */
	private Integer budgetYear = DateUtil.year(DateUtil.date());

	/**
	 * 预算部门id 可多选
	 */
	private List<Long> budgetDeptId;

	/**
	 * 预算单元
	 */
	private Long oaBudgetUnitId;

	/**
	 * 月份搜索
	 */
	private List<Integer> searchMonths;

	/**
	 * 预算单元名称
	 */
	private String budgetUnitName;

	/**
	 * 预算部门名称
	 */
	private String budgetDeptName;

	/**
	 * oa费用类别
	 */
	private List<Long> oaFeeTypeIds;

	/**
	 * 预算类别id
	 */
	private Set<Long> budgetTypeIds;

}
