package com.labway.business.center.finance.dto.v2.bank.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * 电子回单文件查询实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PFReceiptQueryRequest implements Serializable {

    // 银行账号
    private String ACCOUNT_CODE;

    // 查询日期 格式：yyyy-MM-dd，不超过当前日期
    private String QUERY_DATE;

    // 查询笔数 每次响应最大笔数，最大500
    private String QUERY_NUMBER;

    // 开始笔数 从1开始
    private String START_NUMBER;

    // 收支方向枚举值:
    //2 全部，1 收入，0 支出
    private String INOUT_FLAG;


}
