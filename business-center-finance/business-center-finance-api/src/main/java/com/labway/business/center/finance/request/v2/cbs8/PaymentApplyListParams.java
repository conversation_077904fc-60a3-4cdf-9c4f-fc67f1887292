 package com.labway.business.center.finance.request.v2.cbs8;

import java.io.Serializable;
import java.util.List;

import com.labway.business.center.finance.dto.v2.cbs8.Cbs8CommonParams;
import lombok.Data;

/**
 * 查询支付申请单状态及支付状态。
1.当前企业在系统内支付申请单列表信息，包括申请单编号、收付款账号、名称币种、金额等要素。
 * <AUTHOR>
 * @date 2023/11/03
 */

@Data
public class PaymentApplyListParams extends Cbs8CommonParams implements Serializable {

       private String amountMax;
       private String amountMin;
       private List<String> busTypeList;
       private String payAccount;
       private List<String> payBankTypeList;
       private List<String> payStatusList;
       private String queryDateEnd;
       private String queryDateStart;
       private String referenceNum;
       private List<String> statusList;
       private List<String> payChannelList;
}
