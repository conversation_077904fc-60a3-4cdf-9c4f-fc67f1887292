
package com.labway.business.center.finance.dto;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.LocalTime;

import com.swak.frame.dto.base.VO;

import lombok.Data;


/**
 * <p>
 * <B>Description: 辅助核算 (bd_accass / nc.vo.bd.account.AccAssVO)DTO</B>
 * </P>
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class AccassDTO implements VO {

	  private Long id;
	    /**
	    * 辅助核算ID
	    */
	    private String accassId;
	    /**
	    * 会计辅助核算项目 (accassitem)
	    */
	    private String itemId;
	    /**
	    * 辅助核算项编码
	    */
	    private String itemCode;
	    /**
	    * 辅助核算项名称
	    */
	    private String itemName;
	    /**
	    * 所属组织ID
	    */
	    private String orgId;
	    /**
	    * 所属集团ID
	    */
	    private String groupId;
	    /**
	    * 所对应的表
	    */
	    private String entryId;
	    /**
	    * 序号
	    */
	    private Integer accassNo;
	    /**
	    * 会计科目 (accasoa)
	    */
	    private String coverAccasoaId;
	    /**
	    * 创建时间
	    */
	    private LocalDateTime createTime;
}