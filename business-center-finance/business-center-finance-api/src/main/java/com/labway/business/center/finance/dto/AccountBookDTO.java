package com.labway.business.center.finance.dto;

import com.swak.frame.dto.base.VO;

import lombok.Data;


@Data
public class AccountBookDTO implements VO {
    
    /**
     * 核算账簿主键
     */
    private String accountingBookId;
    /**
     * 核算账簿编码
     */
    private String code;
    /**
     * 核算账簿名称
     */
    private String name;
    
    /**
     * 核算账簿名称
     */
    private String orgId;
    
    /**
     * 所属集团
     */
    private String groupId;
    /**
     * 账簿分类 ,1=主账簿;2=报告账簿;
     */
    private Integer accountType;
    /**
     * 总账账簿启用状态
     */
    private Integer accountEnableState;
    
    
    /**
     * 默认的现金流程编码
     */
    private String defaultCashflowCode;
    
    
    /**
     * 是否抵税0 不抵税，1 抵税
     */
    private Integer isTax;
    
    /**
     * 财务组织名称
     */
    private String financeOrgName;
    private String financeOrgId;
}
