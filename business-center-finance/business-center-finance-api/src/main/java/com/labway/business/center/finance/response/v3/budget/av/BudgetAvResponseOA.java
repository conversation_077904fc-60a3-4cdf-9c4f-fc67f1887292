package com.labway.business.center.finance.response.v3.budget.av;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * oa穿透预算
 *
 * <AUTHOR> on 2025/6/28.
 */
@Data
public class BudgetAvResponseOA implements Serializable {

	/**
	 * 部门汇总
	 */
	private BudgetAvSum deptBudgetAvSum;

	/**
	 * 类别汇总
	 */
	private BudgetAvSum typeBudgetAvSum;

	/**
	 * 部门汇总
	 */
	@Data
	public static class BudgetAvSum implements Serializable{

		/**
		 * 1-12月预算
		 */
		private BigDecimal totalBudgetAmount;

		/**
		 * 1-当前月预算
		 */
		private BigDecimal nowMonthBudgetAmount;

		/**
		 * 1-当前月执行数
		 */
		private BigDecimal totalUseAmount;

		/**
		 * 当期可用
		 */
		private BigDecimal nowMonthAvAmount;

		/**
		 * 总可用
		 */
		private BigDecimal totalMonthAvAmount;


		public static BudgetAvSum init() {
			BudgetAvSum budgetAvSum = new BudgetAvSum();
			budgetAvSum.setTotalBudgetAmount(BigDecimal.ZERO);
			budgetAvSum.setNowMonthBudgetAmount(BigDecimal.ZERO);
			budgetAvSum.setTotalUseAmount(BigDecimal.ZERO);
			budgetAvSum.setNowMonthAvAmount(BigDecimal.ZERO);
			budgetAvSum.setTotalMonthAvAmount(BigDecimal.ZERO);
			return budgetAvSum;
		}

	}
}
