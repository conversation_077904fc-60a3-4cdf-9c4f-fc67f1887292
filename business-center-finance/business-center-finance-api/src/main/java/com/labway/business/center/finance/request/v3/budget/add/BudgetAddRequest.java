package com.labway.business.center.finance.request.v3.budget.add;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 追加预算请求
 *
 * <AUTHOR> on 2025/5/14.
 */
@Data
public class BudgetAddRequest implements Serializable {

	private static final long serialVersionUID = 1L;

	@NotBlank(message = "申请用户不能为空")
	private String addUser;

	@NotBlank(message = "预算单元不能为空")
	private String budgetUnitName;

	@NotBlank(message = "预算部门不能为空")
	private String budgetDeptName;

	/**
	 * 预算为审批表 单号
	 */
	private String oaNo;

	/**
	 * 追加发起时间
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private LocalDateTime addTime;

	/**
	 * 审核时间
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private LocalDateTime auditTime;

	/**
	 * 审核人
	 */
	private String auditUser;

	/**
	 * 单据备注
	 */
	private String remark;

	private List<BudgetAddDetailRequest> details;


	@Data
	public static class BudgetAddDetailRequest {
		@NotNull(message = "请选择预算类型")
		private Long typeId;

		@NotNull(message = "请输入追加金额")
		@DecimalMin(value = "0", message = "追加金额不能小于0")
		private BigDecimal addAmount;

		/**
		 * 追加月度
		 * @see com.labway.business.center.finance.constants.MonthEnums
		 */
		private String addMonth;
	}
}
