 package com.labway.business.center.finance.service;

import java.util.List;

import com.labway.business.center.finance.dto.AccountBookDTO;
import com.labway.business.center.core.config.BaseRequest;
import com.labway.business.center.finance.request.SetTaxRuleRequest;
import com.swak.frame.dto.Response;

/**
 * ncc 账簿_财务核算账簿 服务类
 * <AUTHOR>
 * @date 2023/03/23
 */
 public interface AccountingBookService {
     
     /**
      * 清洗NCC账簿_财务核算账簿信息到业务中台数据库
      * @return
      */
     public Response<?> washAccountingBookToCenterDatabase();
     
     /**
      * 获取所有的核算账簿
      * @return
      */
     public Response<List<AccountBookDTO>> getAllAccountBook(BaseRequest baseRequest);
     
     /**
      * 设置税规则
      * @param request
      * @return
      */
     public Response<String> setTaxRule(SetTaxRuleRequest request );
     

}
