package com.labway.business.center.finance.request.v3.budget.release;

import cn.hutool.core.date.DateUtil;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 查询预算释放记录
 *
 * <AUTHOR> on 2025/6/3.
 */
@Data
public class BudgetReleaseSearchRequest implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 默认为当年
	 */
	private Integer budgetYear = DateUtil.year(DateUtil.date());

	/**
	 * 预算部门id
	 */
	private Long budgetDeptId;

	/**
	 * 预算单元
	 */
	@NotNull(message = "请选择预算单元")
	private Long budgetUnitId;

	/**
	 * 预算类别id
	 */
	private Long budgetTypeId;

	/**
	 * 月份搜索
	 */
	private List<Integer> searchMonths;

	/**
	 * 释放状态
	 * 释放状态0-已借用，1-已作废
	 */
	private Integer releaseStatus;

}
