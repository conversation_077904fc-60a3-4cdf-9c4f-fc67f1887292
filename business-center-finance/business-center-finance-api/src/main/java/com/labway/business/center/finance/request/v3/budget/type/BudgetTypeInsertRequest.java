package com.labway.business.center.finance.request.v3.budget.type;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 预算类型-新增
 */
@Data
public class BudgetTypeInsertRequest {

    /**
     * 预算类型编码
     */
	@NotBlank(message = "预算类型编码不能为空")
    private String typeCode;

    /**
     * 预算类型名称
     */
    @NotBlank(message = "预算类型名称不能为空")
    private String typeName;

    /**
     * 费用性质
     */
    @NotBlank(message = "费用性质不能为空")
    private String costNature;

    /**
     * 费用说明
     */
    private String budgetExplain;

	/**
	 * 父级id
	 */
	@NotNull(message = "请选择父级")
	private Long parentId;

    /**
     * 预算类型级别, 1-一级, 2-二级
     * @see LevelTypeEnums
     */
	@NotNull(message = "预算类型级别不能为空")
    private Integer typeLevel;

	/**
	 * 是否可以导入
	 * 0-不可以导入，1-可以导入
	 * @see com.labway.business.center.finance.constants.YesOrNoEnums
	 */
	@NotNull(message = "是否可以导入不能为空")
	private Integer canImport;

    /**
     * 状态 0-启用,1-停用
     */
    @NotNull(message = "状态不能为空")
    private Integer status;
}