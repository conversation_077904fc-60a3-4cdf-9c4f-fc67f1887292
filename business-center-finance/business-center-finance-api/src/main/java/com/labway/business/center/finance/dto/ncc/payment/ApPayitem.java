package com.labway.business.center.finance.dto.ncc.payment;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 2023/12/29 11:06
 **/
@Data
public class ApPayitem implements Serializable {
    /**
     * 摘要
     */
    private String scomment;

    /**
     * 发票号
     */
    private String invoiceno;

    /**
     * 供应商编码
     */
    private String supplier;

    /**
     * 业务员
     */
    private String pk_psndoc;

    /**
     * 付款业务类型 固定为 货款
     */
    private String pk_recpaytype = "001";

    /**
     * 付款性质 固定为 预付款
     */
    private String prepay = "0";

    /**
     * 币种
     */
    private String pk_currtype;

    /**
     * 借方原币金额
     */
    private String money_de;
    /**
     * 组织方金额
     */
    private String local_money_de;

    private String objtype = "1";

    private String pu_deptid;

    private String pu_deptid_v;

    private String pk_deptid_v;

    private String pk_deptid;

    /**
     * 结算方式
     */
    private String pk_balatype;
    /**
     * 付款银行
     */
    private String payaccount;

    /**
     * 订单号
     */
    private String purchaseorder;
}