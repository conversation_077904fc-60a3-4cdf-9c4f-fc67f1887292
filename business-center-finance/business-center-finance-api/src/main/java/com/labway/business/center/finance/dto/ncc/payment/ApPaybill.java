package com.labway.business.center.finance.dto.ncc.payment;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 供应商付款单 (ap_paybill / nc.vo.arap.pay.PayBillVO)
 * <AUTHOR>
 * @version 2023/12/27 17:08
 **/
@Data
public class ApPaybill implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 单据日期
     */
    private String billdate;

    /**
     * 付款财务组织
     * 业务单元
     * */
    private String pk_org;

    /**
     * 供应商编码
     */
    private String supplier;

    /**
     * 部门
     */
    private String pk_dept;

    /**
     * 部门
     */
    private String pk_deptid_v;

    /**
     * 部门
     */
    private String pk_deptid;

    /**
     * 部门
     */
    private String pu_deptid;
    /**
     * 部门
     */
    private String pu_deptid_v;

    /**
     * 业务员编码
     */
    private String pk_psndoc;

    /**
     * 币种
     */
    private String pk_currtype;

    /**
     * 原币金额
     */
    private String money;

    /**
     * 组织本币金额
     */
    private String local_money;

    /**
     * 付款类型 固定 付款单
     */
    private String pk_tradetype = "D3";

    /**
     * 结算方式编码 固定为 网银
     */
    private String pk_balatype = "3";

    /**
     * 付款银行账户
     */
    private String payaccount;

    /**
     * 应付业务类型编码 要用主键
     */
    private String def1;

    /**
     * 往来对象
     */
    private String objtype = "1";

    /**
     * 本币汇率
     */
    private String rate;

    private String billstatus = "-1";

    /**
     * 制单人编码
     */
    private String billmaker;


    /**
     * 详情
     */
    private List<ApPayitem> items;
}