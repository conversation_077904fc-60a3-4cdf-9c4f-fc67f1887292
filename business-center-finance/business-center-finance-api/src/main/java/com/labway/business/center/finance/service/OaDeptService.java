package com.labway.business.center.finance.service;

import com.labway.business.center.finance.dto.DeptTypeDto;
import com.labway.business.center.finance.dto.OaDeptDTO;
import com.labway.business.center.finance.request.DeptMappingSelectRequest;
import com.labway.business.center.finance.request.DeptTypeMappingRequest;
import com.labway.business.center.finance.request.OaDeptRequest;
import com.swak.frame.dto.Response;

import java.util.List;

/**
 * OaDeptService
 * 主数据系统获取OA系统的部门信息
 * <AUTHOR>
 * @version 2023/03/27 10:54
 **/
public interface OaDeptService {
    /**
     * 从主数据 全量同步 oa所有的部门数据
     * @return OA所有的部门信息
     */
    Response<?> syncAllOaDeptByMdm();
    
    /**
     * 批量插入部门
     * @param oaDeptDTOList 部门dto对象
     * @return 成功的条数
     */
    Response<?> insertOaDeptBatch(List<OaDeptDTO> oaDeptDTOList);
    
    /**
     * 从主数据 增量同步 oa所有的部门数据
     * @return OA所有的部门信息
     */
    Response<?> syncOaDeptByMdm();
    
    /**
     * 分页查询
     * @param oaDeptRequest 分页查询部门信息
     * @return 部门信息
     */
    Response<?> selectOaDeptPage(OaDeptRequest oaDeptRequest);
    
    /**
     * 根据名称模糊查询
     * @param oaDeptRequest 模糊查询
     * @return oa部门信息
     */
    Response<?> selectOaDeptList(OaDeptRequest oaDeptRequest);
    
    /**
     * 部门类别对照列表
     * @return 部门类别对照列表
     */
    Response<DeptTypeDto> deptTypeMapping();
    
    /**
     * 部门类别对照操作
     * @param deptTypeMappingRequest 类别对照操作
     * @return 是否成功
     */
    Response<?> deptTypeMappingOperation(DeptTypeMappingRequest deptTypeMappingRequest);
    
    /**
     * 根据部门类型编码 和 业务单元 查询 oa部门列表
     * @param deptMappingSelectRequest
     * @return
     */
    Response<List<OaDeptDTO>> getMappingList(DeptMappingSelectRequest deptMappingSelectRequest);
}