package com.labway.business.center.finance.request.v2;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 银行卡 是否签约银企直连
 *
 * <AUTHOR>
 * @version 2023/11/13 16:35
 **/
@Data
public class BankSignContractRequest implements Serializable {
    @NotBlank(message = "请选择银行账户")
    private String bankId;
    /**
     * 是否签约
     * 0 否 1 是
     */
    @NotNull(message = "请选择是否签约")
    private Integer signContract;
}