package com.labway.business.center.finance.request.v3.budget;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 使用oa的单据id进行单据占用
 *
 * <AUTHOR> on 2025/5/27.
 */
@Data
public class BudgetUseByOaFlowNosRequest implements Serializable {

	@NotBlank(message = "请输入使用单号")
	private String useOaFlowNo;

	/**
	 * 使用类型
	 * @see BudgetUseEnums
	 */
	private Integer useType;
}
