package com.labway.business.center.finance.dto.ncc.employee;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * Ufinterface
 *
 * <AUTHOR>
 * @version 2023/04/21 11:08
 **/
@Data
@Accessors(chain = true)
public class Ufinterface implements Serializable {
    /**
     * 单据类型
     */
    private String billtype;
    /**
     * 是否使用NC翻译：一般默认为Y
     */
    private String isexchange = "Y";
    /**
     * 是否允许更新：一般默认为Y
     */
    private String replace = "Y";
    /**
     * 发送发编码
     */
    private String sender;
    /**
     * 账套 account 对应表中code字段 select * from sm_busicenter
     */
    private String account;
    /**
     * 集团编码 groupcode 对应这个表中code
     *  select * from org_group
     */
    private String groupcode;
    private Bill bill;
}