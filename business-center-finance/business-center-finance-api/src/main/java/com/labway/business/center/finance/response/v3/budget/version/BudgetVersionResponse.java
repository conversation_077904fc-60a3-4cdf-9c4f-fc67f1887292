package com.labway.business.center.finance.response.v3.budget.version;

import com.labway.business.center.finance.response.BaseEntityResponse;
import com.labway.business.center.finance.response.v3.budget.type.BudgetTypeResponse;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 预算表
 */
@Data
public class BudgetVersionResponse extends BaseEntityResponse {

	/**
	 * 预算主键
	 */
	private Long budgetVersionId;

	/**
	 * 对应oa预算单元的id
	 * @see BudgetUnit#getId()
	 */
	private Long budgetUnitId;

	/**
	 * 对应oa预算单元的名称
	 * */
	private String budgetUnitName;

	/**
	 * 对应oa预算部门id
	 */
	private Long budgetDeptId;

	/**
	 * 对应oa预算部门名称
	 */
	private String budgetDeptName;

	/**
	 * 预算年度 2025...
	 */
	private String budgetYear;

	/**
	 * 1月预算额度
	 */
	private BigDecimal jan;

	/**
	 * 2月预算额度
	 */
	private BigDecimal feb;

	/**
	 * 3月预算额度
	 */
	private BigDecimal mar;

	/**
	 * 4月预算额度
	 */
	private BigDecimal apr;

	/**
	 * 5月预算额度
	 */
	private BigDecimal may;

	/**
	 * 6月预算额度
	 */
	private BigDecimal jun;

	/**
	 * 7月预算额度
	 */
	private BigDecimal jul;

	/**
	 * 8月预算额度
	 */
	private BigDecimal aug;

	/**
	 * 9月预算额度
	 */
	private BigDecimal sep;

	/**
	 * 10月预算额度
	 */
	private BigDecimal oct;

	/**
	 * 11月预算额度
	 */
	private BigDecimal nov;

	/**
	 * 12月预算额度
	 */
	private BigDecimal december;

	/**
	 * 预算类别1 小类, 2 大类
	 * @see LevelTypeEnums
	 */
	private Integer levelType;

	/**
	 * 一类预算id，如果是一类预算则默认为0
	 */
	private Long superBudgetId;

	/**
	 * 是否审核，0-未审核，1-已审核。审核之后的数据不可再进行修改
	 * @see com.labway.business.center.core.enums.YesOrNoEnum
	 */
	private Integer isAudit;

	/**
	 * 审核人
	 */
	private String auditUser;

	/**
	 * 审核时间
	 */
	private LocalDateTime auditTime;

	/**
	 * 版本，同一个预算单元，预算部门，年度，只能有一条启用的
	 */
	private String version;

	/**
	 * 是否启用,0-启用 1-停用
	 */
	private Integer enabled;

	/**
	 * 合计
	 */
	private BigDecimal total;

	/**
	 * 预算类别
	 */
	private BudgetTypeResponse budgetType;
}