package com.labway.business.center.finance.service;

import com.labway.business.center.finance.constants.IsCleanByOrgEnum;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2023/12/20 10:55
 **/
public interface CleanSupplyChainFormService {
    /**
     * 清洗费用报销单
     */
    void washCleanSupplyChainForm2Database(List<String> oaFlowNos);

    void washCleanSupplyChainForm2DatabaseByNos(List<String> washOaFlowNos, IsCleanByOrgEnum isCleanByOrgEnum);
}