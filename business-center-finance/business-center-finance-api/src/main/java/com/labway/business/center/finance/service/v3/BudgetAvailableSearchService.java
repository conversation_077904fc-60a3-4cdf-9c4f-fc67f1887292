package com.labway.business.center.finance.service.v3;

import com.labway.business.center.finance.request.v3.budget.av.BudgetAvailableListSearchRequest;
import com.labway.business.center.finance.response.v3.budget.av.BudgetAvResponseOA;
import com.labway.business.center.finance.response.v3.budget.av.BudgetAvailableResponse;
import com.swak.frame.dto.Response;

import javax.validation.Valid;
import java.util.List;

/**
 * 可用预算/v3/budget/control/operation
 *
 * <AUTHOR> on 2025/3/14.
 */
public interface BudgetAvailableSearchService {

	/**
	 * 可用预算查询
	 */
	Response<List<BudgetAvailableResponse>> searchBudgetAvailable(@Valid BudgetAvailableListSearchRequest request);

	/**
	 * oa穿透预算
	 */
	Response<BudgetAvResponseOA> searchBudgetAvailableOA(@Valid BudgetAvailableListSearchRequest request);
}
