package com.labway.business.center.finance.service;

import com.labway.business.center.finance.request.CleanExpenseFormRequest;
import com.labway.business.center.finance.request.CleanNoExistRequest;
import com.swak.frame.dto.Response;

import java.util.List;

/**
 * CleanExpensesFormService 清洗所有报销单接口
 *
 * <AUTHOR>
 * @version 2023/04/27 09:42
 **/
public interface CleanExpensesFormService {
    void washExpensesData(String requestId,Long time);
    List<String> cleanAccsoaIsNull();

    /**
     * 根据流程编号清洗差旅报销单
     * @param cleanExpenseFormRequest
     */
    Response<String> washCleanTravelExpenseForm2DatabaseByNos(CleanExpenseFormRequest cleanExpenseFormRequest);

    /**
     * 根据流程编号清洗供应链汇款申请单
     *
     * @param cleanExpenseFormRequest
     */
    Response<String> washCleanSupplyChainForm2DatabaseByNos(CleanExpenseFormRequest cleanExpenseFormRequest);

    /**
     * 根据流程编号清洗汇款申请单
     * @param cleanExpenseFormRequest
     * @return
     */
    Response<String> washRemittanceForm2DatabaseByNos(CleanExpenseFormRequest cleanExpenseFormRequest);

    /**
     * 根据流程编号清洗费用报销单
     * @param cleanExpenseFormRequest
     * @return
     */
    Response<String> washCleanExpensesClaimsForm2DatabaseByNos(CleanExpenseFormRequest cleanExpenseFormRequest);

    void cleanExpensesByAccass();

    /**
     * 清洗本地库中遗漏的供应链汇款申请单
     * 汇款申请单
     * @param cleanNoExistRequest
     * @return
     */
    Response<String> washNoExistSupplyChainForm(CleanNoExistRequest cleanNoExistRequest);

    /**
     * 清洗本地库中遗漏的汇款申请单
     * 汇款申请单
     * @param cleanNoExistRequest
     * @return
     */
    Response<String> washNoExistRemittanceForm(CleanNoExistRequest cleanNoExistRequest);

    /**
     * 清洗本地库中遗漏的数据
     * 费用报销单
     * @param cleanNoExistRequest
     * @return
     */
    Response<String> washNoExistClaimsForm(CleanNoExistRequest cleanNoExistRequest);

    /**
     * 清洗本地库中遗漏的数据
     * 差旅报销单
     * @param cleanNoExistRequest
     * @return
     */
    Response<String> washTravelNoExistClaimsForm(CleanNoExistRequest cleanNoExistRequest);

    void washAccassoIsNull();
}