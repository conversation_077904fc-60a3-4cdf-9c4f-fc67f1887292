package com.labway.business.center.finance.dto.v2;


import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 付款单
 */
@Data
public class PaymentOrderDTO implements Serializable {

    /**
     * OA表单id
     */
    private String oaFlowNo;

    /**
     * OA流程编号
     */
    private String oaNo;

    /**
     * 标题
     */
    private String title;

    /**
     * 部门id
     */
    private String deptId;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 业务员编码
     */
    private String empCode;

    /**
     * 业务员名称
     */
    private String empName;

    /**
     * 发起付款人
     */
    private String preparedBy;

    /**
     * 发起人
     */
    private String createdBy;

    /**
     * 发起时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATE_PATTERN)
    private LocalDateTime createdTime;

    /**
     * 审核时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private LocalDateTime auditTime;

    /**
     * 表单模板
     */
    private String template;

    /**
     * 组织ID
     */
    private String orgId;

    /**
     * 凭证状态(0-待制单，1，以制单，2 ，不制单)
     */
    private Integer status;

    /**
     * 不制单说明
     */
    private String remark;

    /**
     * 供应商编码
     */
    private String supplierCode;

    /**
     * 收款人（供应商名称）
     */
    private String receiveName;

    /**
     * 收款银行账号
     */
    private String receiveBankAccount;

    /**
     * 付款银行名称
     */
    private String payBankName;

    /**
     * 付款银行账号
     */
    private String payBankAccount;

    /**
     * 付款时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private LocalDateTime payTime;

    /**
     * 付款总金额
     */
    private BigDecimal totalAmount;

    /**
     * nc单据号
     */
    private String ncNo;

    /**
     * 币种
     */
    private String currency;

    /**
     * 应付业务类型编码
     */
    private String businessTypeCode;

    /**
     * 应付业务类型
     */
    private String businessTypeName;
    /**
     * 修改时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private LocalDateTime updateTime;

    /**
     * 制单时间
     */
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date makeDate;
}
