package com.labway.business.center.finance.dto.v2.bank.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PFAccessTokenRequest implements Serializable {

    // 认证模式  默认值:client_credentials
    private String GRANT_TYPE;

    // 授权码 由MBS提供授权码
    private String AUTH_CODE;


}
