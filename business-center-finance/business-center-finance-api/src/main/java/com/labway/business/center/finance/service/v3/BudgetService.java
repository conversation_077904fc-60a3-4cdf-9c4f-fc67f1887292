package com.labway.business.center.finance.service.v3;

import com.labway.business.center.finance.request.v3.budget.BudgetUseByOaFlowNosRequest;
import com.labway.business.center.finance.request.v3.budget.BudgetUseRequest;
import com.labway.business.center.finance.request.v3.budget.version.BudgetListSearchRequest;
import com.swak.frame.dto.Response;

import javax.validation.Valid;
import java.util.Map;

/**
 * 预算
 *
 * <AUTHOR> on 2025/3/14.
 */
public interface BudgetService {

	/**
	 * 查询预算列表
	 */
	Response<Map> searchBudgetList(BudgetListSearchRequest request);

	/**
	 * 预算使用
	 */
	Response<String> budgetUse(@Valid BudgetUseRequest request);

	/**
	 * 预算使用(使用预算)
	 */
	Response<String> budgetUseByOaNo(String oaNo, Integer useType);

	/**
	 * 预算使用（制单）
	 */
	Response<String> budgetUseByOaFlowNos(BudgetUseByOaFlowNosRequest request);
}
