package com.labway.business.center.finance.dto;


import lombok.Data;

/**
 * <p>
 * <B>Description: 科目设置费用类别 部门类别实体
 * </P>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class LedgerFeeDeptMappingDTO{
    
    /**
     * 费用类别code
     */
    private String feeTypeId;
    /**
     * 部门类别code
     */
    private String deptTypeCode;
    /**
     * 科目主键
     */
    private String accountCode;
    /**
     * 科目名称
     */
    private String accasoaName;
    /**
     * 费用类别名称
     */
    private String feeTypeName;
    
    /**
     * 部门类别名称
     */
    private String deptTypeName;
    
    /**
     * 业务单元
     */
    private String orgId;
}