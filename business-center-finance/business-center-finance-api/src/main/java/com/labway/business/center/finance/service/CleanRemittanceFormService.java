package com.labway.business.center.finance.service;

import com.labway.business.center.finance.constants.IsCleanByOrgEnum;

import java.util.List;


/**
 * 清洗OA汇款申请单
 *
 * <AUTHOR>
 * @date
 */
public interface CleanRemittanceFormService {

    /**
     * 清洗费用报销单
     */
    public void washCleanRemittanceForm2Database(List<String> notExistsOaFlowNos);

    void washCleanExpensesClaimsForm2DatabaseByNos(List<String> washOaFlowNos, IsCleanByOrgEnum isCleanByOrgEnum);

}
