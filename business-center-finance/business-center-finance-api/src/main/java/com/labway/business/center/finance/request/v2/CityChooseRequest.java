package com.labway.business.center.finance.request.v2;

import lombok.Data;

import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2024/01/08 13:54
 **/
@Data
public class CityChooseRequest implements Serializable {
    @Size(message = "至少选择一个单据",min = 1)
    private List<String> oaNos;

    /**
     * 是否同城
     * "0": 同城
     * "1": 异地
     */
    private String cityFlag;
}