package com.labway.business.center.finance.response.v3.budget.version;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 预算版本
 *
 * <AUTHOR> on 2025/4/8.
 */
@Data
public class VersionSearchResponse {

	/**
	 * 预算单元id
	 */
	private Long budgetUnitId;

	/**
	 * 预算单元名称
	 */
	private String budgetUnitName;

	/**
	 * 预算部门id
	 */
	private Long budgetDeptId;

	/**
	 * 预算部门名称
	 */
	private String budgetDeptName;

	/**
	 * 版本号
	 * key -> 年度 value -> 版本号列表
	 */
	private Map<String, List<Version>> versionMap;

	@Data
	public static class Version {

		/**
		 * 版本号
		 */
		private String version;

		/**
		 * 是否启用,0-停用 1-启用
		 */
		private Integer enabled;
	}
}
