package com.labway.business.center.finance.service.v3;

import com.labway.business.center.core.config.PageRequest;
import com.labway.business.center.core.config.PageResponse;
import com.labway.business.center.finance.request.v3.budget.dept.BudgetDeptSearchRequest;
import com.labway.business.center.finance.response.v3.budget.dept.BudgetDeptResponse;
import com.swak.frame.dto.Response;

import java.util.List;

/**
 * 预算部门
 *
 * <AUTHOR> on 2025/3/14.
 */
public interface BudgetDeptService {

	/**
	 * 数据清洗
	 */
	Response<Long> wash();

	/**
	 * 预算部门分页查询
	 * @param pageRequest {@link PageRequest 分页查询参数}
	 */
	Response<PageResponse<BudgetDeptResponse>> searchBudgetDeptPage(PageRequest<BudgetDeptSearchRequest> pageRequest);

	/**
	 * 查询所有的预算部门
	 */
	Response<List<BudgetDeptResponse>> searchBudgetDeptList(BudgetDeptSearchRequest request);
}
