package com.labway.business.center.finance.service.v2;

import com.labway.business.center.finance.dto.v2.EmpMappingDTO;
import com.labway.business.center.finance.request.v2.EmpMappingRequest;
import com.swak.frame.dto.Response;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2024/01/02 15:05
 **/
public interface EmpMappingService {
    /**
     * 人员对照
     * @param empMappingRequest
     * @return
     */
    Response<String> saveEmpMapping(List<EmpMappingRequest> empMappingRequestList);

    Response<List<EmpMappingDTO>> searchEmpMappingListByOrg(String orgId);

    /**
     * 删除人员对照
     * @param removeIds
     * @return
     */
    Response<String> removeEmpMapping(List<String> removeIds);
}