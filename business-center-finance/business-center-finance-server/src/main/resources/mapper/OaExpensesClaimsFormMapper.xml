<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.labway.business.center.finance.persistence.mapper.oa.ExpensesClaimsFormMapper">
    <update id="updateOaExpensesClaimsForm">
        <foreach collection="list" index="index" item="item" open="begin" separator=";" close=";end;">
            UPDATE formmain_8492
            SET field0039 = #{item.fileUrl}
            where field0005=#{item.oaNo}
        </foreach>
    </update>

    <update id="updateOaExpensesClaimsFormForNew">
        <foreach collection="list" index="index" item="item" open="begin" separator=";" close=";end;">
            UPDATE formmain_8938
            SET field0039 = #{item.fileUrl}
            where field0005=#{item.oaNo}
        </foreach>
    </update>

    <select id="getExpensesClaimsFormList"
            resultType="com.labway.business.center.finance.persistence.entity.oa.ExpensesClaimsForm"
            parameterType="com.labway.business.center.core.config.Pager">
        <![CDATA[
        SELECT * FROM (
            SELECT tmp.*, ROWNUM RN
            FROM (
                SELECT 
                    f8492.field0004 as company_name,
                    f8492.field0007 as apply_user,
                    m1.NAME as apply_user_name,
                    f8492.field0006 as dept_id,
                    f8492.start_date,
                    col1.FINISH_DATE as approve_date,
                    f8492.field0020 as real_amount,
                    f8492.id as oa_flow_no,
                    f8492.field0005 as oa_no,
                    enums1.showvalue as receive_bank_name,
                    f8492.field0024 as receive_name,
                    f8492.field0025 as receive_bank_account,
                    f8492.field0020 as total_amount
                FROM formmain_8492 f8492
                JOIN col_summary col1 ON f8492.id = col1.FORM_RECORDID
                JOIN ORG_MEMBER m1 ON f8492.FIELD0007 = m1.ID
                JOIN CTP_ENUM_ITEM enums1 ON f8492.field0022 = enums1.id
                WHERE f8492.finishedflag = 1
                AND col1.FINISH_DATE > to_date(#{pager.item}, 'yyyy-mm-dd hh24:mi:ss')

                UNION ALL

                SELECT 
                    f8938.field0004 as company_name,
                    f8938.field0007 as apply_user,
                    m2.NAME as apply_user_name,
                    f8938.field0006 as dept_id,
                    f8938.start_date,
                    col2.FINISH_DATE as approve_date,
                    f8938.field0020 as real_amount,
                    f8938.id as oa_flow_no,
                    f8938.field0005 as oa_no,
                    enums2.showvalue as receive_bank_name,
                    f8938.field0024 as receive_name,
                    f8938.field0025 as receive_bank_account,
                    f8938.field0020 as total_amount
                FROM formmain_8938 f8938
                JOIN col_summary col2 ON f8938.id = col2.FORM_RECORDID
                JOIN ORG_MEMBER m2 ON f8938.FIELD0007 = m2.ID
                JOIN CTP_ENUM_ITEM enums2 ON f8938.field0022 = enums2.id
                WHERE f8938.finishedflag = 1
                AND col2.FINISH_DATE > to_date(#{pager.item}, 'yyyy-mm-dd hh24:mi:ss')
            ) tmp 
            WHERE ROWNUM <= #{pager.end}
        )
        WHERE RN > #{pager.start}
        ]]>
    </select>
    <select id="selectListByOaFlowNos"
            resultType="com.labway.business.center.finance.persistence.entity.oa.ExpensesClaimsForm">
        SELECT
            f8492.field0004 as company_name,
            f8492.field0007 as apply_user,
            m1.NAME as apply_user_name,
            f8492.field0006 as dept_id,
            f8492.start_date,
            col1.FINISH_DATE as approve_date,
            f8492.field0020 as real_amount,
            f8492.id as oa_flow_no,
            f8492.field0005 as oa_no,
            enums1.showvalue as receive_bank_name,
            f8492.field0024 as receive_name,
            f8492.field0025 as receive_bank_account,
            f8492.field0020 as total_amount
        FROM formmain_8492 f8492
        JOIN col_summary col1 ON f8492.id = col1.FORM_RECORDID
        JOIN ORG_MEMBER m1 ON f8492.FIELD0007 = m1.ID
        JOIN CTP_ENUM_ITEM enums1 ON f8492.field0022 = enums1.id
        WHERE f8492.ID IN
        <foreach collection="list" separator="," open="(" close=")" item="item">
            #{item}
        </foreach>

        UNION ALL

        SELECT
            f8938.field0004 as company_name,
            f8938.field0007 as apply_user,
            m2.NAME as apply_user_name,
            f8938.field0006 as dept_id,
            f8938.start_date,
            col2.FINISH_DATE as approve_date,
            f8938.field0020 as real_amount,
            f8938.id as oa_flow_no,
            f8938.field0005 as oa_no,
            enums2.showvalue as receive_bank_name,
            f8938.field0024 as receive_name,
            f8938.field0025 as receive_bank_account,
            f8938.field0020 as total_amount
        FROM formmain_8938 f8938
        JOIN col_summary col2 ON f8938.id = col2.FORM_RECORDID
        JOIN ORG_MEMBER m2 ON f8938.FIELD0007 = m2.ID
        JOIN CTP_ENUM_ITEM enums2 ON f8938.field0022 = enums2.id
        WHERE f8938.ID IN
        <foreach collection="list" separator="," open="(" close=")" item="item">
            #{item}
        </foreach>
    </select>

    <select id="selectListByOaNos"
            resultType="com.labway.business.center.finance.persistence.entity.oa.ExpensesClaimsForm">
        select f8492.field0004 as company_name,f8492.field0007 as apply_user,m.NAME as apply_user_name,
        f8492.field0006 as dept_id,f8492.start_date ,col.FINISH_DATE as approve_date ,
        NVL(f8492.field0020, 0) as real_amount,
        f8492.id as oa_flow_no,f8492.field0005 as oa_no,
        enums.showvalue as receive_bank_name, -- 开户行名称
        f8492.field0024 as receive_name, -- 户名 收款人
        f8492.field0025 as receive_bank_account, -- 收款账号
        NVL(f8492.field0020, 0) as total_amount -- 实报金额合计
        from formmain_8492 f8492
        join col_summary col on f8492.id =col.FORM_RECORDID
        join ORG_MEMBER m on f8492.FIELD0007=m.ID
        join CTP_ENUM_ITEM enums on f8492.field0022 = enums.id

        where f8492.field0005 in
        <foreach collection="list" separator="," open="(" close=")" item="item">
            #{item}
        </foreach>

        UNION ALL

        SELECT f8938.field0004 as company_name,
        f8938.field0007 as apply_user,
        m2.NAME as apply_user_name,
        f8938.field0006 as dept_id,
        f8938.start_date,
        col2.FINISH_DATE as approve_date,
        NVL(f8938.field0020, 0) as real_amount,
        f8938.id as oa_flow_no,
        f8938.field0005 as oa_no,
        enums2.showvalue as receive_bank_name,
        f8938.field0024 as receive_name,
        f8938.field0025 as receive_bank_account,
        NVL(f8938.field0020, 0) as total_amount
        FROM formmain_8938 f8938
        JOIN col_summary col2 ON f8938.id = col2.FORM_RECORDID
        JOIN ORG_MEMBER m2 ON f8938.FIELD0007 = m2.ID
        JOIN CTP_ENUM_ITEM enums2 ON f8938.field0022 = enums2.id
        WHERE f8938.field0005 IN
        <foreach collection="list" separator="," open="(" close=")" item="item">
            #{item}
        </foreach>
    </select>

    <select id="getLocalNoHasOaNo" resultType="java.lang.String">
        <!-- 报销单查询 -->
        SELECT main.field0005 as oa_no
        FROM formmain_8492 main
        INNER JOIN col_summary summary ON main.id = summary.FORM_RECORDID
        INNER JOIN ORG_MEMBER member ON main.FIELD0007 = member.ID
        WHERE main.finishedflag = 1
        AND summary.FINISH_DATE > to_date(#{time}, 'yyyy-mm-dd hh24:mi:ss')
        <if test="list != null and list.size() > 0">
            AND main.field0005 NOT IN
            <foreach collection="list" separator="," open="(" close=")" item="item">
                #{item}
            </foreach>
        </if>
            
        UNION ALL

        <!-- 报销单查询 -->
        SELECT main.field0005 as oa_no
        FROM formmain_8938 main
        INNER JOIN col_summary summary ON main.id = summary.FORM_RECORDID
        INNER JOIN ORG_MEMBER member ON main.FIELD0007 = member.ID
        WHERE main.finishedflag = 1
        AND summary.FINISH_DATE > to_date(#{time}, 'yyyy-mm-dd hh24:mi:ss')
        <if test="list != null and list.size() > 0">
            AND main.field0005 NOT IN
            <foreach collection="list" separator="," open="(" close=")" item="item">
                #{item}
            </foreach>
        </if>
        ORDER BY oa_no ASC
    </select>
</mapper>