<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.business.center.finance.persistence.mapper.ncc.NccDepartmentMapper">

<select id="getNccDepartmentByPager" parameterType="com.labway.business.center.core.config.Pager" resultType="com.labway.business.center.finance.persistence.entity.ncc.NccDepartment">
	  <![CDATA[ 
	  SELECT * FROM
(
SELECT A.*, ROWNUM RN
FROM (
select bp.PK_DEPT as dept_id,bp.PK_ORG as org_id ,bp.PK_GROUP as group_id,bp.CODE as code, bp.NAME as name,bp.PK_FATHERORG as father_dept_id,bp.ENABLESTATE as status,bp.PK_VID as vid from org_dept bp     order by bp.PK_DEPT asc
     ) A
    WHERE ROWNUM <= #{pager.end}
 )
WHERE RN > #{pager.start}
 ]]> 
</select>

<select id="countNccDepartment"  resultType="java.lang.Long" parameterType="java.lang.String" >
	  <![CDATA[ select  count(1) as num from org_dept  ]]> 
</select>


</mapper>