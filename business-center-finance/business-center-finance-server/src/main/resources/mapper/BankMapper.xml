<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.labway.business.center.finance.persistence.mapper.BankMapper">
    <update id="updateBatch">
        <foreach collection="list" item="item" separator=";">
            UPDATE `tb_bank`
            SET
                `accnum` = #{item.accnum},
                `accname` = #{item.accname},
                `code` = #{item.code},
                `name` = #{item.name},
                `bankdoc_id` = #{item.bankdocId},
                `bankdoc_code` = #{item.bankdocCode},
                `bankdoc_name` = #{item.bankdocName},
                `bank_type_id` = #{item.bankTypeId},
                `bank_type_name` = #{item.bankTypeName},
                `bank_type_code` = #{item.bankTypeCode},
                `org_id` = #{item.orgId},
                `finance_org_id` = #{item.financeOrgId},
                `status` = #{item.status},
                `group_id` = #{item.groupId},
                `create_time` = #{item.createTime}
            WHERE
                `bank_id` = #{item.bankId}
        </foreach>
    </update>
    <update id="updateFinancialBalanceBatch">
        <foreach collection="list" item="item" separator=";">
            UPDATE `tb_bank`
            SET
            `financial_balance` = #{item.financialBalance}
            WHERE
            `accnum` = #{item.accNum}
        </foreach>
    </update>
</mapper>