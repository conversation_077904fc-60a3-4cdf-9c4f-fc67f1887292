<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.labway.business.center.finance.persistence.mapper.OaDeptMapper">
    <insert id="insertOaDeptBatch" useGeneratedKeys="true">
        INSERT INTO `business-finance`.`tb_oa_dept`
            ( `dept_id`, `name`, `group_id`, `code`, `parent_id`, `dept_type`, `status`, `create_time`)
        VALUES
        <foreach collection="list" item="item" separator=",">
           (#{item.deptId},
            #{item.name},
            #{item.groupId},
            #{item.code},
            #{item.parentId},
            #{item.deptType},
            #{item.status},
            #{item.createTime})
        </foreach>
    </insert>
    <update id="updateOaDeptBatch">
        UPDATE tb_oa_dept
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="name =case" suffix="end,">
                <foreach collection="list" item="item">
                    when dept_id=#{item.deptId} then #{item.name}
                </foreach>
            </trim>
            <trim prefix="group_id =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when dept_id=#{item.deptId} then #{item.groupId}
                </foreach>
            </trim>
            <trim prefix="code =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when dept_id=#{item.deptId} then #{item.code}
                </foreach>
            </trim>
            <trim prefix="parent_id =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when dept_id=#{item.deptId} then #{item.parentId}
                </foreach>
            </trim>
            <trim prefix="status =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when dept_id=#{item.deptId} then #{item.status}
                </foreach>
            </trim>
            <trim prefix="dept_type =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when dept_id=#{item.deptId} then #{item.deptType}
                </foreach>
            </trim>
        </trim>
        WHERE
        dept_id IN
        <foreach collection="list" index="index" item="i" separator="," open="(" close=")">
            #{i.deptId}
        </foreach>
    </update>
</mapper>