<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.labway.business.center.finance.persistence.mapper.NccDeptMapper">
    <update id="updateBatch">
        <foreach collection="list" item="item">
            UPDATE `business-finance`.`tb_ncc_dept`
            SET
            `org_id` = #{item.orgId},
            `group_id` = #{item.groupId},
            `code` = #{item.code},
            `name` = #{item.name},
            `status` = #{item.status},
            `father_dept_id` = #{item.fatherDeptId},
            `create_time` = #{item.createTime}
            WHERE
            `dept_id` = #{item.deptId};
        </foreach>
    </update>
</mapper>