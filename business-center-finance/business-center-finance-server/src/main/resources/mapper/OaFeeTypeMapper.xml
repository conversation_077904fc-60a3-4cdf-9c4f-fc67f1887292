<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.labway.business.center.finance.persistence.mapper.oa.OaFeeTypeMapper" >

    <select id="getOaFeeTypes" resultType="com.labway.business.center.finance.persistence.entity.oa.OaFeeType">
        select id as oa_feetype_id, SHOWVALUE as name, ENUMVALUE as code, PARENT_ID as parent_id, STATE as status
        from CTP_ENUM_ITEM
        where REF_ENUMID in
        <foreach collection="enumIds" item="enumId" open="(" close=")" separator=",">
            #{enumId}
        </foreach>
    </select>
</mapper>