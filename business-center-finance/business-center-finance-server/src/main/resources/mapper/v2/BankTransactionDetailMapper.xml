<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.labway.business.center.finance.persistence.mapper.v2.BankTransactionDetailMapper">
    <update id="updateCbs8Receipt">
        <foreach collection="list" item="item" separator=";" open="" close="">
            UPDATE `business-finance`.`tb_bank_transaction_detail`
            set
            `has_file` = 1,
            `file_url` = #{item.fileUrl}
            where `check_code` = #{item.serial}
        </foreach>
    </update>
</mapper>