<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.labway.business.center.finance.persistence.mapper.v2.SupplierMapper">
    <delete id="removeInfo">
        delete from tb_supplier where (supplier_code,org_code) in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            (#{item.supplierCode},#{item.orgCode})
        </foreach>
    </delete>
</mapper>