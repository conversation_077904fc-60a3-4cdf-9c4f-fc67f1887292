<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.labway.business.center.finance.persistence.mapper.ncc.NCCMapper">
    <select id="queryListByOrg" resultType="com.labway.business.center.finance.persistence.entity.ncc.NCCCustOrgVO">
        select c1.pk_custorg custorg, c1.pk_customer customerId, c1.pk_org org, c2.code customer
        from bd_custorg c1
                 inner join bd_customer c2 on c2.pk_customer = c1.pk_customer
        where c1.pk_org = #{param.orgCode} and c1.enablestate = 2
    </select>
    <select id="searchAllOrgCustomer"
            resultType="com.labway.business.center.finance.persistence.params.NccOrgCustomer">
        select c1.pk_org org_code, c2.code customer_code
        from bd_custorg c1
                 inner join bd_customer c2 on c2.pk_customer = c1.pk_customer
    </select>
</mapper>