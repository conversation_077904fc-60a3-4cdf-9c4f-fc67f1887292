<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.labway.business.center.finance.persistence.mapper.oa.OaBudgetUnitMapper" >
   <select id="getOaBudgetUnites" resultType="com.labway.business.center.finance.persistence.entity.oa.OaBudgetUnit" parameterType="java.lang.Long">
   		select id as oa_budget_id ,SHOWVALUE as name ,ENUMVALUE as code,PARENT_ID as parent_id,STATE as status from CTP_ENUM_ITEM where REF_ENUMID=#{enumId}
   </select>

   <select id="searchNewOaBudgetUnites"
            resultType="com.labway.business.center.finance.persistence.entity.oa.OaBudgetUnit">
       select id        as oaBudgetId,
              0         as parentId,
              field0001 as name,
              field0019 as code,
              CASE 
                  WHEN field0017 = '8096888898688272583' THEN '1'
                  ELSE '0'
              END as status
       from formmain_8921
   </select>
</mapper>