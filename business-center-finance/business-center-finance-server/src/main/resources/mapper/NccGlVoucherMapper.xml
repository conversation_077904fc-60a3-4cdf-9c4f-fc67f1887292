<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.business.center.finance.persistence.mapper.ncc.NccGlVoucherMapper">

<select id="getMaxVoucherNum"  resultType="java.lang.Integer" parameterType="com.labway.business.center.finance.persistence.params.VoucherNum" >
select max(num)+1 as num from gl_voucher where PK_ORG=#{vno.orgId} and YEAR=#{vno.year} and PERIOD=#{vno.period} and PK_VOUCHERTYPE=#{vno.typeId}
</select>
</mapper>