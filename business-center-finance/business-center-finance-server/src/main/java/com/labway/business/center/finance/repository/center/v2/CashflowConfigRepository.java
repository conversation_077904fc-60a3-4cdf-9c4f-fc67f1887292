package com.labway.business.center.finance.repository.center.v2;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.labway.business.center.finance.persistence.entity.v2.CashflowConfig;
import com.labway.business.center.finance.persistence.mapper.v2.CashflowConfigMapper;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 2023/11/20 16:42
 **/
@Repository
public class CashflowConfigRepository {
    @Resource
    private CashflowConfigMapper cashflowConfigMapper;

    public List<CashflowConfig>  getAllCashflowsConfig() {
        return cashflowConfigMapper.selectList(null);
    }

    public CashflowConfig searchByFeeTypeAndAccass(Long feeTypeId) {
        if (Objects.isNull(feeTypeId)) {
            return null;
        }
        LambdaQueryWrapper<CashflowConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CashflowConfig::getFeeTypeId,feeTypeId);
        return cashflowConfigMapper.selectOne(queryWrapper);
    }
}