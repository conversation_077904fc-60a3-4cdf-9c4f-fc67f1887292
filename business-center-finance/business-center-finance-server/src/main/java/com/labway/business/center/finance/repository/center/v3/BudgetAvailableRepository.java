package com.labway.business.center.finance.repository.center.v3;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.business.center.finance.constants.FinanceConstants;
import com.labway.business.center.finance.persistence.entity.v3.BudgetAvailable;
import com.labway.business.center.finance.persistence.mapper.v3.BudgetAvailableMapper;
import com.labway.business.center.finance.request.v3.budget.av.BudgetAvailableListSearchRequest;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 可用预算
 *
 * <AUTHOR> on 2025/3/14.
 */
@Repository
@CacheConfig(cacheNames = "budgetAvailable")
public class BudgetAvailableRepository {

	@Resource
	private BudgetAvailableMapper budgetAvailableMapper;

	public List<BudgetAvailable> searchBudgetAvailableByUnitIdAndDeptIdAndYearAndMonth(@NotNull(message = "请选择预算单元") Long oaBudgetUnitId, @NotNull(message = "请选择预算部门") Long budgetDeptId, int year, List<Integer> searchMonth) {
		return budgetAvailableMapper.selectList(Wrappers.lambdaQuery(BudgetAvailable.class)
				.eq(BudgetAvailable::getBudgetUnitId, oaBudgetUnitId)
				.eq(BudgetAvailable::getBudgetDeptId, budgetDeptId)
				.eq(BudgetAvailable::getBudgetYear, year)
				.in(BudgetAvailable::getBudgetMonth, searchMonth)
				.orderByDesc(BudgetAvailable::getBudgetMonth));
	}

	@Cacheable(key = "'searchBudgetAvailableByUnitIdAndDeptIdAndYear:' + #oaBudgetUnitId + ':' + #budgetDeptId + ':' + #year")
	public List<BudgetAvailable> searchBudgetAvailableByUnitIdAndDeptIdAndYear(@NotNull(message = "请选择预算单元") Long oaBudgetUnitId, @NotNull(message = "请选择预算部门") Long budgetDeptId, int year) {
		return budgetAvailableMapper.selectList(Wrappers.lambdaQuery(BudgetAvailable.class)
				.eq(BudgetAvailable::getBudgetUnitId, oaBudgetUnitId)
				.eq(BudgetAvailable::getBudgetDeptId, budgetDeptId)
				.eq(BudgetAvailable::getBudgetYear, year)
				.orderByDesc(BudgetAvailable::getBudgetMonth));
	}

	public List<BudgetAvailable> searchBudgetAvailableByUnitIdAndDeptIdAndYearAndMonthAndType(@NotNull(message = "请选择预算单元") Long oaBudgetUnitId, @NotNull(message = "请选择预算部门") Long budgetDeptId, int year, List<Integer> searchMonth, Long budgetTypeId) {
		return budgetAvailableMapper.selectList(Wrappers.lambdaQuery(BudgetAvailable.class)
				.eq(BudgetAvailable::getBudgetUnitId, oaBudgetUnitId)
				.eq(BudgetAvailable::getBudgetDeptId, budgetDeptId)
				.eq(BudgetAvailable::getBudgetYear, year)
				.in(BudgetAvailable::getBudgetMonth, searchMonth)
				.eq(BudgetAvailable::getBudgetTypeId, budgetTypeId)
				.orderByDesc(BudgetAvailable::getBudgetMonth));
	}

	/**
	 * 批量修改预算的相关额度
	 */
	@CacheEvict(allEntries = true)
	public long updateBudgetAvailableBatch(Collection<BudgetAvailable> updateBudgetAvailableList) {
		if (CollectionUtils.isEmpty(updateBudgetAvailableList)) {
			return 0;
		}
		return budgetAvailableMapper.updateBudgetAvailableBatch(List.copyOf(updateBudgetAvailableList));
	}

	/**
	 * 根据id查询可用预算
	 */
	@Cacheable(key = "'searchBudgetAvailableByIds:' + #availableIds")
	public List<BudgetAvailable> searchBudgetAvailableByIds(Collection<Long> availableIds) {
		if (CollectionUtils.isEmpty(availableIds)) {
			return Collections.emptyList();
		}
		return budgetAvailableMapper.selectBatchIds(availableIds);
	}

	@CacheEvict(allEntries = true)
	public void saveBudgetAvailable(List<BudgetAvailable> insertBudgetAvailableList) {
		if (CollectionUtils.isEmpty(insertBudgetAvailableList)) {
			return;
		}
		budgetAvailableMapper.insertBatchSomeColumn(insertBudgetAvailableList);
	}

	public List<BudgetAvailable> searchBudgetAvailableByUnitIdAndDeptIdAndTypeIds(Long budgetUnitId, Long budgetDeptId, List<Long> budgetTypeIds) {
		if (Objects.isNull(budgetUnitId) || Objects.isNull(budgetDeptId) || CollectionUtils.isEmpty(budgetTypeIds)) {
			return Collections.emptyList();
		}

		return budgetAvailableMapper.selectList(Wrappers.lambdaQuery(BudgetAvailable.class)
				.eq(BudgetAvailable::getBudgetUnitId, budgetUnitId)
				.eq(BudgetAvailable::getBudgetDeptId, budgetDeptId)
				.in(BudgetAvailable::getBudgetTypeId, budgetTypeIds));
	}

	public void deleteBudgetAvByTypes(Long budgetUnitId, Long budgetDeptId, Collection<Long> budgetTypeIds) {
		if (Objects.isNull(budgetUnitId) || Objects.isNull(budgetDeptId) || CollectionUtils.isEmpty(budgetTypeIds)) {
			return ;
		}
		budgetAvailableMapper.delete(Wrappers.lambdaQuery(BudgetAvailable.class)
				.eq(BudgetAvailable::getBudgetUnitId, budgetUnitId)
				.eq(BudgetAvailable::getBudgetDeptId, budgetDeptId)
				.in(BudgetAvailable::getBudgetTypeId, budgetTypeIds));
	}

	public List<BudgetAvailable> searchBudgetAvailableList(BudgetAvailableListSearchRequest request, List<Long> budgetDeptIds) {
		LambdaQueryWrapper<BudgetAvailable> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(BudgetAvailable::getBudgetUnitId, request.getOaBudgetUnitId())
					.eq(BudgetAvailable::getBudgetYear, request.getBudgetYear());
		if (CollectionUtils.isNotEmpty(budgetDeptIds)) {
			queryWrapper.in(BudgetAvailable::getBudgetDeptId, budgetDeptIds);
		}

		return budgetAvailableMapper.selectList(queryWrapper);
	}

	public BudgetAvailable searchParentAvailable(BudgetAvailable budgetAvailable) {
		if (Objects.isNull(budgetAvailable)) {
			return null;
		}
		return budgetAvailableMapper.selectOne(Wrappers.lambdaQuery(BudgetAvailable.class)
				.eq(BudgetAvailable::getBudgetUnitId, budgetAvailable.getBudgetUnitId())
				.eq(BudgetAvailable::getBudgetDeptId, budgetAvailable.getBudgetDeptId())
				.eq(BudgetAvailable::getBudgetTypeId, FinanceConstants.COMMON_ID)
				.eq(BudgetAvailable::getBudgetYear, budgetAvailable.getBudgetYear())
				.eq(BudgetAvailable::getBudgetMonth, budgetAvailable.getBudgetMonth()));
	}

	public List<BudgetAvailable> searchDeptYearMonthBudgetAvList(Long oaBudgetId, Long budgetDeptId, int year, List<Integer> searchMonth) {
		return budgetAvailableMapper.selectList(Wrappers.lambdaQuery(BudgetAvailable.class)
				.eq(BudgetAvailable::getBudgetUnitId, oaBudgetId)
				.eq(BudgetAvailable::getBudgetDeptId, budgetDeptId)
				.eq(BudgetAvailable::getBudgetYear, year)
				.eq(BudgetAvailable::getBudgetTypeId, FinanceConstants.COMMON_ID)
				.in(BudgetAvailable::getBudgetMonth, searchMonth));
	}

	public void cleanAmount(Long oaBudgetId, Long budgetDeptId) {
		budgetAvailableMapper.update(null, Wrappers.lambdaUpdate(BudgetAvailable.class)
				.set(BudgetAvailable::getUsedAmount, BigDecimal.ZERO)
				.set(BudgetAvailable::getPreviewAmount, BigDecimal.ZERO)
				.eq(BudgetAvailable::getBudgetUnitId, oaBudgetId)
				.eq(BudgetAvailable::getBudgetDeptId, budgetDeptId));
	}

	public List<BudgetAvailable> searchBudgetAvailableByUnitIdAndYearAndMonth(Long unitId, int year, List<Integer> searchMonth) {
		return budgetAvailableMapper.selectList(Wrappers.lambdaQuery(BudgetAvailable.class)
				.eq(BudgetAvailable::getBudgetUnitId, unitId)
				.eq(BudgetAvailable::getBudgetYear, year)
				.eq(BudgetAvailable::getBudgetTypeId, FinanceConstants.COMMON_ID)
				.in(BudgetAvailable::getBudgetMonth, searchMonth));
	}
}
