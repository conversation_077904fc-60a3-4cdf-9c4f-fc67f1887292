 package com.labway.business.center.finance.persistence.mapper.ncc;

import java.util.List;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.labway.business.center.finance.persistence.entity.ncc.NccOrgs;

/**
 * 清洗NCC 业务单元组织信息
 * <AUTHOR>
 * @date 2023/03/22
 */
@DS("ncc")
public interface NccOrgsMapper extends BaseMapper<NccOrgs> {
    
   /**
    * 加载NCC业务单元组织信息
    * @return
    */
    List<NccOrgs> getAllNccOrgs();
    

}
