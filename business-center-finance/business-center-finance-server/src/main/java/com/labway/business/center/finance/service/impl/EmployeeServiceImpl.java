package com.labway.business.center.finance.service.impl;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.labway.business.center.core.enums.Constants;
import com.labway.business.center.core.enums.SyncDataFrom;
import com.labway.business.center.core.log.SyncLog;
import com.labway.business.center.finance.converter.v2.OaUserConverter;
import com.labway.business.center.finance.dto.mdm.OaUserDTO;
import com.labway.business.center.finance.persistence.entity.mdm.OaUser;
import com.labway.business.center.finance.repository.mdm.OaUserRepository;
import com.labway.business.center.finance.request.v2.EmpSearchRequest;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;

import com.labway.business.center.core.config.Pager;
import com.labway.business.center.finance.converter.EmployeeConverter;
import com.labway.business.center.finance.dto.EmployeeDTO;
import com.labway.business.center.finance.persistence.entity.Employee;
import com.labway.business.center.finance.persistence.entity.ncc.NccEmployee;
import com.labway.business.center.finance.persistence.params.SearchEmployee;
import com.labway.business.center.finance.repository.center.EmployeeRepository;
import com.labway.business.center.finance.repository.ncc.NccEmployeeRepository;
import com.labway.business.center.finance.request.EmployeeListRequest;
import com.labway.business.center.finance.service.EmployeeService;
import com.swak.frame.dto.Response;

/**
 * 辅助核算人员信息实现类
 * 
 * <AUTHOR>
 * @date 2023/03/17
 */
@DubboService
public class EmployeeServiceImpl implements EmployeeService {

    @Resource
    private  NccEmployeeRepository nccEmployeeRepository;
    @Resource
    private OaUserRepository oaUserRepository;

    @Resource
    private EmployeeRepository employeeRepository;
    
    @Resource
    private EmployeeConverter employeeConverter;
    @Resource
    private OaUserConverter oaUserConverter;

    @Override
    @SyncLog(dataFrom = SyncDataFrom.NCC,taskName = Constants.SYNC_TASK_NAME_NCC_EMPLOYEE)
    public Response<?> washNccEmployeeToCenterDatabase() {
        Response<?> response = Response.success();
        Long count = nccEmployeeRepository.countNccEmployee();
        if (count == 0) {
            return response;
        }
        Long total = 0L;
        List<NccEmployee> employees = nccEmployeeRepository.getNccEmployeeByPager();
        total += proccessBatchSaveEmployee(employees);
        return Response.success(total);
    }

    /**
     * 处理批量入库
     * 
     * @param employees
     */
    private Integer proccessBatchSaveEmployee(List<NccEmployee> employees) {
        // 清空原有数据，全量更新
        List<Employee> saveEmployees = employees.stream().map(this::assinEmployeeValue).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(saveEmployees)) {
            return 0;
        }
        employeeRepository.removeAll();
        return employeeRepository.batchInsertEmployee(saveEmployees);
    }
    
    private Employee assinEmployeeValue(NccEmployee p) {
        Employee employee = new Employee();
        BeanUtils.copyProperties(p, employee);
        employee.setCreateTime(LocalDateTime.now());
        return employee;
    }

    @Override
    public Response<Pager<List<EmployeeDTO>>> getEmployeeByDeptId(EmployeeListRequest request) {
        Response<Pager<List<EmployeeDTO>>> response = Response.success();
        Pager<SearchEmployee> pager = new Pager<>(request.getPage(),request.getPageSize());
        Pager<List<EmployeeDTO>> responsePager =  new Pager<>(request.getPage(),request.getPageSize());
        SearchEmployee.SearchEmployeeBuilder builder = SearchEmployee.builder();
        builder.name(request.getName()).orgId(request.getOrgId());
        pager.setItem(builder.build());
        Long num = employeeRepository.countEmployeeByDeptId(builder.build());
        responsePager.setTotal(num);
        if (num==0) {
            response.setData(responsePager);
            return  response;
        }
        List<Employee> employees = employeeRepository.getEmployeeByDeptId(pager);
        if (CollectionUtils.isEmpty(employees)) {
            response.setData(responsePager);
            return  response;
        }
        
        List<EmployeeDTO> employeeDTOs = employees.stream().map(empl ->employeeConverter.covertEmployee2DTO(empl)).collect(Collectors.toList());
        responsePager.setItem(employeeDTOs);
        response.setData(responsePager);
         return response;
    }

    /**
     * nc人员信息查询
     *
     * @param empSearchRequest
     * @return
     */
    @Override
    public Response<List<EmployeeDTO>> searchNccEmployeeByOrg(EmpSearchRequest empSearchRequest) {
        List<Employee> employees = employeeRepository.searchByOrgAndName(empSearchRequest.getOrgId(),empSearchRequest.getUserName());
        if (CollectionUtils.isEmpty(employees)) {
            return Response.success();
        }
        List<EmployeeDTO> employeeDTOs = employees.stream().map(empl ->employeeConverter.covertEmployee2DTO(empl)).collect(Collectors.toList());
        return Response.success(employeeDTOs);
    }

    /**
     * 查询oa人员信息
     *
     * @param empSearchRequest
     * @return
     */
    @Override
    public Response<List<OaUserDTO>> searchOaEmployeeByOrg(EmpSearchRequest empSearchRequest) {
        List<OaUser> oaUsers = oaUserRepository.searchByName(empSearchRequest.getUserName());
        if (CollectionUtils.isEmpty(oaUsers)) {
            return Response.success();
        }
        return Response.success(oaUserConverter.convertEntity2DTOList(oaUsers));
    }

}
