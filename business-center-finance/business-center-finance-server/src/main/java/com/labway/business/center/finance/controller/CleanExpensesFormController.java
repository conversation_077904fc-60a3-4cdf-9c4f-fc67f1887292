package com.labway.business.center.finance.controller;


import com.labway.business.center.finance.anno.SecurityParameter;
import com.labway.business.center.finance.service.CleanExpensesFormService;
import com.swak.frame.dto.Response;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * CleanExpensesFormController 清洗数据接口
 *
 * <AUTHOR>
 * @version 2023/04/27 09:38
 **/
@RestController
@RequestMapping("/clean")
public class CleanExpensesFormController {
	@Autowired
	private CleanExpensesFormService cleanExpensesFormService;

	@GetMapping("/voucher")
	@SecurityParameter(inDecode = false, outEncode = false)
	public Response<String> cleanExpensesForm(String requestId, Long time) {
		cleanExpensesFormService.washExpensesData(requestId, time);
		return Response.success();
	}

	/**
	 * 辅助核算为空 重新计算
	 *
	 * @return
	 */
	@GetMapping("/voucher/accass")
	@SecurityParameter(inDecode = false, outEncode = false)
	public Response<String> cleanExpensesByAccass() {
		cleanExpensesFormService.cleanExpensesByAccass();
		return Response.success();
	}

	/**
	 * 清洗科目为空的数据
	 */
	@GetMapping("/voucher/subject")
	@SecurityParameter(inDecode = false, outEncode = false)
	public void washAccassoaIsNull() {
		cleanExpensesFormService.washAccassoIsNull();
	}
}