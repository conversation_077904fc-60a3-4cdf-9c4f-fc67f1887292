 package com.labway.business.center.finance.repository.ncc;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Repository;

import com.labway.business.center.finance.persistence.entity.ncc.NccCashflow;
import com.labway.business.center.finance.persistence.mapper.ncc.NccCashflowMapper;

/**
 * NCC 现金流量项目资源类
 * <AUTHOR>
 * @date 2023/04/07
 */
@Repository
 public class NccCashflowRepository {

    
    @Resource
    private NccCashflowMapper nccCashflowMapper;
    
    public List<NccCashflow> getAllCashflows(){
        return nccCashflowMapper.getAllCashflows();
    }
}
