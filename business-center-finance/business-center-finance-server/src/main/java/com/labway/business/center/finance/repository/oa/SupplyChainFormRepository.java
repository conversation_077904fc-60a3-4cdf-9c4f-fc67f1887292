package com.labway.business.center.finance.repository.oa;

import com.labway.business.center.core.config.Pager;
import com.labway.business.center.finance.config.FinanceConfig;
import com.labway.business.center.finance.persistence.entity.oa.SupplyChainForm;
import com.labway.business.center.finance.persistence.mapper.oa.SupplyChainFormMapper;
import com.labway.business.center.finance.persistence.params.OaUpdateFileUrl;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * SupplyChainFromRepository 供应链汇款申请单
 *
 * <AUTHOR>
 * @version 2023/04/26 10:04
 **/
@Repository
public class SupplyChainFormRepository {
    @Resource
    private FinanceConfig financeConfig;
    @Resource
    private SupplyChainFormMapper supplyChainFormMapper;

    public List<SupplyChainForm> getSupplyChainFormList(Pager<String> pager) {
        return supplyChainFormMapper.getSupplyChainFormList(pager);
    }

    public List<SupplyChainForm> getSupplyChainFormsByFormIds(List<Long> formIds) {
        return supplyChainFormMapper.selectListByOaFlowNos(formIds);
    }

    public List<SupplyChainForm> getSupplyChainFormsByOaNos(List<String> washOaNos) {
        return supplyChainFormMapper.selectListByOaNos(washOaNos);
    }

    /**
     * 本地库和oa库流程编号对比
     * @param localOaNoList
     * @return
     */
    public List<String> getLocalNoHasOaNo(List<String> localOaNoList,String syncTime) {
        return supplyChainFormMapper.getLocalNoHasOaNo(localOaNoList,syncTime);
    }

    public Long updateOaFileBatch(List<OaUpdateFileUrl> oaUpdateFileUrls) {
        if (CollectionUtils.isEmpty(oaUpdateFileUrls)) {
            return 0L;
        }
        supplyChainFormMapper.updateOaSupplyChainForm(oaUpdateFileUrls);
        return supplyChainFormMapper.updateOaSupplyChainFormForNew(oaUpdateFileUrls);
    }
}