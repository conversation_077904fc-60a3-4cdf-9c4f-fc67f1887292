package com.labway.business.center.finance.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * VoucherStatus 凭证状态
 *
 * <AUTHOR>
 * @version 2023/04/04 14:03
 **/
@Getter
@AllArgsConstructor
public enum FormPayStatus {
    WAIT(0,"formPayWaitExportHandler","待审核"),
    PUSH(1,"formPayPushExportHandler","已推送待支付"),
    PAY(2,"formPayPayExportHandler","支付成功"),
    FAIL(3,"formPayFailExportHandler","支付失败"),
    NOT_PAY(4,"formPayWaitExportHandler","不付款"),
    OFF_LINE(5,"formPayOffLineExportHandler","线下付款"),
    CHECKING(6,"","待推送审核");
    private Integer status;
    private String componentName;
    private String statusName;

    public static FormPayStatus getByStatus(Integer status){
        for (FormPayStatus formPayStatus : FormPayStatus.values()) {
            if (formPayStatus.getStatus().equals(status)) {
                return formPayStatus;
            }
        }
        return null;
    }

    public static Boolean isStatus(Integer status, FormPayStatus formPayStatus){
        if (Objects.isNull(formPayStatus)) {
            return false;
        }
        if (status.equals(formPayStatus.getStatus())) {
            return true;
        }
        return false;
    }

    public static String getStatusName(Integer status){
        for (FormPayStatus formPayStatus : FormPayStatus.values()) {
            if (formPayStatus.getStatus().equals(status)) {
                return formPayStatus.getStatusName();
            }
        }
        return null;
    }
}