 package com.labway.business.center.finance.config;

import com.labway.business.center.core.injector.EasySqlInjector;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;

/**
 * Mybatis-plus 配置类属性
 * <AUTHOR>
 * @date 2023/03/17
 */
@Configuration
 public class MPConfiguration {
     @Bean
     @Primary//批量插入配置
     public EasySqlInjector easySqlInjector() {
         return new EasySqlInjector();
     }
 }
