package com.labway.business.center.finance.aggregation.log;

import cn.hutool.json.JSONUtil;
import com.labway.business.center.core.enums.OperationTypeEnum;
import com.labway.business.center.core.log.param.OperationLogRequest;
import com.labway.business.center.finance.aggregation.log.param.BaseParam;
import com.labway.business.center.finance.aggregation.log.param.MappingLogParam;
import com.labway.business.center.finance.aggregation.log.param.MappingParam;
import com.labway.business.center.finance.config.FinanceLogConfig;
import com.labway.business.center.finance.constants.FinanceConstants;
import com.labway.business.center.finance.util.ObjectCompareUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * 对照
 *
 * <AUTHOR>
 * @version 2023/06/08 13:17
 **/
@Slf4j
@Component("mappingLogHandler")
public class MappingLogHandler implements LogHandler {

	/**
	 * @param body 参数
	 */
	@Override
	public void saveLog(String body) throws Exception {
		BaseParam baseParam = JSONUtil.toBean(body, BaseParam.class);
		MappingLogParam param = JSONUtil.toBean(baseParam.getData(), MappingLogParam.class);
		MappingParam old = JSONUtil.toBean(param.getOldObject().toString(), MappingParam.class);
		MappingParam newObject = JSONUtil.toBean(param.getNewObject().toString(), MappingParam.class);
		String operationMsg = ObjectCompareUtil.compareNotField(old, newObject, param.getMappingType());
		sendMq(OperationTypeEnum.UPDATE, operationMsg, FinanceConstants.ModuleName.BASE_DATA.getModule(), baseParam);
	}

}