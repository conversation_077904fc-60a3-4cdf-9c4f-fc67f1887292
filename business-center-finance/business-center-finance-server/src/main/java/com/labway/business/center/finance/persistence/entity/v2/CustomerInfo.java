package com.labway.business.center.finance.persistence.entity.v2;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 客商信息表
 * @TableName tb_customer_info
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName(value ="tb_customer_info")
public class CustomerInfo implements Serializable {
    /**
     * 
     */
    @TableId(value = "id",type = IdType.AUTO)
    private Long id;

    /**
     * 客商编码
     */
    @TableField(value = "customer_code")
    private String customerCode;

    /**
     * 客商名称
     */
    @TableField(value = "customer_name")
    private String customerName;

    /**
     * 是否有效 0无效 1有效
     */
    @TableField(value = "enabled")
    private Integer enabled;

    /**
     * 客户基本分类
     */
    @TableField(value = "custclass")
    private String custclass;

    /**
     * 地区分类
     */
    @TableField(value = "areacl")
    private String areacl;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 客商状态0-开启，1-关闭
     */
    @TableField(value = "status")
    private Integer status;


    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

}