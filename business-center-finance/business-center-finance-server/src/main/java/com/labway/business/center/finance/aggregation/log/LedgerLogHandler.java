package com.labway.business.center.finance.aggregation.log;

import cn.hutool.json.JSONUtil;
import com.labway.business.center.core.enums.OperationTypeEnum;
import com.labway.business.center.core.log.param.OperationLogRequest;
import com.labway.business.center.finance.aggregation.log.param.BaseParam;
import com.labway.business.center.finance.config.FinanceLogConfig;
import com.labway.business.center.finance.constants.FinanceConstants;
import com.labway.business.center.finance.constants.LogType;
import com.labway.business.center.finance.persistence.entity.LedgerFeeDeptMapping;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * 科目设置
 *
 * <AUTHOR>
 * @version 2023/06/09 11:35
 **/
@Slf4j
@Component("ledgerLogHandler")
public class LedgerLogHandler implements LogHandler{
    @Resource
    private FinanceLogConfig financeLogConfig;
    /**
     * @param body 参数
     */
    @Override
    public void saveLog(String body) throws Exception {
        BaseParam baseParam = JSONUtil.toBean(body, BaseParam.class);
        LedgerFeeDeptMapping ledgerFeeDeptMapping = JSONUtil.toBean(baseParam.getData(), LedgerFeeDeptMapping.class);
        StringBuilder sb = new StringBuilder();
        sb.append(LogType.LEDGER_ACCOUNT.getType()).append(": ");
        sb.append("编辑【").append(ledgerFeeDeptMapping.getDeptTypeName()).append("】");
        sb.append("【").append(ledgerFeeDeptMapping.getFeeTypeName()).append("】");
        sb.append("【").append(ledgerFeeDeptMapping.getAccasoaName()).append("】");
        String operationMsg = sb.toString();
		sendMq(OperationTypeEnum.UPDATE, operationMsg, FinanceConstants.ModuleName.BASE_DATA.getModule(), baseParam);
    }
}