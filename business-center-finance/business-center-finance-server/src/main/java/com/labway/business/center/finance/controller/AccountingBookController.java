package com.labway.business.center.finance.controller;

import com.labway.business.center.core.enums.NeedOrgIdEnum;
import com.labway.business.center.core.enums.OptModuleEnum;
import com.labway.business.center.core.enums.OptTypeEnum;
import com.labway.business.center.core.log.Log;
import com.labway.business.center.core.user.RequiresRoles;
import com.labway.business.center.finance.anno.SecurityParameter;
import com.labway.business.center.finance.dto.AccountBookDTO;
import com.labway.business.center.core.config.BaseRequest;
import com.labway.business.center.finance.request.SetTaxRuleRequest;
import com.labway.business.center.finance.service.AccountingBookService;
import com.swak.frame.dto.Response;

import cn.hutool.json.JSONUtil;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import javax.annotation.Resource;

/**
 *
 * 核算账簿
 *
 * <AUTHOR>
 * @version 2023/04/03 14:58
 **/
@RestController
@RequestMapping("accounting/")
public class AccountingBookController {
    @Resource
    private AccountingBookService accountingBookService;
    
    /**
     * 清洗NCC账簿_财务核算账簿信息到业务中台数据库
     * @return
     */
    @GetMapping("wash")
	@SecurityParameter(inDecode = false, outEncode = false)
    public Response<?> washAccountingBookToCenterDatabase() {
        return accountingBookService.washAccountingBookToCenterDatabase();
    }
    /**
     * 获取所有的核算账簿
     * @return
     */
    @RequiresRoles(NEED_ORG_ID_ENUM = NeedOrgIdEnum.NOT_NEED)
    @PostMapping("find/all")
    public Response<List<AccountBookDTO>> getAllAccountBook(@RequestBody BaseRequest baseRequest) {
        return accountingBookService.getAllAccountBook(baseRequest);
    }
    
    /**
     * 更新税
     * @param request
     * @return
     */
    @PostMapping("update/tax")
    @RequiresRoles(NEED_ORG_ID_ENUM = NeedOrgIdEnum.NEED_FINANCE)
    public Response<String> updateCashFlowOrTaxByBookId(@RequestBody SetTaxRuleRequest request) {
        return accountingBookService.setTaxRule(request);
    }
    
    public static void main(String[] args) {
        SetTaxRuleRequest request = new SetTaxRuleRequest();
        request.setAccountBookId("10010110000000000BDI");
        request.setOrgId("00010110000000001WJJ");
        request.setTax(1);
//        request.setRoleId("-1");
        System.out.println(JSONUtil.toJsonStr(request));
    }
    
    
}