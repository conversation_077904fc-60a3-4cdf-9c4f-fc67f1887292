package com.labway.business.center.finance.persistence.entity.v2;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * CBS（支付平台）交易明细实体类
 */
@Data
@TableName("tb_cbs_transaction_detail")
public class CbsTransactionDetail {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 账户名称
     */
    @TableField(value = "account_name")
    private String accountName;

    /**
     * 银行账号
     */
    @TableField(value = "account_no")
    private String accountNo;

    /**
     * 开户行名称
     */
    @TableField(value = "open_bank")
    private String openBank;

    /**
     * 银行流水号
     */
    @TableField(value = "bank_serial_number")
    private String bankSerialNumber;

    /**
     * 交易日期
     */
    @TableField(value = "bank_transaction_date")
    private Date bankTransactionDate;

    /**
     * 交易后余额
     */
    @TableField(value = "account_balance")
    private BigDecimal accountBalance;

    /**
     * 币种
     */
    @TableField(value = "currency")
    private String currency;

    /**
     * 明细类型（只存储历史明细）
     */
    @TableField(value = "detail_type")
    private String detailType;

    /**
     * 业务参考号
     */
    @TableField(value = "serial")
    private String serial;

    /**
     * 发生的金额
     */
    @TableField(value = "incurred_amount")
    private BigDecimal incurredAmount;

    /**
     * 借贷类型1-借;2-贷
     */
    @TableField(value = "loan_type")
    private String loanType;

    /**
     * 对方账号
     */
    @TableField(value = "opposite_account")
    private String oppositeAccount;

    /**
     * 对方户名
     */
    @TableField(value = "opposite_name")
    private String oppositeName;

    /**
     * 对方开户行
     */
    @TableField(value = "opposite_opening_bank")
    private String oppositeOpeningBank;
}
