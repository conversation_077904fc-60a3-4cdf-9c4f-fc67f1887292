package com.labway.business.center.finance.persistence.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.swak.frame.dto.base.Entity;
import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * <B>Description: 业务单元组织对照表实体类</B>
 * </P>
 *
 * <AUTHOR>
 * @version 1.0
 */
@TableName("tb_orgs_mapping")
@ApiModel(value="OrgsMapping对象", description="业务单元组织对照表")
@Data
public class OrgsMapping extends Entity{

    /**
     * 序列号
     */
    private static final long serialVersionUID = 1L;
    /**
    * 主键ID
    */
    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
    * OA 公司编码
    */
    @ApiModelProperty(value = "OA 公司编码")
    @TableField("oa_code")
    private String oaCode;
    /**
    * oa 公司名称
    */
    @ApiModelProperty(value = "oa 公司名称")
    @TableField("oa_name")
    private String oaName;
    /**
    * NCC 公司编码
    */
    @ApiModelProperty(value = "NCC 公司编码")
    @TableField("ncc_code")
    private String nccCode;
    /**
    * NCC 公司名称
    */
    @ApiModelProperty(value = "NCC 公司名称")
    @TableField("ncc_name")
    private String nccName;
    /**
    * 财务组织ID对应org_financeorg表 业务单元表
    */
    @ApiModelProperty(value = "财务组织ID对应org_financeorg表 业务单元表")
    @TableField("finance_org_id")
    private String financeOrgId;
    /**
    * NCC 组织主键
    */
    @ApiModelProperty(value = "NCC 组织主键")
    @TableField("org_id")
    private String orgId;
    /**
    * NCC 组织编码
    */
    @ApiModelProperty(value = "NCC 对应的公司org_corps 编码")
    @TableField("corp_id")
    private String corpId;
    /**
    * 上级NCC组织主键
    */
    @ApiModelProperty(value = "上级NCC组织主键")
    @TableField("father_org_id")
    private String fatherOrgId;
    
    /**
    * 集团ID
    */
    @ApiModelProperty(value = "集团ID")
    @TableField("group_id")
    private String groupId;
    /**
    * 创建时间
    */
    @ApiModelProperty(value = "创建时间")
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 付款是否需要前置审批
     */
    @TableField("is_check")
    private Boolean isCheck;
  
}