package com.labway.business.center.finance.persistence.entity.v2;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 前置审核推送记录
 */
@Data
@TableName("tb_form_pay_front_record")
public class FormPayFrontRecord {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 推送次数
     */
    @TableField("org_id")
    private String orgId;

    /**
     * 推送次数
     */
    @TableField("push_number")
    private Integer pushNumber;

    /**
     * 时间区间 yyyyMM
     */
    @TableField("date_range")
    private String dateRange;

    /**
     * 推送的单据
     */
    @TableField("oa_nos")
    private String oaNos;

    /**
     * 推送时间
     */
    @TableField("push_time")
    private LocalDateTime pushTime;

    /**
     * 推送人
     */
    @TableField("push_user")
    private String pushUser;
}
