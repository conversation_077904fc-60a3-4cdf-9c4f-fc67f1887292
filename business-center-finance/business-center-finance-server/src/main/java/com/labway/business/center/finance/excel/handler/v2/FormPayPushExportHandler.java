package com.labway.business.center.finance.excel.handler.v2;

import com.google.common.collect.Sets;
import com.labway.business.center.finance.constants.FormPayStatus;
import com.labway.business.center.finance.excel.param.v2.FormPayExportDTO;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 已推送未支付
 *
 * <AUTHOR>
 * @version 2024/01/15 14:54
 **/
@Service("formPayPushExportHandler")
public class FormPayPushExportHandler implements FormPayExcelExportHandler {
    /**
     * 导出数据处理
     *
     * @param formPayStatus
     * @param exportData
     * @param response
     */
    @Override
    public void exportDataHandler(FormPayStatus formPayStatus, List<FormPayExportDTO> exportData, HttpServletResponse response) {
        Set<String> includeColumnFiledNames = Sets.newHashSet("rowNo","orgName","oaNo","checkPushNumber","serial","receiveName","totalAmount","payRemark",
                "cityFlag","payBankName","payUser","pushTime","receiveBankName","receiveBankAccount","title","personalFlag","createdBy",
                "createdTime","auditTime");
        responseExcel(formPayStatus,includeColumnFiledNames,exportData,response);
    }
}