package com.labway.business.center.finance.persistence.entity.v3;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.labway.business.center.finance.constants.MonthEnums;
import com.labway.business.center.finance.persistence.entity.BaseEntity;
import com.labway.business.center.finance.persistence.entity.BudgetUnit;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 预算释放表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tb_budget_release")
public class BudgetRelease extends BaseEntity {

    /**
     * 主键
     */
    @TableId(value = "budget_release_id", type = IdType.ASSIGN_ID)
    private Long budgetReleaseId;

    /**
     * oa预算单元id
     * @see BudgetUnit#getOaBudgetId()
     */
    @TableField("budget_unit_id")
    private Long budgetUnitId;

    /**
     * 预算单元
     * @see BudgetUnit#getName()
     */
    @TableField("budget_unit_name")
    private String budgetUnitName;

    /**
     * 预算部门id
     * @see BudgetDept#getBudgetDeptId()
     */
    @TableField("budget_dept_id")
    private Long budgetDeptId;

    /**
     * 预算部门
     * @see BudgetDept#getDeptName()
     */
    @TableField("budget_dept_name")
    private String budgetDeptName;

    /**
     * 预算年度
     */
    @TableField("budget_year")
    private Integer budgetYear;

    /**
     * 月度
     * @see MonthEnums#getMonth()
     */
    @TableField("budget_month")
    private Integer budgetMonth;

    /**
     * 预算类别id（不限制默认为0）
     * @see BudgetType#getTypeId()
     */
    @TableField("budget_type_id")
    private Long budgetTypeId;

	/**
	 * 费用类别名称
	 */
	@TableField("budget_type_name")
	private String budgetTypeName;

	/**
     * 是否控制类型
	 * @see com.labway.business.center.finance.constants.YesOrNoEnums
     */
	@TableField("is_type")
	private Integer isType;

	/**
	 * 释放状态
	 * 释放状态0-已借用，1-已作废
	 * @see com.labway.business.center.finance.constants.YesOrNoEnums
	 */
	@TableField("release_status")
	private Integer releaseStatus;

    /**
     * 释放金额
     */
    @TableField("amount")
    private BigDecimal amount;

	/**
	 * 借用备注
	 */
	@TableField("release_remark")
	private String releaseRemark;

	/**
	 * 作废备注
	 */
	@TableField("invalid_remark")
	private String invalidRemark;
}