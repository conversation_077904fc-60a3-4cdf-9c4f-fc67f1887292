package com.labway.business.center.finance.repository.center;

import com.labway.business.center.finance.persistence.entity.VoucherResponse;
import com.labway.business.center.finance.persistence.mapper.VoucherResponseMapper;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

/**
 * ncc制单日志持久层
 *
 * <AUTHOR>
 * @version 2023/04/07 13:28
 **/
@Repository
public class VoucherResponseRepository {
    @Resource
    private VoucherResponseMapper voucherResponseMapper;
    
    /**
     * 制单日志
     * @param voucherResponse
     */
    public void insertVoucherResponse(VoucherResponse voucherResponse) {
        voucherResponseMapper.insert(voucherResponse);
    }
}