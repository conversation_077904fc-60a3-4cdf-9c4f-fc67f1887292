package com.labway.business.center.finance.persistence.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * SyncRecord 数据同步日志
 *
 * <AUTHOR>
 * @version 2023/04/27 11:03
 **/
@TableName("tb_sync_record")
@Data
@Accessors(chain = true)
public class SyncRecord {
    @TableId(type = IdType.AUTO)
    private Long id;
    private LocalDateTime syncTime;
    /**
     * 状态0-未完成1-已完成
     */
    private Integer status;
    private Long total;
    /**
     * 任务名称
     */
    private String taskName;
    /**
     * 数据来源
     */
    private String dataFromName;
}