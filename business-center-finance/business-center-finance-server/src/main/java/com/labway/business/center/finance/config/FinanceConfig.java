package com.labway.business.center.finance.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 2023/10/09 10:04
 **/
@Data
@RefreshScope
@Configuration
@ConfigurationProperties(prefix = "finance")
public class FinanceConfig {
    /**
     * 二期排除的业务单元
     */
    private Set<String> exclusions;

    /**
     * 304 付款用途
     */
    private Map<String,String> supplyChainMap;

    /**
     * 304 应付业务类型
     */
    private Map<String,String> supplyBusinessType;

    /**
     * 可以给予消息提醒的角色
     */
    private List<String> hasRoleIdList;

	/**
	 * 2024.10.24 添加
	 * <ul>
	 *     特殊的账户类型
	 *     <p>用途：从cbs获取交易明细时候 此类银行账户需要获取 T+1 交易日期的交易明细，否则会导致，<b>对账码</b>字段为空，无法匹配到回单</p>
	 * </ul>
	 * {@link com.labway.business.center.finance.cbs8.constants.BankTypeEnums 银行类型}
	 *
	 */
	private List<String> specialBankType;

	/**
	 * 2025.1.14
	 * 工会编码
	 * @Description 工会没有nc账套，但是需要再中台完成付款但不制单
	 */
	private List<String> laborUnion;

	/**
	 * 2025.4.15
	 * 排除校验预算表单类型
	 * @since v3
	 * @Description 排除校验预算表单类型 这些类型的费用类别不会进行预算的校验
	 */
	private List<String> exclusionBudgetFormType;

	/**
	 * 排除的路径
	 * @since v3
	 */
	private List<String> exclusionPath;

    /**
     * 请汇类型
     * @param remittanceType
     * @return
     */
    public String getSupplyChainPayRemark(String remittanceType) {
        return supplyChainMap.get(remittanceType);
    }

    /**
     * 应付业务类型
     * @param busType
     * @return
     */
    public String getBusTypeCode(String busType) {
        return supplyBusinessType.get(busType);
    }
}