 package com.labway.business.center.finance.controller;

import com.labway.business.center.core.user.RequiresRoles;
import com.labway.business.center.finance.anno.SecurityParameter;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.labway.business.center.finance.service.CashflowService;
import com.swak.frame.dto.Response;

/**
  * 现金流项目服务类
  * <AUTHOR>
  * @date 2023/04/07
  */
 @RestController
 @RequestMapping("cashflow/")
 public class CashflowController {
     
     @DubboReference
     private CashflowService cashflowService;
    @RequiresRoles
     @RequestMapping("wash")
	@SecurityParameter(inDecode = false, outEncode = false)
     public Response<?> washCashflow2Database(){
         return cashflowService.washCashflow2Database();
     }
     
    

}
