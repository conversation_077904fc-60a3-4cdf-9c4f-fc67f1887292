package com.labway.business.center.finance.repository.center.v3;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.business.center.finance.constants.YesOrNoEnums;
import com.labway.business.center.finance.persistence.entity.v3.BudgetRelease;
import com.labway.business.center.finance.persistence.mapper.v3.BudgetReleaseMapper;
import com.labway.business.center.finance.request.v3.budget.release.BudgetReleaseSearchRequest;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 预算释放记录
 *
 * <AUTHOR> on 2025/5/23.
 */
@Repository
@CacheConfig(cacheNames = "budget-release:")
public class BudgetReleaseRepository {

	@Resource
	private BudgetReleaseMapper budgetReleaseMapper;

	@CacheEvict(allEntries = true)
	public void insertBudgetRelease(BudgetRelease insertBudgetRelease) {
		if (Objects.isNull(insertBudgetRelease)) {
			return;
		}
		budgetReleaseMapper.insert(insertBudgetRelease);
	}

	public List<BudgetRelease> searchBudgetReleaseByIds(List<Long> invalidIds) {
		if (CollectionUtils.isEmpty(invalidIds)) {
			return Collections.emptyList();
		}
		return budgetReleaseMapper.selectBatchIds(invalidIds);
	}

	@CacheEvict(allEntries = true)
	public void invalidBudgetRelease(List<Long> invalidIds, String invalidRemark) {
		if (CollectionUtils.isEmpty(invalidIds)) {
			return;
		}
		budgetReleaseMapper.update(null, Wrappers.lambdaUpdate(BudgetRelease.class)
				.in(BudgetRelease::getBudgetReleaseId, invalidIds)
				.set(BudgetRelease::getReleaseStatus, YesOrNoEnums.YES.getStatus())
				.set(BudgetRelease::getReleaseRemark, invalidRemark));
	}

	public List<BudgetRelease> searchBudgetReleaseList(BudgetReleaseSearchRequest request) {
		if (Objects.isNull(request)) {
			return Collections.emptyList();
		}
		return budgetReleaseMapper.selectList(Wrappers.lambdaQuery(BudgetRelease.class)
				.eq(BudgetRelease::getBudgetUnitId, request.getBudgetUnitId())
				.eq(BudgetRelease::getBudgetYear, request.getBudgetYear())
				.in(BudgetRelease::getBudgetMonth, request.getSearchMonths())
				.eq(Objects.nonNull(request.getBudgetDeptId()), BudgetRelease::getBudgetDeptId, request.getBudgetDeptId())
				.eq(Objects.nonNull(request.getReleaseStatus()), BudgetRelease::getReleaseStatus, request.getReleaseStatus())
				.eq(Objects.nonNull(request.getBudgetTypeId()), BudgetRelease::getBudgetTypeId, request.getBudgetTypeId()));
	}

	/**
	 * 查询 预算单元 预算部门 年度 月度 的释放额度
	 * @param oaBudgetId 预算单元
	 * @param budgetDeptId 预算部门
	 * @param year 年度
	 * @param month 月度
	 */
	@Cacheable(key = "'searchReleaseByUnitAndDeptAndYearAndMonth:' + #oaBudgetId + ':' + #budgetDeptId + ':' + #budgetDeptId+ ':' + #year+ ':' + #int")
	public List<BudgetRelease> searchReleaseByUnitAndDeptAndYearAndMonth(Long oaBudgetId, Long budgetDeptId, int year, int month) {
		return budgetReleaseMapper.selectList(Wrappers.lambdaQuery(BudgetRelease.class)
				.eq(BudgetRelease::getBudgetUnitId, oaBudgetId)
				.eq(BudgetRelease::getBudgetDeptId, budgetDeptId)
				.eq(BudgetRelease::getBudgetYear, year)
				.eq(BudgetRelease::getBudgetMonth, month)
				.eq(BudgetRelease::getReleaseStatus, YesOrNoEnums.NO.getStatus()));
	}
}
