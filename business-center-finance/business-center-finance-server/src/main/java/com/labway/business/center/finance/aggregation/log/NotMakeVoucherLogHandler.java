package com.labway.business.center.finance.aggregation.log;

import cn.hutool.json.JSONUtil;
import com.labway.business.center.core.enums.OperationTypeEnum;
import com.labway.business.center.core.log.param.OperationLogRequest;
import com.labway.business.center.finance.aggregation.log.param.BaseParam;
import com.labway.business.center.finance.config.FinanceLogConfig;
import com.labway.business.center.finance.constants.FinanceConstants;
import com.labway.business.center.finance.constants.LogType;
import com.labway.business.center.finance.repository.center.VoucherRepository;
import com.labway.business.center.finance.request.voucher.VoucherUnMakeRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 制单
 *
 * <AUTHOR>
 * @version 2023/06/09 11:35
 **/
@Slf4j
@Component("notMakeVoucherLogHandler")
public class NotMakeVoucherLogHandler implements LogHandler {

	@Resource
	private VoucherRepository voucherRepository;

	/**
	 * @param body 参数
	 */
	@Override
	public void saveLog(String body) throws Exception {
		BaseParam baseParam = JSONUtil.toBean(body, BaseParam.class);
		VoucherUnMakeRequest voucherUnMakeRequest = JSONUtil.toBean(baseParam.getData(), VoucherUnMakeRequest.class);
		List<String> oaNoList = voucherRepository.getVoucherOaNoByOasFlowNos(voucherUnMakeRequest.getOaflowNoList());
		String oaFlowNos = oaNoList.stream().collect(Collectors.joining(","));
		StringBuilder sb = new StringBuilder();
		sb.append(LogType.VOUCHER_NOT.getType()).append("，【").append(oaFlowNos).append("】，备注【").append(voucherUnMakeRequest.getRemark()).append("】");
		String operationMsg = sb.toString();
		sendMq(OperationTypeEnum.MAKE, operationMsg, FinanceConstants.ModuleName.VOUCHER.getModule(), baseParam);
	}
}