package com.labway.business.center.finance.service.chain.budget.version.modify;

import cn.hutool.json.JSONUtil;
import com.labway.business.center.finance.constants.MonthEnums;
import com.labway.business.center.finance.persistence.entity.v3.Budget;
import com.labway.business.center.finance.persistence.entity.v3.BudgetVersion;
import com.labway.business.center.finance.repository.center.v3.BudgetRepository;
import com.labway.business.center.finance.repository.center.v3.BudgetVersionRepository;
import com.labway.business.center.finance.request.v3.budget.version.BudgetModifyRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Objects;

/**
 * 实际的修改
 *
 * <AUTHOR> on 2025/4/2.
 */
@Slf4j
@Component
public class ModifyBudgetAmountUpdateCommand implements Command {

	@Resource
	private BudgetRepository budgetRepository;

	@Resource
	private BudgetVersionRepository budgetVersionRepository;

	@Override
	public boolean execute(Context c) throws Exception {
		ModifyBudgetAmountContext context = ModifyBudgetAmountContext.from(c);
		BudgetModifyRequest request = context.getRequest();
		BudgetVersion originBudgetVersion = context.getOriginBudgetVersion();

		// 修改后的预算金额
		BigDecimal newBudgetAmount = request.getBudgetAmount();
		if (context.isParent) {
			BigDecimal childrenAmount = MonthEnums.getAnyMonthBudget(context.getChildrenBudget(), MonthEnums.getMonthByMonth(request.getMonth()));

			newBudgetAmount = MonthEnums.getAnyMonthBudget(originBudgetVersion, MonthEnums.getMonthByMonth(request.getMonth()))
					.add(newBudgetAmount.subtract(childrenAmount));
			context.setParentAmount(newBudgetAmount);
			log.info("当前金额{}，预算{}", newBudgetAmount, JSONUtil.toJsonStr(originBudgetVersion));
		}
		MonthEnums.setAnyMonthBudget(originBudgetVersion, MonthEnums.getMonthByMonth(request.getMonth()), newBudgetAmount);
		budgetVersionRepository.updateById(originBudgetVersion);

		Budget originBudget = context.getOriginBudget();
		if (Objects.nonNull(originBudget)) {
			MonthEnums.setAnyMonthBudget(originBudget, MonthEnums.getMonthByMonth(request.getMonth()), newBudgetAmount);
			budgetRepository.updateById(originBudget);
		}


		return CONTINUE_PROCESSING;
	}
}
