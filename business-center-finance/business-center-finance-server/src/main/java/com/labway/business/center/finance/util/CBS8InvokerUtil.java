package com.labway.business.center.finance.util;

import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;

import com.labway.business.center.finance.cbs8.model.Cbs8ResponseEnum;
import com.labway.business.center.finance.cbs8.model.request.Cbs8TokenRequest;
import com.labway.business.center.finance.cbs8.model.response.CBS8ListResponse;
import com.labway.business.center.finance.cbs8.model.response.CBS8SimpleResponse;
import com.labway.business.center.finance.cbs8.model.response.Cbs8TokenDTO;
import com.labway.business.center.finance.config.Cbs8Config;
import com.labway.business.center.finance.constants.FinanceConstants;
import com.labway.business.center.finance.exception.CBS8Exception;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.io.IOUtils;
import org.apache.http.Header;
import org.apache.http.HttpMessage;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ByteArrayEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.protocol.HTTP;
import org.checkerframework.checker.units.qual.C;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.zip.GZIPInputStream;

/**
 * 招商对接http工具类
 *
 * <AUTHOR> Tianhao
 * @version 2023/10/25 11:11
 **/
@Slf4j
@Component
public class CBS8InvokerUtil {
    @Resource
    private Cbs8Config cbs8Config;

    public  static final String GET_TOKEN_URL = "/openapi/app/v1/app/token";

    public  CBS8SimpleResponse<Cbs8TokenDTO> getCbs8Token(Cbs8Config cbs8Config) {
        Cbs8TokenRequest cbs8TokenRequest = new Cbs8TokenRequest();
        cbs8TokenRequest.setAppId(cbs8Config.getAppId());
        cbs8TokenRequest.setAppSecret(cbs8Config.getAppSecret());
        String body = JSONUtil.toJsonStr(cbs8TokenRequest);
        log.info("请求获取招商银行token参数：{}",body);
        try {
            cn.hutool.http.HttpResponse response = HttpRequest.post(cbs8Config.getUrl() + GET_TOKEN_URL).body(body).execute();
            String responseStr = response.body();
            log.info("获取招商银行token结果：{}",responseStr);
            CBS8SimpleResponse<Cbs8TokenDTO> cbs8TokenResponse = convert2Cbs2Response(responseStr);
            if (cbs8TokenResponse.isFail()) {
                throw new CBS8Exception(cbs8TokenResponse.getCode(),cbs8TokenResponse.getMsg()); 
            }
            return cbs8TokenResponse;
        } catch (Exception e) {
            log.error("获取招商银行token出现异常",e);
           throw new CBS8Exception("获取招商银行Token 失败");
        }
    }

    private  CBS8SimpleResponse<Cbs8TokenDTO> convert2Cbs2Response(String responseStr) {
        CBS8SimpleResponse<Cbs8TokenDTO> response = CBS8SimpleResponse.success();
        JSONObject jsonObject = JSONUtil.parseObj(responseStr);
        response.setCode(jsonObject.getStr("code"));
        response.setMsg(jsonObject.getStr("msg"));
        Cbs8TokenDTO cbs8TokenDTO = jsonObject.getJSONObject("data").toBean(Cbs8TokenDTO.class);
        response.setData(cbs8TokenDTO);
        return response;
    }


    public  CBS8SimpleResponse<?> invokeCbs8WithSimpleResult( String url, String body) throws IOException {
        CloseableHttpClient client = HttpClients.custom()
                // 禁止HttpClient自动解压缩
                .disableContentCompression()
                .build();
        CBS8SimpleResponse<Cbs8TokenDTO> cbs8Token = getCbs8Token(cbs8Config);
        HttpPost httpPost = setupRequest(cbs8Config, url, body,cbs8Token.getData().getToken());
        try (CloseableHttpResponse response = client.execute(httpPost)) {
            byte[] finalResponseData = handleResponse(cbs8Config, response);
            String result = new String(finalResponseData);
            log.info("\n返回结果：{}",result );
            return CBS8SimpleResponse.success(result);
        } catch (IOException ignored) {
            return CBS8SimpleResponse.fail("1","网络连接失败或超时");
        } catch (Exception e) {
            return CBS8SimpleResponse.fail("1","调用过程发生异常");
        } finally {
            client.close();
        }
    }

    
    public  String invokeCbs8ReturnString( String url, String body) throws IOException {
        CloseableHttpClient client = HttpClients.custom()
                // 禁止HttpClient自动解压缩
                .disableContentCompression()
                .build();
        CBS8SimpleResponse<Cbs8TokenDTO> cbs8Token = getCbs8Token(cbs8Config);
       
        HttpPost httpPost = setupRequest(cbs8Config, url, body,cbs8Token.getData().getToken());
        try (CloseableHttpResponse response = client.execute(httpPost)) {
            byte[] finalResponseData = handleResponse(cbs8Config, response);
            String result = new String(finalResponseData);
            if (log.isDebugEnabled()) {
                log.debug("\n返回结果：{}",result );
            }
            return result;
        } catch (IOException ignored) {
            throw new CBS8Exception("1","网络连接失败或超时");
        } catch (Exception e) {
            throw new CBS8Exception("1","调用过程发生异常");
        } finally {
            client.close();
        }
    }

    /**
     * 生成请求报文
     */
    private  HttpPost setupRequest(Cbs8Config cbs8Config, String url,String body, String token) {
        long timestamp = System.currentTimeMillis();

        // 请求数据拼接：  报文体+时间戳
        byte[] requestDataBytes = body.getBytes(StandardCharsets.UTF_8);
        byte[] timestampBytes = ("&timestamp=" + timestamp).getBytes(StandardCharsets.UTF_8);
        byte[] newBytes = new byte[requestDataBytes.length + timestampBytes.length];
        System.arraycopy(requestDataBytes, 0, newBytes, 0, requestDataBytes.length);
        System.arraycopy(timestampBytes, 0, newBytes, requestDataBytes.length, timestampBytes.length);

        // 生成签名
        byte[] signature = SM2Util.sign(cbs8Config.getSignEncryptionPrivateKey(), newBytes);
        String sign = Base64.encodeBase64String(SM2Util.encodeDERSignature(signature));
        log.info("签名:{}", sign);

        // 设置请求URL
        HttpPost httpPost = new HttpPost(cbs8Config.getUrl() + url);
        // 请求头设置签名
        httpPost.setHeader(FinanceConstants.SIGN_HEADER_NAME, sign);
        // 请求头设置时间戳
        httpPost.setHeader(FinanceConstants.TIMESTAMP_HEADER, Long.toString(timestamp));
        // 请求头设置请求参数格式，请根据实际情况改写
        httpPost.setHeader(HTTP.CONTENT_TYPE, FinanceConstants.TARGET_CONTENT_TYPE);
        // 请求头设置TOKEN
        httpPost.setHeader(FinanceConstants.AUTHORIZATION, FinanceConstants.BEARER + token);

        // 报文体加密
        byte[] encryptedData = SM2Util.encrypt(cbs8Config.getBodyEncryptionKey(), requestDataBytes);
        // 设置请求体
        httpPost.setEntity(new ByteArrayEntity(encryptedData));

        return httpPost;
    }

    /**
     * 处理响应报文
     */
    private  byte[] handleResponse(Cbs8Config cbs8Config, HttpResponse response) throws Exception {
        InputStream content = response.getEntity().getContent();
        byte[] responseData = IOUtils.toByteArray(content);

        if (responseData == null || responseData.length == 0) {
            return responseData == null ? new byte[0] : responseData;
        }

        // 步骤1 原始响应报文解密 如果服务网关获取加解密密钥失败，则无法解密请求报文，且无法加密响应报文。 这时候，网关会直接返回错误信息，响应报文是未加密状态。
        Boolean encryptionEnable = getHeader(response, FinanceConstants.ENCRYPTION_ENABLED_HEADER_NAME);

        if (Boolean.TRUE.equals(encryptionEnable)) {
            responseData = SM2Util.decrypt(cbs8Config.getBodyDecryptionKey(), responseData);
        }

        Boolean xMbcloudCompress = getHeader(response, FinanceConstants.X_MBCLOUD_COMPRESS);
        if (Boolean.TRUE.equals(xMbcloudCompress)) {
            responseData = decompress(responseData);
        }
        return responseData;
    }

    private  Boolean getHeader(HttpMessage message, String name) {
        Header header = message.getFirstHeader(name);
        return header != null;
    }

    public  byte[] decompress(byte[] data) throws IOException {
        ByteArrayInputStream input = new ByteArrayInputStream(data);
        GZIPInputStream gzipInput = new GZIPInputStream(input);
        return IOUtils.toByteArray(gzipInput);
    }
}