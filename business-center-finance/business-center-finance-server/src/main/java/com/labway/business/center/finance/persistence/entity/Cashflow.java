
package com.labway.business.center.finance.persistence.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * <B>Description: 现金流量项目实体类</B>
 * </P>
 *
 * <AUTHOR>
 * @version 1.0
 */
@TableName("tb_cashflow")
@ApiModel(value="Cashflow对象", description="现金流量项目")
@Data
public class Cashflow{

    /**
     * 序列号
     */
    private static final long serialVersionUID = 1L;
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
    * 	现金流量项目主键
    */
    @ApiModelProperty(value = "	现金流量项目主键")
    @TableField("cashflow_id")
    private String cashflowId;
    /**
    * 组织ID
    */
    @ApiModelProperty(value = "组织ID")
    @TableField("org_id")
    private String orgId;
    /**
    * 集团ID
    */
    @ApiModelProperty(value = "集团ID")
    @TableField("group_id")
    private String groupId;
    /**
    * 现金流量项目编码
    */
    @ApiModelProperty(value = "现金流量项目编码")
    @TableField("code")
    private String code;
    /**
    * 现金流量项目名称
    */
    @ApiModelProperty(value = "现金流量项目名称")
    @TableField("name")
    private String name;
    /**
    * 上级项目
    */
    @ApiModelProperty(value = "上级项目")
    @TableField("parent_id")
    private String parentId;
    /**
    * 现金流量项目类型 (itemtype)
    */
    @ApiModelProperty(value = "现金流量项目类型 (itemtype)")
    @TableField("item_type")
    private Integer itemType;
    /**
    * 是否是主表项目
    */
    @ApiModelProperty(value = "是否是主表项目")
    @TableField("is_main")
    private String isMain;
    /**
    * 内部码
    */
    @ApiModelProperty(value = "内部码")
    @TableField("inner_code")
    private String innerCode;
    /**
    * 启动状态，1=未启用;2=已启用;3=已停用;
    */
    @ApiModelProperty(value = "启动状态，1=未启用;2=已启用;3=已停用;")
    @TableField("enable_state")
    private Integer enableState;
   
}