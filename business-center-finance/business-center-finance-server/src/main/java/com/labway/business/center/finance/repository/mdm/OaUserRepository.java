package com.labway.business.center.finance.repository.mdm;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.labway.business.center.finance.persistence.entity.mdm.OaUser;
import com.labway.business.center.finance.persistence.mapper.mdm.OaUserMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2024/01/02 13:53
 **/
@DS("mdm")
@Repository
public class OaUserRepository {
    @Resource
    private OaUserMapper oaUserMapper;


    /**
     * 根据用户名称搜索
     * @param userName
     * @return
     */
    public List<OaUser> searchByName(String userName) {
        LambdaQueryWrapper<OaUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StringUtils.isNotBlank(userName), OaUser::getName, userName);
        return oaUserMapper.selectList(queryWrapper);
    }
}