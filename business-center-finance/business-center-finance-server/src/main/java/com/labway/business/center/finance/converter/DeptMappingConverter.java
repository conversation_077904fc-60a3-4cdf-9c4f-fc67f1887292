package com.labway.business.center.finance.converter;

import com.labway.business.center.finance.persistence.entity.DeptMapping;
import com.labway.business.center.finance.request.DeptMappingRequest;
import org.mapstruct.Mapper;

/**
 * DeptMappingConverter 部门关系转换器
 *
 * <AUTHOR>
 * @version 2023/03/27 16:37
 **/
@Mapper(componentModel = "spring")
public interface DeptMappingConverter {
    /**
     * 请求对象转为实体对象
     * @param deptMappingRequest 部门关系请求对象
     * @return 部门实体
     */
    DeptMapping deptMappingRequest2DeptMapping(DeptMappingRequest deptMappingRequest);
}