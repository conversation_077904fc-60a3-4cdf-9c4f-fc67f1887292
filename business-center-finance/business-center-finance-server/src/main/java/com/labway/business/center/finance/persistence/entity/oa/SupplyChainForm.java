package com.labway.business.center.finance.persistence.entity.oa;

import com.labway.business.center.finance.constants.OaAccountClassEnums;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 供应链汇款申请单
 *
 * <AUTHOR>
 * @date 2023/04/18
 */
@Data
public class SupplyChainForm implements Serializable {

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 申请人
     */
    private Long applyUser;

    /**
     * 申请人用户名
     */
    private String applyUserName;

    /**
     * 部门ID
     */
    private Long deptId;

    /**
     * 开始时间
     */
    private Date startDate;

    /**
     * 审核通过时间
     */
    private Date approveDate;

    /**
     * 真实金额
     */
    private BigDecimal realAmount;

    /**
     * OA 表单编码
     */
    private Long oaFlowNo;

    /**
     * OA 表单编码
     */
    private String oaNo;

    /**
     * 开户行名称
     */
    private String receiveBankName;

    /**
     * 收款人
     */
    private String receiveName;

    /**
     * 收款账号
     */
    private String receiveBankAccount;

    /**
     * 实报金额
     */
    private BigDecimal totalAmount;

    /**
     * 付款用途
     */
    private String payRemark;

    /**
     * 对公/对私
     * @see OaAccountClassEnums
     */
    private String personalFlag;

    /**
     * 联行号
     */
    private String correspondentNumber;

    /**
     * 请汇类型
     */
    private String remittanceType;

    /**
     * 币种
     * RMB/USD/空白
     */
    private String currency;

    /**
     * 订单号
     * field0065
     */
    private String orderNum;

    /**
     * 发票号
     * field0067
     */
    private String invoiceIds;

	/**
	 * 是否募投
	 * field0055 varchar(5)
	 */
	private String isFundraising;
}
