 package com.labway.business.center.finance.persistence.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.labway.business.center.core.injector.ExtBaseMapper;
import com.labway.business.center.finance.persistence.entity.BusinessUnitCompany;

import java.util.List;
 
 
 /**
 * 组织_业务单元_公司 业务中台数据库
 * <AUTHOR>
 * @date 2023/03/21
 */
@DS("master")
public interface BusinessUnitCompanyMapper extends ExtBaseMapper<BusinessUnitCompany> {
    
    Integer batchUpdateBusinessUnitCompany(List<BusinessUnitCompany> updateBusinessUnitCompanies);
}
