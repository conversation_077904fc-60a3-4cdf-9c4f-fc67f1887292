package com.labway.business.center.finance.service.impl.v3;

import cn.hutool.core.date.DateUtil;
import com.labway.business.center.finance.config.FinanceConfig;
import com.labway.business.center.finance.constants.*;
import com.labway.business.center.finance.converter.v3.BudgetTypeConverter;
import com.labway.business.center.finance.persistence.entity.BudgetUnit;
import com.labway.business.center.finance.persistence.entity.v3.*;
import com.labway.business.center.finance.repository.center.BudgetUnitRepository;
import com.labway.business.center.finance.repository.center.v3.*;
import com.labway.business.center.finance.request.v3.budget.av.BudgetAvailableListSearchRequest;
import com.labway.business.center.finance.response.v3.budget.av.BudgetAvResponseOA;
import com.labway.business.center.finance.response.v3.budget.av.BudgetAvailableResponse;
import com.labway.business.center.finance.response.v3.budget.type.BudgetTypeResponse;
import com.labway.business.center.finance.service.v3.BudgetAvailableSearchService;
import com.labway.business.center.finance.service.v3.BudgetVersionService;
import com.swak.frame.dto.Response;
import com.swak.frame.exception.BizException;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 这里查询
 * 1. 查询 tb_budget 启用预算表，这里的数据是按照年维度的，一条数据包含了一个部门，一个预算类别12个月的额度
 * 2. tb_budget_flow 表中的数据关联的是av表中的数据
 * 3. 考虑在tb_budget_flow 表中增加字段：预算单元、预算部门、预算年度、预算月度
 * 4. tb_budget_add 预算追加记录表中，记录的是追加的预算，通过 tb_budget 这张表的数据匹配追加预算的时候，使用预算单元、部门、年度、月度、类别 进行匹配（要求在追加预算的时候类别不能为空）
 * 5. 每个月的预算数据只跟当月的预算额度对比,做加减 比如 我现在查询4月分的数据, 那么可用额度就是 4月份的预算 - 4月份的预占用- 4月份的已占用 (不按照他是总额还是费用类别的控制)
 */
@Slf4j
@Service
public class BudgetAvailableSearchServiceImpl implements BudgetAvailableSearchService {

    @Resource
    private BudgetRepository budgetRepository;

    @Resource
    private BudgetAddRepository budgetAddRepository;

    @Resource
    private BudgetTypeRepository budgetTypeRepository;

	@Resource
	private BudgetUnitRepository budgetUnitRepository;

	@Resource
	private BudgetDeptRepository budgetDeptRepository;

    @Resource
    private BudgetFlowRepository budgetFlowRepository;

	@Resource
	private FeeBudgetFormTypeRepository feeBudgetFormTypeRepository;

	@Resource
	private BudgetFeeTypeMappingRepository budgetFeeTypeMappingRepository;


    @Resource
    private BudgetTypeConverter budgetTypeConverter;

    @Resource
    private BudgetVersionService budgetVersionService;


	@Resource
	private FinanceConfig financeConfig;


    @SneakyThrows
    @Override
    public Response<List<BudgetAvailableResponse>> searchBudgetAvailable(BudgetAvailableListSearchRequest request) {
        Integer budgetYear = request.getBudgetYear();
		if (CollectionUtils.isNotEmpty(request.getOaFeeTypeIds())) {

			// 过滤掉不需要校验的类别
			List<String> exclusionBudgetFormType = financeConfig.getExclusionBudgetFormType();
			if (CollectionUtils.isNotEmpty(exclusionBudgetFormType)) {
				List<FeeBudgetFormType> feeBudgetFormTypes = feeBudgetFormTypeRepository.searchListByExclusionBudgetFormType(exclusionBudgetFormType);
				Set<Long> feeTypeIds = feeBudgetFormTypes.stream().map(FeeBudgetFormType::getOaFeeTypeId).collect(Collectors.toSet());
				request.getOaFeeTypeIds().removeIf(feeTypeIds::contains);
			}

			if (CollectionUtils.isEmpty(request.getOaFeeTypeIds())) {
				return Response.success(Collections.emptyList());
			}

		}

		// 查询参数处理
	    handlerSearchRequestParam(request);
		boolean searchByType = Boolean.FALSE;
	    if (CollectionUtils.isNotEmpty(request.getBudgetTypeIds())) {
			searchByType = Boolean.TRUE;
	    }

	    Long unitId = request.getOaBudgetUnitId();
	    List<Long> budgetDeptIds = request.getBudgetDeptId();

	    // 查询预算信息
        // 预算信息查到之后  直接按照所有的预算类别 & 月份循环 不存在的就补0
        List<Budget> budgetInfos = budgetRepository.searchBudgetList2AvList(unitId, budgetYear, budgetDeptIds);
        if (CollectionUtils.isEmpty(budgetInfos)) {
            return Response.success(Collections.emptyList());
        }
        if (CollectionUtils.isEmpty(request.getSearchMonths())) {
            request.setSearchMonths(Arrays.stream(MonthEnums.values())
                    .map(MonthEnums::getMonth)
                    .collect(Collectors.toList()));
        }

        List<BudgetFlow> budgetFlows = budgetFlowRepository.searchFlowListByUnitAndeDeptAndYearMonths(unitId, budgetDeptIds, budgetYear, request.getSearchMonths(),
                        FlowStatusEnums.PROCESSING).stream().filter(budgetFlow -> TransactionTypeEnums.isAnyType(budgetFlow.getTransactionType(), TransactionTypeEnums.USE, TransactionTypeEnums.PRE_USE))
                .collect(Collectors.toList());
        Map<String, List<BudgetFlow>> flowMap = budgetFlows.stream().filter(item -> Objects.nonNull(item.getBudgetTypeId())).collect(Collectors.groupingBy(item -> item.getBudgetTypeId() + "-" + item.getBudgetMonth()));

        Map<Long, List<Budget>> budgetMap = budgetInfos.stream().collect(Collectors.groupingBy(Budget::getBudgetTypeId));

        List<BudgetType> budgetTypes = budgetTypeRepository.searchAllType();

        // 查询追加的预算记录
        List<BudgetAdd> budgetAdds = budgetAddRepository.searchAddBudgetListByUnitAndDeptAndYearMonths(unitId, budgetDeptIds, budgetYear, request.getSearchMonths());
        // 按照部门 类别 月度 分组
        Map<String, List<BudgetAdd>> addMap = budgetAdds.stream().collect(Collectors.groupingBy(add ->
                add.getBudgetTypeId() + "-" + add.getBudgetMonth()));

        List<BudgetAvailableResponse> budgetAvailableResponses = new ArrayList<>(budgetTypes.size());

        Map<Integer, List<BudgetType>> typeLevelMap = budgetTypes.stream().collect(Collectors.groupingBy(BudgetType::getTypeLevel, Collectors.toList()));

        // 这里只处理二级的
        for (BudgetType budgetType : typeLevelMap.getOrDefault(LevelTypeEnums.SECOND_LEVEL.getTypeLevel(), Collections.emptyList())) {
			if(searchByType && !request.getBudgetTypeIds().contains(budgetType.getTypeId())){
				continue;
			}

            BudgetTypeResponse budgetTypeResponse = budgetTypeConverter.convertEntity2Response(budgetType);
            BudgetAvailableResponse response = new BudgetAvailableResponse();
            response.setBudgetYear(budgetYear);
            response.setBudgetUnitId(unitId);
            response.setBudgetTypeResponse(budgetTypeResponse);
            // 这个集合中数据的条数应该等于查询月份的个数
            List<BudgetAvailableResponse.DynamicBudgetAv> dynamicList = new ArrayList<>(request.getSearchMonths().size());
            response.setDynamicBudgetList(dynamicList);

            for (MonthEnums monthEnum : MonthEnums.of(request.getSearchMonths().toArray(Integer[]::new))) {
                List<Budget> budgets = budgetMap.get(budgetType.getTypeId());
                // 如果这个类别下没有预算数据 直接生成默认数据（金额填充为0）
                if (CollectionUtils.isEmpty(budgets)) {
                    BudgetAvailableResponse.DynamicBudgetAv dynamicBudgetAv = BudgetAvailableResponse.DynamicBudgetAv.of();
                    dynamicBudgetAv.setBudgetMonth(monthEnum.getMonth());
                    dynamicList.add(dynamicBudgetAv);
                    continue;
                }
                BudgetAvailableResponse.DynamicBudgetAv av = BudgetAvailableResponse.DynamicBudgetAv.of();
                av.setBudgetMonth(monthEnum.getMonth());
                dynamicList.add(av);

                // 需要将每个部门有这个类别预算的整合为一条数据 放在 av 对象中

                // 初始预算额度 = 这些部门这个月的预算额度之和
                av.setBudgetAmount(budgets.stream().map(bud -> {
                    try {
                        return MonthEnums.getAnyMonthBudget(bud, monthEnum);
                    } catch (NoSuchFieldException | IllegalAccessException e) {
                        throw new RuntimeException(e);
                    }
                }).reduce(BigDecimal.ZERO, BigDecimal::add));

                List<BudgetFlow> flows = flowMap.getOrDefault(budgetType.getTypeId() + "-" + monthEnum.getMonth(), List.of());
                // 已用预算额度 = flow中这个类别这个月 状态为占用的总和
                av.setUsedAmount(flows.stream()
                        .filter(item -> TransactionTypeEnums.isAnyType(item.getTransactionType(), TransactionTypeEnums.USE))
                        .map(BudgetFlow::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add));

                // 预占用额度 = flow中这个类别这个月 状态为预占的总和
                av.setPreviewAmount(flows.stream()
                        .filter(item -> TransactionTypeEnums.isAnyType(item.getTransactionType(), TransactionTypeEnums.PRE_USE))
                        .map(BudgetFlow::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add));

                // 执行笔数
                // av.setCount((long) flows.stream().map(BudgetFlow::getOaNo).collect(Collectors.toSet()).size());

                // 追加额度 = 追加表中 这个类别 这个月追加的总额
                av.setAddAmount(addMap.getOrDefault(budgetType.getTypeId() + "-" + monthEnum.getMonth(), List.of())
                        .stream().map(BudgetAdd::getAddAmount).reduce(BigDecimal.ZERO, BigDecimal::add));

                // 总预算额度 = 初始预算额度 + 追加额度
                av.setTotalAmount(av.getBudgetAmount().add(av.getAddAmount()));

                // 可用预算 = 总预算额度 - 已用预算 - 预占用预算
                av.setAvailableAmount(av.getTotalAmount().subtract(av.getUsedAmount()).subtract(av.getPreviewAmount()));
            }

            // 计算合并的数据
            BudgetAvailableResponse.DynamicBudgetAv merge = BudgetAvailableResponse.DynamicBudgetAv.of();
            merge.setBudgetMonth(0);

            for (BudgetAvailableResponse.DynamicBudgetAv dynamicBudgetAv : dynamicList) {
                merge.setAvailableAmount(merge.getAvailableAmount().add(dynamicBudgetAv.getAvailableAmount()));
                merge.setPreviewAmount(merge.getPreviewAmount().add(dynamicBudgetAv.getPreviewAmount()));
                merge.setUsedAmount(merge.getUsedAmount().add(dynamicBudgetAv.getUsedAmount()));
                merge.setBudgetAmount(merge.getBudgetAmount().add(dynamicBudgetAv.getBudgetAmount()));
                merge.setAddAmount(merge.getAddAmount().add(dynamicBudgetAv.getAddAmount()));
                merge.setTotalAmount(merge.getTotalAmount().add(dynamicBudgetAv.getTotalAmount()));
                // merge.setCount(merge.getCount() + dynamicBudgetAv.getCount());
                merge.setCanMerge(Boolean.FALSE);
            }
            response.setMergerDynamicBudgetAv(merge);
            budgetAvailableResponses.add(response);
        }

        Map<Long, List<BudgetAvailableResponse>> typeAvMap = budgetAvailableResponses.stream()
                .collect(Collectors.groupingBy(item -> item.getBudgetTypeResponse().getParentId()));

        // 父级的信息在这里合并
        for (BudgetType budgetType : typeLevelMap.getOrDefault(LevelTypeEnums.FIRST_LEVEL.getTypeLevel(), Collections.emptyList())) {
	        if (searchByType && !request.getBudgetTypeIds().contains(budgetType.getTypeId())) {
		        continue;
	        }

            BudgetTypeResponse budgetTypeResponse = budgetTypeConverter.convertEntity2Response(budgetType);
            BudgetAvailableResponse response = new BudgetAvailableResponse();
            response.setBudgetYear(budgetYear);
            response.setBudgetUnitId(unitId);
            response.setBudgetTypeResponse(budgetTypeResponse);
            // 这个集合中数据的条数应该等于查询月份的个数
            List<BudgetAvailableResponse.DynamicBudgetAv> dynamicList = new ArrayList<>(request.getSearchMonths().size());
            response.setDynamicBudgetList(dynamicList);

            // 根据父级id获取子集的预算数据 按照月度汇总
            Map<Integer, List<BudgetAvailableResponse.DynamicBudgetAv>> typeMonthAvMap = typeAvMap.getOrDefault(budgetType.getTypeId(), List.of()).stream().flatMap(item -> item.getDynamicBudgetList().stream())
                    .collect(Collectors.groupingBy(BudgetAvailableResponse.DynamicBudgetAv::getBudgetMonth));
            for (MonthEnums monthEnum : MonthEnums.of(request.getSearchMonths().toArray(Integer[]::new))) {
                // 获取类别当前月子集类别的预算数据
                List<BudgetAvailableResponse.DynamicBudgetAv> values = typeMonthAvMap.get(monthEnum.getMonth());

                BudgetAvailableResponse.DynamicBudgetAv av = BudgetAvailableResponse.DynamicBudgetAv.of();
                av.setBudgetMonth(monthEnum.getMonth());
                dynamicList.add(av);
                av.setParentBudgetId(0L);

                // 如果有子集，就用所有子集进行汇总计算父级
                if (CollectionUtils.isNotEmpty(values)) {
                    av.setAvailableAmount(values.stream().map(BudgetAvailableResponse.DynamicBudgetAv::getAvailableAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
                    av.setBudgetAmount(values.stream().map(BudgetAvailableResponse.DynamicBudgetAv::getBudgetAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
                    av.setPreviewAmount(values.stream().map(BudgetAvailableResponse.DynamicBudgetAv::getPreviewAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
                    av.setUsedAmount(values.stream().map(BudgetAvailableResponse.DynamicBudgetAv::getUsedAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
                    av.setAddAmount(values.stream().map(BudgetAvailableResponse.DynamicBudgetAv::getAddAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
                    av.setTotalAmount(values.stream().map(BudgetAvailableResponse.DynamicBudgetAv::getTotalAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
                    // av.setCount(values.stream().mapToLong(BudgetAvailableResponse.DynamicBudgetAv::getCount).sum());
                    av.setCanMerge(Boolean.FALSE);
                } else {
                    // 当前类别没有子集，就用自己的预算信息构建可用预算
                    List<Budget> budgets = budgetMap.get(budgetType.getTypeId());
                    // 如果这个类别下没有预算数据 直接生成默认数据（金额填充为0）
                    if (CollectionUtils.isEmpty(budgets)) {
                        BudgetAvailableResponse.DynamicBudgetAv dynamicBudgetAv = BudgetAvailableResponse.DynamicBudgetAv.of();
                        dynamicBudgetAv.setBudgetMonth(monthEnum.getMonth());
                        dynamicList.add(dynamicBudgetAv);
                        continue;
                    }

                    // 初始预算额度 = 这些部门这个月的预算额度之和
                    av.setBudgetAmount(budgets.stream().map(bud -> {
                        try {
                            return MonthEnums.getAnyMonthBudget(bud, monthEnum);
                        } catch (NoSuchFieldException | IllegalAccessException e) {
                            throw new RuntimeException(e);
                        }
                    }).reduce(BigDecimal.ZERO, BigDecimal::add));

                    List<BudgetFlow> flows = flowMap.getOrDefault(budgetType.getTypeId() + "-" + monthEnum.getMonth(), List.of());
                    // 已用预算额度 = flow中这个类别这个月 状态为占用的总和
                    av.setUsedAmount(flows.stream()
                            .filter(item -> TransactionTypeEnums.isAnyType(item.getTransactionType(), TransactionTypeEnums.USE))
                            .map(BudgetFlow::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add));

                    // 预占用额度 = flow中这个类别这个月 状态为预占的总和
                    av.setPreviewAmount(flows.stream()
                            .filter(item -> TransactionTypeEnums.isAnyType(item.getTransactionType(), TransactionTypeEnums.PRE_USE))
                            .map(BudgetFlow::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add));

                    // 执行笔数
                    // av.setCount((long) flows.stream().map(BudgetFlow::getOaNo).collect(Collectors.toSet()).size());

                    // 追加额度 = 追加表中 这个类别 这个月追加的总额
                    av.setAddAmount(addMap.getOrDefault(budgetType.getTypeId() + "-" + monthEnum.getMonth(), List.of())
                            .stream().map(BudgetAdd::getAddAmount).reduce(BigDecimal.ZERO, BigDecimal::add));

                    // 总预算额度 = 初始预算额度 + 追加额度
                    av.setTotalAmount(av.getBudgetAmount().add(av.getAddAmount()));

                    // 可用预算 = 总预算额度 - 已用预算 - 预占用预算
                    av.setAvailableAmount(av.getTotalAmount().subtract(av.getUsedAmount()).subtract(av.getPreviewAmount()));
                }
            }
            // 合并月度信息
            BudgetAvailableResponse.DynamicBudgetAv merge = BudgetAvailableResponse.DynamicBudgetAv.of();
            merge.setBudgetMonth(0);

            for (BudgetAvailableResponse.DynamicBudgetAv dynamicBudgetAv : dynamicList) {
                merge.setAvailableAmount(merge.getAvailableAmount().add(dynamicBudgetAv.getAvailableAmount()));
                merge.setPreviewAmount(merge.getPreviewAmount().add(dynamicBudgetAv.getPreviewAmount()));
                merge.setUsedAmount(merge.getUsedAmount().add(dynamicBudgetAv.getUsedAmount()));
                merge.setBudgetAmount(merge.getBudgetAmount().add(dynamicBudgetAv.getBudgetAmount()));
                merge.setAddAmount(merge.getAddAmount().add(dynamicBudgetAv.getAddAmount()));
                merge.setTotalAmount(merge.getTotalAmount().add(dynamicBudgetAv.getTotalAmount()));
                // merge.setCount(merge.getCount() + dynamicBudgetAv.getCount());
                merge.setCanMerge(Boolean.FALSE);
            }
            response.setMergerDynamicBudgetAv(merge);
            budgetAvailableResponses.add(response);

        }

		// 如果按照类型查询 到这里就结束了，只能查询末级
	    if (searchByType) {
		    return Response.success(budgetAvailableResponses);
	    }
        return Response.success(budgetAvailableResponses.stream().sorted(Comparator.comparing(res -> res.getBudgetTypeResponse().getTypeCode())).collect(Collectors.toList()));
    }

	/**
	 * oa穿透预算
	 */
	@Override
	public Response<BudgetAvResponseOA> searchBudgetAvailableOA(BudgetAvailableListSearchRequest request) {
		int month = DateUtil.month(new Date()) + 1;
		BudgetAvResponseOA budgetAvResponseOA = new BudgetAvResponseOA();
		BudgetAvResponseOA.BudgetAvSum dept= BudgetAvResponseOA.BudgetAvSum.init();
		BudgetAvResponseOA.BudgetAvSum type = BudgetAvResponseOA.BudgetAvSum.init();
		budgetAvResponseOA.setDeptBudgetAvSum(dept);
		budgetAvResponseOA.setTypeBudgetAvSum(type);

		if (Objects.isNull(request.getOaBudgetUnitId()) || CollectionUtils.isEmpty(request.getBudgetDeptId())) {
			return Response.success(budgetAvResponseOA);
		}
		if (CollectionUtils.isEmpty(request.getOaFeeTypeIds())) {
			return Response.fail(422, "请选择费用类别");
		}
		// oa费用类别  目前只有一个
		Long oaFeeTypeId = request.getOaFeeTypeIds().iterator().next();
		// 查询对照关系
		BudgetFeeTypeMapping budgetFeeTypeMapping = budgetFeeTypeMappingRepository.searchMappingByOaFeeTypeId(oaFeeTypeId);
		if (Objects.isNull(budgetFeeTypeMapping)) {
			return Response.success(budgetAvResponseOA);
		}

		request.setOaFeeTypeIds(null);
		Response<List<BudgetAvailableResponse>> response = searchBudgetAvailable(request);
		List<BudgetAvailableResponse> data = response.getData();
		if (CollectionUtils.isEmpty(data)) {
			return Response.success();
		}

		BudgetAvailableResponse typeAv = data.stream()
				.filter(item -> item.getBudgetTypeResponse().getTypeId().equals(budgetFeeTypeMapping.getBudgetTypeId()))
				.findAny().orElseThrow(() -> new BizException("没有查询到对应的预算信息"));

		List<BudgetAvailableResponse.DynamicBudgetAv> typeMonthAvList = typeAv.getDynamicBudgetList().stream().filter(item -> item.getBudgetMonth() <= month)
				.collect(Collectors.toList());

		type.setTotalBudgetAmount(typeAv.getMergerDynamicBudgetAv().getTotalAmount());
		type.setNowMonthBudgetAmount(typeMonthAvList.stream().map(BudgetAvailableResponse.DynamicBudgetAv::getBudgetAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
		type.setTotalUseAmount(typeAv.getMergerDynamicBudgetAv().getUsedAmount().add(typeAv.getMergerDynamicBudgetAv().getPreviewAmount()));
		type.setNowMonthAvAmount(type.getNowMonthBudgetAmount().subtract(type.getTotalUseAmount()));
		type.setTotalMonthAvAmount(type.getTotalMonthAvAmount().subtract(type.getTotalUseAmount()));

		// 部门汇总
		dept.setTotalBudgetAmount(data.stream().map(item -> item.getMergerDynamicBudgetAv().getTotalAmount()));
		return Response.success(budgetAvResponseOA);
	}

	private void handlerSearchRequestParam(BudgetAvailableListSearchRequest request) {
		List<Long> oaFeeTypeIds = request.getOaFeeTypeIds();
		if (CollectionUtils.isNotEmpty(oaFeeTypeIds)) {
			Set<Long> budgetTypeIds = budgetFeeTypeMappingRepository.searchMappingByOaFeeTypeIds(oaFeeTypeIds)
					.stream().map(BudgetFeeTypeMapping::getBudgetTypeId).collect(Collectors.toSet());
			request.getBudgetTypeIds().addAll(budgetTypeIds);
		}

		if (StringUtils.isNotBlank(request.getBudgetUnitName())) {
			BudgetUnit budgetUnit = budgetUnitRepository.getBudgetUnitsByBudgetUnitName(request.getBudgetUnitName());
			if (Objects.isNull(budgetUnit)) {
					throw new BizException(String.format("预算单元[%s]不存在", request.getBudgetUnitName()));
			}
			request.setOaBudgetUnitId(budgetUnit.getOaBudgetId());
		}

		if (StringUtils.isNotBlank(request.getBudgetDeptName())) {
			BudgetDept budgetDept = budgetDeptRepository.searchBudgetDeptByNameAndUnit(request.getBudgetDeptName(), request.getOaBudgetUnitId());
			if (Objects.isNull(budgetDept)) {
				throw new BizException(String.format("预算单元[%s]预算部门[%s]不存在", request.getBudgetUnitName(), request.getBudgetDeptName()));
			}
			request.setBudgetDeptId(List.of(budgetDept.getBudgetDeptId()));
		} else {
	        // 1. 处理部门ID集合（同步操作，后续步骤依赖此结果）
	        List<Long> budgetDeptIds = budgetVersionService.processBudgetDeptIds(request.getBudgetDeptId(), request.getOaBudgetUnitId());
			request.setBudgetDeptId(budgetDeptIds);
		}
	}

}