package com.labway.business.center.finance.controller.v2;

import com.labway.business.center.finance.constants.FinanceConstants;
import com.labway.business.center.finance.dto.v2.SupplierDTO;
import com.labway.business.center.finance.request.v2.SupplierSearchRequest;
import com.labway.business.center.finance.request.v2.SyncSupplierRequest;
import com.labway.business.center.finance.service.v2.SupplierService;
import com.swak.frame.dto.Response;
import lombok.Data;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 供应商
 *
 * <AUTHOR>
 * @version 2023/12/15 15:38
 **/
@RestController
@RequestMapping(FinanceConstants.V2_PREFIX + "/supplier")
public class SupplierController {

    @Resource
    private SupplierService supplierService;

    @GetMapping("/sync-all")
    public Response<Long> syncAllSupplierInfo() {
        return supplierService.syncAllSupplierInfo();
    }

    @GetMapping("/sync")
    public Response<Long> syncSupplierInfo() {
        return supplierService.syncSupplierInfo();
    }

    /**
     * 根据名称或编码同步
     * @param request
     * @return
     */
    @PostMapping("/sync-param")
    public Response<Long> syncSupplierByParams(@RequestBody SyncSupplierRequest request) {
        return supplierService.syncSupplierInfoByParams(request);
    }

    /**
     * 供应商搜索（客商辅助核算）
     * @param request
     * @return
     */
    @PostMapping("/supplier-search")
    public Response<List<SupplierDTO>> searchSupplier(@Valid @RequestBody SupplierSearchRequest request) {
        return supplierService.searchSupplier(request);
    }
}