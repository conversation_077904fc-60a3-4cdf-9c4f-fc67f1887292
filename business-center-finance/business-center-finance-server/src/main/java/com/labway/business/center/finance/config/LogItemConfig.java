package com.labway.business.center.finance.config;

import com.google.common.collect.Maps;
import com.labway.business.center.finance.constants.LogType;

import java.util.Map;

/**
 * 日志
 */
public class LogItemConfig {

    private static Map<String, String> handleMappingMap = Maps.newHashMap();

    static {
        handleMappingMap.put(LogType.DEPARTMENT.getCode(), "mappingLogHandler");
        handleMappingMap.put(LogType.MANAGEMENT_ORGS.getCode(), "mappingManagementLogHandler");
        handleMappingMap.put(LogType.LEDGER_ACCOUNT.getCode(), "ledgerLogHandler");
        handleMappingMap.put(LogType.BANK.getCode(), "bankLogHandler");
        handleMappingMap.put(LogType.CAR_NO.getCode(), "carLogHandler");
        handleMappingMap.put(LogType.PROJECT.getCode(), "projectLogHandler");
        handleMappingMap.put(LogType.ACCOUNTING_BOOK.getCode(), "accountBookLogHandler");
        handleMappingMap.put(LogType.VOUCHER_MAKE.getCode(), "makeVoucherLogHandler");
        handleMappingMap.put(LogType.VOUCHER_NOT.getCode(), "notMakeVoucherLogHandler");
        handleMappingMap.put(LogType.VOUCHER_RESET.getCode(), "resetMakeVoucherLogHandler");
        handleMappingMap.put(LogType.FORM_PAY.getCode(), "formPayLogHandler");
        handleMappingMap.put(LogType.FORM_NOT_PAY.getCode(), "formNotPayLogHandler");
    }

    public static Map<String, String> getHandleMappingMap() {
        return handleMappingMap;
    }
}
