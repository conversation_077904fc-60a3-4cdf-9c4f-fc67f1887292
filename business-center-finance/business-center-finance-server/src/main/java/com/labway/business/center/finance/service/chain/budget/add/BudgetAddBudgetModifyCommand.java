package com.labway.business.center.finance.service.chain.budget.add;

import cn.hutool.core.date.DateUtil;
import com.labway.business.center.finance.constants.FinanceConstants;
import com.labway.business.center.finance.constants.MonthEnums;
import com.labway.business.center.finance.persistence.entity.BudgetUnit;
import com.labway.business.center.finance.persistence.entity.v3.BudgetAvailable;
import com.labway.business.center.finance.persistence.entity.v3.BudgetDept;
import com.labway.business.center.finance.persistence.entity.v3.BudgetType;
import com.labway.business.center.finance.repository.center.v3.BudgetAvailableRepository;
import com.labway.business.center.finance.request.v3.budget.add.BudgetAddRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 已存在预算调整
 *
 * <AUTHOR> Tianhao on 2025/5/15.
 */
@Slf4j
@Component
public class BudgetAddBudgetModifyCommand implements Command {

	@Resource
	private BudgetAvailableRepository budgetAvailableRepository;

	@Override
	public boolean execute(Context c) throws Exception {
		BudgetAddContext context = BudgetAddContext.from(c);
		BudgetAddRequest request = context.getRequest();
		BudgetUnit budgetUnit = context.getBudgetUnit();
		BudgetDept budgetDept = context.getBudgetDept();
		int year = DateUtil.thisYear();

		List<BudgetAvailable> budgetAvailableList = budgetAvailableRepository.searchBudgetAvailableByUnitIdAndDeptIdAndYearAndMonth(budgetUnit.getOaBudgetId(), budgetDept.getBudgetDeptId(), year, context.getAddMonth());
		if (CollectionUtils.isEmpty(budgetAvailableList)) {
			// 没有启用的预算 不需要处理
			return CONTINUE_PROCESSING;
		}

		Map<String, List<BudgetAddRequest.BudgetAddDetailRequest>> detailMap = request.getDetails().stream()
				.collect(Collectors.groupingBy(item -> item.getTypeId() + "-" + MonthEnums.getMonthByMonthName(item.getAddMonth()).getMonth()));

		List<BudgetAvailable> updateAvs = new ArrayList<>(budgetAvailableList.size());

		// 统计哪些类别是单独控制的，这部分金额不累加到月度预算中
		Set<Long> typeIds = budgetAvailableList.stream().map(BudgetAvailable::getBudgetTypeId).filter(budgetTypeId -> !Objects.equals(budgetTypeId, FinanceConstants.COMMON_ID)).collect(Collectors.toSet());

		for (BudgetAvailable budgetAvailable : budgetAvailableList) {
			BudgetAvailable update = new BudgetAvailable();
			update.setAvailableId(budgetAvailable.getAvailableId());
			if (Objects.isNull(budgetAvailable.getBudgetTypeId()) || Objects.equals(budgetAvailable.getBudgetTypeId(), FinanceConstants.COMMON_ID)) {
				update.setAddAmount(budgetAvailable.getAddAmount().add(
						detailMap.values().stream().flatMap(List::stream)
								// 过滤出当前月度的
								.filter(item -> MonthEnums.getMonthByMonthName(item.getAddMonth()).getMonth().equals(budgetAvailable.getBudgetMonth()))
								// 过滤出不是受控类型的
								.filter(item -> !typeIds.contains(item.getTypeId()))
								// 汇总金额
								.map(BudgetAddRequest.BudgetAddDetailRequest::getAddAmount).reduce(BigDecimal.ZERO, BigDecimal::add)
				));
			} else {
				// 匹配类别
				if (!detailMap.containsKey(budgetAvailable.getBudgetTypeId() + "-" + budgetAvailable.getBudgetMonth())) {
					continue;
				}
				BigDecimal reduce = detailMap.get(budgetAvailable.getBudgetTypeId() + "-" + budgetAvailable.getBudgetMonth()).stream().map(BudgetAddRequest.BudgetAddDetailRequest::getAddAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
				update.setAddAmount(budgetAvailable.getAddAmount().add(reduce));
				// 匹配到类别
			}
			update.setTotalAmount(update.getAddAmount().add(budgetAvailable.getBudgetAmount()));
			updateAvs.add(update);
		}

		budgetAvailableRepository.updateBudgetAvailableBatch(updateAvs);
		context.put(BudgetAddContext.UPDATE_BUDGET_AV, updateAvs);

		return CONTINUE_PROCESSING;
	}
}
