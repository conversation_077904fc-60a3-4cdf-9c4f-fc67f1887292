package com.labway.business.center.finance.repository.center;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.labway.business.center.finance.persistence.entity.OaNccOrgMapping;
import com.labway.business.center.finance.persistence.mapper.OaNccOrgMappingMapper;
import com.labway.business.center.finance.request.OaNccOrgMappingRequest;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * OaNccOrgMappingRepository 预算单元对照持久层
 *
 * <AUTHOR>
 * @version 2023/04/14 15:41
 **/
@Repository
public class OaNccOrgMappingRepository {
    @Resource
    private OaNccOrgMappingMapper oaNccOrgMappingMapper;
    
    /**
     * 新增映射关系
     * @param oaNccOrgMapping
     * @return
     */
    public int insertOaNccOrgMapping(OaNccOrgMapping oaNccOrgMapping) {
        return oaNccOrgMappingMapper.insert(oaNccOrgMapping);
    }
    
    /**
     * 删除映射关系
     * @param orgId
     * @param nccCode
     * @param oaOrgCode
     * @return
     */
    public int deleteOaNccOrgMapping(String orgId,String nccCode,String oaOrgCode) {
        LambdaQueryWrapper<OaNccOrgMapping> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OaNccOrgMapping::getOrgId,orgId)
                .eq(OaNccOrgMapping::getNccCode,nccCode)
                .eq(OaNccOrgMapping::getOaOrgCode,oaOrgCode);
        return oaNccOrgMappingMapper.delete(queryWrapper);
    }
    
    /**
     * 根据业务单元查询对照列表
     * @param orgId
     * @return
     */
    public List<OaNccOrgMapping> getMappingList(String orgId) {
        LambdaQueryWrapper<OaNccOrgMapping> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OaNccOrgMapping::getOrgId,orgId);
        return oaNccOrgMappingMapper.selectList(queryWrapper);
    }
    
    /**
     * 判断当前预算单元是否已经对照
     * @param orgId
     * @param nccCode
     * @return
     */
    public OaNccOrgMapping checkExist(String orgId,String nccCode) {
        LambdaQueryWrapper<OaNccOrgMapping> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OaNccOrgMapping::getOrgId,orgId)
                .eq(OaNccOrgMapping::getNccCode,nccCode);
        return oaNccOrgMappingMapper.selectOne(queryWrapper);
    }
    

    public OaNccOrgMapping getMappingByOaBudgetIdAndOrgId(String orgId,String budgetId) {
        LambdaQueryWrapper<OaNccOrgMapping> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OaNccOrgMapping::getOrgId,orgId)
                .eq(OaNccOrgMapping::getOaOrgName,budgetId);
        return oaNccOrgMappingMapper.selectOne(queryWrapper);
    }

    public int deleteByOaOrgCodeAndOrgId(String oaOrgCode, String orgId) {
        LambdaQueryWrapper<OaNccOrgMapping> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OaNccOrgMapping::getOaOrgCode,oaOrgCode).eq(OaNccOrgMapping::getOrgId,orgId);
        return oaNccOrgMappingMapper.delete(queryWrapper);
    }
}