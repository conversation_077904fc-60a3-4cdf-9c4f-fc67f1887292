package com.labway.business.center.finance.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.labway.business.center.core.enums.Constants;
import com.labway.business.center.core.enums.ResultCode;
import com.labway.business.center.core.enums.SyncDataFrom;
import com.labway.business.center.core.log.SyncLog;
import com.labway.business.center.finance.converter.OaDeptConverter;
import com.labway.business.center.finance.converter.PageConverter;
import com.labway.business.center.finance.dto.DeptTypeDto;
import com.labway.business.center.finance.dto.OaDeptDTO;
import com.labway.business.center.finance.persistence.entity.DeptMappingName;
import com.labway.business.center.finance.persistence.entity.DeptTypeMapping;
import com.labway.business.center.finance.persistence.entity.OaDept;
import com.labway.business.center.finance.repository.center.DeptMappingRepository;
import com.labway.business.center.finance.repository.center.DeptTypeMappingRepository;
import com.labway.business.center.finance.repository.center.OaDeptRepository;
import com.labway.business.center.finance.request.DeptMappingSelectRequest;
import com.labway.business.center.finance.request.DeptTypeMappingRequest;
import com.labway.business.center.finance.request.OaDeptRequest;
import com.labway.business.center.finance.service.OaDeptService;
import com.labway.business.center.mdm.api.user.param.DepartmentVo;
import com.labway.business.center.mdm.api.user.param.RequestVo;
import com.labway.business.center.mdm.api.user.service.DeptServerBaseService;
import com.swak.frame.dto.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * OaDeptServiceImpl 从主数据获取OA部门数据实现类
 *
 * <AUTHOR>
 * @version 2023/03/27 11:21
 **/
@Slf4j
@DubboService
public class OaDeptServiceImpl implements OaDeptService {
    @Resource
    private OaDeptConverter oaDeptConverter;
    
    @Resource
    private PageConverter pageConverter;
    
    @Resource
    private OaDeptRepository oaDeptRepository;
    
    @Resource
    private DeptMappingRepository deptMappingRepository;
    
    @Resource
    private DeptTypeMappingRepository deptTypeMappingRepository;
    
    @Value("${dept.type}")
    private Object deptType;

    @DubboReference
    private DeptServerBaseService deptServerBaseService;
    
    /**
     * 从主数据 获取 oa所有的部门数据
     *
     * @return OA所有的部门信息
     */
    @Override
    @SyncLog(dataFrom = SyncDataFrom.OA,taskName = Constants.SYNC_TASK_NAME_OA_DEPT)
    public Response<?> syncAllOaDeptByMdm() {
        log.info("调用主数据系统，全量获取OA系统中所有的部门信息");
        try {
            RequestVo requestVo = new RequestVo();
            requestVo.setCurrent(1);
            requestVo.setSize(1000);
            Response<List<DepartmentVo>> deptList = deptServerBaseService.getDeptList(requestVo);

            List<OaDeptDTO> oaDeptList = convertToOaDeptDto(deptList.getData());
            // 数据入库
            Response<Integer> insertOaDeptBatch = insertOaDeptBatch(oaDeptList);
            if (!insertOaDeptBatch.isSuccess()) {
                return insertOaDeptBatch;
            }
            return Response.success();
        } catch (Exception e) {
            log.error("请求主数据发生异常", e);
        }
        return Response.fail(ResultCode.MDM_DEPT_REQUEST_FAIL);
    }
    
    /**
     * 批量插入部门
     *
     * @param oaDeptDTOList 部门dto对象
     * @return 成功的条数
     */
    @SyncLog(dataFrom = SyncDataFrom.OA,taskName = Constants.SYNC_TASK_NAME_OA_DEPT)
    @Override
    public Response<Integer> insertOaDeptBatch(List<OaDeptDTO> oaDeptDTOList) {
        if (CollectionUtils.isEmpty(oaDeptDTOList)) {
            return Response.success();
        }
        Integer row = oaDeptRepository.insertOaDeptBatch(oaDeptDTOList);
        return row > 0 ? Response.success() : Response.fail(ResultCode.INTERNAL_SERVER_ERROR);
    }
    
    /**
     * 从主数据 增量同步 oa所有的部门数据
     *
     * @return OA所有的部门信息
     */
    @Override
    public Response<?> syncOaDeptByMdm() {
        log.info("调用主数据系统，全量获取OA系统中所有的部门信息");
        // 当前时间前一天
        DateTime oneHourBefore = DateUtil.offsetDay(DateUtil.date(), -1);
        try{
            RequestVo requestVo = new RequestVo();
            requestVo.setCurrent(1);
            requestVo.setSize(1000);
            requestVo.setUpdateTime(oneHourBefore.toStringDefaultTimeZone());
            Response<List<DepartmentVo>> deptList = deptServerBaseService.getDeptList(requestVo);
            List<OaDeptDTO> oaDeptList = convertToOaDeptDto(deptList.getData());
            // 增量处理
            Response<Integer> incrementOaDept = incrementOaDept(oaDeptList);
            if (!incrementOaDept.isSuccess()) {
                return incrementOaDept;
            }
            return Response.success(incrementOaDept.getData());
        } catch (Exception e) {
            log.error("请求主数据发生异常", e);
        }
        return Response.fail(ResultCode.MDM_DEPT_REQUEST_FAIL);
    }
    
    /**
     * 分页查询
     *
     * @param oaDeptRequest 查询部门信息
     * @return 部门信息
     */
    @Override
    public Response<?> selectOaDeptPage(OaDeptRequest oaDeptRequest) {
        Page<OaDept> page = new Page<>(oaDeptRequest.getPager().getPage(), oaDeptRequest.getPager().getPageSize());
        Page<OaDept> oaDeptPage = oaDeptRepository.selectPage(page,oaDeptRequest.getDeptName());
        Page<OaDeptDTO> oaDeptDTOPage = pageConverter.covertPage2DTO(oaDeptPage);
        return Response.success(oaDeptDTOPage);
    }
    
    /**
     * 根据名称模糊查询
     *
     * @param oaDeptRequest 模糊查询
     * @return oa部门信息
     */
    @Override
    public Response<List<OaDeptDTO>> selectOaDeptList(OaDeptRequest oaDeptRequest) {
        List<OaDept> oaDepts = oaDeptRepository.selectList(oaDeptRequest.getDeptName());
        List<OaDeptDTO> list = oaDepts.stream().map(dept -> oaDeptConverter.conertOaDept2Dto(dept)).collect(Collectors.toList());
        // 查询oa部门对照的ncc部门名称
        List<DeptMappingName> mappingNameList = deptMappingRepository.selectMappingNccDeptByOaDeptIds(oaDeptRequest.getOrgId());
        mappingNameList = mappingNameList.stream().distinct().collect(Collectors.toList());
        Map<String, String> mappingNameMap = mappingNameList.stream().collect(Collectors.toMap(DeptMappingName::getOaDeptId, DeptMappingName::getNccDeptName));
        Map<String, String> mappingIdMap = mappingNameList.stream().collect(Collectors.toMap(DeptMappingName::getOaDeptId, DeptMappingName::getNccDeptId));
        list.forEach(item ->{
            item.setLinkedNccName(mappingNameMap.get(item.getDeptId()));
            item.setLinkedNccId(mappingIdMap.get(item.getDeptId()));
        });
        return Response.success(list);
    }
    
    
    /**
     * 部门类别对照列表
     *
     * @return 部门类别对照列表
     */
    @Override
    public Response<DeptTypeDto> deptTypeMapping() {
        List<OaDept> oaDepts = oaDeptRepository.selectList(StringUtils.EMPTY);
        List<OaDeptDTO> deptDTOS = oaDepts.stream().map(item -> oaDeptConverter.conertOaDept2Dto(item)).collect(Collectors.toList());
        DeptTypeDto deptTypeDto = new DeptTypeDto();
        deptTypeDto.setOaDeptList(deptDTOS);
        deptTypeDto.setDeptType(deptType);
        return Response.success(deptTypeDto);
    }
    
    /**
     * 部门类别对照操作
     *
     * @param deptTypeMappingRequest 类别对照操作
     * @return 是否成功
     */
    @Override
    public Response<?> deptTypeMappingOperation(DeptTypeMappingRequest deptTypeMappingRequest) {
        
        Integer row = oaDeptRepository.deptTypeMappingOperation(deptTypeMappingRequest.getOaDeptIds(),deptTypeMappingRequest.getCode());
       
        return row > 0 ? Response.success() : Response.fail(ResultCode.INTERNAL_SERVER_ERROR);
    }
    
    /**
     * 根据部门类型编码 和 业务单元 查询 oa部门列表
     *
     * @param deptMappingSelectRequest
     * @return
     */
    @Override
    public Response<List<OaDeptDTO>> getMappingList(DeptMappingSelectRequest deptMappingSelectRequest) {
        // 查询所有的oa部门
        List<OaDept> oaDeptList = oaDeptRepository.selectList(StringUtils.EMPTY);
        // 根据业务单元和部门类别查询绑定的科目
        List<DeptTypeMapping> deptTypeMappings = deptTypeMappingRepository.getMappingListByOrgIdAndDeptTypeCode(deptMappingSelectRequest.getOrgId(),deptMappingSelectRequest.getDeptTypeCode());
        Map<String, String> deptTypeMappingMap = deptTypeMappings.stream().collect(Collectors.toMap(item -> item.getOaDeptId() + item.getOrgId(), DeptTypeMapping::getDeptTypeCode));
        List<OaDeptDTO> oaDeptDTOList = oaDeptList.stream().map(oa -> oaDeptConverter.conertOaDept2Dto(oa)).collect(Collectors.toList());
        oaDeptDTOList.forEach(oa -> {
            String key = oa.getDeptId() + deptMappingSelectRequest.getOrgId();
            if (deptTypeMappingMap.containsKey(key)) {
                oa.setDeptTypeCode(deptTypeMappingMap.get(key));
            }
        });
        return Response.success(oaDeptDTOList);
    }
    
    
    /**
     * 增量更新数据
     * @param oaDeptList
     * @return
     */
    private Response<Integer> incrementOaDept(List<OaDeptDTO> oaDeptList) {
        if (CollectionUtils.isEmpty(oaDeptList)) {
            // 没有数据发生改变 不需要同步
            return Response.success();
        }
        // 查询已有的oa部门id
        List<OaDept> oaDepts = selectAllOaDept();
        List<String> oaDeptIds = oaDepts.stream().map(OaDept::getDeptId).collect(Collectors.toList());
        // 过滤出不存在的数据 新增
        List<OaDeptDTO> insertDeptList = oaDeptList.stream().filter(dept -> !oaDeptIds.contains(dept.getDeptId())).collect(Collectors.toList());
        Response<Integer> insertOaDeptBatch = insertOaDeptBatch(insertDeptList);
        if (!insertOaDeptBatch.isSuccess()) {
            return insertOaDeptBatch;
        }
        // 过滤出存在的数据 批量更新
        List<OaDeptDTO> updateDeptList = oaDeptList.stream().filter(dept -> oaDeptIds.contains(dept.getDeptId())).collect(Collectors.toList());
        Response<Integer> updateOaDeptBatch = updateOaDeptBatch(updateDeptList);
        if (!updateOaDeptBatch.isSuccess()) {
            return insertOaDeptBatch;
        }
        return Response.success(updateOaDeptBatch.getData());
    }
    
    /**
     * 批量更新 oa部门数据
     * @param updateDeptList 更新列表
     * @return 是否成功 影响的行数
     */
    private Response<Integer> updateOaDeptBatch(List<OaDeptDTO> updateDeptList) {
        if (CollectionUtils.isEmpty(updateDeptList)) {
            return Response.success();
        }
        Integer row = oaDeptRepository.updateOaDeptBatch(updateDeptList);
        return row > 0 ? Response.success() : Response.fail(ResultCode.INTERNAL_SERVER_ERROR);
    }
    
    /**
     * 查询已有的所有OA部门
     * @return 已有的所有OA部门
     */
    private List<OaDept> selectAllOaDept() {
        return oaDeptRepository.selectList(StringUtils.EMPTY);
    }

    /**
     * 主数据 请求结果 结构转为 中台 oa部门对象
     * @param array 请求结果
     * @return list 物料信息
     */
    private List<OaDeptDTO> convertToOaDeptDto(List<DepartmentVo> array) {
        if (CollectionUtils.isEmpty(array)) {
            return Collections.emptyList();
        }
        List<OaDeptDTO> list = new ArrayList<>();
        for (int i = 0; i < array.size(); i++) {
            DepartmentVo departmentVo = array.get(i);
            OaDeptDTO oaDeptDTO = new OaDeptDTO();
            oaDeptDTO.setDeptId(departmentVo.getDeptId());
            oaDeptDTO.setParentId(departmentVo.getParentId());
            oaDeptDTO.setName(departmentVo.getDeptName());
            oaDeptDTO.setGroupId(departmentVo.getOrgAccountId());
            oaDeptDTO.setDeptType(departmentVo.getType());
            oaDeptDTO.setStatus(departmentVo.getIsDelete());
            list.add(oaDeptDTO);
        }
        return list;
    }
}