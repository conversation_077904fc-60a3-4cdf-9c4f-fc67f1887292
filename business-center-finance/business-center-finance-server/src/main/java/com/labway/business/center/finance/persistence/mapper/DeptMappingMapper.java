package com.labway.business.center.finance.persistence.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.labway.business.center.core.injector.ExtBaseMapper;
import com.labway.business.center.finance.persistence.entity.DeptMapping;
import com.labway.business.center.finance.persistence.entity.DeptMappingName;

import java.util.List;

/**
 * DeptMappingMapper 部门映射关系 数据库操作
 *
 * <AUTHOR>
 * @version 2023/03/27 16:35
 **/
@DS("master")
public interface DeptMappingMapper extends ExtBaseMapper<DeptMapping> {
    
    /**
     * 查询oa部门对照的ncc部门的名称
     * @return
     */
    List<DeptMappingName> selectMappingNccDeptByOaDeptIds(String orgId);
}