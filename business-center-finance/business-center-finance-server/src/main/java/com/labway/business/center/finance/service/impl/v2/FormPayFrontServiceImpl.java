package com.labway.business.center.finance.service.impl.v2;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.base.Joiner;
import com.labway.business.center.core.config.Pager;
import com.labway.business.center.core.enums.ResultCode;
import com.labway.business.center.core.user.LoginUserInfoUtil;
import com.labway.business.center.core.util.NotifyUtil;
import com.labway.business.center.finance.constants.FinanceConstants;
import com.labway.business.center.finance.constants.FormPayStatus;
import com.labway.business.center.finance.constants.IsCleanByOrgEnum;
import com.labway.business.center.finance.converter.v2.FormPayFrontConverter;
import com.labway.business.center.finance.dto.v2.FormPayDTO;
import com.labway.business.center.finance.dto.v2.FormPayFrontDTO;
import com.labway.business.center.finance.persistence.entity.Bank;
import com.labway.business.center.finance.persistence.entity.OrgsMapping;
import com.labway.business.center.finance.persistence.entity.v2.FormPay;
import com.labway.business.center.finance.persistence.entity.v2.FormPayFront;
import com.labway.business.center.finance.persistence.entity.v2.FormPayFrontRecord;
import com.labway.business.center.finance.persistence.params.SearchLedger;
import com.labway.business.center.finance.repository.center.BankRepository;
import com.labway.business.center.finance.repository.center.v2.FormPayFrontRecordRepository;
import com.labway.business.center.finance.repository.center.v2.FormPayFrontRepository;
import com.labway.business.center.finance.repository.center.v2.FormPayRepository;
import com.labway.business.center.finance.request.CleanExpenseFormRequest;
import com.labway.business.center.finance.request.FrontCancelPushRequest;
import com.labway.business.center.finance.request.QueryFormPayFrontPageRequest;
import com.labway.business.center.finance.request.v2.FormPayFrontPushCheckRequest;
import com.labway.business.center.finance.request.v2.FrontNotPayRequest;
import com.labway.business.center.finance.request.v2.FrontPayBankUpdateRequest;
import com.labway.business.center.finance.service.CleanExpensesFormService;
import com.labway.business.center.finance.service.v2.FormPayFrontService;
import com.swak.frame.dto.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Tianhao
 * @version 2024/02/18 16:00
 **/
@Slf4j
@Service
public class FormPayFrontServiceImpl implements FormPayFrontService {
    @Resource
    private FormPayFrontRepository formPayFrontRepository;

    @Resource
    private FormPayFrontRecordRepository formPayFrontRecordRepository;

    @Resource
    private FormPayRepository formPayRepository;

    @Resource
    private BankRepository bankRepository;

    @Resource
    private FormPayFrontConverter formPayFrontConverter;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private ThreadPoolTaskExecutor notPayWashExecutor;

    @Resource
    private CleanExpensesFormService cleanExpensesFormService;

    @Resource
    private NotifyUtil notifyUtil;


    /**
     * 列表查询
     *
     * @param request
     * @return
     */
    @Override
    public Response<Pager<List<FormPayFrontDTO>>> formPayFrontPage(QueryFormPayFrontPageRequest request) {
        // 分页查询费用单信息
        IPage<FormPayFront> formPayPage = formPayFrontRepository.queryFormPayFrontPage(request);
        List<FormPayFront> records = formPayPage.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return Response.success();
        }
        Pager<List<FormPayFrontDTO>> resultPage = new Pager<>();
        resultPage.setTotal(formPayPage.getTotal());
        resultPage.setPageSize(request.getPageSize());
        resultPage.setPage(request.getPage());
        resultPage.setItem(formPayFrontConverter.convertEntityList2DTO(records));
        return Response.success(resultPage);
    }

    /**
     * 批量修改付款银行（前置审核）
     *
     * @param updateRequest
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Response<String> changeFormBankBatch(FrontPayBankUpdateRequest updateRequest) {
        Bank bank = bankRepository.getBankByBankId(updateRequest.getBankId());
        if (Objects.isNull(bank)) {
            return Response.fail(ResultCode.BATCH_CHANGE_BANK_ERR);
        }
        formPayFrontRepository.changeFormBankBatch(bank, updateRequest.getOaNos(), LoginUserInfoUtil.getUserUserName());
        return Response.success();
    }

    /**
     * 推送至费用审核
     *
     * @param oaNos
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Response<String> pushCheck(FormPayFrontPushCheckRequest formPayFrontPushCheckRequest) {
        List<String> oaNos = formPayFrontPushCheckRequest.getOaNos();
        Boolean isOffline = formPayFrontPushCheckRequest.getIsOffline();
        String remark = formPayFrontPushCheckRequest.getRemark();

        if (CollectionUtils.isEmpty(oaNos)) {
            return Response.fail(ResultCode.PLEASE_SELECT_FORM);
        }
        List<FormPay> formPays = formPayRepository.searchByOaNos(oaNos);
        if (CollectionUtils.isNotEmpty(formPays)) {
            List<String> existOaNos = formPays.stream().map(FormPay::getOaNo).collect(Collectors.toList());
            oaNos = oaNos.stream().filter(item -> !existOaNos.contains(item)).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(oaNos)) {
            return Response.fail(ResultCode.ALL_FORM_EXIST);
        }
        // 获取当前账期
        List<FormPayFront> formPayFronts = formPayFrontRepository.searchByOaNos(oaNos);

        String paymentDays = DateUtil.format(new Date(), DatePattern.SIMPLE_MONTH_PATTERN);
        String orgId = formPayFronts.get(0).getOrgId();
        FormPayFrontRecord frontRecord = formPayFrontRecordRepository.getPaymentDays(orgId, paymentDays);
        Integer pushNumber = frontRecord == null ? 1 : frontRecord.getPushNumber() + 1;


        // 1.修改前置审核的状态
        formPayFrontRepository.changeFormPayStatus(oaNos, FormPayStatus.CHECKING.getStatus(), LoginUserInfoUtil.getUserUserName(), remark);
        // 2.数据清洗至费用审核

        List<FormPay> insertFormPays = formPayFrontConverter.convertEntityList2FormPayList(formPayFronts);
        insertFormPays.forEach(item -> {
            item.setRemark(remark);
            item.setStatus(FormPayStatus.WAIT.getStatus());
            item.setCheckPushNumber(pushNumber);
            // 如果是线下支付
            if (isOffline) {
                item.setIsOffline(Boolean.TRUE);
            }
        });
        formPayRepository.saveBatch(insertFormPays);

        FormPayFrontRecord formPayFrontRecord = new FormPayFrontRecord();
        formPayFrontRecord.setDateRange(paymentDays);
        formPayFrontRecord.setPushTime(LocalDateTime.now());
        formPayFrontRecord.setPushNumber(pushNumber);
        formPayFrontRecord.setOrgId(orgId);
        formPayFrontRecord.setOaNos(String.join(",", oaNos));
        formPayFrontRecord.setPushUser(LoginUserInfoUtil.getUserUserName());
        formPayFrontRecordRepository. saveRecord(formPayFrontRecord);

        List<String> finalOaNos = oaNos;
        new Thread(() -> {
            notifyUtil.send(String.format("单据：【%s】当前状态：【申请付款】目标状态【%s】", Joiner.on(",").join(finalOaNos), "费用审核"));
        }).start();
        return Response.success();
    }

    /**
     * 金额计算
     *
     * @param request
     * @return
     */
    @Override
    public Response<BigDecimal> computeFormPayFront(QueryFormPayFrontPageRequest request) {
        List<FormPayFront> formPaysFront = formPayFrontRepository.queryFormPay(request,false);
        if (CollectionUtils.isEmpty(formPaysFront)) {
            return Response.success(new BigDecimal("0"));
        }

        return Response.success(formPaysFront.stream().map(FormPayFront::getTotalAmount).reduce(BigDecimal.ZERO,BigDecimal::add));
    }

    /**
     * 线下/不付款
     *
     * @param notPayRequest
     * @return
     */
    @Override
    public Response<String> updateNotPay(FrontNotPayRequest notPayRequest) {
        List<String> oaNos = notPayRequest.getOaNos();
        List<FormPayFront> formPayFronts = formPayFrontRepository.searchByOaNosAndStatus(oaNos,FormPayStatus.WAIT.getStatus());
        if (oaNos.size() != formPayFronts.size()) {
            return Response.fail(ResultCode.PLEASE_REFRESH_TRY);
        }
        if (notPayRequest.getIsOffline()) {
            // 线下付款
            formPayFrontRepository.changeFormPayStatus(oaNos, FormPayStatus.OFF_LINE.getStatus(), LoginUserInfoUtil.getUserUserName(), notPayRequest.getRemark());
            washOaNo2Vouchers(oaNos,Boolean.TRUE);
            new Thread(() -> {
                notifyUtil.send(String.format("单据：【%s】当前状态：【申请付款】目标状态【费用审核】", Joiner.on(",").join(oaNos)));
            }).start();
        } else {
            formPayFrontRepository.changeFormPayStatus(oaNos, FormPayStatus.NOT_PAY.getStatus(), LoginUserInfoUtil.getUserUserName(), notPayRequest.getRemark());
            new Thread(() -> {
                notifyUtil.send(String.format("单据：【%s】当前状态：【申请付款】目标状态【申请付款-不付款(流程终止)】", Joiner.on(",").join(oaNos)));
            }).start();
        }
        return Response.success();
    }

    /**
     * 查询本次推送次数
     *
     * @param orgId
     * @return
     */
    @Override
    public Response<Integer> getPaymentDays(String orgId) {
        if (StringUtils.isBlank(orgId)) {
            return Response.fail(ResultCode.PARAMS_NOT_VALIDATE);
        }
        Integer pushNumber = 1;
        // 获取当前账期
        String paymentDays = DateUtil.format(new Date(), DatePattern.SIMPLE_MONTH_PATTERN);
        FormPayFrontRecord formPayFrontRecord = formPayFrontRecordRepository.getPaymentDays(orgId,paymentDays);
        if (Objects.isNull(formPayFrontRecord)) {
            return Response.success(pushNumber);
        }
        Integer number = formPayFrontRecord.getPushNumber();
        if (number > pushNumber) {
            pushNumber += number;
        }
        return Response.success(pushNumber);
    }

    /**
     * 撤销推送
     *
     * @param frontCancelPushRequest
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Response<String> cancelPush(FrontCancelPushRequest frontCancelPushRequest) {
        List<String> oaNos = frontCancelPushRequest.getOaNos();
        List<FormPay> formPays = formPayRepository.searchByOaNos(oaNos);
        StringBuilder sb = new StringBuilder();
        if (CollectionUtils.isEmpty(formPays)) {
            return Response.fail(ResultCode.NOT_FIND_FORM);
        }
        List<String> updateFormPayNos = new ArrayList<>();
        formPays.forEach(item -> {
            if (FormPayStatus.isStatus(item.getStatus(), FormPayStatus.WAIT)) {
                // 如果状态符合 添加到 待修改
                updateFormPayNos.add(item.getOaNo());
            } else {
                // 状态不允许撤回
                sb.append(item.getOaNo()).append("目前状态是:").append(FormPayStatus.getStatusName(item.getStatus())).append("/n");
            }
        });
        String errorInfo = sb.toString();
        if (StringUtils.isNotBlank(errorInfo)) {
            return Response.fail(500,errorInfo);
        }
        // 撤销数据
        formPayRepository.removePushForm(updateFormPayNos);
        // 改变状态
        formPayFrontRepository.changeFormPayStatus(updateFormPayNos, FormPayStatus.WAIT.getStatus(), null, frontCancelPushRequest.getRemark());
        return Response.success();
    }

    public void washOaNo2Vouchers(List<String> oaNos, Boolean isContinue) {
        if (CollectionUtils.isEmpty(oaNos)) {
            return;
        }
        if (isContinue) {
            stringRedisTemplate.opsForValue().set(FinanceConstants.WASH_CONTINUE_KEY, JSONUtil.toJsonStr(oaNos));
        }

        notPayWashExecutor.execute(() -> {
            CleanExpenseFormRequest cleanExpenseFormRequest = new CleanExpenseFormRequest();
            cleanExpenseFormRequest.setWashOaNos(oaNos);
            cleanExpenseFormRequest.setIsCleanByOrgEnum(IsCleanByOrgEnum.IGNORE);
            // 数据清洗 根据流程编号
            Response<String> travelVouchersResponse = cleanExpensesFormService.washCleanTravelExpenseForm2DatabaseByNos(cleanExpenseFormRequest);
            Response<String> expensesClaimsResponse = cleanExpensesFormService.washCleanExpensesClaimsForm2DatabaseByNos(cleanExpenseFormRequest);
            Response<String> washRemittanceResponse = cleanExpensesFormService.washRemittanceForm2DatabaseByNos(cleanExpenseFormRequest);
            Response<String> washSupplyChainResponse = cleanExpensesFormService.washCleanSupplyChainForm2DatabaseByNos(cleanExpenseFormRequest);
        });
    }
}