package com.labway.business.center.finance.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * 联行号查询 的 配置类
 * <AUTHOR>
 * @version 2023/11/23 10:00
 **/
@Data
@RefreshScope
@Configuration
@ConfigurationProperties(prefix = "cnaps")
public class CnapsConfig {

    /**
     * 地址
     */
    private String url;

    private String appid;

    private String sign;
}