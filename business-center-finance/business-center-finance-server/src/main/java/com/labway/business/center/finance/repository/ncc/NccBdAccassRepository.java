package com.labway.business.center.finance.repository.ncc;

import java.util.List;

import javax.annotation.Resource;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import com.labway.business.center.core.config.Pager;
import com.labway.business.center.finance.persistence.entity.ncc.NccBdAccass;
import com.labway.business.center.finance.persistence.mapper.ncc.NccBdAccassMapper;

/**
 * NCC 会辅助核算信息资源类
 * 
 * <AUTHOR>
 * @date 2023/03/21
 */
@Repository
public class NccBdAccassRepository {

    @Resource
    private NccBdAccassMapper nccBdAccassMapper;

    /**
     * 分页加载NCC辅助核算信息
     * 
     * @param pager
     * @return
     */
    public List<NccBdAccass> getNccBdAccassByPager(@Param("pager") Pager<String> pager) {
        return nccBdAccassMapper.getNccBdAccassByPager(pager);
    }

    public Long countNccBdAccass() {
        return nccBdAccassMapper.countNccBdAccass();
    }
    
    public List<NccBdAccass> getNccBdAccassByIds(List<String> accassIds) {
        return nccBdAccassMapper.getNccBdAccassByAccassIds(accassIds);
    }
}
