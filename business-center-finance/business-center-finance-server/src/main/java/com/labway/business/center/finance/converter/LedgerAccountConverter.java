 package com.labway.business.center.finance.converter;

import com.labway.business.center.finance.dto.AccchartTypeDTO;
import org.mapstruct.Mapper;

import com.labway.business.center.finance.dto.LedgerAccountDTO;
import com.labway.business.center.finance.persistence.entity.LedgerAccount;

/**
 * 会计科目 转换器
 * <AUTHOR>
 * @date 2023/03/24
 */
@Mapper(componentModel = "spring")
public interface LedgerAccountConverter {
     
    /**
     * 转换会计科目
     * @param account
     * @return
     */
     LedgerAccountDTO covertLedgerAccount2DTO(LedgerAccount account);
    
    /**
     * 转为科目表
     * @param account
     * @return
     */
     AccchartTypeDTO converLedgerAccount2AccchartTypeDTO(LedgerAccount account);

}
