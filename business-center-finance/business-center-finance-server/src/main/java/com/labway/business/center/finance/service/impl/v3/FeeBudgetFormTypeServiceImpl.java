package com.labway.business.center.finance.service.impl.v3;

import com.labway.business.center.finance.converter.v3.FeeBudgetFormTypeConverter;
import com.labway.business.center.finance.persistence.entity.v3.FeeBudgetFormType;
import com.labway.business.center.finance.repository.center.v3.FeeBudgetFormTypeRepository;
import com.labway.business.center.finance.request.v3.feeForm.OperationFeeBudgetFormTypeRequest;
import com.labway.business.center.finance.response.v3.feeForm.FeeBudgetFormTypeResponse;
import com.labway.business.center.finance.service.SnowflakeService;
import com.labway.business.center.finance.service.v3.FeeBudgetFormTypeService;
import com.swak.frame.dto.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 费用类型的预算表单
 *
 * <AUTHOR> on 2025/4/15.
 */
@Slf4j
@Service
public class FeeBudgetFormTypeServiceImpl implements FeeBudgetFormTypeService {

	@Resource
	private FeeBudgetFormTypeRepository feeBudgetFormTypeRepository;

	@Resource
	private FeeBudgetFormTypeConverter feeBudgetFormTypeConverter;

	@Resource
	private SnowflakeService snowflakeService;

	/**
	 * 查询所有的类型
	 */
	@Override
	public Response<List<FeeBudgetFormTypeResponse>> getAll() {
		List<FeeBudgetFormType> feeBudgetFormTypes = feeBudgetFormTypeRepository.searchAll();
		if (CollectionUtils.isEmpty(feeBudgetFormTypes)) {
			return Response.success(Collections.emptyList());
		}
		return Response.success(feeBudgetFormTypeConverter.convertEntityList2ResponseList(feeBudgetFormTypes));
	}

	/**
	 * 新增类型
	 */
	@Override
	public Response<String> insert(OperationFeeBudgetFormTypeRequest request) {
		FeeBudgetFormType feeBudgetFormType = feeBudgetFormTypeConverter.convertOperationRequest2Entity(request);
		if (Objects.isNull(feeBudgetFormType)) {
			return Response.fail(500, "新增失败");
		}
		feeBudgetFormType.setFormTypeId(snowflakeService.genId());
		feeBudgetFormTypeRepository.insertFeeBudgetFormType(feeBudgetFormType);
		return Response.success();
	}

	/**
	 * 修改类型
	 */
	@Override
	public Response<String> update(OperationFeeBudgetFormTypeRequest request) {
		FeeBudgetFormType feeBudgetFormType = feeBudgetFormTypeConverter.convertOperationRequest2Entity(request);
		if (Objects.isNull(feeBudgetFormType)) {
			return Response.fail(500, "修改失败");
		}
		feeBudgetFormTypeRepository.updateFeeBudgetFormType(feeBudgetFormType);
		return Response.success();
	}

	/**
	 * 删除类型
	 *
	 */
	@Override
	public Response<String> delete(Long oaFeeTypeId) {
		if (Objects.isNull(oaFeeTypeId)) {
			return Response.fail(500, "删除失败");
		}
		feeBudgetFormTypeRepository.deleteFeeBudgetFormType(oaFeeTypeId);
		return Response.success();
	}
}
