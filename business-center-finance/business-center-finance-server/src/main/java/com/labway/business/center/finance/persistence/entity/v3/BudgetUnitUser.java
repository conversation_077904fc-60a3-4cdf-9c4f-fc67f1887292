package com.labway.business.center.finance.persistence.entity.v3;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.labway.business.center.finance.persistence.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户预算单元权限表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tb_budget_unit_user")
public class BudgetUnitUser extends BaseEntity {

    /**
     * 权限id
     */
    @TableId(value = "use_unit_id", type = IdType.ASSIGN_ID)
    private Long useUnitId;

    /**
     * 预算单元id
     */
    @TableField("unit_id")
    private Long unitId;

    /**
     * 预算部门名称
     */
    @TableField("unit_name")
    private String unitName;

    /**
     * 用户id
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 用户名
     */
    @TableField("user_name")
    private String userName;

    /**
     * 用户性别：1：男,2：女，-1：未定义
     */
    @TableField("sex")
    private Integer sex;

    /**
     * 部门名称
     */
    @TableField("dept_name")
    private String deptName;
}