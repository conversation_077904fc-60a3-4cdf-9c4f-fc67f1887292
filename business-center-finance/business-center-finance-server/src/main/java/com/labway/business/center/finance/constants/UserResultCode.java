package com.labway.business.center.finance.constants;

import com.swak.frame.enums.IResultCode;


public enum UserResultCode implements IResultCode {
	USER_NOT_FOUND(1404,"用户不存在");
	private Integer code;
	private String msg;
	 UserResultCode(Integer code, String msg) {
		this.code = code;
		this.msg = msg;
	}

	@Override
	public Integer getCode() {
		return code;
	}

	@Override
	public String getMsg() {
		return msg;
	}

}
