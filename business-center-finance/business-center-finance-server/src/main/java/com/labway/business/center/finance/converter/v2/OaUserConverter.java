package com.labway.business.center.finance.converter.v2;

import com.labway.business.center.finance.dto.mdm.OaUserDTO;
import com.labway.business.center.finance.persistence.entity.mdm.OaUser;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2024/01/02 14:05
 **/
@Mapper(componentModel = "spring")
public interface OaUserConverter {
    List<OaUserDTO> convertEntity2DTOList(List<OaUser> oaUsers);
}