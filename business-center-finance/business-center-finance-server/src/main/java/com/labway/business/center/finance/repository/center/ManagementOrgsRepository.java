 package com.labway.business.center.finance.repository.center;

import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.labway.business.center.finance.converter.ManagementOrgsConverter;
import com.labway.business.center.finance.dto.AccchartTypeDTO;
import com.labway.business.center.finance.dto.ManagementOrgsDTO;
import com.labway.business.center.finance.persistence.entity.LedgerAccount;
import com.labway.business.center.finance.request.ManagementOrgsMappingRequest;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.labway.business.center.finance.persistence.entity.ManagementOrgs;
import com.labway.business.center.finance.persistence.mapper.ManagementOrgsMapper;


@Repository
public class ManagementOrgsRepository {
    
    @Resource
    private ManagementOrgsMapper managementOrgsMapper;
    
    @Resource
    private ManagementOrgsConverter managementOrgsConverter;

    public Integer batchInsertManagementOrgs(List<ManagementOrgs> orgs) {
        if (CollectionUtils.isEmpty(orgs)) {
            return 0;
        }
        return managementOrgsMapper.insertBatchSomeColumn(orgs);
    }

    public List<ManagementOrgs> getManagementOrgsByDefdocIds(List<String> ids) {
        LambdaQueryWrapper<ManagementOrgs> queryWrapper = new  LambdaQueryWrapper<>();
        queryWrapper.in(ManagementOrgs::getDefdocId, ids);
        queryWrapper.select(ManagementOrgs::getDefdocId);
        return managementOrgsMapper.selectList(queryWrapper);
    }
    
    
    /**
     * 查询管理组织列表
     * @return 管理组织列表
     */
    public List<ManagementOrgsDTO> selectManagementOrgsList(String managementOrgName) {
        LambdaQueryWrapper<ManagementOrgs> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(ManagementOrgs::getDefdocId,ManagementOrgs::getCode,ManagementOrgs::getName,
                ManagementOrgs::getParentId);
        if (StringUtils.isNotBlank(managementOrgName)) {
            queryWrapper.like(ManagementOrgs::getName,managementOrgName);
        }
        List<ManagementOrgs> managementOrgs = managementOrgsMapper.selectList(queryWrapper);
        return managementOrgs.stream().map(item -> managementOrgsConverter.convertManagementOrgs2DTO(item)).collect(Collectors.toList());
    }
    
    /**
     * 根据主键id查询预算单元
     * @param nccManagementDefdocId 主键id
     * @return
     */
    public ManagementOrgs getManagementOrgsByDefdocId(String nccManagementDefdocId) {
        LambdaQueryWrapper<ManagementOrgs> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ManagementOrgs::getDefdocId,nccManagementDefdocId);
        return managementOrgsMapper.selectOne(queryWrapper);
    }
    
    public ManagementOrgs getManagementOrgsByDefdocCode(String nccManagementDefdocCode) {
        LambdaQueryWrapper<ManagementOrgs> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ManagementOrgs::getCode,nccManagementDefdocCode);
        return managementOrgsMapper.selectOne(queryWrapper);
    }

    public void removeAll() {
        managementOrgsMapper.removeAll();
    }
}
