 package com.labway.business.center.finance.persistence.entity.ncc;

import com.swak.frame.dto.base.Entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * NCC 人员信息
 * <AUTHOR>
 * @date 2023/03/17
 */
@Data
public class NccEmployee extends Entity {
    /**
     * 人员编码
     */
     @ApiModelProperty(value = "人员编码")
     private String psndocId;
     
     /**
      * 部门编码
      */
      @ApiModelProperty(value = "部门编码")
      private String deptId;
      
      /**
       * 部门名称
       */
      @ApiModelProperty(value = "部门名称")
      private String deptName;
     /**
     * 所属组织
     */
     @ApiModelProperty(value = "所属组织")
     private String orgId;
     /**
     * 集团编码
     */
     @ApiModelProperty(value = "集团编码")
     private String groupId;
     /**
     * 编码
     */
     @ApiModelProperty(value = "编码")
     private String code;
     /**
     * 姓名
     */
     @ApiModelProperty(value = "姓名")
     private String name;
     
     /**
      *   1=未启用;  2=已启用;  3=已停用;
      */
      @ApiModelProperty(value = " 1=未启用;  2=已启用;  3=已停用;")
      private Integer status;

}
