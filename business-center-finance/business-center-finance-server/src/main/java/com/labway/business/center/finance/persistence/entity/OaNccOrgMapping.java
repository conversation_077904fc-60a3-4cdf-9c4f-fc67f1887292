package com.labway.business.center.finance.persistence.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * OaNccOrgMapping 业务单元对照实体
 *
 * <AUTHOR>
 * @version 2023/04/14 15:33
 **/
@TableName("tb_oa_ncc_org_mapping")
@ApiModel(value = "业务单元对照实体")
@Data
public class OaNccOrgMapping {
    /**
     * 序列号
     */
    private static final long serialVersionUID = 1L;
    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * oa预算单元code
     */
    @ApiModelProperty(value = "oa预算单元code")
    @TableField(value = "oa_org_code")
    private String oaOrgCode;
    /**
     * oa预算单元名称
     */
    @ApiModelProperty(value = "oa预算单元名称")
    @TableField(value = "oa_org_name")
    private String oaOrgName;
    /**
     * ncc预算单元名称
     */
    @ApiModelProperty(value = "ncc预算单元名称")
    @TableField(value = "ncc_name")
    private String nccName;
    /**
     * ncc预算单元code
     */
    @ApiModelProperty(value = "ncc预算单元code")
    @TableField(value = "ncc_code")
    private String nccCode;
    /**
     * 所属的业务单元
     */
    @ApiModelProperty(value = "业务单元id")
    @TableField(value = "org_id")
    private String orgId;
}