package com.labway.business.center.finance.service.impl.v2;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.labway.business.center.core.enums.SyncDataFrom;
import com.labway.business.center.core.log.SyncLog;
import com.labway.business.center.finance.constants.FormTypeEnums;
import com.labway.business.center.finance.converter.v2.ReconciliationConverter;
import com.labway.business.center.finance.persistence.entity.oa.ExpensesClaimsForm;
import com.labway.business.center.finance.persistence.entity.oa.RemittanceForm;
import com.labway.business.center.finance.persistence.entity.oa.SupplyChainForm;
import com.labway.business.center.finance.persistence.entity.oa.TravelExpenseForm;
import com.labway.business.center.finance.persistence.entity.v2.*;
import com.labway.business.center.finance.repository.center.CbsTransactionDetailRepository;
import com.labway.business.center.finance.repository.center.v2.FinanceBillDiffRepository;
import com.labway.business.center.finance.repository.center.v2.FormPayRepository;
import com.labway.business.center.finance.repository.center.v2.OaBillDiffRepository;
import com.labway.business.center.finance.repository.center.v2.ReconciliationRecordRepository;
import com.labway.business.center.finance.repository.oa.ExpensesClainsFormRepository;
import com.labway.business.center.finance.repository.oa.RemittanceFormRepository;
import com.labway.business.center.finance.repository.oa.SupplyChainFormRepository;
import com.labway.business.center.finance.repository.oa.TravelExpenseFormRepository;
import com.labway.business.center.finance.service.v2.ReconciliationService;
import com.swak.frame.dto.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Tianhao
 * @version 2023/11/14 19:51
 **/
@Slf4j
@DubboService
public class ReconciliationServiceImpl implements ReconciliationService {

    @Resource
    private FormPayRepository formPayRepository;
    @Resource
    private OaBillDiffRepository oaBillDiffRepository;
    @Resource
    private FinanceBillDiffRepository financeBillDiffRepository;
    @Resource
    private ExpensesClainsFormRepository expensesClainsFormRepository;
    @Resource
    private TravelExpenseFormRepository travelExpenseFormRepository;
    @Resource
    private RemittanceFormRepository remittanceFormRepository;
    @Resource
    private SupplyChainFormRepository supplyChainFormRepository;
    @Resource
    private ReconciliationRecordRepository reconciliationRecordRepository;
    @Resource
    private CbsTransactionDetailRepository cbsTransactionDetailRepository;


    @Resource
    private ReconciliationConverter reconciliationConverter;


    /**
     * oa对账
     *
     * @return
     */
    @SyncLog(dataFrom = SyncDataFrom.OA,taskName = "OA对账")
    @Override
    public Response<Long> oaReconciliation() {
        StopWatch stopWatch = new StopWatch("oaReconciliation");
        log.info("开始oa对账：{}", System.currentTimeMillis());
        // 分页参数
        long current = 1;
        long size = 200;
        long total = 0;
        boolean hasNext = false;
        do {
            Page<ReconciliationRecord> page = new Page<>(current, size);
            page = reconciliationRecordRepository.searchReconciliationRecordsByPage(page, true, false);
            List<ReconciliationRecord> records = page.getRecords();
            // 有数据未进行对账
            if (CollectionUtils.isNotEmpty(records)) {
                total += oaReconciliationHandler(records);
            }
            current = page.getCurrent() + 1 ;
            hasNext = page.hasNext();
        } while (hasNext);
        log.info("结束OA对账,{}", stopWatch.prettyPrint());
        return Response.success(total);
    }

    /**
     * cbs对账
     *
     * @return
     */
    @SyncLog(dataFrom = SyncDataFrom.CBS8,taskName = "CBS对账")
    @Override
    public Response<Long> cbsReconciliation() {
        StopWatch stopWatch = new StopWatch("cbsReconciliation");
        log.info("开始cbs对账：{}", System.currentTimeMillis());
        // 分页参数
        long current = 1;
        long size = 200;
        long total = 0;
        boolean hasNext = false;
        do {
            Page<ReconciliationRecord> page = new Page<>(current, size);
            page = reconciliationRecordRepository.searchReconciliationRecordsByPage(page, false, true);
            List<ReconciliationRecord> records = page.getRecords();
            // 有数据未进行对账
            if (CollectionUtils.isNotEmpty(records)) {
                total += cbsReconciliationHandler(records);
            }
            current = page.getCurrent() + 1 ;
            hasNext = page.hasNext();
        } while (hasNext);
        log.info("结束cbs对账,{}", stopWatch.prettyPrint());
        return Response.success(total);
    }

    private Long cbsReconciliationHandler(List<ReconciliationRecord> records) {
        // 本批次的业务标识号
        List<String> serials = records.stream().map(ReconciliationRecord::getSerial).collect(Collectors.toList());
        log.info("本地对账的CBS业务标识：{}",serials);
        if (CollectionUtils.isEmpty(serials)) {
            return 0L;
        }
        // 本地库中查询
        List<FormPay> formPays = formPayRepository.searchListBySerials(serials);

        List<CbsTransactionDetail> cbsTransactionDetails = cbsTransactionDetailRepository.searchListBySerials(serials);
        if (CollectionUtils.isEmpty(cbsTransactionDetails)) {
            log.info("当前对账单据还未获取");
            return 0L;
        }
        Map<String, BigDecimal> amountMap = cbsTransactionDetails.stream().collect(Collectors.toMap(CbsTransactionDetail::getSerial, CbsTransactionDetail::getIncurredAmount));

        List<FinanceBillDiff> financeBillDiffs = Lists.newArrayList();
        for (FormPay formPay : formPays) {
            BigDecimal cbsAmount = amountMap.get(formPay.getSerial());
            if (Objects.isNull(cbsAmount)) {
                continue;
            }
            BigDecimal totalAmount = formPay.getTotalAmount();
            if (cbsAmount.compareTo(totalAmount) != 0) {
                FinanceBillDiff financeBillDiff = reconciliationConverter.convertFormPay2FinanceDiff(formPay);
                financeBillDiffs.add(financeBillDiff);
            }
        }
        if (CollectionUtils.isNotEmpty(financeBillDiffs)) {
            financeBillDiffs.forEach(item -> item.setStatus(0));
            financeBillDiffRepository.saveBatch(financeBillDiffs);
        }
        return reconciliationRecordRepository.updateBatchToCbsInfo(serials, LocalDateTime.now());
    }

    /**
     * oa对账处理
     * @param records
     */
    private Long oaReconciliationHandler(List<ReconciliationRecord> records) {
        // 本批次的OA流程编号
        List<String> oaNos = records.stream().map(ReconciliationRecord::getOaNo).collect(Collectors.toList());
        log.info("本地对账的OA流程编号：{}",oaNos);
        if (CollectionUtils.isEmpty(oaNos)) {
            return 0L;
        }
        // 本地库中查询
        List<FormPay> formPays = formPayRepository.searchListByOaNos(oaNos);
        Map<String, List<FormPay>> templateMap = formPays.stream().collect(Collectors.groupingBy(FormPay::getTemplate));
        Map<String,BigDecimal> resultMap = new HashMap<>();

        List<FormPay> formPays301 = templateMap.get(FormTypeEnums.EXPENTSE_FORM.getCode());

        List<FormPay> formPays302 = templateMap.get(FormTypeEnums.TRAVEL_FORM.getCode());

        List<FormPay> formPays303 = templateMap.get(FormTypeEnums.REMITTANCE_FROM.getCode());

        List<FormPay> formPays304 = templateMap.get(FormTypeEnums.SUPPLY_CHAIN_FROM.getCode());

        if (CollectionUtils.isNotEmpty(formPays301)) {
            List<String> oaNosWith301 = formPays301.stream().map(FormPay::getOaNo).collect(Collectors.toList());
            List<ExpensesClaimsForm> expensesClaimsForms = expensesClainsFormRepository.getExpensesClaimsFormsByOaNos(oaNosWith301);
            Map<String, BigDecimal> formMapWith301 = expensesClaimsForms.stream().collect(Collectors.toMap(ExpensesClaimsForm::getOaNo, ExpensesClaimsForm::getTotalAmount));
            resultMap.putAll(formMapWith301);
        }

        if (CollectionUtils.isNotEmpty(formPays302)) {
            List<String> oaNosWith302 = formPays302.stream().map(FormPay::getOaNo).collect(Collectors.toList());
            List<TravelExpenseForm> cleanTravelExpenseFormList = travelExpenseFormRepository.getCleanTravelExpenseFormListByOaNos(oaNosWith302);
            Map<String, BigDecimal> formMapWith302 = cleanTravelExpenseFormList.stream().collect(Collectors.toMap(TravelExpenseForm::getOaNo, TravelExpenseForm::getTotalAmount));
            resultMap.putAll(formMapWith302);
        }

        if (CollectionUtils.isNotEmpty(formPays303)) {
            List<String> oaNosWith303 = formPays303.stream().map(FormPay::getOaNo).collect(Collectors.toList());
            List<RemittanceForm> cleanRemittanceFormList = remittanceFormRepository.getRemittanceFormsByOaNos(oaNosWith303);
            Map<String, BigDecimal> formMapWith303 = cleanRemittanceFormList.stream().collect(Collectors.toMap(RemittanceForm::getOaNo, RemittanceForm::getTotalAmount));
            resultMap.putAll(formMapWith303);
        }

        if (CollectionUtils.isNotEmpty(formPays304)) {
            List<String> oaNosWith304 = formPays304.stream().map(FormPay::getOaNo).collect(Collectors.toList());
            List<SupplyChainForm> cleanSupplyChainFormList = supplyChainFormRepository.getSupplyChainFormsByOaNos(oaNosWith304);
            Map<String, BigDecimal> formMapWith304 = cleanSupplyChainFormList.stream().collect(Collectors.toMap(SupplyChainForm::getOaNo, SupplyChainForm::getTotalAmount));
            resultMap.putAll(formMapWith304);
        }

        List<OaBillDiff> oaBillDiffs = Lists.newArrayList();
        for (FormPay formPay : formPays) {
            // 总金额
            BigDecimal totalAmount = resultMap.get(formPay.getOaNo());
            BigDecimal localTotalAmount = formPay.getTotalAmount();
            if (totalAmount.compareTo(localTotalAmount) != 0) {
                OaBillDiff oaBillDiff = reconciliationConverter.convertFormPay2OaDiff(formPay);
                // 不相等
                oaBillDiffs.add(oaBillDiff);
            }
        }
        if (CollectionUtils.isNotEmpty(oaBillDiffs)) {
            oaBillDiffRepository.saveBatch(oaBillDiffs);
        }
        return reconciliationRecordRepository.updateBatchToOaInfo(oaNos, LocalDateTime.now());
    }
}