package com.labway.business.center.finance.persistence.params;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;

/**
 * 发票查询
 *
 * <AUTHOR> on 2025/1/23.
 */
@Data
public class InvoiceSearchParams implements Serializable {

	private String orgId;
	/**
	 * 制单 start
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd")
	private Date makeTimeStart;

	/**
	 * 制单 end
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd")
	private Date makeTimeEnd;

	/**
	 * 票据类型
	 */
	private String piBillname;

	/**
	 * nc凭证号
	 */
	private String voucherNo;

	/**
	 * 票据金额
	 */
	private String piAmount;

	/**
	 * 报销人
	 */
	private String createdBy;

	/**
	 * 发票号
	 */
	private String piFphm;
}
