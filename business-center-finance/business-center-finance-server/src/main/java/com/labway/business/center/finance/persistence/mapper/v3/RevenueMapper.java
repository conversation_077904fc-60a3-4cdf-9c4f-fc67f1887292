package com.labway.business.center.finance.persistence.mapper.v3;

import com.labway.business.center.core.injector.ExtBaseMapper;
import com.labway.business.center.finance.persistence.entity.v3.Revenue;
import org.apache.ibatis.annotations.Param;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 收入
 *
 * <AUTHOR> on 2025/3/14.
 */
public interface RevenueMapper extends ExtBaseMapper<Revenue> {

	int canModify(@Param("budgetUnitId") Long budgetUnitId, @Param("year") Integer year);

	void batchUpdateBudgetDeptRevenue(@Param("list") List<Revenue> updateBudgetRevenue);

	Revenue searchAnyOneByUnitAndDept(@Param("unitId") Long budgetUnitId, @Param("deptId") Long budgetDeptId);
}
