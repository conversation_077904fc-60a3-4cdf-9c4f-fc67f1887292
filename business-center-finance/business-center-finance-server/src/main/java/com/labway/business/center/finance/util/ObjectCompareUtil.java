package com.labway.business.center.finance.util;

import cn.hutool.core.annotation.AnnotationUtil;
import com.labway.business.center.finance.aggregation.log.param.MappingParam;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Field;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 2023/06/08 13:38
 **/
public class ObjectCompareUtil {

    public static void main(String[] args) throws Exception {
        MappingParam newMappingParam = new MappingParam();
        newMappingParam.setParamA("123");
        newMappingParam.setParamB("123");
        MappingParam oldMappingParam = new MappingParam();
        oldMappingParam.setParamA("123");
        String test = compareNotField(oldMappingParam, newMappingParam, "test");
        System.out.println(test);
    }

    public static<T> String compareNotField(T oldData, T newData, String logType) throws Exception {
        StringBuffer content = new StringBuffer(logType + "：");

        Class clazz = newData.getClass();
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            // with modifiers "private"
            ReflectionUtils.makeAccessible(field);
            Object newValue = field.get(newData);
            Object oldValue = field.get(oldData);
            if(newValue != null && newValue.equals(oldValue)){
                content.append("编辑【");
                content.append(oldValue);
                content.append("】");
            }

            if(!Objects.equals(newValue,oldValue)){
                content.append("【取消");
                content.append(String.valueOf(oldValue));
                content.append("，改为");
                content.append(String.valueOf(newValue));
                content.append("】");
            }
        }
        return content.toString();
    }
    public static<T> String compare(T oldData, T newData) throws Exception {
        StringBuffer content = new StringBuffer();

        Class clazz = newData.getClass();
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            // with modifiers "private"
            ReflectionUtils.makeAccessible(field);
            Object newValue = field.get(newData);
            Object oldValue = field.get(oldData);
            Object annotationValue = AnnotationUtil.getAnnotationValue(field, ApiModelProperty.class);
            if(newValue != null && newValue != oldValue){

                content.append("编辑【");
                content.append(annotationValue);
                content.append("】");
                content.append("从'");
                content.append(String.valueOf(oldValue));
                content.append("'改成'");
                content.append(String.valueOf(newValue));
                content.append("';");

            }
        }


        return content.toString();
    }
}