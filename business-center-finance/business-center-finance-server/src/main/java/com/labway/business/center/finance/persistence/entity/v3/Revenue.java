package com.labway.business.center.finance.persistence.entity.v3;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.labway.business.center.finance.constants.MonthEnums;
import com.labway.business.center.finance.persistence.entity.BaseEntity;
import com.labway.business.center.finance.persistence.entity.BudgetUnit;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 收入表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tb_revenue")
public class Revenue extends BaseEntity {

    /**
     * 收入表id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long revenueId;

    /**
     * 对应oa预算单元的id
     * @see BudgetUnit#getId()
     */
    @TableField("budget_unit_id")
    private Long budgetUnitId;

    /**
     * 对应oa预算单元的名称
     * @see BudgetUnit#getName()
     */
    @TableField("budget_unit_name")
    private String budgetUnitName;

    /**
     * 对应oa预算部门id
     */
    @TableField("budget_dept_id")
    private Long budgetDeptId;

    /**
     * 对应oa预算部门名称
     */
    @TableField("budget_dept_name")
    private String budgetDeptName;

    /**
     * 收入类别
     */
    @TableField("revenue_type")
    private String revenueType;

    /**
     * 收入年度 2025...
     */
    @TableField("revenue_year")
    private Integer revenueYear;

    /**
     * 收入月度 01...
     * @see MonthEnums#getMonth()
     */
    @TableField("revenue_month")
    private Integer revenueMonth;

    /**
     * 预计收入
     */
    @TableField("anticipated_revenue")
    private BigDecimal anticipatedRevenue;

    /**
     * 实际收入
     */
    @TableField("real_revenue")
    private BigDecimal realRevenue;

    /**
     * 是否可以修改，0-不可修改，1-可以修改
     * @see com.labway.business.center.core.enums.YesOrNoEnum
     */
    @TableField("can_modify")
    private Integer canModify;
}