package com.labway.business.center.finance.controller.v3;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.excel.EasyExcel;
import com.labway.business.center.core.util.ObsUtil;
import com.labway.business.center.finance.constants.FinanceConstants;
import com.labway.business.center.finance.controller.v2.BankTransactionDetailController;
import com.labway.business.center.finance.excel.handler.v3.BudgetAvImportAnalyzeHandler;
import com.labway.business.center.finance.repository.center.v3.BudgetTypeRepository;
import com.labway.business.center.finance.request.v3.budget.av.BudgetAvailableListSearchRequest;
import com.labway.business.center.finance.response.v3.budget.av.BudgetAvResponseOA;
import com.labway.business.center.finance.response.v3.budget.av.BudgetAvailableResponse;
import com.labway.business.center.finance.service.v3.BudgetAvailableOperationService;
import com.labway.business.center.finance.service.v3.BudgetAvailableSearchService;
import com.swak.frame.dto.Response;
import com.swak.frame.exception.BizException;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Objects;

/**
 * 可用预算
 *
 * <AUTHOR> Tianhao on 2025/3/14.
 */
@RestController
@RequestMapping(FinanceConstants.V3_PREFIX + "/budget/available")
public class BudgetAvailableController {

	@Resource
	private BudgetAvailableSearchService budgetAvailableService;

	@Resource
	private BudgetAvailableOperationService budgetAvailableOperationService;

	@Resource
	private ObsUtil obsUtil;

	/**
	 * 可用预算查询
	 */
	@PostMapping("/list")
	public Response<List<BudgetAvailableResponse>> searchBudgetAvailable(@Valid @RequestBody BudgetAvailableListSearchRequest request) {
		return budgetAvailableService.searchBudgetAvailable(request);
	}

	/**
	 * oa穿透预算
	 */
	@PostMapping("/av-oa")
	public Response<BudgetAvResponseOA> searchBudgetAvailableOA(@Valid @RequestBody BudgetAvailableListSearchRequest request) {
		return budgetAvailableService.searchBudgetAvailableOA(request);
	}

	/**
	 * 执行数导入
	 */
	@PostMapping("/import-excel-file")
	public Response<String> importExcelFile(@RequestParam("file") MultipartFile file, @RequestParam Long oaBudgetUnitId
			, @RequestParam Long budgetDeptId, @RequestParam Integer budgetYear) {

		if (Objects.isNull(oaBudgetUnitId) || Objects.isNull(budgetDeptId) || Objects.isNull(budgetYear)) {
			return Response.fail(422, "预算单元或预算部门或预算年份为空");
		}
		BankTransactionDetailController.checkExcelFile(file);

		try {

			BudgetAvImportAnalyzeHandler analyzeHandler = new BudgetAvImportAnalyzeHandler(SpringUtil.getBean(BudgetTypeRepository.class));
			analyzeHandler.setBudgetYear(budgetYear);
			analyzeHandler.setBudgetDeptId(budgetDeptId);
			analyzeHandler.setOaBudgetUnitId(oaBudgetUnitId);

			EasyExcel.read(file.getInputStream()).headRowNumber(2)
					.registerReadListener(analyzeHandler).sheet().doReadSync();

			String fileUrl = obsUtil.uploadFile(file, FinanceConstants.getObsKey(file.getOriginalFilename()));
			return budgetAvailableOperationService.importExcelFile(analyzeHandler.getTargetList(), budgetYear, oaBudgetUnitId, budgetDeptId, fileUrl);

		} catch (Exception e) {
			throw new BizException(e.getMessage());
		}
	}
}
