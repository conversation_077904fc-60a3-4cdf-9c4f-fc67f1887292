package com.labway.business.center.finance.excel.handler.v2.bank;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.read.metadata.holder.ReadRowHolder;
import com.labway.business.center.finance.cbs8.constants.CurrencyEnums;
import com.labway.business.center.finance.cbs8.constants.LoanTypeEnum;
import com.labway.business.center.finance.converter.v2.TransactionDetailConverter;
import com.labway.business.center.finance.dto.v2.ImportErrorResponse;
import com.labway.business.center.finance.dto.v2.BankTransactionDetailImportDTO;
import com.labway.business.center.finance.excel.param.v2.ImportBankDetailDTO;
import com.labway.business.center.finance.request.v2.AnalyzePhysicalRegisterFileHeadMappingRequest;
import com.swak.frame.exception.ExcelException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;

import static java.util.stream.Collectors.toMap;

/**
 * 导入 银行流水明细
 */
@Slf4j
public class ImportBankDetailListener extends AnalysisEventListener<Map<Integer, String>> {

    private final List<AnalyzePhysicalRegisterFileHeadMappingRequest> headSetting;

    public ImportBankDetailListener(List<AnalyzePhysicalRegisterFileHeadMappingRequest> headSetting) {
        this.headSetting = headSetting;
    }

    private final Map<Integer, ImportBankDetailDTO> excelDataMap = new HashMap<>();

    private final List<ImportErrorResponse> importErrorResponseVoList = new ArrayList<>();
    private final List<BankTransactionDetailImportDTO> targetList = new ArrayList<>();

    public List<ImportErrorResponse> getImportErrorResponseVoList() {
        return importErrorResponseVoList;
    }

    public List<BankTransactionDetailImportDTO> getTargetList() {
        return targetList;
    }

    private final Map<String, Integer> reverseHeadMap = new HashMap<>();

    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        // 校验表头
        List<String> importHeadList = ImportBankDetailDTO.getHeadList();

        Map<String, AnalyzePhysicalRegisterFileHeadMappingRequest> settingByTemplateHead =
                headSetting.stream().collect(toMap(AnalyzePhysicalRegisterFileHeadMappingRequest::getTemplateHead,
                        Function.identity(), (key1, key2) -> key1));

        importHeadList.forEach(item -> {
            AnalyzePhysicalRegisterFileHeadMappingRequest setting = settingByTemplateHead.get(item);
            if (Objects.nonNull(setting)) {
                reverseHeadMap.put(item, setting.getExcelIndex());
            }
        });

    }

    @Override
    public void invoke(Map<Integer, String> dataMap, AnalysisContext analysisContext) {
        ImportBankDetailDTO data = new ImportBankDetailDTO(dataMap, reverseHeadMap);

        ReadRowHolder readRowHolder = analysisContext.readRowHolder();
        Integer rowIndex = readRowHolder.getRowIndex() + 1;

        // 检查数据 格式
        String errorMessage = validateAndHandlerData(data);

        if (StringUtils.isNotBlank(errorMessage)) {
            importErrorResponseVoList
                    .add(ImportErrorResponse.builder().rowNo(rowIndex).errorInfo(errorMessage).build());
        }

        excelDataMap.put(rowIndex, data);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        if (CollectionUtils.isEmpty(excelDataMap.values())) {
            // 无 导入数据
            return;
        }

        if (CollectionUtils.isNotEmpty(importErrorResponseVoList)) {
            // 存在错误数据 相关 检查 失败
            throw new ExcelException(importErrorResponseVoList.toString());
        }

        for (Map.Entry<Integer, ImportBankDetailDTO> entry : excelDataMap.entrySet()) {
            ImportBankDetailDTO value = entry.getValue();
            // 数据导入赋值
            targetList.add(TransactionDetailConverter.INSTANCE.convertImportObj2ImportDTO(value));
        }
    }

    /**
     * 检查 并处理 导入数据
     */
    private String validateAndHandlerData(ImportBankDetailDTO data) {
        StringBuilder errorMessage = new StringBuilder();
        // 交易后余额
        String accountBalance = data.getAccountBalance();
        if (StringUtils.isBlank(accountBalance)) {
            errorMessage.append("账户余额不能为空");
        }
        accountBalance = accountBalance.replaceAll(",", "");
        if (!NumberUtil.isNumber(accountBalance)) {
            // 不是数字类型
            errorMessage.append("账户余额格式错误，请输入数字例如：123,456.00");
        }
        data.setAccountBalance(accountBalance);
        // 交易时间
        String bankTransactionDate = data.getBankTransactionDate();
        if (StringUtils.isNotBlank(bankTransactionDate)) {
            try {
                DateUtil.parse(bankTransactionDate, DatePattern.NORM_DATETIME_PATTERN);
            } catch (Exception e) {
                errorMessage.append("交易时间格式错误，请输入日期格式例如：2020-01-01 00:00:00");
            }
        } else {
            errorMessage.append("交易时间不能为空");
        }
        // 起息日
        String valueDate = data.getValueDate();
        if (StringUtils.isNotBlank(valueDate)) {
            try {
                DateUtil.parse(valueDate, DatePattern.NORM_DATE_PATTERN);
            } catch (Exception e) {
                errorMessage.append("起息日格式错误，请输入日期格式例如：2020-01-01");
            }
        }
        // 金额
        String amount = data.getAmount();
        String jieAmount = data.getJieAmount();
        String daiAmount = data.getDaiAmount();

        if (StringUtils.isAllBlank(amount, jieAmount, daiAmount)) {
            errorMessage.append("交易金额不能为空");
        }

        boolean jieBoolean = amountValid(jieAmount);

        boolean daiBoolean = amountValid(daiAmount);

        boolean amountBoolean = amountValid(amount);

        if (jieBoolean && daiBoolean && amountBoolean) {
            errorMessage.append("对照中，借贷方金额，与借方贷方不可同时存在");
        }

        if (StringUtils.isNotBlank(amount) && StringUtils.isNotBlank(jieAmount) && StringUtils.isNotBlank(daiAmount)) {
            // 金额和借方金额和贷方金额同时存在
            errorMessage.append("交易金额、借方金额、贷方金额不能同时存在");
        }

        // 金额 / 借贷方 赋值
        if (daiBoolean) {
            data.setIncurredAmount(daiAmount);
            data.setLoanType(LoanTypeEnum.DAI.getValue());
        } else if (jieBoolean) {
            data.setIncurredAmount(jieAmount);
            data.setLoanType(LoanTypeEnum.JIE.getValue());
        } else if (amountBoolean){
            BigDecimal bigDecimal = new BigDecimal(amount.replaceAll(",", ""));
            if (bigDecimal.compareTo(BigDecimal.ZERO) > 0) {
                // 正数 贷方
                data.setIncurredAmount(amount);
                data.setLoanType(LoanTypeEnum.DAI.getValue());
            } else {
                // 负数 借方
                data.setIncurredAmount(amount.replaceFirst("-", ""));
                data.setLoanType(LoanTypeEnum.JIE.getValue());
            }
        }
        if (StringUtils.isNotBlank(data.getIncurredAmount())) {
            data.setIncurredAmount(data.getIncurredAmount().replaceAll(",", ""));
        }
        // 币种判断
        if (StringUtils.isBlank(data.getCurrency())) {
            data.setCurrency(CurrencyEnums.RMB.getCurrencyNo());
        }
        return errorMessage.toString();
    }

    /**
     * 金额的校验
     * @param amount
     * @return
     */
    private static boolean amountValid(String amount) {
        return StringUtils.isNotBlank(amount)
                && NumberUtil.isNumber(amount.replaceAll(",", ""))
                && new BigDecimal(amount.replaceAll(",", "")).compareTo(BigDecimal.ZERO) > 0;
    }
}
