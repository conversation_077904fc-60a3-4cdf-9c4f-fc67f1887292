package com.labway.business.center.finance.service.impl;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.google.common.collect.Lists;
import com.labway.business.center.core.enums.Constants;
import com.labway.business.center.core.enums.SyncDataFrom;
import com.labway.business.center.core.log.SyncLog;
import com.labway.business.center.finance.persistence.entity.BusinessUnitCompany;
import com.labway.business.center.finance.persistence.entity.DeptMapping;
import com.labway.business.center.finance.persistence.entity.ncc.NccOrgCorp;
import com.labway.business.center.finance.repository.center.DeptMappingRepository;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import com.labway.business.center.core.config.Pager;
import com.labway.business.center.finance.converter.NccDepartmentConverter;
import com.labway.business.center.finance.converter.NccDeptConverter;
import com.labway.business.center.finance.dto.NccDeptDTO;
import com.labway.business.center.finance.persistence.entity.NccDept;
import com.labway.business.center.finance.persistence.entity.ncc.NccDepartment;
import com.labway.business.center.finance.repository.center.NccDeptRepository;
import com.labway.business.center.finance.repository.ncc.NccDepartmentRepository;
import com.labway.business.center.finance.request.NccDeptRequest;
import com.labway.business.center.finance.service.NccDeptService;
import com.swak.frame.dto.Response;

/**
 * 辅助核算部门信息实现类
 * 
 * <AUTHOR>
 * @date 2023/03/21
 */
@DubboService
public class NccDeptServiceImpl implements NccDeptService {

    @Resource
    private NccDepartmentRepository nccDepartmentRepository;

    @Resource
    private NccDeptRepository nccDeptRepository;
    
    @Resource
    private DeptMappingRepository deptMappingRepository;
    
    @Autowired
    private NccDeptConverter nccDeptConverter;
    
    @Autowired
    private NccDepartmentConverter nccDepartmentConverter;

    @Override
    @SyncLog(dataFrom = SyncDataFrom.NCC,taskName = Constants.SYNC_TASK_NAME_NCC_DEPT)
    public Response<?> washNccDeptToCenterDatabase() {
        Response<?> response = Response.success();
        Long count = nccDepartmentRepository.countNccDepartment();
        if (count == 0) {
            return response;
        }
        int pageSize = 100;
        Pager<String> pager = new Pager<>(pageSize, count);
        int totalPage = pager.getTotalPage();
        Long total = 0L;
        for (int i = 1; i <= totalPage; i++) {
            pager = new Pager<>(i, pageSize);
            List<NccDepartment> departments = nccDepartmentRepository.getNccDepartmentByPager(pager);
            total += proccessBatchSaveNccDept(departments);
        }
        return Response.success(total);
    }

    /**
     * 处理批量入库
     * 
     * @param departments
     */
    private Integer proccessBatchSaveNccDept(List<NccDepartment> departments) {
        if (CollectionUtils.isEmpty(departments)) {
            return 0;
        }
        List<String> deptIds = departments.stream().map(NccDepartment::getDeptId).collect(Collectors.toList());
        List<NccDept> centerNccDepts = nccDeptRepository.getnccDeptsByDeptIds(deptIds);
        List<NccDept> saveNccDepts = null;
        List<NccDept> updateNccDepts = null;
        if (CollectionUtils.isEmpty(centerNccDepts)) {
            saveNccDepts = departments.stream().map(this::assinDepartmentValue).collect(Collectors.toList());
        } else {
            List<String> deptIdList = centerNccDepts.stream().map(NccDept::getDeptId).collect(Collectors.toList());
            saveNccDepts = departments.stream().filter(p -> !deptIdList.contains(p.getDeptId()))
                .map(this::assinDepartmentValue).collect(Collectors.toList());
            updateNccDepts = getUpdateNccDepts(departments,centerNccDepts,deptIds);
            // 过滤出 本地库中有的，ncc没有的 标记为停用
            List<String> deleteNccDept = deptIdList.stream().filter(centerDept -> !deptIds.contains(centerDept)).collect(Collectors.toList());
            Integer batchedStopNccDept = nccDeptRepository.batchStopNccDept(deleteNccDept);
        }
        Integer batchInsertNccDept = nccDeptRepository.batchInsertNccDept(saveNccDepts);
        Integer batchedUpdateNccDept = nccDeptRepository.batchUpdateNccDept(updateNccDepts);
        return batchInsertNccDept + batchedUpdateNccDept;
    }
    
    /**
     * 获取更新的数据
     * @param departments
     * @param centerNccDepts
     * @param deptIds
     * @return
     */
    private List<NccDept> getUpdateNccDepts(List<NccDepartment> departments, List<NccDept> centerNccDepts, List<String> deptIds) {
        List<NccDept> nccDepts = Lists.newArrayList();
        Map<String, NccDepartment> nccDepartmentMap = departments.stream().filter(p -> deptIds.contains(p.getDeptId())).collect(Collectors.toMap(NccDepartment::getDeptId, Function.identity()));
        for (NccDept nccDept : centerNccDepts) {
            NccDepartment nccDepartment = nccDepartmentMap.get(nccDept.getDeptId());
            if (Objects.isNull(nccDepartment)) {
                // 为空 没有
                break;
            }
            // 判断状态是否发生改变
            if (!Objects.equals(nccDept.getStatus(),nccDepartment.getStatus()) || !(nccDepartment.getName().equals(nccDept.getName()))) {
                // 要更新数据
                NccDept updateNccDept = assinDepartmentValue(nccDepartment);
                updateNccDept.setId(nccDept.getId());
                nccDepts.add(updateNccDept);
            }
        }
        return nccDepts;
    }
    
    private NccDept assinDepartmentValue(NccDepartment p) {
        NccDept dept =nccDepartmentConverter.converterNccDepartment2Dept(p);
        dept.setCreateTime(LocalDateTime.now());
        return dept;
    }
    
    /**
     * 通过组织id获取ncc部门
     * @param request 组织id
     * @return
     */
    @Override
    public Response<List<NccDeptDTO>> getNccDeptByOrgId(NccDeptRequest request) {
        Response<List<NccDeptDTO>> response = Response.success();
        List<NccDept> nccDepts = nccDeptRepository.getDeptsByOrgId(request.getOrgId(),request.getDeptName());
        if (CollectionUtils.isEmpty(nccDepts)) {
            return response;
        }
        // 查询中间表 统计数量
        List<String> nccDeptIds = nccDepts.stream().map(NccDept::getDeptId).collect(Collectors.toList());
        List<DeptMapping> allDeptMapping = deptMappingRepository.getDeptMappingByNccDeptId(nccDeptIds);
        Map<String, Long> mappingCountMap = allDeptMapping.stream()
                .collect(Collectors.groupingBy(DeptMapping::getNccDeptId, Collectors.mapping(DeptMapping::getOaDeptId, Collectors.counting())));
        List<NccDeptDTO> nccDeptDTOs = nccDepts.stream().map( dept -> nccDeptConverter.covertNccDept2DTO(dept)).collect(Collectors.toList());
        nccDeptDTOs.forEach(item -> item.setLinkedNumbers(mappingCountMap.get(item.getDeptId())));
        response.setData(nccDeptDTOs);
        return response;
    }

}
