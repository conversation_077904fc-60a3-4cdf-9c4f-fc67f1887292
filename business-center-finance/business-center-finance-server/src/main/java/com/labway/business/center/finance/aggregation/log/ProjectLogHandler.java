package com.labway.business.center.finance.aggregation.log;

import cn.hutool.json.JSONUtil;
import com.labway.business.center.core.enums.EnableFlagEnum;
import com.labway.business.center.core.enums.OperationTypeEnum;
import com.labway.business.center.finance.aggregation.log.param.BaseParam;
import com.labway.business.center.finance.constants.FinanceConstants;
import com.labway.business.center.finance.constants.LogType;
import com.labway.business.center.finance.persistence.entity.Project;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 研发项目
 *
 * <AUTHOR>
 * @version 2023/06/09 11:35
 **/
@Slf4j
@Component("projectLogHandler")
public class ProjectLogHandler implements LogHandler {

	/**
	 * @param body 参数
	 */
	@Override
	public void saveLog(String body) throws Exception {
		BaseParam baseParam = JSONUtil.toBean(body, BaseParam.class);
		Project project = JSONUtil.toBean(baseParam.getData(), Project.class);
		StringBuilder sb = new StringBuilder();
		sb.append(LogType.PROJECT.getType()).append(": ");
		if (EnableFlagEnum.ENABLE.getValue() == project.getDefaultAble()) {
			sb.append("编辑【").append(project.getProjectName()).append("】设为默认");
		} else {
			sb.append("取消【").append(project.getProjectName()).append("】");
		}
		String operationMsg = sb.toString();
		sendMq(OperationTypeEnum.UPDATE, operationMsg, FinanceConstants.ModuleName.BASE_DATA.getModule(), baseParam);
	}
}