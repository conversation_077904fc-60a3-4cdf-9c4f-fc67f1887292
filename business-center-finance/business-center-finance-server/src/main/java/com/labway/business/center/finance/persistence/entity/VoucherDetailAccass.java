
package com.labway.business.center.finance.persistence.entity;

import com.baomidou.mybatisplus.annotation.TableName;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * <B>Description: 实体类</B>
 * </P>
 *
 * <AUTHOR>
 * @version 1.0
 */
@TableName("tb_voucher_detail_accass")
@ApiModel(value = "VoucherDetailAccass对象", description = "")
@Data
public class VoucherDetailAccass {
    
    /**
     * 序列号
     */
    private static final long serialVersionUID = 1L;
    @TableId("id")
    private Long id;
    /**
     * OA 流程编码
     */
    @ApiModelProperty(value = "OA流程编码")
    @TableField("oa_flow_id")
    private String oaFlowId;
    
    /**
     * 凭证详情ID
     */
    @ApiModelProperty(value = "凭证详情ID")
    @TableField("detail_id")
    private Long detailId;
    
    /**
     * 科目的Code
     */
    @ApiModelProperty(value = "科目的Code")
    @TableField("accasoa_code")
    private String accasoaCode;
    
    /**
     * 辅助核算项目ID
     */
    @ApiModelProperty(value = "辅助核算项目code")
    @TableField("accass_item_code")
    private String accassItemCode;
    
    /**
     * 辅助核算项目名称
     */
    @ApiModelProperty(value = "辅助核算项目名称")
    @TableField("accass_item_name")
    private String accassItemName;
    /**
     * 辅助核算值
     */
    @ApiModelProperty(value = "辅助核算对应的名称")
    @TableField("accass_entity_name")
    private String accassEntityName;
    
    /**
     * 辅助核算值
     */
    @ApiModelProperty(value = "辅助核算对应的Code")
    @TableField("accass_entity_code")
    private String accassEntityCode;
    
    
    /**
     * 是否删除0，未删除，1删除
     */
    @ApiModelProperty(value = "是否删除0，未删除，1删除")
    @TableField("deleted")
    private Integer deleted;
    
}