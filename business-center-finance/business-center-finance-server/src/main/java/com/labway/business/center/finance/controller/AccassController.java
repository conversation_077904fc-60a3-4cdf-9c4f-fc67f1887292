package com.labway.business.center.finance.controller;

import com.labway.business.center.core.user.RequiresRoles;
import com.labway.business.center.finance.anno.SecurityParameter;
import com.labway.business.center.finance.dto.AccassDTO;
import com.labway.business.center.finance.request.AccassListRequest;
import com.labway.business.center.finance.service.AccassService;
import com.swak.frame.dto.Response;

import lombok.extern.slf4j.Slf4j;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 辅助核算
 *
 * <AUTHOR>
 * @version 2023/04/07 09:35
 **/
@RestController
@RequestMapping("/accass")
@Slf4j
public class AccassController {
    @Resource
    private AccassService accassService;
    

    @RequestMapping("/wash")
	@SecurityParameter(inDecode = false, outEncode = false)
    public Response<?> washAccassToCenterDatabase() {
        log.info("同步辅助核算数据");
        return accassService.washAccassToCenterDatabase();
    }
    
    /**
     * 根据科目id查询对应的辅助核算
     * @param accassListRequest 科目id
     * @return
     */
    @RequiresRoles
    @PostMapping("/getAccass")
    public Response<List<AccassDTO>> getAccassByAccasoaId(@RequestBody AccassListRequest accassListRequest) {
        return accassService.getAccassByAccasoaId(accassListRequest);
    }
}