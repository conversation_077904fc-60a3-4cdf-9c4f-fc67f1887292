package com.labway.business.center.finance.converter;

import com.labway.business.center.core.aop.param.OptRecordDTO;
import com.labway.business.center.finance.persistence.entity.SysOptRecord;
import org.mapstruct.Mapper;

/**
 * OptRecordConverter 日志转换器
 *
 * <AUTHOR>
 * @version 2023/04/11 17:56
 **/
@Mapper(componentModel = "spring")
public interface OptRecordConverter {
    SysOptRecord convertOptRecordDTO2OptRecord(OptRecordDTO optRecordDTO);
}