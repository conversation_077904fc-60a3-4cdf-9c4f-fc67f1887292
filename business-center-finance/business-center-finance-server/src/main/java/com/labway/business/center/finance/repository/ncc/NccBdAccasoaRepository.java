 package com.labway.business.center.finance.repository.ncc;

import java.util.List;

import javax.annotation.Resource;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import com.labway.business.center.core.config.Pager;
import com.labway.business.center.finance.persistence.entity.ncc.NccBdAccasoa;
import com.labway.business.center.finance.persistence.mapper.ncc.NccBdAccasoaMapper;

/**
 * NCC 会计科目信息资源类
 * <AUTHOR>
 * @date 2023/03/21
 */
@Repository
 public class NccBdAccasoaRepository {
    
    @Resource
    private NccBdAccasoaMapper nccBdAccasoaMapper;

    /**
     * 分页加载NCC会计科目信息
     * @param pager
     * @return
     */
  public  List<NccBdAccasoa> getNccBdAccasoaByPager(@Param("pager") Pager<String> pager){
      return nccBdAccasoaMapper.getNccBdAccasoaByPager(pager);
  }
    
   public Long countNccBdAccasoa() {
       return nccBdAccasoaMapper.countNccBdAccasoa();
   }
}
