package com.labway.business.center.finance.persistence.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * <B>Description: 实体类</B>
 * </P>
 *
 * <AUTHOR>
 * @version 1.0
 */
@TableName("tb_budget_unit")
@ApiModel(value="BudgetUnit对象", description="")
@Data
public class BudgetUnit implements Serializable {

    /**
     * 序列号
     */
    private static final long serialVersionUID = 1L;
    @TableId("id")
    private Long id;
    /**
    * 预算单元code
    */
    @ApiModelProperty(value = "预算单元code")
    @TableField("code")
    private String code;
    /**
    * 上级id
    */
    @ApiModelProperty(value = "上级id")
    @TableField("parent_id")
    private Long parentId;
    /**
    * 预算单元名称
    */
    @ApiModelProperty(value = "预算单元名称")
    @TableField("name")
    private String name;
    /**
    * 预算单元年份
    */
    @ApiModelProperty(value = "预算单元年份")
    @TableField("year")
    private String year;
    /**
    * NCC预算单元id
    */
    @ApiModelProperty(value = "NCC预算单元id")
    @TableField("defdoc_id")
    private String defdocId;
    
    /**
     * OA预算单元ID
     */
    @TableField("oa_budget_id")
    private Long oaBudgetId;
    
    /**
     * 状态0：停用1：启用3：删除
     */
    @TableField("status")
    private Integer status;
  
}