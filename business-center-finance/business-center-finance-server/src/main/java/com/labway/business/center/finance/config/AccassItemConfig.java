 package com.labway.business.center.finance.config;

import java.util.Map;

import com.google.common.collect.Maps;

/**
 * 辅助核算
 * <AUTHOR>
 * @date 2023/03/31
 */
public class AccassItemConfig {

     private  static Map<String, String> handleMappingMap=Maps.newHashMap();
     static {
        handleMappingMap.put("0011", "bankCardHandler");
        handleMappingMap.put("CW03", "carNoHandler");
        handleMappingMap.put("0001", "deptHandler");
        handleMappingMap.put("0002", "employeeHandler");
        handleMappingMap.put("CW05", "mangeOrgHandler");
        handleMappingMap.put("0010", "projectHandler");
//        handleMappingMap.put("0004", "customerHandler");
     }
     
     public static Map<String, String> getHandleMappingMap() {
         return handleMappingMap;
     }
}
