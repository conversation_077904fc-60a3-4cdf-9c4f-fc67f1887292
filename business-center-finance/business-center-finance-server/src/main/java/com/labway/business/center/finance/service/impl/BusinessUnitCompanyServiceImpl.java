 package com.labway.business.center.finance.service.impl;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.util.StopWatch;

import com.labway.business.center.core.config.Pager;
import com.labway.business.center.finance.persistence.entity.BusinessUnitCompany;
import com.labway.business.center.finance.persistence.entity.ncc.NccOrgCorp;
import com.labway.business.center.finance.repository.center.BusinessUnitCompanyRepository;
import com.labway.business.center.finance.repository.ncc.NccOrgCorpRepository;
import com.labway.business.center.finance.service.BusinessUnitCompanyService;
import com.swak.frame.dto.Response;

import lombok.extern.slf4j.Slf4j;

/**
 * 业务单元公司服务类
 * <AUTHOR>
 * @date 2023/03/21
 */
@DubboService
@Slf4j
public class BusinessUnitCompanyServiceImpl implements BusinessUnitCompanyService {

    @Resource
    private  NccOrgCorpRepository nccOrgCorpRepository;

    @Resource
    private BusinessUnitCompanyRepository businessUnitCompanyRepository;

    @Override
    public Response<?> washBusinessUnitCompanyToCenterDatabase() {
        StopWatch watch = new StopWatch("BusinessUnitCompanyService");
        watch.start("count");
        log.info("开始清洗业务单元信息,startTime={}",System.currentTimeMillis());
        Response<?> response = Response.success();
        Long count = nccOrgCorpRepository.countNccOrgCorp();
        if (count == 0) {
            return response;
        }
        watch.stop();
        int pageSize = 100;
        Pager<String> pager = new Pager<>(pageSize, count);
        int totalPage = pager.getTotalPage();
        for (int i = 1; i <= totalPage; i++) {
            pager = new Pager<>(i, pageSize);
            watch.start("washBusinessUnitCompanyToCenterDatabase:"+i);
            List<NccOrgCorp> banks = nccOrgCorpRepository.getNccOrgCorpByPager(pager);
            proccessBatchSaveNccOrgCorp(banks);
            watch.stop();
        }
        log.info("结束清洗业务单元信息,{}",watch.prettyPrint());
        return response;
    }

    /**
     * 处理批量入库
     * 
     * @param projects
     */
    private void proccessBatchSaveNccOrgCorp(List<NccOrgCorp> corps) {
        if (CollectionUtils.isEmpty(corps)) {
            return;
        }
        List<String> companyIds = corps.stream().map(NccOrgCorp::getCompanyId).collect(Collectors.toList());
        List<BusinessUnitCompany> businessUnitCompanies = businessUnitCompanyRepository.getBusinessUnitCompanyBycompanyIds(companyIds);
        List<BusinessUnitCompany> saveBusinessUnitCompanies =null;
        List<BusinessUnitCompany> updateBusinessUnitCompanies =null;
        if (CollectionUtils.isEmpty(businessUnitCompanies)) {
            saveBusinessUnitCompanies = corps.stream().map(this::assinBusinessUnitCompanyValue).collect(Collectors.toList());
        }else {
            List<String> companyIdList = businessUnitCompanies.stream().map(BusinessUnitCompany::getCompanyId).collect(Collectors.toList());
            saveBusinessUnitCompanies=corps.stream().filter(p ->!companyIdList.contains(p.getCompanyId())).map(this::assinBusinessUnitCompanyValue).collect(Collectors.toList());
            // 状态发生改变的
            updateBusinessUnitCompanies = getUpdateBusinessUnitCompanies(corps, businessUnitCompanies, companyIds);
        }
        businessUnitCompanyRepository.batchInsertBusinessUnitCompany(saveBusinessUnitCompanies);
        businessUnitCompanyRepository.batchUpdateBusinessUnitCompany(updateBusinessUnitCompanies);
    }
    
    /**
     * 获取需要更新的数据
     * @param corps
     * @param businessUnitCompanies
     * @param companyIds
     * @return
     */
    private List<BusinessUnitCompany> getUpdateBusinessUnitCompanies(List<NccOrgCorp> corps, List<BusinessUnitCompany> businessUnitCompanies, List<String> companyIds) {
        List<BusinessUnitCompany> updateBusinessUnitCompanies = Lists.newArrayList();
        Map<String, NccOrgCorp> nccOrgAccountingBookMap = corps.stream().filter(p -> companyIds.contains(p.getCompanyId())).collect(Collectors.toMap(NccOrgCorp::getCompanyId, Function.identity()));
        for (BusinessUnitCompany businessUnitCompany : businessUnitCompanies) {
            NccOrgCorp nccOrgCorp = nccOrgAccountingBookMap.get(businessUnitCompany.getCompanyId());
            if (Objects.isNull(nccOrgCorp)) {
                // 为空 没有
                break;
            }
            // 判断状态是否发生改变
            if (!Objects.equals(businessUnitCompany.getEnableState(),nccOrgCorp.getEnableState())) {
                // 要更新数据
                BusinessUnitCompany updateBusinessUnitCompany = assinBusinessUnitCompanyValue(nccOrgCorp);
                updateBusinessUnitCompany.setId(businessUnitCompany.getId());
                updateBusinessUnitCompanies.add(updateBusinessUnitCompany);
            }
        }
        return updateBusinessUnitCompanies;
    }
    
    private BusinessUnitCompany assinBusinessUnitCompanyValue(NccOrgCorp p) {
        BusinessUnitCompany company = new BusinessUnitCompany();
        BeanUtils.copyProperties(p, company);
        company.setCreateTime(LocalDateTime.now());
        return company;
    }

}
