package com.labway.business.center.finance.service.impl;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import com.labway.business.center.core.enums.Constants;
import com.labway.business.center.core.enums.SyncDataFrom;
import com.labway.business.center.core.log.SyncLog;
import com.labway.business.center.finance.aggregation.MappingSaveService;
import com.labway.business.center.finance.dto.ProjectDTO;
import com.labway.business.center.finance.request.ProjectRequest;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import com.labway.business.center.core.config.Pager;
import com.labway.business.center.core.enums.DefaultAbleEnum;
import com.labway.business.center.core.enums.ResultCode;
import com.labway.business.center.finance.persistence.entity.Project;
import com.labway.business.center.finance.repository.center.ProjectRepository;
import com.labway.business.center.finance.repository.ncc.NccProjectRepository;
import com.labway.business.center.finance.request.SetupDefaultProjectRequest;
import com.labway.business.center.finance.service.ProjectService;
import com.swak.frame.dto.Response;

import javax.annotation.Resource;

/**
 * 辅助核算项目信息实现类
 * 
 * <AUTHOR>
 * @date 2023/03/17
 */
@DubboService
public class ProjectServiceImpl implements ProjectService {

    @Autowired
    private NccProjectRepository nccProjectRepository;

    @Autowired
    private ProjectRepository projectRepository;

    @Resource
    private MappingSaveService mappingSaveService;

    @Override
    @SyncLog(dataFrom = SyncDataFrom.NCC,taskName = Constants.SYNC_TASK_NAME_NCC_PROJECT)
    public Response<?> washNccProjectToCenterDatabase() {
        Response<?> response = Response.success();
        Long count = nccProjectRepository.countNccProject();
        if (count == 0) {
            return response;
        }
        int pageSize = 50;
        Long total = 0L;
        Pager<?> pager = new Pager<>(pageSize, count);
        int totalPage = pager.getTotalPage();
        for (int i = 1; i <= totalPage; i++) {
            pager = new Pager<>(i, pageSize);
            List<Project> projects = nccProjectRepository.getNccProjectByPager(pager);
            total += proccessBatchSaveProject(projects);
        }
        return Response.success(total);
    }

    /**
     * 处理批量入库
     * 
     * @param projects
     */
    private Integer proccessBatchSaveProject(List<Project> projects) {
        if (CollectionUtils.isEmpty(projects)) {
            return 0;
        }
        List<String> projectIds = projects.stream().map(Project::getProjectId).collect(Collectors.toList());
        List<Project> centerProjects = projectRepository.getProjectByProjectIds(projectIds);
        List<Project> saveProjects = null;
        if (CollectionUtils.isEmpty(centerProjects)) {
            saveProjects = projects.stream().map(this::assinProjectValue).collect(Collectors.toList());
        } else {
            List<String> projectIdList =
                centerProjects.stream().map(Project::getProjectId).collect(Collectors.toList());
            saveProjects = projects.stream().filter(p -> !projectIdList.contains(p.getProjectId()))
                .map(this::assinProjectValue).collect(Collectors.toList());
        }
        
        return projectRepository.batchInsertProject(saveProjects);
    }

    private Project assinProjectValue(Project p) {
        p.setCreateTime(LocalDateTime.now());
        p.setDefaultAble(DefaultAbleEnum.NO_DEFAULT.getCode());
        return p;
    }

    @Override
    public Response<String> setDefaultProject(SetupDefaultProjectRequest setupDefaultProjectRequest) {
        String projectId = setupDefaultProjectRequest.getProjectId();
        Integer status = setupDefaultProjectRequest.getStatus();
        Project project = projectRepository.getProjectByProjectId(projectId);
        if (Objects.isNull(project)) {
            return Response.builder(ResultCode.DATA_NOT_EXITS);
        }
        if (!DefaultAbleEnum.validate(status)) {
            return Response.builder(ResultCode.PARAMS_NOT_VALIDATE);
        }
        Integer num = projectRepository.updateProjectDefaultAble(projectId, status);
        mappingSaveService.saveDefaultProject(project,status);
        return num > 0 ? Response.success() : Response.builder(ResultCode.INTERNAL_SERVER_ERROR);
    }
    
    /**
     * 研发项目列表 (可以据名称模糊查询)
     *
     * @param projectRequest 名称
     * @return 研发项目列表
     */
    @Override
    public Response<List<ProjectDTO>> selectProjectList(ProjectRequest projectRequest) {
        List<ProjectDTO> projectlist = projectRepository.selectProjectList(projectRequest.getOrgId(),projectRequest.getProjectName());
        return Response.success(projectlist);
    }
    
}
