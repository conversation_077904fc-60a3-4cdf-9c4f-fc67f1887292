package com.labway.business.center.finance.excel.handler.v3;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.util.ConverterUtils;
import com.labway.business.center.finance.constants.MonthEnums;
import com.labway.business.center.finance.constants.YesOrNoEnums;
import com.labway.business.center.finance.excel.param.v3.BudgetAvImportDTO;
import com.labway.business.center.finance.persistence.entity.v3.BudgetType;
import com.labway.business.center.finance.repository.center.v3.BudgetTypeRepository;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.math.BigDecimal;
import java.util.*;

/**
 * 可用预算导入解析
 *
 * <AUTHOR> Tianhao on 2025/5/6.
 */
@Slf4j
@Getter
@Setter
public class BudgetAvImportAnalyzeHandler extends AnalysisEventListener<Map<Integer, String>> {

	private Long oaBudgetUnitId;

	private Long budgetDeptId;

	private Integer budgetYear;

	private List<String> errorMessageList = new ArrayList<>();

	private Map<Integer, String> firstHeadMap = new HashMap<>(40);

	private Map<Integer, String> secondHeadMap = new HashMap<>(40);

	private Integer headRowNum = 1;

	private List<BudgetAvImportDTO> targetList = new ArrayList<>(30);

	private final BudgetTypeRepository budgetTypeRepository;

	public BudgetAvImportAnalyzeHandler(BudgetTypeRepository budgetTypeRepository) {
		this.budgetTypeRepository = budgetTypeRepository;
	}

	@Override
	public void invokeHead(Map<Integer, CellData> headMap, AnalysisContext context) {

		Map<Integer, String> stringMap = ConverterUtils.convertToStringMap(headMap, context);
		stringMap.put(28, null);
		invokeHeadMap(stringMap, context);

	}

	@Override
	public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
		// 这里共有两行表头 会回调这个方法两次
		for (int i = 0; i < headMap.size(); i++) {
			String cellName = headMap.get(i);
			if (headRowNum == 1) {
				// 列名为空 取上一个
				if (StringUtils.isBlank(cellName) && i >= 4) {
					firstHeadMap.put(i, firstHeadMap.get(i - 1));
					continue;
				}
				firstHeadMap.put(i, cellName);
			} else if (headRowNum == 2) {
				// 第二行表头如果为空 获取第一行当前列的列名
				if (StringUtils.isBlank(cellName)) {
					secondHeadMap.put(i, firstHeadMap.get(i));
					continue;
				}
				secondHeadMap.put(i, cellName);
			}
		}
		headRowNum++;
	}

	@Override
	public void invoke(Map<Integer, String> data, AnalysisContext context) {
		log.info("解析到一条数据:{}", data.toString());

		String budgetTypeCode = data.get(0);
		String budgetTypeName = data.get(1);
		if (StringUtils.isAnyBlank(budgetTypeCode, budgetTypeName) || data.size() < 5) {
			return;
		}

		BudgetType budgetType = budgetTypeRepository.searchBudgetTypeByCode(budgetTypeCode);

		if (YesOrNoEnums.NO.getStatus().equals(budgetType.getCanImport())) {
			log.warn("第{}行，财务科目名称【{}】不允许导入，跳过处理", context.readRowHolder().getRowIndex(), budgetTypeCode);
			return;
		}

		// 将一条数据转为导入的对象
		BudgetAvImportDTO budgetAvImportDTO = new BudgetAvImportDTO();
		budgetAvImportDTO.setBudgetUnitId(oaBudgetUnitId);
		budgetAvImportDTO.setBudgetDeptId(budgetDeptId);
		budgetAvImportDTO.setBudgetTypeId(budgetType.getTypeId());
		budgetAvImportDTO.setBudgetType(budgetType);
		budgetAvImportDTO.setBudgetYear(budgetYear);
		budgetAvImportDTO.setBudgetUseDetailMap(new HashMap<>(12));

		// 循环12个月
		for (MonthEnums monthEnum : MonthEnums.values()) {
			List<Integer> indexList = new ArrayList<>(3);
			for (Map.Entry<Integer, String> entry : firstHeadMap.entrySet()) {
				if (monthEnum.getMonthName().equals(entry.getValue())) {
					indexList.add(entry.getKey());
				}
			}
			if (CollectionUtils.isEmpty(indexList)) {
				// 跳过循环
				log.warn("月份：[{}]，没有找到对应的列", monthEnum.getMonthName());
				continue;
			}
			BudgetAvImportDTO.BudgetUseDetail budgetUseDetail = BudgetAvImportDTO.initBudgetUseDetail();
			// 获取这些列的值
			int index = 0;
			for (Integer i : indexList) {
				String cellValue = data.get(i);
				if (StringUtils.isNotBlank(cellValue)) {
					cellValue = cellValue.replaceAll(",", "");
					// 在单元格格式为 "会计专用" 时候 读取到的负数比如 -12 实际读取是 (12.00) 所以需要手动处理一下
					if (cellValue.startsWith("(") && cellValue.endsWith(")")) {
						cellValue = "-" + cellValue.substring(1, cellValue.length() - 1);
					}
				}
				if (!NumberUtil.isNumber(cellValue)) {
					// 如果当前列不是数字 默认为0
					cellValue = "0";
				}
				BigDecimal bigDecimal = new BigDecimal(cellValue);
				if (index == 0 ) {
					// 预占用额度
					budgetUseDetail.setPreviewAmount(bigDecimal);
					if (bigDecimal.compareTo(BigDecimal.ZERO) != 0 ) {
						budgetUseDetail.setPreviewCount(NumberUtils.LONG_ONE);
					}
				} else if (index == 1) {
					// 实际占用额度
					budgetUseDetail.setUsedAmount(bigDecimal);
					if (bigDecimal.compareTo(BigDecimal.ZERO) != 0 ) {
						budgetUseDetail.setUsedCount(NumberUtils.LONG_ONE);
					}
				}
				index++;
			}
			budgetAvImportDTO.getBudgetUseDetailMap().put(monthEnum.getMonth(), budgetUseDetail);
		}

		targetList.add(budgetAvImportDTO);
	}

	@Override
	public void doAfterAllAnalysed(AnalysisContext context) {
	}
}
