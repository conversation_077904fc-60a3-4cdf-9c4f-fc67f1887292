package com.labway.business.center.finance.cbs8.constants;

import lombok.Getter;

/**
 * cbs8业务类型
 *
 * <AUTHOR>
 * @version 2023/11/06 10:09
 **/
@Getter
public enum BusType {
    EXTERNAL_PAYMENT("202","对外支付","10"),
    BANK_TRANSFER("204","银行调拨","20"),
    LINKAGE_PAYMENT("215","联动支付","15"),
    CENTRALIZED_PAYMENT("401","集中支付","21"),
    ;

    private String code;
    private String remark;
    private String cancelCode;

    BusType(String code, String remark, String cancelCode) {
        this.code = code;
        this.remark = remark;
        this.cancelCode = cancelCode;
    }

    public static String getCodeByTemplate(String code) {
        String busTypeCode = "";
        switch (code) {
            case "001":
            case "002":
            case "003":
            case "004":
                busTypeCode = EXTERNAL_PAYMENT.getCode();
                break;
        }
        return busTypeCode;
    }

    public static String getCancelCodeByTemplate(String code) {
        String busTypeCode = "";
        switch (code) {
            case "001":
            case "002":
            case "003":
            case "004":
                busTypeCode = EXTERNAL_PAYMENT.getCancelCode();
                break;
        }
        return busTypeCode;
    }
}