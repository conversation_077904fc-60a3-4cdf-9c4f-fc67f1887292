 package com.labway.business.center.finance.cbs8.service;

import java.util.Objects;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.labway.business.center.finance.cbs8.model.Cbs8ResponseEnum;
import com.labway.business.center.finance.request.v2.cbs8.ElectronicBillParams;
import com.labway.business.center.finance.cbs8.model.response.CBS8SimpleResponse;
import com.labway.business.center.finance.cbs8.model.response.ElectronicBillDTO;
import com.labway.business.center.finance.dto.v2.cbs8.PagerDTO;
import com.labway.business.center.finance.cbs8.constants.CBS8UrlConstants;
import com.labway.business.center.finance.util.CBS8InvokerUtil;
import com.labway.business.center.finance.util.CBS8ResponseParseor;

import lombok.extern.slf4j.Slf4j;

/**
 * 此接口用于查询电子回单信息和电子回单文件信息。
 * 支付水单
  * <AUTHOR>
  * @date 2023/11/02
  */
 @Component
 @Slf4j
 public class CBS8ElectronicBillListQueryService {
     
    
     
     @Resource
     private CBS8InvokerUtil cbs8InvokerUtil;
     
     
     /**
      * 1.返回参数中回单文件下载地址字段bucketFileUrl包含的下载链接自回单文件生成之日起7天有效。客户需要尽早将此文件取回本地，若该链接超过有效期，可在CBS8重取电子回单刷新下载链接。该链接地址直接GET方法请求即可获取到文件流。
        2.回单文件状态为已取回的才能下载。
        3.回单的借贷方向不一定是有值的，招行回单借贷方向可能为空。
        4.支付接口的referenceNum和交易明细查询接口的erpSerialNumber字段与本接口的settleBusinessReferenceCod一致。
        5.前置机对接模式回单下载支持以下三种方式，OPEN API只支持第一种：
        ①　返回存储通的外链路径：该方式需要用户自行通过接口返回的链接下载文件。
        ②　返回文件内容：该方式会返回文件内容的 base64 编码信息，客户端需要对该编码字段进行解码，即可获取文件。
        ③　返回CBSLink 机器文件路径：CBSLink 通过接口返回的文件下载链接，将文件下载到本地，然后将下载到本地的文件路径信息返回给客户端。
        6.如需通过该接口同步电子回单，建议每小时或以上频率调用该接口取数。
      * @param params
      * @return
      */
     public CBS8SimpleResponse<PagerDTO<ElectronicBillDTO>> invokeCBS8ElectronicBillListQuery(ElectronicBillParams params){
         if (Objects.isNull(params)) {
            return CBS8SimpleResponse.fail(Cbs8ResponseEnum.PARAMS_NOT_VALIDATE);
        }
         
         try {
             String body = JSON.toJSONString(params);
             log.info("查询cbs8电子回参数：【{}】",body);
             String result = cbs8InvokerUtil.invokeCbs8ReturnString(CBS8UrlConstants.ELECTRONIC_BILL_URI, body);
        return    CBS8ResponseParseor.parseSimpleResponseText(result, ElectronicBillDTO.class);
            
        } catch (Exception e) {
           log.error("查询cbs8电子失败", e);
           return CBS8SimpleResponse.fail(Cbs8ResponseEnum.INVOKE_CBS8_ERR);
        }
     }

     
     
}
