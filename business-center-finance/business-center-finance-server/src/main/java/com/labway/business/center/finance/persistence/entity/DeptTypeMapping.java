package com.labway.business.center.finance.persistence.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.swak.frame.dto.base.Entity;
import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * DeptTypeMapping 部门类别对照实体
 *
 * <AUTHOR>
 * @version 2023/04/14 10:47
 **/
@Data
@TableName("tb_dept_type_mapping")
@ApiModel(value="部门类别对照实体", description="部门类别对照实体")
public class DeptTypeMapping extends Entity {
    
    /**
     * 序列号
     */
    private static final long serialVersionUID = 1L;
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 部门类别code
     */
    @TableField(value = "dept_type_code")
    private String deptTypeCode;
    
    /**
     * OA部门id
     */
    @TableField(value = "oa_dept_id")
    private String oaDeptId;
    
    /**
     * 业务单元
     */
    @TableField(value = "org_id")
    private String orgId;
}