package com.labway.business.center.finance.persistence.mapper.v2;

import com.labway.business.center.core.config.Pager;
import com.labway.business.center.core.injector.ExtBaseMapper;
import com.labway.business.center.finance.dto.v2.VoucherCountDTO;
import com.labway.business.center.finance.persistence.entity.v2.PaymentOrder;
import com.labway.business.center.finance.persistence.params.SearchVoucher;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2023/12/29 10:16
 **/
public interface PaymentOrderMapper extends ExtBaseMapper<PaymentOrder> {
    /**
     * 批量修改
     * @param paymentOrders
     * @return
     */
    Integer updateBatch(@Param("list") List<PaymentOrder> paymentOrders);

    Long countPaymentBySearch(@Param("voucher") SearchVoucher build);


    List<PaymentOrder> getPaymentBySearch(@Param("pager") Pager<SearchVoucher> pager);

    List<VoucherCountDTO> voucherCount(@Param("orgId") String orgId,@Param("startDate")  Date startDate,@Param("endDate") Date endDate);
}