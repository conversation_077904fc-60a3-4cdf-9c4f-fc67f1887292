 package com.labway.business.center.finance.service.impl;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.labway.business.center.core.enums.Constants;
import com.labway.business.center.core.enums.SyncDataFrom;
import com.labway.business.center.core.log.SyncLog;
import com.labway.business.center.core.user.LoginUserInfoUtil;
import com.labway.business.center.finance.aggregation.MappingSaveService;
import com.labway.business.center.finance.aop.DataPermissionPointcut;
import com.labway.business.center.finance.persistence.entity.OrgsMapping;
import com.labway.business.center.finance.repository.center.DataPermissionRepository;
import com.labway.business.center.finance.repository.center.OrgsMappingRepository;
import com.labway.business.center.core.config.BaseRequest;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.util.StopWatch;

import com.alibaba.nacos.common.utils.StringUtils;
import com.google.common.collect.Lists;
import com.labway.business.center.core.enums.ResultCode;
import com.labway.business.center.finance.converter.AccountBookConverter;
import com.labway.business.center.finance.dto.AccountBookDTO;
import com.labway.business.center.finance.persistence.entity.AccountBook;
import com.labway.business.center.finance.persistence.entity.ncc.NccOrgAccountingBook;
import com.labway.business.center.finance.repository.center.AccountBookRepository;
import com.labway.business.center.finance.repository.ncc.NccOrgAccountBookRepository;
import com.labway.business.center.finance.request.SetTaxRuleRequest;
import com.labway.business.center.finance.service.AccountingBookService;
import com.swak.frame.dto.Response;

import lombok.extern.slf4j.Slf4j;

/**
 *  ncc 账簿_财务核算账簿 服务类
 * <AUTHOR>
 * @date 2023/03/23
 */
@DubboService
@Slf4j
@RefreshScope
public class AccountingBookServiceImpl implements AccountingBookService {

    @Resource
    private  NccOrgAccountBookRepository nccOrgAccountBookRepository;

    @Resource
    private AccountBookRepository accountBookRepository;
    
    @Resource
    private OrgsMappingRepository orgsMappingRepository;

    @Resource
    private MappingSaveService mappingSaveService;
    
    @Value(value = "${cashflow.code:1124}")
    private String cashFlowCode;
    
    @Autowired
    private AccountBookConverter accountBookConverter;
    

    @Override
    @SyncLog(dataFrom = SyncDataFrom.NCC,taskName = Constants.SYNC_TASK_NAME_NCC_ACCOUNT_BOOK)
    public Response<?> washAccountingBookToCenterDatabase() {
        StopWatch watch = new StopWatch("AccountingBookService");
        watch.start("count");
        log.info("开始清洗财务核算账簿信息,startTime={}",System.currentTimeMillis());
        List<NccOrgAccountingBook> books = nccOrgAccountBookRepository.getAllAccountBook();
        Long total = Long.valueOf(proccessBatchSaveNccAccountingBook(books));
        watch.stop();
        log.info("结束清洗财务核算账簿信息,{}",watch.prettyPrint());
        return Response.success(total);
    }

    /**
     * 处理批量入库
     * 
     * @param books
     */
    private Integer proccessBatchSaveNccAccountingBook( List<NccOrgAccountingBook> books) {
        List<AccountBook> accountBooks = accountBookRepository.searchAll();
        Map<String, Integer> accountTaxMap = accountBooks.stream().collect(Collectors.toMap(AccountBook::getAccountingBookId, AccountBook::getIsTax));
        List<AccountBook>  saveAccountBooks = books.stream().map(item -> {
            AccountBook accountBook = assinAccountBookValue(item);
            if (accountTaxMap.containsKey(accountBook.getAccountingBookId())) {
                accountBook.setIsTax(accountTaxMap.get(accountBook.getAccountingBookId()));
            }
            return accountBook;
        }).collect(Collectors.toList());
        // 清空
        accountBookRepository.removeAll();
        return accountBookRepository.batchInsertAccountBook(saveAccountBooks);
    }

    
    private AccountBook assinAccountBookValue(NccOrgAccountingBook p) {
        AccountBook book = new AccountBook();
        BeanUtils.copyProperties(p, book);
        book.setIsTax(0);
        book.setDefaultCashflowCode(cashFlowCode);
        book.setCreateTime(LocalDateTime.now());
        return book;
    }

    @Override
    public Response<List<AccountBookDTO>> getAllAccountBook(BaseRequest baseRequest) {
        // 获取用户有权限的组织
        String roleId = baseRequest.getRoleId();
        List<String> userOrgPermission = LoginUserInfoUtil.getUserHasOrgs();
        List<OrgsMapping> orgMappingsByorgIds = Lists.newArrayList();
        if (roleId.equals(DataPermissionPointcut.ADMIN_ROLEID)) {
            // 查询所有的业务单元
            orgMappingsByorgIds = orgsMappingRepository.getAllOrgsMappingsNoParam();
        }else {
            orgMappingsByorgIds = orgsMappingRepository.getOrgMappingsByorgIds(userOrgPermission);
        }
        // 通过组织权限获取对应的金融组织
        List<String> searchFinanceOrgIds = orgMappingsByorgIds.stream().map(OrgsMapping::getFinanceOrgId).collect(Collectors.toList());
        Response<List<AccountBookDTO>> response = Response.success();
        List<AccountBook> accountBooks = accountBookRepository.getAccountBookByFinanceIds(searchFinanceOrgIds);
        if (CollectionUtils.isEmpty(accountBooks)) {
            log.error("获取核算账簿数据为空");
            return response;
        }
        List<String> financeOrgIds = accountBooks.stream().map(AccountBook::getFinanceOrgId).collect(Collectors.toList());
        // 查询财务组织
        List<OrgsMapping> financeOrgs = orgsMappingRepository.getOrgMappingsByFinanceOrgIds(financeOrgIds);
        Map<String, String> financeOrgsMap = financeOrgs.stream().collect(Collectors.toMap(OrgsMapping::getFinanceOrgId, OrgsMapping::getNccName));
        List<AccountBookDTO> list = new ArrayList<>();
        accountBooks.forEach(accountBook -> {
            AccountBookDTO accountBookDTO = accountBookConverter.convertAccountBook2DTO(accountBook);
            accountBookDTO.setFinanceOrgName(financeOrgsMap.get(accountBook.getFinanceOrgId()));
            list.add(accountBookDTO);
        });
        response.setData(list);
        return response;
    }

    @Override
    public Response<String> setTaxRule(SetTaxRuleRequest request) {
     
        if (Objects.isNull(request)) {
          return Response.builder(ResultCode.PARAMS_NOT_VALIDATE);
        }
       String accountBookId = request.getAccountBookId();
       Integer tax = request.getTax();
        if (Objects.isNull(tax)||StringUtils.isBlank(accountBookId)||(tax!=0&&tax!=1)) {
            return Response.builder(ResultCode.PARAMS_NOT_VALIDATE); 
        }
        List<AccountBook> accountBooks = accountBookRepository.getAccountBookBybookIds(Lists.newArrayList(accountBookId));
        if (CollectionUtils.isEmpty(accountBooks)) {
            return Response.builder(ResultCode.DATA_NOT_EXITS); 
        }
        try {
            AccountBook accountBook = new AccountBook();
            accountBook.setIsTax(tax);
            accountBook.setAccountingBookId(request.getAccountBookId());
            accountBook.setName(accountBooks.get(0).getName());
            accountBookRepository.updateCashFlowOrTaxByBookId(accountBook);
            mappingSaveService.saveSetTaxRule(accountBook);
        } catch (Exception e) {
           log.error("设置默认的现金流项目和是否合并计税失败失败", e);
           return Response.builder(ResultCode.INTERNAL_SERVER_ERROR);
        }
         return Response.success();
    }

}
