package com.labway.business.center.finance.persistence.entity.v2;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;

/**
 * 支付申请单失败记录
 * <AUTHOR>
 */
@Data
@TableName("tb_form_fail_message")
public class FormFailMessage {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 失败的流程编号，逗号隔开
     */
    @TableField("oa_nos")
    private String oaNos;

    /**
     * 查询状态
     */
    @TableField("search_status")
    private Boolean searchStatus;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private Date updateTime;
}
