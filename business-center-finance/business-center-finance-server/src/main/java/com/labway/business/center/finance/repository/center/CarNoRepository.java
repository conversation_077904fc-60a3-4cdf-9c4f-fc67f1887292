 package com.labway.business.center.finance.repository.center;

import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.labway.business.center.core.enums.DefaultAbleEnum;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.labway.business.center.core.config.Pager;
import com.labway.business.center.finance.converter.CarNoConverter;
import com.labway.business.center.finance.dto.CarNoDTO;
import com.labway.business.center.finance.persistence.params.SearchCarNo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.labway.business.center.finance.persistence.entity.CarNo;
import com.labway.business.center.finance.persistence.mapper.CarNoMapper;

/**
 * 处理车牌信息资源类
 * <AUTHOR>
 * @date 2023/03/20
 */
@Repository
 public class CarNoRepository {
    
    @Resource
    private CarNoMapper carNoMapper;
    
    @Resource
    private CarNoConverter carNoConverter;
    
    public Integer batchInsertCarNo(List<CarNo> carNos) {
        if (CollectionUtils.isEmpty(carNos)) {
            return 0 ;
        }
        return carNoMapper.insertBatchSomeColumn(carNos);
    }
    
    public List<CarNo> getCarNoByDefdocIds(List<String> ids){
        LambdaQueryWrapper<CarNo> queryWrapper = new  LambdaQueryWrapper<>();
        queryWrapper.in(CarNo::getDefdocId, ids);
        queryWrapper.select(CarNo::getDefdocId);
        return carNoMapper.selectList(queryWrapper);
    }
    
    public CarNo getCarNoByDefDocId(String docId) {
        LambdaQueryWrapper<CarNo> queryWrapper = new  LambdaQueryWrapper<>();
        queryWrapper.eq(CarNo::getDefdocId, docId);
        queryWrapper.select(CarNo::getDefdocId,CarNo::getDefaultAble,CarNo::getName);
        return carNoMapper.selectOne(queryWrapper);
    }
    
    public CarNo getDefaultCarNoByOrgId(String orgId) {
        LambdaQueryWrapper<CarNo> queryWrapper = new  LambdaQueryWrapper<>();
        queryWrapper.eq(CarNo::getOrgId, orgId).eq(CarNo::getDefaultAble, DefaultAbleEnum.DEFAULT.getCode());
        return carNoMapper.selectOne(queryWrapper);
    }
    
    public Integer updateCarNoDefaultAble(String defdocId,Integer status) {
        LambdaUpdateWrapper<CarNo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(CarNo::getDefaultAble, status);
        updateWrapper.eq(CarNo::getDefdocId, defdocId);
        return carNoMapper.update(null, updateWrapper);
    }
    
    
    /**
     * 查询车辆列表
     * @param pager 车辆名称
     * @return 车辆列表
     */
    public List<CarNoDTO> selectCarNoList(Pager<SearchCarNo> pager) {
        LambdaQueryWrapper<CarNo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(CarNo::getId,CarNo::getCode,CarNo::getName,
                CarNo::getDefdocId,CarNo::getDefaultAble);
        queryWrapper.eq(CarNo::getOrgId,pager.getItem().getOrgId());
        if (StringUtils.isNotBlank(pager.getItem().getName())) {
            queryWrapper.like(CarNo::getName,pager.getItem().getName());
        }
        Page<CarNo> page = new Page<>(pager.getPage(), pager.getPageSize());
        Page<CarNo> carNos = carNoMapper.selectPage(page,queryWrapper);
        return carNos.getRecords().stream().map(carNo -> carNoConverter.convertCarNo2DTO(carNo)).collect(Collectors.toList());
    }
    
    /**
     * 根据车号模糊查询总条数
     * @param name 车号
     * @return 总条数
     */
    public Long carNoCountByCarNoNameAndOrgId(String name,String orgId) {
        return carNoMapper.getTotalRows(name,orgId);
    }
}
