package com.labway.business.center.finance.persistence.params;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.labway.business.center.finance.constants.BillDiffStatus;
import com.labway.business.center.finance.request.PageRequest;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 2023/10/08 19:42
 **/
@Data
public class FinanceBillDiffPageParams extends PageRequest implements Serializable {
    /**
     * 业务单元
     */
    private String orgId;
    /**
     * 状态
     * @see BillDiffStatus
     */
    private Integer status;

    /**
     * 支付流水号
     */
    private String serial;

    /**
     * 开始时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime startTime;

    /**
     * 开始时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime endTime;
}