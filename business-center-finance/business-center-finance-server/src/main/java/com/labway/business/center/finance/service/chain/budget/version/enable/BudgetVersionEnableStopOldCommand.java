package com.labway.business.center.finance.service.chain.budget.version.enable;

import com.labway.business.center.finance.repository.center.v3.BudgetVersionRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 停用旧版本
 *
 * <AUTHOR> on 2025/3/28.
 */
@Slf4j
@Component
public class BudgetVersionEnableStopOldCommand implements Command {

	@Resource
	private BudgetVersionRepository budgetVersionRepository;

	@Override
	public boolean execute(Context c) throws Exception {
		BudgetVersionEnableContext context = BudgetVersionEnableContext.from(c);

		budgetVersionRepository.starNewVersion(context.getRequest());
		log.info("当前启用[{}]预算单元[{}]预算部门的[{}]版本", context.getBudgetUnit().getName(), context.getBudgetDept().getDeptName(), context.getRequest().getVersion());

		if (!context.isStopEnabled()) {
			log.info("当前启用[{}]预算单元[{}]预算部门的[{}]版本，无需停用旧版本", context.getBudgetUnit().getName(), context.getBudgetDept().getDeptName(), context.getRequest().getVersion());
			return CONTINUE_PROCESSING;
		}

		// 停用旧版本 直接修改
		budgetVersionRepository.stopOldVersion(context.getRequest());

		log.info("当前启用[{}]预算单元[{}]预算部门的[{}]版本，已停用旧版本", context.getBudgetUnit().getName(), context.getBudgetDept().getDeptName(), context.getRequest().getVersion());
		return CONTINUE_PROCESSING;
	}
}
