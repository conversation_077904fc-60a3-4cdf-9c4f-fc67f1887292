package com.labway.business.center.finance.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 同城标志
 *
 * <AUTHOR>
 * @version 2024/01/08 11:17
 **/
@Getter
@AllArgsConstructor
public enum CityFlagEnums {
    /**
     * 同城
     */
    CITY_WIDE("0"),
    /**
     * 异地
     */
    REMOTE("1");

    private final String cityFlag;

    public static String getByCityFlag(String cityFlag) {
        if (StringUtils.isBlank(cityFlag)) {
            return "异地";
        }
        for (CityFlagEnums cityFlagEnums : CityFlagEnums.values()) {
            if (cityFlagEnums.getCityFlag().equals(cityFlag)) {
                switch (cityFlagEnums) {
                    case CITY_WIDE:
                        return "同城";
                    case REMOTE:
                    default:
                        return "异地";
                }
            }
        }
        return "异地";
    }
}