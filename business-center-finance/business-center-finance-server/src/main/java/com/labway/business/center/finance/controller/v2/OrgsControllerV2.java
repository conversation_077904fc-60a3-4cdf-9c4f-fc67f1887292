package com.labway.business.center.finance.controller.v2;

import com.labway.business.center.finance.constants.FinanceConstants;
import com.labway.business.center.finance.dto.OrgsMappingDTO;
import com.labway.business.center.finance.request.v2.OrgSettingCheckRequest;
import com.labway.business.center.finance.service.OrgMappingService;
import com.swak.frame.dto.Response;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2024/02/18 17:02
 **/
@RestController
@RequestMapping(FinanceConstants.V2_PREFIX + "/orgs")
public class OrgsControllerV2 {
    @Resource
    private OrgMappingService orgMappingService;

    /**
     * 开关设置
     * @return
     */
    @PostMapping("/setting")
    public Response<String> settingCheck(@Valid @RequestBody OrgSettingCheckRequest orgSettingCheckRequest) {
        return orgMappingService.settingCheck(orgSettingCheckRequest);
    }

    /**
     * 查询有权限的业务单元
     * @return
     */
    @GetMapping("/hasPermission")
    public Response<List<OrgsMappingDTO>> getHasPermissionOrgs() {
        return orgMappingService.getHasPermissionOrgs();
    }
}