package com.labway.business.center.finance.service.chain.budget.use;

import cn.hutool.core.date.DateUtil;
import com.labway.business.center.finance.constants.BudgetUseEnums;
import com.labway.business.center.finance.constants.MonthEnums;
import com.labway.business.center.finance.constants.YesOrNoEnums;
import com.labway.business.center.finance.persistence.entity.BudgetUnit;
import com.labway.business.center.finance.persistence.entity.v3.*;
import com.labway.business.center.finance.repository.center.v3.BudgetAvailableRepository;
import com.labway.business.center.finance.repository.center.v3.BudgetControlPolicyRepository;
import com.labway.business.center.finance.repository.center.v3.BudgetReleaseRepository;
import com.labway.business.center.finance.repository.center.v3.RevenueRepository;
import com.labway.business.center.finance.request.v3.budget.BudgetUseRequest;
import com.labway.business.center.finance.util.BudgetUtil;
import com.labway.business.center.finance.util.EnvDetector;
import com.swak.frame.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 预算汇总额度校验
 * 做法：同一个预算单元-预算部门的把费用总额加起来，跟部门当月总的预算额度校验
 *
 * <AUTHOR> Tianhao on 2025/5/9.
 */
@Slf4j
@Component
public class BudgetUseValidTotalAmountCommand implements Command {

	@Resource
	private EnvDetector envDetector;

	@Resource
	private RevenueRepository revenueRepository;

	@Resource
	private BudgetReleaseRepository budgetReleaseRepository;

	@Resource
	private BudgetAvailableRepository budgetAvailableRepository;

	@Resource
	private BudgetControlPolicyRepository budgetControlPolicyRepository;

	@Override
	public boolean execute(Context c) throws Exception {
		BudgetUseContext context = BudgetUseContext.from(c);
		BudgetUseRequest request = context.getRequest();

		List<BudgetUseRequest.BudgetUseDetailRequest> detail = request.getBudgetUseDetailRequestList();
		Map<String, BudgetDept> budgetDeptMap = context.getBudgetDeptList().stream()
				.collect(Collectors.toMap(item -> item.getOaBudgetUnitId() + "-" + item.getDeptName(), Function.identity(), (v1, v2) -> v1));
		Map<String, BudgetUnit> budgetUnitMap = context.getBudgetUnitList().stream()
				.collect(Collectors.toMap(BudgetUnit::getName, Function.identity(), (v1, v2) -> v1));

		Map<String, List<BudgetUseRequest.BudgetUseDetailRequest>> detailMap = detail.stream().collect(Collectors.groupingBy(item -> item.getOaBudgetUnitName() + "-" + item.getOaBudgetDeptName()));

		Date useDate = request.getUseDate();

		// 年度
		int year = DateUtil.year(useDate);
		// 发起日期所在的月份 1 2 3 4 5...
		List<Integer> searchMonth = MonthEnums.getSearchMonth(useDate);
		if (CollectionUtils.isEmpty(searchMonth)) {
			throw new BizException("获取预算月份失败，请稍后重试");
		}

		// 在这里提前获取一下 按照单元进行控制 需要的预算 / 收入
		Map<String, List<BudgetRelease>> releaseMap = new HashMap<>();

		// 占用 和 释放 不校验这个
		if (BudgetUseEnums.isAnyStatus(request.getUseType(), BudgetUseEnums.CHECK, BudgetUseEnums.PRE_USE)) {

			StringBuilder errorInfo = new StringBuilder();

			for (Map.Entry<String, List<BudgetUseRequest.BudgetUseDetailRequest>> entry : detailMap.entrySet()) {
				String[] split = entry.getKey().split("-");

				BudgetUnit budgetUnit = budgetUnitMap.getOrDefault(split[0], new BudgetUnit());
				BudgetDept budgetDept = budgetDeptMap.getOrDefault(budgetUnit.getOaBudgetId() + "-" + split[1], new BudgetDept());

				// 查询这个预算单元 部门 年度 月度 的预算额度
				List<BudgetAvailable> availableList = budgetAvailableRepository.searchDeptYearMonthBudgetAvList(budgetUnit.getOaBudgetId(), budgetDept.getBudgetDeptId(), year, searchMonth);
				if (CollectionUtils.isEmpty(availableList)) {
					continue;
				}
				// 获取一下这个月的释放额度
				List<BudgetRelease> releases = budgetReleaseRepository.searchReleaseByUnitAndDeptAndYearAndMonth(budgetUnit.getOaBudgetId(), budgetDept.getBudgetDeptId(), year, DateUtil.month(useDate) + 1);
				releaseMap.put(budgetUnit.getOaBudgetId() + "@" + budgetDept.getBudgetDeptId(), releases);

				Triple<BigDecimal, BigDecimal, Boolean> triple = BudgetUtil.totalCanUse(availableList, entry.getValue()
						.stream().map(BudgetUseRequest.BudgetUseDetailRequest::getAmount)
						.reduce(BigDecimal.ZERO, BigDecimal::add),
						releases.stream().filter(item -> YesOrNoEnums.isNo(item.getIsType())).collect(Collectors.toList()));
				if (!triple.getRight()) {
					// 预算额度不足
					if (envDetector.isTest()) {
						errorInfo.append(String.format("预算不足！[%s]-[%s]剩余预算额度：[%s]，使用后额度：[%s]", budgetUnit.getName(), budgetDept.getDeptName(), triple.getLeft(), triple.getMiddle()));
					} else {
						errorInfo.append(String.format("预算单元：%s，预算部门：%s。超出费控总额度，请发起310预算外审批表或通过协同申请额度释放。", budgetUnit.getName(), budgetDept.getDeptName()));
					}
				}
			}

			if (StringUtils.isNotBlank(errorInfo)) {
				throw new BizException(errorInfo.toString());
			}
		}

		// 查询预算单元的控制规则  (放在这里为了逻辑清晰一点)
		Map<Long, Integer> unitCtrlMap = budgetControlPolicyRepository.searchUnitBudgetControlPolicyList(context.getBudgetUnitList().stream().map(BudgetUnit::getOaBudgetId).collect(Collectors.toSet()))
				.stream().collect(Collectors.toMap(BudgetControlPolicy::getBudgetUnitId, BudgetControlPolicy::getCtrlType, (v1, v2) -> v1));

		Map<Long, List<BudgetAvailable>> unitAvMap = new HashMap<>();
		Map<Long, List<Revenue>> unitRvMap = new HashMap<>();

		unitCtrlMap.forEach((k, v) -> {
			// 按照预算单元来查询 xx 年 xx xx xx 月的收入信息
			List<Revenue> revenueList = revenueRepository.searchRevenueByUnitIdAndYearAndMonth(k, year, searchMonth);
			List<BudgetAvailable> unitBudgetAvailableList = budgetAvailableRepository.searchBudgetAvailableByUnitIdAndYearAndMonth(k, year, searchMonth);
			if (CollectionUtils.isNotEmpty(revenueList)) {
				unitRvMap.put(k, revenueList);
			}
			if (CollectionUtils.isNotEmpty(unitBudgetAvailableList)) {
				unitAvMap.put(k, unitBudgetAvailableList);
			}
		});

		context.setBudgetYear(year);
		context.setSearchMonth(searchMonth);
		context.put(BudgetUseContext.UNIT_REVENUE, unitRvMap);
		context.put(BudgetUseContext.UNIT_BUDGET_AV, unitAvMap);
		context.put(BudgetUseContext.RELEASE_BUDGET, releaseMap);
		context.put(BudgetUseContext.UNIT_CTRL_TYPE, unitCtrlMap);

		return CONTINUE_PROCESSING;
	}
}
