 package com.labway.business.center.finance.persistence.mapper.ncc;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.labway.business.center.core.config.Pager;
import com.labway.business.center.finance.persistence.entity.ncc.NccBank;

/**
 * 清洗NCC 银行账号信息
 * <AUTHOR>
 * @date 2023/03/20
 */
@DS("ncc")
public interface NccBankMapper extends BaseMapper<NccBank> {
    
    /**
     * 分页加载NCC银行账号洗数据
     * @param pager
     * @return
     */
    List<NccBank> getNccBankByPager(@Param("pager") Pager<String> pager);
    
    Long countNccBank();

}
