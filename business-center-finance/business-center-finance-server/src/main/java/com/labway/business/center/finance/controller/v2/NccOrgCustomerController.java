package com.labway.business.center.finance.controller.v2;

import cn.hutool.json.JSONUtil;
import com.google.common.base.Joiner;
import com.labway.business.center.finance.persistence.mapper.ncc.NCCMapper;
import com.labway.business.center.finance.persistence.params.NccOrgCustomer;
import com.labway.business.center.finance.persistence.params.QueryListByOrgParam;
import com.swak.frame.dto.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 客商-业务单元
 * <AUTHOR>
 * @version 2023/12/06 15:21
 **/
@Slf4j
@RestController
@RequestMapping("/ncc-org/customer")
public class NccOrgCustomerController {
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private NCCMapper nccMapper;
    
    private static final String KEY_ORG_CUSTOMER = "finance:ncc:org:customer";
    
//    @PostConstruct
    public void init() {
        refreshRedis();
    }

    private void refreshRedis() {
        log.info("开始初始化redis中【客商-业务单元关系】");
        List<NccOrgCustomer> nccOrgCustomers = nccMapper.searchAllOrgCustomer();
        Map<String, List<NccOrgCustomer>> nccOrgCustomerMap = nccOrgCustomers.stream().collect(Collectors.groupingBy(NccOrgCustomer::getOrgCode));
        // 删除缓存
        stringRedisTemplate.opsForHash().delete(KEY_ORG_CUSTOMER, Joiner.on(",").join(nccOrgCustomerMap.keySet()));
        for (Map.Entry<String, List<NccOrgCustomer>> nccOrgCustomerEntry : nccOrgCustomerMap.entrySet()) {
            String key = nccOrgCustomerEntry.getKey();
            List<NccOrgCustomer> value = nccOrgCustomerEntry.getValue();
            if (CollectionUtils.isEmpty(value)) {
                continue;
            }
            stringRedisTemplate.opsForHash().put(KEY_ORG_CUSTOMER, key, JSONUtil.toJsonStr(value.stream().map(NccOrgCustomer::getCustomerCode).collect(Collectors.toSet())));
        }
        log.info("初始化redis中【客商-业务单元关系】结束");
    }

    /**
     * 从redis查询业务单元编码所有的客商编码
     */
    @GetMapping("/{orgCode}")
    public Response<List<String>> getOrgByCustomerCode(@PathVariable String orgCode) {
        Object o = stringRedisTemplate.opsForHash().get(KEY_ORG_CUSTOMER, orgCode);
        if (Objects.isNull(o)) {
            return Response.fail(500,"没有查到");
        }
        List<String> list = JSONUtil.toList(o.toString(), String.class);
        if (CollectionUtils.isEmpty(list)) {
            return Response.fail(500,"没有查到");
        }
        return Response.success(list);
    }

    /**
     * 刷新缓存
     */
    @GetMapping("/refresh")
    public Response refreshRedisInfo() {
        refreshRedis();
        return Response.success();
    }

    public boolean hasCustomer(String orgCode,String customerCode) {
        Object o = stringRedisTemplate.opsForHash().get(KEY_ORG_CUSTOMER, orgCode);
        if (Objects.isNull(o)) {
            return false;
        }
        List<String> list = JSONUtil.toList(o.toString(), String.class);
        if (CollectionUtils.isEmpty(list)) {
            return false;
        }
        return list.contains(customerCode);
    }
}