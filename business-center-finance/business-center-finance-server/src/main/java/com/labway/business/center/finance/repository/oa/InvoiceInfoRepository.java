package com.labway.business.center.finance.repository.oa;

import com.google.common.collect.Lists;
import com.labway.business.center.finance.persistence.entity.oa.invoice.InvoicePublicInfo;
import com.labway.business.center.finance.persistence.mapper.oa.InvoicePublicInfoMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * InvoiceInfoRepository 发票持久层
 *
 * <AUTHOR>
 * @version 2023/04/27 14:47
 **/
@Repository
public class InvoiceInfoRepository {
    @Resource
    private InvoicePublicInfoMapper invoicePublicInfoMapper;
    
    /**
     * 通过发票id获取发票信息
     * @param invoiceIds
     * @return
     */
    public List<InvoicePublicInfo> getInvoiceInfo(List<String> invoiceIds) {
        if (CollectionUtils.isEmpty(invoiceIds)) {
            return Lists.newArrayList();
        }
        return invoicePublicInfoMapper.getInvoicePublicInfoByKeys(invoiceIds);
    }
}