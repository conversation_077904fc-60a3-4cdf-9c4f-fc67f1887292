package com.labway.business.center.finance.repository.center.v2;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.business.center.core.enums.DeleteFlagEnum;
import com.labway.business.center.finance.persistence.entity.v2.FormPayDetail;
import com.labway.business.center.finance.persistence.mapper.v2.FormPayDetailMapper;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public class FormPayDetailRepository {

    @Resource
    private FormPayDetailMapper formPayDetailMapper;


    /**
     * 填充费用单明细信息
     * @param oaFlowNos
     * @return
     */
    public List<FormPayDetail> queryListByOaFlowNos(List<String> oaFlowNos) {
        if (CollectionUtils.isEmpty(oaFlowNos)){
            return Collections.emptyList();
        }

        return formPayDetailMapper.selectList(Wrappers.lambdaQuery(FormPayDetail.class)
                .in(FormPayDetail::getOaFlowNo,oaFlowNos)
                .eq(FormPayDetail::getIsDelete, DeleteFlagEnum.NO_DELETE.getCode()));
    }

    public void saveBatch(List<FormPayDetail> insertPayDetails) {
        if (CollectionUtils.isEmpty(insertPayDetails)) {
            return;
        }
        formPayDetailMapper.insertBatchSomeColumn(insertPayDetails);
    }
}
