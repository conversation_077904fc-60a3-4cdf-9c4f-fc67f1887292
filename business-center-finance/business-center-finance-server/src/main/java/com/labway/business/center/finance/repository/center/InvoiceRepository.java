package com.labway.business.center.finance.repository.center;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.labway.business.center.finance.persistence.entity.Invoice;
import com.labway.business.center.finance.persistence.mapper.InvoiceMapper;
import com.labway.business.center.finance.persistence.params.InvoiceSearchParams;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 2023/07/07 10:18
 **/
@Slf4j
@Repository
public class InvoiceRepository {
    @Resource
    private InvoiceMapper invoiceMapper;

    public Page<Invoice> getInvoicePage(Page<Invoice> searchPage, InvoiceSearchParams searchParams ) {
        LambdaQueryWrapper<Invoice> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Invoice::getOrgId,searchParams.getOrgId());
        if (Objects.nonNull(searchParams.getMakeTimeStart())) {
            queryWrapper.ge(Invoice::getVoucherTime, DateUtil.format(searchParams.getMakeTimeStart(), DatePattern.NORM_DATE_PATTERN));
        }
        if (Objects.nonNull(searchParams.getMakeTimeEnd())) {
            queryWrapper.le(Invoice::getVoucherTime, DateUtil.format(searchParams.getMakeTimeEnd(), DatePattern.NORM_DATE_PATTERN));
        }
        queryWrapper.eq(StringUtils.isNotBlank(searchParams.getPiBillname()), Invoice::getPiBillname,searchParams.getPiBillname())
		        .like(StringUtils.isNotBlank(searchParams.getVoucherNo()), Invoice::getVoucherNo,searchParams.getVoucherNo())
		        .like(StringUtils.isNotBlank(searchParams.getPiAmount()), Invoice::getPiAmount,searchParams.getPiAmount())
		        .like(StringUtils.isNotBlank(searchParams.getCreatedBy()), Invoice::getCreatedBy,searchParams.getCreatedBy())
		        .like(StringUtils.isNotBlank(searchParams.getPiFphm()), Invoice::getPiFphm,searchParams.getPiFphm());
        return invoiceMapper.selectPage(searchPage,queryWrapper);
    }

    public Invoice getMaxVoucherUpdateTime() {
        QueryWrapper<Invoice> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("max(voucher_update_time) as voucher_update_time");
        return invoiceMapper.selectOne(queryWrapper);
    }

    public int insertBatch(List<Invoice> insertList) {
        if (CollectionUtils.isEmpty(insertList)) {
            return 0;
        }
        int i = 0;
        if (insertList.size() > 100) {
            List<List<Invoice>> partition = ListUtils.partition(insertList, 100);
            for (List<Invoice> invoices : partition) {
                i += invoiceMapper.insertBatchSomeColumn(invoices);
            }
        } else {
            i += invoiceMapper.insertBatchSomeColumn(insertList);
        }
        return i;
    }

    public Long countByInvoiceId(String value) {
        if (StringUtils.isBlank(value)) {
            return 0L;
        }
        LambdaQueryWrapper<Invoice> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Invoice::getInvoiceId,value);
        return invoiceMapper.selectCount(queryWrapper);
    }

    public List<Invoice> getInvoiceList(InvoiceSearchParams searchParams) {
        LambdaQueryWrapper<Invoice> queryWrapper = new LambdaQueryWrapper<>();
	    queryWrapper.eq(Invoice::getOrgId,searchParams.getOrgId());
	    if (Objects.nonNull(searchParams.getMakeTimeStart())) {
		    queryWrapper.ge(Invoice::getVoucherTime, DateUtil.format(searchParams.getMakeTimeStart(), DatePattern.NORM_DATE_PATTERN));
	    }
	    if (Objects.nonNull(searchParams.getMakeTimeEnd())) {
		    queryWrapper.le(Invoice::getVoucherTime, DateUtil.format(searchParams.getMakeTimeEnd(), DatePattern.NORM_DATE_PATTERN));
	    }
	    queryWrapper.eq(StringUtils.isNotBlank(searchParams.getPiBillname()), Invoice::getPiBillname,searchParams.getPiBillname())
			    .like(StringUtils.isNotBlank(searchParams.getVoucherNo()), Invoice::getVoucherNo,searchParams.getVoucherNo())
			    .like(StringUtils.isNotBlank(searchParams.getPiAmount()), Invoice::getPiAmount,searchParams.getPiAmount())
			    .like(StringUtils.isNotBlank(searchParams.getCreatedBy()), Invoice::getCreatedBy,searchParams.getCreatedBy())
			    .like(StringUtils.isNotBlank(searchParams.getPiFphm()), Invoice::getPiFphm,searchParams.getPiFphm());
        return invoiceMapper.selectList(queryWrapper);
    }
}