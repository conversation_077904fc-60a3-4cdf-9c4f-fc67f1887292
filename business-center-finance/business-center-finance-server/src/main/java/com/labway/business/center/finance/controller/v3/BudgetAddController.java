package com.labway.business.center.finance.controller.v3;

import com.labway.business.center.finance.constants.FinanceConstants;
import com.labway.business.center.finance.request.v3.budget.add.BudgetAddRequest;
import com.labway.business.center.finance.service.v3.BudgetAddService;
import com.swak.frame.dto.Response;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 追加预算
 *
 * <AUTHOR> on 2025/3/14.
 */
@RestController
@RequestMapping(FinanceConstants.V3_PREFIX + "/budget-add")
public class BudgetAddController {

	@Resource
	private BudgetAddService budgetAddService;

	/**
	 * 追加预算
	 * @Description 这里默认追加到当前年度/月度下
	 */
	@PostMapping("/add")
	public Response<String> addBudget(@RequestBody BudgetAddRequest budgetAddRequest) {
		return budgetAddService.addBudget(budgetAddRequest);
	}
}
