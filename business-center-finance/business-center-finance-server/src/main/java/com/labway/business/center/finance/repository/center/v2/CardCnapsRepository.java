package com.labway.business.center.finance.repository.center.v2;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.labway.business.center.finance.persistence.entity.v2.CardCnaps;
import com.labway.business.center.finance.persistence.mapper.v2.CardCnapsMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 2023/11/23 11:03
 **/
@Repository
public class CardCnapsRepository {
    @Resource
    private CardCnapsMapper cardCnapsMapper;

    public CardCnaps searchByCardNo(String cardNo) {
        if (StringUtils.isBlank(cardNo)) {
            return null;
        }
        LambdaQueryWrapper<CardCnaps> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CardCnaps::getCardNo,cardNo);
        return cardCnapsMapper.selectOne(queryWrapper);
    }

    public Integer save(CardCnaps cardCnaps) {
        if (Objects.isNull(cardCnaps)) {
            return -1;
        }
        return cardCnapsMapper.insert(cardCnaps);
    }
}