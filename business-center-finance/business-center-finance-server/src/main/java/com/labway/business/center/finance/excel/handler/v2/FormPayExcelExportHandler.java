package com.labway.business.center.finance.excel.handler.v2;

import cn.hutool.core.date.DateUtil;
import com.alibaba.excel.EasyExcel;
import com.labway.business.center.finance.constants.CityFlagEnums;
import com.labway.business.center.finance.constants.FormPayStatus;
import com.labway.business.center.finance.excel.ExcelExportConstants;
import com.labway.business.center.finance.excel.handler.CustomCellWriteHandler;
import com.labway.business.center.finance.excel.param.v2.FormPayExportDTO;
import com.labway.business.center.finance.util.ResponseUtil;

import javax.servlet.http.HttpServletResponse;
import java.io.FileOutputStream;
import java.net.URLEncoder;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * 费用审核页面导出
 *
 * <AUTHOR>
 * @version 2024/01/15 13:19
 **/
public interface FormPayExcelExportHandler {
    /**
     * 导出数据处理
     * @param formPayStatus
     * @param exportData
     * @param response
     */
    void exportDataHandler(FormPayStatus formPayStatus , List<FormPayExportDTO> exportData , HttpServletResponse response);

    /**
     * 生成excel
     * @param formPayStatus
     * @param includeColumnFiledNames
     * @param exportData
     * @param response
     */
    default void responseExcel(FormPayStatus formPayStatus ,Set<String> includeColumnFiledNames, List<FormPayExportDTO> exportData, HttpServletResponse response) {
        exportData.forEach(formPay -> {
            if (Objects.isNull(formPay.getCityFlag())) {
                formPay.setCityFlag(CityFlagEnums.REMOTE.getCityFlag());
            }
        });
        try {
            // 获取文件名称
            String formPayExcelName = ExcelExportConstants.getFormPayExcelName(formPayStatus) + DateUtil.today();
            String fileName = URLEncoder.encode(formPayExcelName,"UTF-8").replaceAll("\\+", "%20");
            response.setContentType("application/vnd.ms-excel;charset=UTF-8");
            response.setCharacterEncoding("UTF-8");
            /*请求头设置，Content-Disposition为下载标识，attachment标识以附件方式下载*/
            response.setHeader("Content-Disposition", "attachment;fileName=" + fileName + ".xls");
            response.setHeader("Access-Control-Expose-Headers","Content-Disposition");
            // 这里需要设置不关闭流
            EasyExcel.write(response.getOutputStream(), FormPayExportDTO.class)
                    .includeColumnFiledNames(includeColumnFiledNames)
                    .registerWriteHandler(new CustomCellWriteHandler())
                    .sheet(formPayExcelName.split("-")[1])
                    .doWrite(exportData);
        } catch (Exception e) {
            ResponseUtil.responseFailMessage(response, 500, ExcelExportConstants.EXPORT_ERR);
        }
    }
}