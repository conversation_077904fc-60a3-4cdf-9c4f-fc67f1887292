
package com.labway.business.center.finance.persistence.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.swak.frame.dto.base.Entity;
import com.baomidou.mybatisplus.annotation.IdType;
import java.sql.Timestamp;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * <B>Description: 车号表实体类</B>
 * </P>
 *
 * <AUTHOR>
 * @version 1.0
 */
@TableName("tb_car_no")
@ApiModel(value="CarNo对象", description="车号表")
public class CarNo extends Entity{

    /**
     * 序列号
     */
    private static final long serialVersionUID = 1L;
    /**
    * 主键
    */
    @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
    * 自定义档案主键
    */
    @ApiModelProperty(value = "自定义档案主键")
    @TableField("defdoc_id")
    private String defdocId;
    /**
    * 自定义档案列表主键
    */
    @ApiModelProperty(value = "自定义档案列表主键")
    @TableField("defdoclist_id")
    private String defdoclistId;
    /**
    * 所属组织
    */
    @ApiModelProperty(value = "所属组织")
    @TableField("org_id")
    private String orgId;
    /**
    * 集团ID
    */
    @ApiModelProperty(value = "集团ID")
    @TableField("group_id")
    private String groupId;
    /**
    * 档案编码
    */
    @ApiModelProperty(value = "档案编码")
    @TableField("code")
    private String code;
    /**
    * 档案名称
    */
    @ApiModelProperty(value = "档案名称")
    @TableField("name")
    private String name;
    /**
    * 是否默认
    */
    @ApiModelProperty(value = "是否默认")
    @TableField("default_able")
    private Integer defaultAble;
    /**
    * 上级档案ID
    */
    @ApiModelProperty(value = "上级档案ID")
    @TableField("parent_id")
    private String parentId;
    /**
    * 创建时间
    */
    @ApiModelProperty(value = "创建时间")
    @TableField("create_time")
    private LocalDateTime createTime;
    public Long getId() {
    return id;
    }

    public void setId(Long id) {
    this.id = id;
    }
    public String getDefdocId() {
    return defdocId;
    }

    public void setDefdocId(String defdocId) {
    this.defdocId = defdocId;
    }
    public String getDefdoclistId() {
    return defdoclistId;
    }

    public void setDefdoclistId(String defdoclistId) {
    this.defdoclistId = defdoclistId;
    }
    public String getOrgId() {
    return orgId;
    }

    public void setOrgId(String orgId) {
    this.orgId = orgId;
    }
    public String getGroupId() {
    return groupId;
    }

    public void setGroupId(String groupId) {
    this.groupId = groupId;
    }
    public String getCode() {
    return code;
    }

    public void setCode(String code) {
    this.code = code;
    }
    public String getName() {
    return name;
    }

    public void setName(String name) {
    this.name = name;
    }
    public Integer getDefaultAble() {
    return defaultAble;
    }

    public void setDefaultAble(Integer defaultAble) {
    this.defaultAble = defaultAble;
    }
    public String getParentId() {
    return parentId;
    }

    public void setParentId(String parentId) {
    this.parentId = parentId;
    }
   

    @Override
    public String toString() {
    return "CarNo{" +
            "id=" + id +
            ", defdocId=" + defdocId +
            ", defdoclistId=" + defdoclistId +
            ", orgId=" + orgId +
            ", groupId=" + groupId +
            ", code=" + code +
            ", name=" + name +
            ", defaultAble=" + defaultAble +
            ", parentId=" + parentId +
            ", createTime=" + createTime +
    "}";
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
}