package com.labway.business.center.finance.persistence.entity.v2;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 3.1.4.交易明细查询
 *
 * <AUTHOR>
 */
@Data
@TableName("tb_bank_transaction_detail")
public class BankTransactionDetail implements Serializable {
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 业务单元
     */
    private String orgId;

    /**
     * 交易后余额
     */
    private BigDecimal accountBalance;
    /**
     * 账户名称
     */
    private String accountName;
    /**
     * 账户性质
     */
    private String accountNature;
    /**
     * 银行账号
     */
    private String accountNo;
    /**
     * 账户状态
     */
    private String accountStatus;
    /**
     * 关联客户号
     */
    private String associatedCustomerNumber;
    /**
     * 银行流水号
     */
    private String bankSerialNumber;
    /**
     * 交易日期
     */
    private Date bankTransactionDate;
    /**
     * 银行类型
     */
    private String bankType;
    /**
     * 对账码
     */
    private String checkCode;
    /**
     * 币种
     */
    private String currency;
    /**
     * 明细来源
     */
    private String detailSource;
    /**
     * 1-当日明细;
     * 2-历史明细
     * 只存 历明细
     */
    private String detailType;
    /**
     * 摘要
     */
    private String digest;
    /**
     * 业务参考号
     */
    private String erpSerialNumber;
    /**
     * 发生的金额
     */
    private BigDecimal incurredAmount;
    /**
     * 借贷类型
     * 1-借;2-贷
     */
    private String loanType;
    /**
     * 客商名称
     */
    private String merchantName;
    /**
     * 客商编号
     */
    private String merchantNumber;
    /**
     * 开户行名称
     */
    private String openBank;
    /**
     * 对方账号
     */
    private String oppositeAccount;
    /**
     * 对方户名
     */
    private String oppositeName;
    /**
     * 对方开户行
     */
    private String oppositeOpeningBank;
    /**
     * 款项性质代码
     */
    private String paymentNature;
    /**
     * 款项性质匹配标记
     * 1-未匹配;2-手工处理;3-系统处理;4-匹配失败
     */
    private String paymentNatureFlag;
    /**
     * 用途
     */
    private String purpose;
    /**
     * 备注
     */
    private String remark;
    /**
     * 交易代码
     */
    private String transactionCode;
    /**
     * 交易流水号
     */
    private long transactionSerialNumber;
    /**
     * 单位编码
     */
    private String unitCode;
    /**
     * 单位名称
     */
    private String unitName;
    /**
     * 起息日
     */
    private Date valueDate;

    /**
     * 是否有回单
     */
    private Boolean hasFile;

    /**
     * 回单文件地址
     */
    private String fileUrl;

    private Date createTime;

    private Date updateTime;
}