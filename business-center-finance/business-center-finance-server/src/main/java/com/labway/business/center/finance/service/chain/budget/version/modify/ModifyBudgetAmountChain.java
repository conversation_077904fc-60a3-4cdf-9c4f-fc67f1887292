package com.labway.business.center.finance.service.chain.budget.version.modify;

import org.apache.commons.chain.impl.ChainBase;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 修改预算金额责任链
 *
 * <AUTHOR> on 2025/4/1.
 */
@Component
public class ModifyBudgetAmountChain extends ChainBase implements InitializingBean {

	@Resource
	private ModifyBudgetAmountLimitCommand modifyBudgetAmountLimitCommand;

	@Resource
	private ModifyBudgetAmountValidAndInitCommand modifyBudgetAmountValidAndInitCommand;

	@Resource
	private ModifyBudgetAmountHandlerParentCommand modifyBudgetAmountHandlerParentCommand;

	@Resource
	private ModifyBudgetAmountUpdateCommand modifyBudgetAmountUpdateCommand;

	@Resource
	private ModifyAvBudgetAmountCommand modifyAvBudgetAmountCommand;

	@Resource
	private ModifyBudgetAmountLogCommand modifyBudgetAmountLogCommand;

	@Override
	public void afterPropertiesSet() throws Exception {

		// 限流
		addCommand(modifyBudgetAmountLimitCommand);

		// 校验 & 基础数据获取
		addCommand(modifyBudgetAmountValidAndInitCommand);

		// 处理父级预算
		addCommand(modifyBudgetAmountHandlerParentCommand.getDefaultChainProvider());

		// 递归调用处理父级
		addCommand(modifyBudgetAmountHandlerParentCommand);

		// 修改对应的金额
		addCommand(modifyBudgetAmountUpdateCommand);

		// 修改预算可用金额
		addCommand(modifyAvBudgetAmountCommand);

		// 发送操作日志
		addCommand(modifyBudgetAmountLogCommand);

		addCommand(context -> PROCESSING_COMPLETE);
	}
}
