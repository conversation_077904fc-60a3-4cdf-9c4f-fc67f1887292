package com.labway.business.center.finance.service.impl.v2;

import cn.hutool.core.date.DateUtil;
import com.labway.business.center.core.config.Pager;
import com.labway.business.center.core.enums.Constants;
import com.labway.business.center.core.enums.SyncDataFrom;
import com.labway.business.center.core.log.SyncLog;
import com.labway.business.center.finance.converter.v2.SupplierConverter;
import com.labway.business.center.finance.dto.v2.SupplierDTO;
import com.labway.business.center.finance.persistence.entity.ncc.NccBank;
import com.labway.business.center.finance.persistence.entity.v2.Supplier;
import com.labway.business.center.finance.persistence.mapper.ncc.NccSupplierMapper;
import com.labway.business.center.finance.repository.center.v2.SupplierRepository;
import com.labway.business.center.finance.request.v2.SupplierSearchRequest;
import com.labway.business.center.finance.request.v2.SyncSupplierRequest;
import com.labway.business.center.finance.service.v2.SupplierService;
import com.swak.frame.dto.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 供应商
 *
 * <AUTHOR> Tianhao
 * @version 2023/12/15 15:39
 **/
@Slf4j
@DubboService
public class SupplierServiceImpl implements SupplierService {
    @Resource
    private NccSupplierMapper nccSupplierMapper;
    @Resource
    private SupplierRepository supplierRepository;
    @Resource
    private SupplierConverter supplierConverter;
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    private static final String FINANCE_SUPPLIER_SYNC_KET = "finance:supplier:sync:time";


    /**
     * 全量同步供应商信息
     *
     * @return
     */
    @Override
    @SyncLog(dataFrom = SyncDataFrom.NCC,taskName = "全量同步供应商信息")
    public Response<Long> syncAllSupplierInfo() {
        log.warn("全量同步供应商信息开始，请确保库中没有数据，否则会导致重复");
        StopWatch watch = new StopWatch("syncAllSupplierInfo");
        log.info("开始全量同步供应商信息,startTime={}",System.currentTimeMillis());
        Long count = nccSupplierMapper.countNccSupplier();
        if (count == 0) {
            watch.stop();
            return Response.success();
        }
        int pageSize = 300;
        Long total = 0L;
        Pager<String> pager = new Pager<>(pageSize, count);
        int totalPage = pager.getTotalPage();
        for (int i = 1; i <= totalPage; i++) {
            pager = new Pager<>(i, pageSize);
            watch.start("washNccBankToCenterDatabase:"+i);
            List<Supplier> suppliers = nccSupplierMapper.searchSupplierByPager(pager);
            total += proccessBatchSaveSupplier(suppliers);
            watch.stop();
        }
        log.info("结束全量同步供应商信息,{}",watch.prettyPrint());
        return Response.success(total);
    }

    /**
     * 增量同步供应商
     *
     * @return
     */
    @Override
    public Response<Long> syncSupplierInfo() {
        // 最后一次更新时间
        String lastTime = stringRedisTemplate.opsForValue().get(FINANCE_SUPPLIER_SYNC_KET);
        String now = DateUtil.now();
        if (StringUtils.isBlank(lastTime)) {
            lastTime = now;
        }
        StopWatch watch = new StopWatch("syncSupplierInfo");
        watch.start("syncSupplierInfo");
        log.info("开始增量同步供应商信息,startTime={}",System.currentTimeMillis());
        List<Supplier> suppliers = nccSupplierMapper.searchSupplierByTime(lastTime);
        long total = supplierDataHandler(suppliers);
        watch.stop();
        log.info("结束增量同步供应商信息,{}",watch.prettyPrint());
        stringRedisTemplate.opsForValue().set(FINANCE_SUPPLIER_SYNC_KET,now);
        return Response.success(total);
    }

    /**
     * 通过名称或编码同步
     *
     * @param request
     * @return
     */
    @Override
    public Response<Long> syncSupplierInfoByParams(SyncSupplierRequest request) {
        StopWatch watch = new StopWatch("syncSupplierInfo");
        watch.start("syncSupplierInfo");
        log.info("开始增量同步供应商信息,startTime={}",System.currentTimeMillis());
        List<Supplier> suppliers = nccSupplierMapper.searchSupplierByParmas(request.getSupplierCode(),request.getSupplierName());
        long total = supplierDataHandler(suppliers);
        watch.stop();
        log.info("结束增量同步供应商信息,{}",watch.prettyPrint());
        return Response.success(total);
    }

    /**
     * 供应商查询（辅助核算）
     *
     * @param request
     * @return
     */
    @Override
    public Response<List<SupplierDTO>> searchSupplier(SupplierSearchRequest request) {
        List<Supplier> suppliers = supplierRepository.searchListBySupplierName(request.getSupplierName(), request.getOrgCode());
        return Response.success(supplierConverter.convertEntity2SupplierDTOList(suppliers).stream().distinct().collect(Collectors.toList()));
    }

    private long supplierDataHandler(List<Supplier> suppliers) {
        supplierRepository.removeInfo(suppliers);

        return proccessBatchSaveSupplier(suppliers);
    }

    /**
     * 全量同步供应商信息
     * @param suppliers
     * @return
     */
    private Long proccessBatchSaveSupplier(List<Supplier> suppliers) {
        if (CollectionUtils.isEmpty(suppliers)) {
            return 0L;
        }
        Date date = new Date();
        suppliers.forEach(item -> item.setCreateTime(date));
        return supplierRepository.saveBatch(suppliers);
    }
}