 package com.labway.business.center.finance.persistence.mapper;

import java.util.List;

import com.labway.business.center.finance.persistence.params.SearchLedger;
import com.labway.business.center.finance.persistence.params.SearchLedgerCount;
import com.labway.business.center.finance.request.LedgerAccountListRequest;
import org.apache.ibatis.annotations.Param;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.labway.business.center.core.config.Pager;
import com.labway.business.center.core.injector.ExtBaseMapper;
import com.labway.business.center.finance.persistence.entity.LedgerAccount;

/**
 * 中台业务库会计科目
 * <AUTHOR>
 * @date 2023/03/21
 */
@DS("master")
public interface LedgerAccountMapper extends ExtBaseMapper<LedgerAccount> {
    
    /**
     * 获取业务中台会计科目信息
     * @param pager
     * @return
     */
    public List<LedgerAccount> getLedgerAccountBySearch(@Param("pager") Pager<SearchLedger> pager);
    
    /**
     * 获取业务中台信息条目
     * @param name
     * @return
     */
    public Long countLedgerAccountBySearch(@Param("request") SearchLedgerCount searchLedgerCount);
    
    /**
     * 批量更新数据
     * @param updateLedgerAccounts
     * @return
     */
    Integer batchUpdateLedgerAccount(@Param("list") List<LedgerAccount> updateLedgerAccounts);
}
