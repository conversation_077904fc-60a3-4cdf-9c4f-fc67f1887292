package com.labway.business.center.finance.service.chain.budget.use;

import com.labway.business.center.finance.constants.BudgetUseEnums;
import com.labway.business.center.finance.persistence.entity.BudgetUnit;
import com.labway.business.center.finance.persistence.entity.v3.BudgetDept;
import com.labway.business.center.finance.repository.center.BudgetUnitRepository;
import com.labway.business.center.finance.repository.center.v3.BudgetDeptRepository;
import com.labway.business.center.finance.request.v3.budget.BudgetUseRequest;
import com.swak.frame.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 校验 & 获取基础信息
 *
 * <AUTHOR> on 2025/4/8.
 */
@Slf4j
@Component
public class BudgetUseBaseValidAndInitCommand implements Command {

	@Resource
	private BudgetUnitRepository budgetUnitRepository;

	@Resource
	private BudgetDeptRepository budgetDeptRepository;

	@Override
	public boolean execute(Context c) throws Exception {
		BudgetUseContext context = BudgetUseContext.from(c);
		BudgetUseRequest request = context.request;

		context.needOaNo = BudgetUseEnums.needOaNo(request.getUseType());
		if (context.needOaNo && StringUtils.isBlank(request.getOaNo())) {
			throw new BizException(String.format("当前操作：[%s],请输入OA单号", BudgetUseEnums.getDesc(request.getUseType())));
		}

		List<BudgetUseRequest.BudgetUseDetailRequest> budgetUseDetailRequestList = request.getBudgetUseDetailRequestList();
		context.feeTypeIds = budgetUseDetailRequestList.stream().map(BudgetUseRequest.BudgetUseDetailRequest::getOaFeeTypeId)
				.filter(Objects::nonNull).collect(Collectors.toList());

		if (context.feeTypeIds.size() != budgetUseDetailRequestList.size()) {
			throw new BizException("请选择费用类别");
		}

		List<BudgetDept> budgetDeptList = new ArrayList<>();


		Map<String, List<BudgetUseRequest.BudgetUseDetailRequest>> budgetUseDetailMap = budgetUseDetailRequestList.stream().collect(Collectors.groupingBy(BudgetUseRequest.BudgetUseDetailRequest::getOaBudgetUnitName));
		List<BudgetUnit> budgetUnits = budgetUnitRepository.getBudgetUnitsByBudgetUnitNames(budgetUseDetailMap.keySet());
		if (CollectionUtils.isEmpty(budgetUnits)) {
			throw new BizException("所选预算单元不存在");
		}

		for (Map.Entry<String, List<BudgetUseRequest.BudgetUseDetailRequest>> entry : budgetUseDetailMap.entrySet()) {
			List<BudgetUseRequest.BudgetUseDetailRequest> values = entry.getValue();
			if (CollectionUtils.isEmpty(values)) {
				continue;
			}

			for (BudgetUseRequest.BudgetUseDetailRequest value : values) {
				BudgetUnit budgetUnit = budgetUnitRepository.getBudgetUnitsByBudgetUnitName(value.getOaBudgetUnitName());
				if (Objects.isNull(budgetUnit)) {
					throw new BizException(String.format("所选预算单元[%s]不存在", value.getOaBudgetUnitName()));
				}
				BudgetDept budgetDept = budgetDeptRepository.searchBudgetDeptByNameAndUnit(value.getOaBudgetDeptName(), budgetUnit.getOaBudgetId());
				if (Objects.isNull(budgetDept)) {
					throw new BizException(String.format("预算单元[%s]预算部门[%s]不存在",value.getOaBudgetUnitName(), value.getOaBudgetDeptName()));
				}
				budgetDeptList.add(budgetDept);
			}
		}


		context.put(BudgetUseContext.BUDGET_UNIT, budgetUnits);
		context.put(BudgetUseContext.BUDGET_DEPT, budgetDeptList);
		return CONTINUE_PROCESSING;
	}
}
