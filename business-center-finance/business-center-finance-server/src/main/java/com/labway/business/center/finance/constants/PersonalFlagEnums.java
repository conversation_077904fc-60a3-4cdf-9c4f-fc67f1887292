package com.labway.business.center.finance.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 对公/对私标志
 * <AUTHOR>
 * @version 2024/04/09 13:53
 **/
@Getter
@AllArgsConstructor
public enum PersonalFlagEnums {
    PUBLIC("0","对公"),PRIVACY("1","对私");

    private String code;

    private String desc;

    public static boolean isPublic(String code) {
        return PUBLIC.getCode().equals(code);
    }

    public static boolean isPrivacy(String code) {
        return PRIVACY.getCode().equals(code);
    }
}