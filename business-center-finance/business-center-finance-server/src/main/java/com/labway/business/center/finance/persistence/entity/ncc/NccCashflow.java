
package com.labway.business.center.finance.persistence.entity.ncc;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * <B>Description: 现金流量项目实体类</B>
 * </P>
 *
 * <AUTHOR>
 * @version 1.0
 */
@ApiModel(value="Cashflow对象", description="现金流量项目")
@Data
public class NccCashflow{


    /**
    * 	现金流量项目主键
    */
    @ApiModelProperty(value = "	现金流量项目主键")
    private String cashflowId;
    /**
    * 组织ID
    */
    @ApiModelProperty(value = "组织ID")
    private String orgId;
    /**
    * 集团ID
    */
    @ApiModelProperty(value = "集团ID")
    private String groupId;
    /**
    * 现金流量项目编码
    */
    @ApiModelProperty(value = "现金流量项目编码")
    private String code;
    /**
    * 现金流量项目名称
    */
    @ApiModelProperty(value = "现金流量项目名称")
    private String name;
    /**
    * 上级项目
    */
    @ApiModelProperty(value = "上级项目")
    private String parentId;
    /**
    * 现金流量项目类型 (itemtype)
    */
    @ApiModelProperty(value = "现金流量项目类型 (itemtype)")
    private Integer itemType;
    /**
    * 是否是主表项目
    */
    @ApiModelProperty(value = "是否是主表项目")
    private String isMain;
    /**
    * 内部码
    */
    @ApiModelProperty(value = "内部码")
    private String innerCode;
    /**
    * 启动状态，1=未启用;2=已启用;3=已停用;
    */
    @ApiModelProperty(value = "启动状态，1=未启用;2=已启用;3=已停用;")
    private Integer enableState;
    
}