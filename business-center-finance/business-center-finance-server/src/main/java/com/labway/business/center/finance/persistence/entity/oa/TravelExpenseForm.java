package com.labway.business.center.finance.persistence.entity.oa;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 差旅报销单
 *
 * <AUTHOR>
 * @date 2023/04/18
 */
@Data
public class TravelExpenseForm {
    
    /**
     * 公司名称
     */
    private String companyName;
    
    /**
     * 申请人
     */
    private Long applyUser;
    
    /**
     * 申请人用户名
     */
    private String applyUserName;
    
    /**
     * 部门ID
     */
    private Long deptId;
    
    /**
     * 开始时间
     */
    private Date startDate;
    
    /**
     * 审核通过时间
     */
    private Date approveDate;
    
    /**
     * 真实金额
     */
    private BigDecimal realAmount;
    
    /**
     * OA 表单编码
     */
    private Long oaFlowNo;
    
    /**
     * OA 表单编码
     */
    private String oaNo;

    /**
     * 开户行名称
     */
    private String receiveBankName;

    /**
     * 收款人
     */
    private String receiveName;

    /**
     * 收款账号
     */
    private String receiveBankAccount;

    /**
     * 实报金额
     */
    private BigDecimal totalAmount;
}
