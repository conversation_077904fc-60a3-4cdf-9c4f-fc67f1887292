 package com.labway.business.center.finance.persistence.entity;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.swak.frame.dto.base.Entity;

import lombok.Data;

/**
 * 同步OA记录日志表
 * <AUTHOR>
 * @date 2023/04/21
 */
@TableName("tb_sync_oa_record")
@Data
public class SyncOaRecord extends Entity {
    @TableId(type = IdType.AUTO)
    private Long id;
    private Long oaFlowNo;
    /**
     * 任务状态 状态0-未完成1-已完成
     */
    private Integer status;
    /**
     * 凭证类型 001 费用报销单 002 差旅报销单
     */
    private String template;
    private LocalDateTime createTime;
    

}
