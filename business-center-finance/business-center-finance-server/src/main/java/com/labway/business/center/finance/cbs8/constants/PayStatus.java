package com.labway.business.center.finance.cbs8.constants;

import lombok.Getter;

/**
 * 支付状态
 * <AUTHOR>
 * @version 2023/10/08 14:57
 **/
public enum PayStatus {
    WAIT_PAY(0,"待支付"),
    PUSH_WAIT_PAY(1,"推送待支付"),
    PUSH_PAYED(2,"已支付"),
    PAY_ERR(3,"支付失败"),
    NOT_PAY(4,"不付款"),
    OFF_LINE_PAY(5,"线下付款"),
    ;
    private Integer code;
    private String value;

    PayStatus(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public Integer getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }
}