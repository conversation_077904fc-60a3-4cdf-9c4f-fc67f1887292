 package com.labway.business.center.finance.persistence.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.swak.frame.dto.base.Entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class DefineDocument extends Entity {

    /**
     * 自定义档案主键
     */
     @ApiModelProperty(value = "自定义档案主键")
     @TableField("defdoc_id")
     private String defdocId;
     /**
     * 自定义档案列表主键
     */
     @ApiModelProperty(value = "自定义档案列表主键")
     @TableField("defdoclist_id")
     private String defdoclistId;
     /**
     * 所属组织
     */
     @ApiModelProperty(value = "所属组织")
     @TableField("org_id")
     private String orgId;
     /**
     * 集团ID
     */
     @ApiModelProperty(value = "集团ID")
     @TableField("group_id")
     private String groupId;
     /**
     * 档案编码
     */
     @ApiModelProperty(value = "档案编码")
     @TableField("code")
     private String code;
     /**
     * 档案名称
     */
     @ApiModelProperty(value = "档案名称")
     @TableField("name")
     private String name;
     /**
     * 上级档案ID
     */
     @ApiModelProperty(value = "上级档案ID")
     @TableField("parent_id")
     private String parentId;
}
