package com.labway.business.center.finance.persistence.mapper.oa;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.labway.business.center.core.config.Pager;
import com.labway.business.center.finance.persistence.entity.oa.SupplyChainForm;
import com.labway.business.center.finance.persistence.params.OaUpdateFileUrl;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 供应链汇款申请单mapper
 *
 * <AUTHOR>
 * @date 2023/04/18
 */
@DS("oa")
public interface SupplyChainFormMapper extends BaseMapper<SupplyChainForm> {

    /**
     * 获取供应链汇款申请单
     *
     * @param pager
     * @return
     */
    public List<SupplyChainForm> getSupplyChainFormList(@Param("pager") Pager<String> pager);

    List<SupplyChainForm> selectListByOaFlowNos(@Param("list") List<Long> formIds);

    List<SupplyChainForm> selectListByOaNos(@Param("list") List<String> washOaNoList);

    List<String> getLocalNoHasOaNo(@Param("list") List<String> localOaNoList, @Param("time") String syncTime);

    Long updateOaSupplyChainForm(@Param("list") List<OaUpdateFileUrl> updateFileUrlList);

    Long updateOaSupplyChainFormForNew(@Param("list") List<OaUpdateFileUrl> updateFileUrlList);
}
