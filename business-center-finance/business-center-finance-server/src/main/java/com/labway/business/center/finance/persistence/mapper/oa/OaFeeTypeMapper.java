 package com.labway.business.center.finance.persistence.mapper.oa;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.labway.business.center.finance.persistence.entity.oa.OaFeeType;

/**
  * OA 费用类别
  * <AUTHOR>
  * @date 2023/04/18
  */
@DS("oa")
 public interface OaFeeTypeMapper extends BaseMapper<OaFeeType> {
    
    /**
     * 获取OA的费用类型
     * @param enumId
     * @return
     */
    public List<OaFeeType> getOaFeeTypes(@Param("enumIds") List<String> enumId);

}
