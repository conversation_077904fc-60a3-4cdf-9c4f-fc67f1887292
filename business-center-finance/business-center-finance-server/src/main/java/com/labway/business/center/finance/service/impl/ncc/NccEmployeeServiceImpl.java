package com.labway.business.center.finance.service.impl.ncc;

import com.labway.business.center.finance.repository.ncc.NccEmployeeRepository;
import com.labway.business.center.finance.service.NccEmployeeService;
import com.swak.frame.dto.Response;

import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;

/**
 * NccEmployeeServiceImpl
 *
 * <AUTHOR>
 * @version 2023/04/23 17:46
 **/
@DubboService
public class NccEmployeeServiceImpl implements NccEmployeeService {
    @Resource
    private NccEmployeeRepository nccEmployeeRepository;
    
    /**
     * 通过自定义的用户编码 来反差ncc中的用户pk值
     *
     * @param oaFlowNo
     * @return
     */
    @Override
    public  Response<String> getEmployeePk(String oaFlowNo) {
        String psnId= nccEmployeeRepository.getEmployeePk(oaFlowNo);
        Response<String> response= Response.success();
        response.setData(psnId);
        return response;
    }
}