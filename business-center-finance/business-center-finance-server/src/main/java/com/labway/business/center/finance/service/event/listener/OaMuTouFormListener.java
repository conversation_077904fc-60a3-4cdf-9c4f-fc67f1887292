package com.labway.business.center.finance.service.event.listener;

import com.google.common.collect.Lists;
import com.labway.business.center.finance.service.event.OaMuTouFormEvent;
import com.labway.business.center.finance.service.v2.FormPayWriteService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description 募投单据事件监听
 * @Date 2024/7/8 10:49
 */
@Slf4j
@Component
public class OaMuTouFormListener {

	@Resource
	private FormPayWriteService formPayWriteService;

	@EventListener
	public void oaMuTouFormListener(OaMuTouFormEvent event) {
		log.info("募投处理监听：[{}]，开始处理",event.getOaNo());

		// 募投的流程编号
		String oaNo = event.getOaNo();

		// 清洗数据
		formPayWriteService.washOaNo2Vouchers(Lists.newArrayList(oaNo),Boolean.TRUE);
		log.info("募投处理监听：[{}]，处理完毕",event.getOaNo());
	}
}
