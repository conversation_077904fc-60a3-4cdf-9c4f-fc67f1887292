

package com.labway.business.center.finance.persistence.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.swak.frame.dto.base.Entity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * <B>Description: 费用类别实体类</B>
 * </P>
 *
 * <AUTHOR>
 * @version 1.0
 */
@TableName("tb_fee_type")
@ApiModel(value="FeeType对象", description="费用类别")
@Data
public class FeeType extends Entity{

    /**
     * 序列号
     */
    private static final long serialVersionUID = 1L;
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
    * 费用名称
    */
    @ApiModelProperty(value = "费用名称")
    @TableField("name")
    private String name;
    /**
    * 编码
    */
    @ApiModelProperty(value = "编码")
    @TableField("code")
    private String code;
    /**
    * 父节点
    */
    @ApiModelProperty(value = "父节点")
    @TableField("parent_id")
    private Long parentId;
    /**
     * OA费用类别ID
     */
    @TableField("oa_feetype_id")
    private Long oaFeetypeId;
    
    /**
     * 状态0：停用1：启用3：删除
     */
    @TableField("status")
    private Integer status;
}