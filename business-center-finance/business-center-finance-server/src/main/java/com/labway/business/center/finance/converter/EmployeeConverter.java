 package com.labway.business.center.finance.converter;

import org.mapstruct.Mapper;

import com.labway.business.center.finance.dto.EmployeeDTO;
import com.labway.business.center.finance.persistence.entity.Employee;

/**
 * 人员关系转换
 * <AUTHOR>
 * @date 2023/03/29
 */
@Mapper(componentModel = "spring")
 public interface EmployeeConverter {
    
    public EmployeeDTO covertEmployee2DTO(Employee employee);

}
