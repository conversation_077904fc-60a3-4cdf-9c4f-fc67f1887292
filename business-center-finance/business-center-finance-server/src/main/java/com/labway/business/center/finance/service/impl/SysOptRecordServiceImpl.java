package com.labway.business.center.finance.service.impl;

import com.labway.business.center.finance.converter.OptRecordConverter;
import com.labway.business.center.core.aop.param.OptRecordDTO;
import com.labway.business.center.finance.persistence.entity.SysOptRecord;
import com.labway.business.center.finance.repository.center.SysOptRecordRepository;
import com.labway.business.center.finance.service.SysOptRecordService;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.scheduling.annotation.Async;

import javax.annotation.Resource;

/**
 * SysOptRecordServiceImpl 日志记录服务实现
 *
 * <AUTHOR>
 * @version 2023/04/11 17:55
 **/
@DubboService
public class SysOptRecordServiceImpl implements SysOptRecordService {
    @Resource
    private SysOptRecordRepository sysOptRecordRepository;
    @Resource
    private OptRecordConverter optRecordConverter;
    
    @Async
    @Override
    public void insertOptRecord(OptRecordDTO optRecordDTO) {
        SysOptRecord sysOptRecord = optRecordConverter.convertOptRecordDTO2OptRecord(optRecordDTO);
        sysOptRecordRepository.insertOptRecord(sysOptRecord);
    }
}