package com.labway.business.center.finance.persistence.mapper.v2;

import com.labway.business.center.core.injector.ExtBaseMapper;
import com.labway.business.center.finance.persistence.entity.v2.BankTransactionDetail;
import com.labway.business.center.finance.persistence.params.Cbs8ReceiptUpdateParams;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2024/05/06 13:38
 **/
public interface BankTransactionDetailMapper extends ExtBaseMapper<BankTransactionDetail> {
    Long updateCbs8Receipt(@Param("list") List<Cbs8ReceiptUpdateParams> result);
}