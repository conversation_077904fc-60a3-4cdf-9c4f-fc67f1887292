package com.labway.business.center.finance.service.impl;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

import cn.hutool.json.JSONUtil;
import com.labway.business.center.core.enums.Constants;
import com.labway.business.center.core.enums.ResultCode;
import com.labway.business.center.core.enums.SyncDataFrom;
import com.labway.business.center.core.log.SyncLog;
import com.labway.business.center.core.user.LoginUserInfoUtil;
import com.labway.business.center.core.util.RedisUtils;
import com.labway.business.center.finance.constants.FinanceConstants;
import com.labway.business.center.finance.converter.OrgsMappingConverter;
import com.labway.business.center.finance.repository.center.DataPermissionRepository;
import com.labway.business.center.finance.request.OrgMappingRequest;
import com.labway.business.center.finance.request.v2.OrgSettingCheckRequest;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;

import com.labway.business.center.finance.dto.OrgsMappingDTO;
import com.labway.business.center.finance.persistence.entity.OrgsMapping;
import com.labway.business.center.finance.persistence.entity.ncc.NccOrgs;
import com.labway.business.center.finance.repository.center.OrgsMappingRepository;
import com.labway.business.center.finance.repository.ncc.NccOrgsRepository;
import com.labway.business.center.finance.service.OrgMappingService;
import com.swak.frame.dto.Response;

import lombok.extern.slf4j.Slf4j;

/**
 * 业务单元组织服务类
 *
 * <AUTHOR>
 * @date 2023/03/22
 */
@DubboService
@Slf4j
public class OrgMappingServiceImpl implements OrgMappingService {
    
    @Resource
    private NccOrgsRepository nccOrgsRepository;
    @Resource
    private OrgsMappingRepository orgsMappingRepository;
    @Resource
    private OrgsMappingConverter orgsMappingConverter;
    @Resource
    private RedisUtils redisUtils;


    @PostConstruct
    public void initOrgInfo() {
        List<OrgsMapping> orgsMappings = orgsMappingRepository.getSettingTrueList();
        if (CollectionUtils.isNotEmpty(orgsMappings)) {
            redisUtils.sSet(FinanceConstants.ORG_INFO_KEY, orgsMappings.stream().map(OrgsMapping::getOrgId).toArray());
        }
    }

    
    @Override
    @SyncLog(dataFrom = SyncDataFrom.NCC,taskName = Constants.SYNC_TASK_NAME_NCC_ORG_MAPPING)
    public Response<?> washNccOrgsToCenterDatabase() {
        StopWatch watch = new StopWatch("washNccOrgsToCenterDatabase");
        watch.start("count");
        log.info("开始清洗业务单元组织信息,startTime={}", System.currentTimeMillis());
        Response<?> response = Response.success();
        
        List<NccOrgs> accasses = nccOrgsRepository.getAllNccOrgs();
        Long total = (long) proccessBatchSaveOrgMapping(accasses);
        watch.stop();
        
        log.info("结束清洗业务单元信息,{}", watch.prettyPrint());
        return Response.success(total);
    }
    
    /**
     * 处理批量入库
     *
     * @param nccOrgs
     */
    private Integer proccessBatchSaveOrgMapping(List<NccOrgs> nccOrgs) {
        if (CollectionUtils.isEmpty(nccOrgs)) {
            return 0;
        }
        List<String> orgIds = nccOrgs.stream().map(NccOrgs::getOrgId).collect(Collectors.toList());
        List<OrgsMapping> orgsMappings = orgsMappingRepository.getOrgMappingsByorgIds(orgIds);
        List<OrgsMapping> saveOrgsMappings = null;
        Map<String, OrgsMapping> orgMap = orgsMappings.stream().collect(Collectors.toMap(OrgsMapping::getOrgId, Function.identity()));
        if (CollectionUtils.isEmpty(orgsMappings)) {
            saveOrgsMappings = nccOrgs.stream().map(item -> assinOrgMappingValueMap(item,orgMap)).collect(Collectors.toList());
        } else {
            saveOrgsMappings = nccOrgs.stream().filter(p -> !orgMap.containsKey(p.getOrgId())).map(item -> assinOrgMappingValueMap(item,orgMap)).collect(Collectors.toList());
        }
        return orgsMappingRepository.batchInsertOrgMapping(saveOrgsMappings);
    }

    private OrgsMapping assinOrgMappingValueMap(NccOrgs p,Map<String, OrgsMapping> orgsMap) {
        OrgsMapping orgsMapping = new OrgsMapping();
        BeanUtils.copyProperties(p, orgsMapping);
        orgsMapping.setOaName(p.getNccName());
        orgsMapping.setFinanceOrgId(p.getOrgId());
        orgsMapping.setCreateTime(LocalDateTime.now());
        OrgsMapping mapping = orgsMap.get(orgsMapping.getOrgId());
        if (Objects.nonNull(mapping)) {
            orgsMapping.setIsCheck(mapping.getIsCheck());
        }
        return orgsMapping;
    }
    
    @Override
    public Response<List<OrgsMappingDTO>> getAllOrgsMapping(OrgMappingRequest orgMappingRequest) {
        Response<List<OrgsMappingDTO>> response = Response.success();
        // 查询有权限的业务单元
        List<String> userOrgPermission = LoginUserInfoUtil.getUserHasOrgs();
        List<OrgsMapping> orgsMappings = orgsMappingRepository.getAllOrgsMappings(orgMappingRequest.getNccName(),orgMappingRequest.getRoleId(),userOrgPermission);
        if (CollectionUtils.isEmpty(orgsMappings)) {
            return response;
        }
        
        List<OrgsMappingDTO> orgsMappingDTOs = orgsMappings.stream().map(org ->orgsMappingConverter.covertOrgsMapping2DTO(org)).collect(Collectors.toList());
        response.setData(orgsMappingDTOs);
         return response;
    }
    
    /**
     * 获取所有的业务单元
     *
     * @return
     */
    @Override
    public Response<List<OrgsMappingDTO>> getAllOrgsMappings() {
        List<OrgsMapping> orgsMapping = orgsMappingRepository.getAll();
        return Response.success(orgsMapping.stream().map(item -> orgsMappingConverter.covertOrgsMapping2DTO(item)).collect(Collectors.toList()));
    }
    
    @Override
    public Response<String> getOrgIdByFinanceOrgId(String financeOrgId) {
        OrgsMapping orgsMapping = orgsMappingRepository.getOrgIdByFinanceOrgId(financeOrgId);
        if (Objects.isNull(orgsMapping)) {
            return Response.fail(ResultCode.INTERNAL_SERVER_ERROR);
        }
        return Response.success(orgsMapping.getOrgId());
    }

    /**
     * 是否需要前置审核开关
     *
     * @param orgSettingCheckRequest
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Response<String> settingCheck(OrgSettingCheckRequest orgSettingCheckRequest) {
        orgsMappingRepository.settingCheck(orgSettingCheckRequest.getOrgId(),orgSettingCheckRequest.getIsCheck());
        if (orgSettingCheckRequest.getIsCheck()) {
            redisUtils.sSet(FinanceConstants.ORG_INFO_KEY, orgSettingCheckRequest.getOrgId());
        } else {
            redisUtils.setRemove(FinanceConstants.ORG_INFO_KEY, orgSettingCheckRequest.getOrgId());
        }
        return Response.success();
    }

    /**
     * 查询有权限的业务单元
     *
     * @return
     */
    @Override
    public Response<List<OrgsMappingDTO>> getHasPermissionOrgs() {
        List<String> userHasOrgs = LoginUserInfoUtil.getUserHasOrgs();
        if (CollectionUtils.isEmpty(userHasOrgs)) {
            return Response.success();
        }
        List<OrgsMapping> userHasOrgsMapping = orgsMappingRepository.getOrgMappingsByorgIds(userHasOrgs);
        if (CollectionUtils.isEmpty(userHasOrgsMapping)) {
            return Response.success();
        }
        List<OrgsMappingDTO> orgsMappingDTOs = userHasOrgsMapping.stream().map(org -> orgsMappingConverter.covertOrgsMapping2DTO(org)).collect(Collectors.toList());
        return Response.success(orgsMappingDTOs);
    }

}
