package com.labway.business.center.finance.converter.v2;

import com.labway.business.center.finance.dto.v2.FinanceBillDiffDTO;
import com.labway.business.center.finance.persistence.entity.v2.FinanceBillDiff;
import com.labway.business.center.finance.persistence.params.FinanceBillDiffPageParams;
import com.labway.business.center.finance.request.v2.FinanceBillDiffPageRequest;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2023/10/09 16:10
 **/
@Mapper(componentModel = "spring")
public interface FinanceBillDiffConverter {

    FinanceBillDiffPageParams convertPageRequest2SearchParam(FinanceBillDiffPageRequest financeBillDiffPageRequest);

    List<FinanceBillDiffDTO> convertBDList2FinanceBillDiffList(List<FinanceBillDiff> financeBillDiffs);
}