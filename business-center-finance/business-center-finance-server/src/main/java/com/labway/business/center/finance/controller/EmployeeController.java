package com.labway.business.center.finance.controller;

import com.labway.business.center.core.user.RequiresRoles;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.labway.business.center.finance.request.EmployeeListRequest;
import com.labway.business.center.finance.service.EmployeeService;
import com.swak.frame.dto.Response;


/**
 * 雇员
 *
 * <AUTHOR>
 * @date 2023/03/29
 */

@RestController
public class EmployeeController {
    
    @Autowired
    private EmployeeService employeeService;
    
    @GetMapping("timer/ncc/employee")
    public Response<?> washNccEmployeeData() {
        return employeeService.washNccEmployeeToCenterDatabase();
    }
    
    /**
     * 人员档案查询接口
     *
     * @param request 查询对象
     * @return
     */
    @RequiresRoles
    @PostMapping("dept/employee")
    public Response<?> getDeptEmployees(@RequestBody EmployeeListRequest request) {
        return employeeService.getEmployeeByDeptId(request);
    }
}
