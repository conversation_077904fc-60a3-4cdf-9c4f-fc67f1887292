package com.labway.business.center.finance.controller;

import com.labway.business.center.finance.anno.SecurityParameter;
import com.labway.business.center.finance.request.CleanExpenseFormRequest;
import com.labway.business.center.finance.request.CleanNoExistRequest;
import com.labway.business.center.finance.service.CleanExpensesFormService;
import com.labway.business.center.finance.service.CleanSupplyChainFormService;
import com.swak.frame.dto.Response;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 汇款申请单controller
 *
 * <AUTHOR>
 * @date 2023/04/21
 */
@RestController
public class CleanSupplyChainFormController {

	@Resource
	private CleanSupplyChainFormService cleanSupplyChainFormService;
	@Resource
	private CleanExpensesFormService cleanExpensesFormService;

	@GetMapping("/supply/wash")
	@SecurityParameter(inDecode = false, outEncode = false)
	public void washNccCarNoToCenterDatabase() {
		cleanSupplyChainFormService.washCleanSupplyChainForm2Database(null);
	}

	/**
	 * 根据流程编号 单独清洗数据
	 *
	 * @param cleanExpenseFormRequest
	 */
	@PostMapping("/supply/washByNos")
	@SecurityParameter(inDecode = false, outEncode = false)
	public Response<String> cleanTravelExpenseForm(@RequestBody CleanExpenseFormRequest cleanExpenseFormRequest) {
		return cleanExpensesFormService.washCleanSupplyChainForm2DatabaseByNos(cleanExpenseFormRequest);
	}

	/**
	 * 清洗这段时间内，本地库中漏掉的数据
	 * 汇款申请单
	 *
	 * @param cleanNoExistRequest
	 * @return
	 */
	@PostMapping("/supply/noExist")
	@SecurityParameter(inDecode = false, outEncode = false)
	public Response<String> expenseNoExist(@RequestBody CleanNoExistRequest cleanNoExistRequest) {
		return cleanExpensesFormService.washNoExistSupplyChainForm(cleanNoExistRequest);
	}
}
