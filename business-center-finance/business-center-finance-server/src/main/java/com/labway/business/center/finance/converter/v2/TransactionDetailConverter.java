package com.labway.business.center.finance.converter.v2;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.labway.business.center.finance.cbs8.model.response.CBS8TransactionDetailDTO;
import com.labway.business.center.finance.dto.v2.BankTransactionDetailDTO;
import com.labway.business.center.finance.dto.v2.BankTransactionDetailImportDTO;
import com.labway.business.center.finance.excel.param.v2.BankTransactionDetailExportDTO;
import com.labway.business.center.finance.excel.param.v2.ImportBankDetailDTO;
import com.labway.business.center.finance.persistence.entity.v2.BankTransactionDetail;
import com.labway.business.center.finance.persistence.params.BankTransactionDetailSearchParams;
import com.labway.business.center.finance.request.v2.BankTransactionDetailSearchRequest;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2023/11/15 15:47
 **/
@Mapper(componentModel = "spring")
public interface TransactionDetailConverter {

    TransactionDetailConverter INSTANCE = Mappers.getMapper(TransactionDetailConverter.class);

    List<BankTransactionDetail> convertTransactionDetail2DetailEntityList(List<CBS8TransactionDetailDTO> list);

    BankTransactionDetailSearchParams convertPageRequest2Params(BankTransactionDetailSearchRequest request);

    Page<BankTransactionDetailDTO> convertEntityPage2DTO(Page<BankTransactionDetail> page);

    @Mapping(target = "fileObsUrl", source = "fileUrl")
    BankTransactionDetailDTO convertEntityPage2DTO(BankTransactionDetail detail);

    List<BankTransactionDetail> convertImportDTOList2EntityList(List<BankTransactionDetailImportDTO> targetList);

    @Mapping(target = "bankTransactionDate", source = "bankTransactionDate", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Mapping(target = "valueDate", source = "valueDate", dateFormat = "yyyy-MM-dd")
    BankTransactionDetailImportDTO convertImportObj2ImportDTO(ImportBankDetailDTO value);

    List<BankTransactionDetailExportDTO> convertEntityList2ExportList(List<BankTransactionDetail> bankTransactionDetails);

    @Mapping(target = "bankTransactionDate", source = "bankTransactionDate", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Mapping(target = "valueDate", source = "valueDate", dateFormat = "yyyy-MM-dd")
    BankTransactionDetailExportDTO convertEntityList2Export(BankTransactionDetail bankTransactionDetail);
}