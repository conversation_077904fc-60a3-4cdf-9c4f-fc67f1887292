 package com.labway.business.center.finance.persistence.entity.ncc;

import com.swak.frame.dto.base.Entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 组织_业务单元_公司 (org_corp / nc.vo.org.CorpVO)
 * <AUTHOR>
 * @date 2023/03/21
 */
@Data
public class NccOrgCorp extends Entity {

    /**
     * 公司主键
     */
     @ApiModelProperty(value = "公司主键")
     private String companyId;
     /**
     * 公司名称
     */
     @ApiModelProperty(value = "公司名称")
     private String name;
     /**
     * 公司简称
     */
     @ApiModelProperty(value = "公司简称")
     private String shortName;
     /**
     * 公司编码
     */
     @ApiModelProperty(value = "公司编码")
     private String code;
     /**
     * 上级公司
     */
     @ApiModelProperty(value = "上级公司")
     private String parentId;
     /**
     * 所属集团
     */
     @ApiModelProperty(value = "所属集团")
     private String groupId;
     /**
     * 启用状态,1=未启用;2=已启用;3=已停用;
     */
     @ApiModelProperty(value = "启用状态,1=未启用;2=已启用;3=已停用;")
     private Integer enableState;
}
