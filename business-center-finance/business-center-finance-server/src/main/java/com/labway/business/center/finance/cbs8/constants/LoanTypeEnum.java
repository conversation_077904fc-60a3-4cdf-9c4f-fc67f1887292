package com.labway.business.center.finance.cbs8.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 借贷类型
 * @<PERSON> <PERSON>
 * @Description
 * @Date 2024/6/5 15:04
 */
@AllArgsConstructor
@Getter
public enum LoanTypeEnum {
    JIE("1"),
    DAI("2");

    private String value;

    public static LoanTypeEnum getByValue(String value) {
        return LoanTypeEnum.valueOf(value);
    }

    public static boolean isJie(String value) {
        return JIE.getValue().equals(value);
    }
}
