package com.labway.business.center.finance.service.impl.v3;

import cn.hutool.json.JSONUtil;
import com.labway.business.center.core.enums.OperationTypeEnum;
import com.labway.business.center.core.user.LoginUserInfoUtil;
import com.labway.business.center.finance.aggregation.log.LogMessageProducer;
import com.labway.business.center.finance.constants.FinanceConstants;
import com.labway.business.center.finance.constants.FlowStatusEnums;
import com.labway.business.center.finance.constants.TransactionTypeEnums;
import com.labway.business.center.finance.constants.YesOrNoEnums;
import com.labway.business.center.finance.excel.param.v3.BudgetAvImportDTO;
import com.labway.business.center.finance.persistence.entity.BudgetUnit;
import com.labway.business.center.finance.persistence.entity.v3.BudgetAvailable;
import com.labway.business.center.finance.persistence.entity.v3.BudgetDept;
import com.labway.business.center.finance.persistence.entity.v3.BudgetFlow;
import com.labway.business.center.finance.persistence.entity.v3.BudgetType;
import com.labway.business.center.finance.repository.center.BudgetUnitRepository;
import com.labway.business.center.finance.repository.center.v3.BudgetAvailableRepository;
import com.labway.business.center.finance.repository.center.v3.BudgetDeptRepository;
import com.labway.business.center.finance.repository.center.v3.BudgetFlowRepository;
import com.labway.business.center.finance.service.SnowflakeService;
import com.labway.business.center.finance.service.v3.BudgetAvailableOperationService;
import com.swak.frame.dto.Response;
import com.swak.frame.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 操作可用预算
 *
 * <AUTHOR> Tianhao on 2025/5/6.
 */
@Slf4j
@Service
public class BudgetAvailableOperationServiceImpl implements BudgetAvailableOperationService {

	@Resource
	private BudgetUnitRepository budgetUnitRepository;

	@Resource
	private BudgetDeptRepository budgetDeptRepository;

	@Resource
	private BudgetFlowRepository budgetFlowRepository;

	@Resource
	private BudgetAvailableRepository budgetAvailableRepository;

	@Resource
	private SnowflakeService snowflakeService;

	@Resource
	private LogMessageProducer logMessageProducer;

	/**
	 * 执行数导入
	 * 1、只导入子部门
	 * 2、模板要填写二级预算类别的金额，因为"可用预算"的查询一级是根据二级汇总的(没有二级的只填一级)
	 * 3、数据解析完成之后要计算需要在tb_budget_flow表中插入几条记录，需要修改tb_budget_available表的哪些记录
	 * ❗❗❗tips: 注意这个接口只能在上线后使用一次，在所有地区执行之后需要立即下线接口，如果系统中已经发生了部分占用的数据，那么再导入执行数可能会存在未知情况，金额不对（已跟财务部钟芸同步）
	 * 因为使用场景的情况，在导入时候可以直接覆盖！
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public Response<String> importExcelFile(List targetList, Integer budgetYear, Long oaBudgetUnitId, Long budgetDeptId, String fileUrl) {
		BudgetUnit budgetUnit = budgetUnitRepository.getBudgetUnitByOaBudgetId(oaBudgetUnitId);
		if (Objects.isNull(budgetUnit)) {
			log.info("预算单元不存在");
			return Response.fail(422, "预算单元不存在");
		}

		BudgetDept budgetDept = budgetDeptRepository.searchBudgetDeptByBudgetDeptIdAndOaBudgetUnitId(oaBudgetUnitId, budgetDeptId);
		if (Objects.isNull(budgetDept)) {
			log.error("预算部门不存在");
			return Response.fail(422, "预算单元：[" + budgetUnit.getName() + "]下，选择的预算部门不存在");
		}

		if (YesOrNoEnums.isYes(budgetDept.getHasChild())) {
			log.error("预算部门存在子部门，请选择子部门");
			return Response.fail(422, "预算单元：[" + budgetUnit.getName() + "]下，选择的预算部门存在子部门，仅可导入子部门");
		}

		List<BudgetAvImportDTO> importList = JSONUtil.toList(JSONUtil.toJsonStr(targetList), BudgetAvImportDTO.class);
		if (CollectionUtils.isEmpty(importList)) {
			log.info("导入的数据为空");
			return Response.success();
		}

		List<BudgetAvailable> budgetAvailableList = budgetAvailableRepository.searchBudgetAvailableByUnitIdAndDeptIdAndYear(oaBudgetUnitId, budgetDeptId, budgetYear);
		Map<Integer, BudgetAvailable> monthAvMap = budgetAvailableList.stream().filter(item -> Objects.equals(item.getBudgetTypeId(), FinanceConstants.COMMON_ID))
				.collect(Collectors.toMap(BudgetAvailable::getBudgetMonth, Function.identity()));
		Map<String, BudgetAvailable> monthTypeAvMap = budgetAvailableList.stream().filter(item -> !Objects.equals(item.getBudgetTypeId(), FinanceConstants.COMMON_ID))
				.collect(Collectors.toMap(item -> item.getBudgetMonth() + "-" + item.getBudgetTypeId(), Function.identity()));

		List<BudgetFlow> budgetFlows = new ArrayList<>();

		List<BudgetAvailable> updateBudgetAv = new ArrayList<>();

		Set<Integer> flowDeleteMonths = importList.stream().flatMap(item -> item.getBudgetUseDetailMap().keySet().stream())
				.collect(Collectors.toSet());

		// 处理导入的数据
		for (BudgetAvImportDTO budgetAvImportDTO : importList) {
			BudgetType budgetType = budgetAvImportDTO.getBudgetType();
			for (Map.Entry<Integer, BudgetAvImportDTO.BudgetUseDetail> entry : budgetAvImportDTO.getBudgetUseDetailMap().entrySet()) {
				Integer k = entry.getKey();
				BudgetAvImportDTO.BudgetUseDetail v = entry.getValue();
				if (v.getUsedCount() == 0 && v.getPreviewCount() == 0) {
					continue;
				}

				BudgetAvailable detailAvailable = monthTypeAvMap.get(k + "-" + budgetType.getTypeId());
				if (Objects.nonNull(detailAvailable)) {
					// 详细不为空，处理一下详情的预算
					handlerUsedAvAmount(budgetAvImportDTO, detailAvailable, v, k, budgetFlows, updateBudgetAv, Boolean.FALSE);
				}

				BudgetAvailable available = monthAvMap.get(k);
				if (Objects.isNull(available)) {
					throw new BizException(String.format("预算单元：[%s]下，[%s]部门没有在启用的预算", budgetUnit.getName(), budgetDept.getDeptName()));
				}
				handlerUsedAvAmount(budgetAvImportDTO, available, v, k, budgetFlows, updateBudgetAv, Boolean.TRUE);
			}

		}
		List<BudgetAvailable> finalUpdateInfo = new ArrayList<>();

		// 修改的需要汇总一下
		Map<Long, List<BudgetAvailable>> updateAvMap = updateBudgetAv.stream().collect(Collectors.groupingBy(BudgetAvailable::getAvailableId));
		updateAvMap.forEach((k, v) -> {
			BudgetAvailable updateAv = new BudgetAvailable();
			updateAv.setAvailableId(k);
			updateAv.setPreviewAmount(v.stream().map(BudgetAvailable::getPreviewAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
			updateAv.setUsedAmount(v.stream().map(BudgetAvailable::getUsedAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
			finalUpdateInfo.add(updateAv);
		});

		// 删除旧的flow
		budgetFlowRepository.deleteBudgetFlowByUnitIdAndDeptIdAndYearAndMonthIn(oaBudgetUnitId, budgetDeptId, budgetYear, flowDeleteMonths);

		// 先清空 已用 预占用 额度
//		budgetAvailableRepository.cleanAmount(budgetUnit.getOaBudgetId(), budgetDept.getBudgetDeptId());

		budgetAvailableRepository.updateBudgetAvailableBatch(finalUpdateInfo);

		budgetFlowRepository.saveBudgetFlowBatch(budgetFlows);

		logMessageProducer.sendOperationLog(OperationTypeEnum.IMPORT, String.format("[%s]-导入执行数，导入文件：[%s]", LoginUserInfoUtil.getUserUserName(), fileUrl), FinanceConstants.ModuleName.BUDGET.getModule());

		return Response.success();
	}

	private void handlerUsedAvAmount(BudgetAvImportDTO budgetAvImportDTO, BudgetAvailable available,
	                                 BudgetAvImportDTO.BudgetUseDetail v, Integer k, List<BudgetFlow> budgetFlows,
	                                 List<BudgetAvailable> updateBudgetAv, boolean addFlow) {
		BudgetAvailable updateAv = new BudgetAvailable();
		updateAv.setAvailableId(available.getAvailableId());

		if (v.getPreviewCount() > 0) {
			// 生成flow
			if (addFlow) {
				for (int i = 0; i < v.getPreviewCount(); i++) {
					BudgetFlow insertBudgetFlow = initBudgetFlow(budgetAvImportDTO, TransactionTypeEnums.PRE_USE, k);
					if (i == 0) {
						insertBudgetFlow.setAmount(v.getPreviewAmount());
					}
					budgetFlows.add(insertBudgetFlow);
				}
			}
			updateAv.setPreviewAmount(v.getPreviewAmount());
		}
		if (v.getUsedCount() > 0) {
			// 生成flow
			if (addFlow) {
				for (int i = 0; i < v.getUsedCount(); i++) {
					BudgetFlow insertBudgetFlow = initBudgetFlow(budgetAvImportDTO, TransactionTypeEnums.USE, k);
					if (i == 0) {
						insertBudgetFlow.setAmount(v.getUsedAmount());
					}
					budgetFlows.add(insertBudgetFlow);
				}
			}
			updateAv.setUsedAmount(v.getUsedAmount());
		}
		updateBudgetAv.add(updateAv);
	}

	private BudgetFlow initBudgetFlow(BudgetAvImportDTO budgetAvImportDTO, TransactionTypeEnums tranType, Integer month) {
		BudgetFlow budgetFlow = new BudgetFlow();
		budgetFlow.setFlowId(snowflakeService.genId());
		budgetFlow.setAvailableId(0L);
		budgetFlow.setAmount(BigDecimal.ZERO);
		budgetFlow.setTransactionType(tranType.getType());
		budgetFlow.setTransactionTime(LocalDateTime.now());
		// 这里保证oaNo不相同为了预算查询统计的数量的正确性
		budgetFlow.setOaNo(String.format("执行数导入-[%s]", budgetFlow.getFlowId()));
		budgetFlow.setOaFeetypeId(0L);
		budgetFlow.setBudgetTypeId(budgetAvImportDTO.getBudgetTypeId());
		budgetFlow.setFlowStatus(FlowStatusEnums.PROCESSING.getCode());
		budgetFlow.setBudgetUnitId(budgetAvImportDTO.getBudgetUnitId());
		budgetFlow.setBudgetDeptId(budgetAvImportDTO.getBudgetDeptId());
		budgetFlow.setBudgetYear(budgetAvImportDTO.getBudgetYear());
		budgetFlow.setBudgetMonth(month);
		return budgetFlow;
	}
}
