package com.labway.business.center.finance.service.chain.budget.version.enable;

import com.labway.business.center.finance.repository.center.v3.BudgetRepository;
import com.labway.business.center.finance.request.v3.budget.version.BudgetVersionEnableRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class BudgetVersionEnableCleanOldCommand implements Command {

	@Resource
	private BudgetRepository budgetRepository;

	@Override
	public boolean execute(Context c) throws Exception {
		BudgetVersionEnableContext context = BudgetVersionEnableContext.from(c);

		BudgetVersionEnableRequest request = context.getRequest();
		budgetRepository.cleanStopOldVersion(request.getOaBudgetUnitId(), request.getBudgetDeptId(), request.getBudgetYear());
		return CONTINUE_PROCESSING;
	}
}
