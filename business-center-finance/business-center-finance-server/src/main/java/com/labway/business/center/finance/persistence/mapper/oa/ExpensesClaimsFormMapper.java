 package com.labway.business.center.finance.persistence.mapper.oa;

import java.util.List;

import com.labway.business.center.finance.persistence.params.OaUpdateFileUrl;
import org.apache.ibatis.annotations.Param;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.labway.business.center.core.config.Pager;
import com.labway.business.center.finance.persistence.entity.oa.ExpensesClaimsForm;

/**
  * 费用报销单mapper
  * <AUTHOR>
  * @date 2023/04/18
  */
 @DS("oa")
 public interface ExpensesClaimsFormMapper extends BaseMapper<ExpensesClaimsForm> {
     
     /**
      * 获取费用报销单列表
      * @param pager
      * @return
      */
     public List<ExpensesClaimsForm> getExpensesClaimsFormList(@Param("pager") Pager<String> pager);
    
    List<ExpensesClaimsForm> selectListByOaFlowNos(@Param("list") List<Long> formIds);

    List<ExpensesClaimsForm> selectListByOaNos(@Param("list")List<String> washOaNoList);

    List<String> getLocalNoHasOaNo(@Param("list") List<String> localOaNoList,@Param("time") String syncTime);

    Long updateOaExpensesClaimsForm(@Param("list")List<OaUpdateFileUrl> updateFileUrlList);

    Long updateOaExpensesClaimsFormForNew(@Param("list")List<OaUpdateFileUrl> updateFileUrlList);
}
