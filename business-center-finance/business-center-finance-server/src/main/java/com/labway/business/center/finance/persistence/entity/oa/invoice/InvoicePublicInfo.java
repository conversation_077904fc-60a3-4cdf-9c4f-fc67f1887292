package com.labway.business.center.finance.persistence.entity.oa.invoice;

import java.math.BigDecimal;

import com.swak.frame.dto.base.Entity;

import lombok.Data;

/**
 * 发票公共信息
 * <AUTHOR>
 * @date 2023/04/19
 */
@Data
public class InvoicePublicInfo extends Entity {

	/**
	 * 关键字
	 */
	private String piKey;

	/**
	 * 数字主键
	 */
	private Long piDatakey;

	/**
	 * 具体表名称
	 */
	private String piTablename;
	/**
	 * 发票金额
	 */
	private BigDecimal piAmount;

	/**
	 * 发票代码
	 */
	private String piFpdm;

	/**
	 * 发票种类
	 */
	private String piClassName;

	/**
	 * 发票类型
	 */
	private String piBillname;

	/**
	 * 发票号码
	 */
	private String piFphm;

	/**
	 * 税率
	 */
	private String piTaxrate;

	/**
	 * 税额
	 */
	private BigDecimal piTaxamount;

	/**
	 * 不含税金额
	 */
	private BigDecimal piNotaxamount;
	/**
	 * 价税金额
	 */
	private BigDecimal totalAmount;

	/**
	 * 抵扣额
	 */
	private BigDecimal piDeductionamount;

	/**
	 * 销售方名称
	 */
	private String piSalername;

	/**
	 * 票种编码
	 */
	private String piInvoicetype;

	/**
	 * 火车票类型的发票号字段
	 */
	private String piTraincode;
}
