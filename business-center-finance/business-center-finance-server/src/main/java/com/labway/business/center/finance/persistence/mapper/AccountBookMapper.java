 package com.labway.business.center.finance.persistence.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.labway.business.center.core.injector.ExtBaseMapper;
import com.labway.business.center.finance.persistence.entity.AccountBook;
import org.apache.ibatis.annotations.Param;

import java.util.List;
 
 /**
  * 账簿_财务核算账簿操作类
  * <AUTHOR>
  * @date 2023/03/23
  */
@DS("master")
 public interface AccountBookMapper extends ExtBaseMapper<AccountBook> {
    
    Integer batchUpdateAccountBook(@Param("list") List<AccountBook> updateAccountBooks);

     void removeAll();
 }
