package com.labway.business.center.finance.config;

import cn.hutool.json.JSONNull;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.databind.ser.BeanSerializerModifier;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.labway.business.center.core.config.JacksonSerializerFeatureCompatibleForJackson;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurationSupport;
import org.springframework.web.servlet.i18n.SessionLocaleResolver;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Locale;
import java.util.TimeZone;

@Slf4j
@Configuration
public class WebMvcConfig extends WebMvcConfigurationSupport  {
    @Override
    protected void extendMessageConverters(List<HttpMessageConverter<?>> converters) {
	    final MappingJackson2HttpMessageConverter jackson2HttpMessageConverter = new MappingJackson2HttpMessageConverter();
	    ObjectMapper objectMapper = jackson2HttpMessageConverter.getObjectMapper();

	    // ====================== 新增 Long 序列化为字符串的配置 ======================
	    SimpleModule longToStringModule = new SimpleModule();
	    // 处理 Long 类型（包装类）和 long 类型（基本类型）
	    longToStringModule.addSerializer(Long.class, ToStringSerializer.instance);
	    longToStringModule.addSerializer(Long.TYPE, ToStringSerializer.instance);
	    objectMapper.registerModule(longToStringModule);

	    // ====================== 原有其他配置 ======================
	    SimpleModule simpleModule = new SimpleModule();


        simpleModule.addSerializer(JSONNull.class, new JsonSerializer<JSONNull>(){
            @Override
            public void serialize(JSONNull jsonNull, JsonGenerator jsonGenerator
                    , SerializerProvider serializerProvider) throws IOException {
                jsonGenerator.writeNull();
            }
        });
        simpleModule.addDeserializer(JSONNull.class, new JsonDeserializer<JSONNull>() {
            @Override
            public JSONNull deserialize(JsonParser jsonParser
                    , DeserializationContext deserializationContext) {
                return null;
            }
        });

	    objectMapper.registerModule(simpleModule);
	    objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
	    objectMapper.setDateFormat(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
	    objectMapper.setTimeZone(TimeZone.getTimeZone("GMT+8"));
	    objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
	    BeanSerializerModifier beanSerializerModifier = new JacksonSerializerFeatureCompatibleForJackson(
			    JacksonSerializerFeatureCompatibleForJackson.SerializerFeature.WriteNullListAsEmpty);
	    objectMapper.setSerializerFactory(objectMapper.getSerializerFactory().withSerializerModifier(beanSerializerModifier));

	    // ====================== 将自定义的 ObjectMapper 设置到转换器中 ======================
	    jackson2HttpMessageConverter.setObjectMapper(objectMapper);
	    converters.add(0, jackson2HttpMessageConverter);
	    super.configureMessageConverters(converters);
    }

    @Bean
    public SessionLocaleResolver localeResolver() {
        SessionLocaleResolver localeResolver = new SessionLocaleResolver();
        localeResolver.setDefaultLocale(Locale.SIMPLIFIED_CHINESE);
        return localeResolver;
    }

}
