package com.labway.business.center.finance.service.chain.budget.version.enable;

import com.labway.business.center.core.exception.BizTipException;
import com.labway.business.center.finance.constants.YesOrNoEnums;
import com.labway.business.center.finance.persistence.entity.BudgetUnit;
import com.labway.business.center.finance.persistence.entity.v3.Budget;
import com.labway.business.center.finance.persistence.entity.v3.BudgetDept;
import com.labway.business.center.finance.persistence.entity.v3.BudgetVersion;
import com.labway.business.center.finance.repository.center.BudgetUnitRepository;
import com.labway.business.center.finance.repository.center.v3.BudgetDeptRepository;
import com.labway.business.center.finance.repository.center.v3.BudgetRepository;
import com.labway.business.center.finance.repository.center.v3.BudgetVersionRepository;
import com.labway.business.center.finance.request.v3.budget.version.BudgetVersionEnableRequest;
import com.swak.frame.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * 预算版本启用参数校验&初始化
 *
 * <AUTHOR> Tianhao on 2025/3/28.
 */
@Slf4j
@Component
public class BudgetVersionEnableValidAndInitCommand implements Command {

	@Resource
	private BudgetRepository budgetRepository;

	@Resource
	private BudgetUnitRepository budgetUnitRepository;

	@Resource
	private BudgetDeptRepository budgetDeptRepository;

	@Resource
	private BudgetVersionRepository budgetVersionRepository;

	@Override
	public boolean execute(Context c) throws Exception {
		BudgetVersionEnableContext context = BudgetVersionEnableContext.from(c);
		context.put(BudgetVersionEnableContext.STOP_ENABLED, Boolean.FALSE);
		BudgetVersionEnableRequest request = context.getRequest();

		BudgetUnit budgetUnit = budgetUnitRepository.getBudgetUnitByOaBudgetId(request.getOaBudgetUnitId());
		if (Objects.isNull(budgetUnit)) {
			throw new BizException("所选预算单元不存在");
		}

		if (Objects.nonNull(request.getBudgetDeptId())) {

			BudgetDept budgetDept = budgetDeptRepository.searchBudgetDeptByBudgetDeptIdAndOaBudgetUnitId(request.getOaBudgetUnitId(), request.getBudgetDeptId());
			if (Objects.isNull(budgetDept)) {
				throw new BizException(String.format("所选预算单元：[%s]下，选择的预算部门不存在", budgetUnit.getName()));
			}

			List<BudgetVersion> budgetVersionList = budgetVersionRepository.searchBudgetListByDeptIdAndYearAndVersion(request.getBudgetDeptId(), request.getBudgetYear(), request.getVersion());
			List<Budget> budgetList = budgetRepository.searchEnableBudgetListByDeptIdAndYear(request.getBudgetDeptId(), request.getBudgetYear());

			context.put(BudgetVersionEnableContext.BUDGET_DEPT, budgetDept);
			context.put(BudgetVersionEnableContext.BUDGET_ENABLE_LIST, budgetList);
			context.put(BudgetVersionEnableContext.BUDGET_VERSION_LIST, budgetVersionList);
		}

		if (CollectionUtils.isEmpty(context.getBudgetVersionList())) {
			List<BudgetVersion> budgetVersionList = budgetVersionRepository.searchBudgetListByUnitIdAndYearAndVersion(request.getOaBudgetUnitId(), request.getBudgetYear(), request.getVersion());
			List<Budget> budgetList = budgetRepository.searchEnableBudgetListByUnitIdAndYear(request.getOaBudgetUnitId(), request.getBudgetYear());

			context.put(BudgetVersionEnableContext.BUDGET_ENABLE_LIST, budgetList);
			context.put(BudgetVersionEnableContext.BUDGET_VERSION_LIST, budgetVersionList);
		}

		if (CollectionUtils.isEmpty(context.getBudgetVersionList())) {
			throw new BizException("选择的预算版本不存在");
		}

		if (CollectionUtils.isNotEmpty(context.getBudgetList())) {
			if (YesOrNoEnums.isNo(request.getStopOld())) {
				throw new BizTipException(String.format("预算版本[%s]已启用，是否要停用旧版本？", context.getBudgetList().iterator().next().getVersion()));
			}
			context.put(BudgetVersionEnableContext.STOP_ENABLED, Boolean.TRUE);
		}

		context.put(BudgetVersionEnableContext.OA_BUDGET_UNIT, budgetUnit);

		return CONTINUE_PROCESSING;
	}
}
