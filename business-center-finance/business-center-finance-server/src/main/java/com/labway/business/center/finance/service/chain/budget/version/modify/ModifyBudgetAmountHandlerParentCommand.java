package com.labway.business.center.finance.service.chain.budget.version.modify;

import com.labway.business.center.finance.persistence.entity.v3.BudgetVersion;
import com.labway.business.center.finance.repository.center.v3.BudgetVersionRepository;
import com.labway.business.center.finance.request.v3.budget.version.BudgetModifyRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.chain.impl.ChainBase;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 处理修改对应的父级预算
 *
 * <AUTHOR> on 2025/4/2.
 */
@Slf4j
@Component
public class ModifyBudgetAmountHandlerParentCommand implements Command {

	@Resource
	private BudgetVersionRepository budgetVersionRepository;

	@Resource
	private ModifyBudgetAmountChain modifyBudgetAmountChain;

	@Override
	public boolean execute(Context c) throws Exception {
		ModifyBudgetAmountContext context = ModifyBudgetAmountContext.from(c);
		BudgetVersion originBudgetVersion = context.getOriginBudgetVersion();
		BudgetModifyRequest request = context.getRequest();
		// 获取父级预算
		BudgetVersion superBudgetVersion = budgetVersionRepository.searchBudgetByBudgetVersionId(originBudgetVersion.getSuperBudgetId());
		if (Objects.isNull(superBudgetVersion)) {
			// 退出递归
			return CONTINUE_PROCESSING;
		}

		// 自己递归调用 处理父级内容 修改父级预算内容
		BudgetModifyRequest budgetModifyRequest = new BudgetModifyRequest();
		budgetModifyRequest.setId(superBudgetVersion.getBudgetVersionId());
		budgetModifyRequest.setMonth(request.getMonth());
		budgetModifyRequest.setBudgetAmount(request.getBudgetAmount());
		budgetModifyRequest.setForceModify(request.getForceModify());

		ModifyBudgetAmountContext newContext = new ModifyBudgetAmountContext(budgetModifyRequest);
		newContext.setIsParent(Boolean.TRUE);
		// 把当前的预算放入上下文 用于计算父级的金额
		newContext.put(ModifyBudgetAmountContext.CHILDREN_BUDGET, originBudgetVersion);
		((ChainProvider) context.get(ModifyBudgetAmountContext.CHAIN_PROVIDER)).chain().execute(newContext);
		return CONTINUE_PROCESSING;
	}

	public interface ChainProvider extends Command {
		ChainBase chain();
	}

	public ChainProvider getDefaultChainProvider() {
		return new ChainProvider() {
			@Override
			public ChainBase chain() {
				return modifyBudgetAmountChain;
			}

			@Override
			public boolean execute(Context c) throws Exception {
				ModifyBudgetAmountContext context = ModifyBudgetAmountContext.from(c);
				context.put(ModifyBudgetAmountContext.CHAIN_PROVIDER, this);
				return CONTINUE_PROCESSING;
			}
		};
	}
}
