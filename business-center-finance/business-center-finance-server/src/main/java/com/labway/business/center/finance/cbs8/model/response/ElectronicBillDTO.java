 package com.labway.business.center.finance.cbs8.model.response;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import lombok.Data;

/**
 * 电子回单DTO
 * <AUTHOR>
 * @date 2023/11/03
 */
@Data
public class ElectronicBillDTO implements Serializable {


       private String accountName;
       private String accountNatureCode;
       private String accountNo;
       private String bankSerialNumber;
       private String bankType;
       private String bankTypeName;
       private String billFileName;
       private String bucketFileName;
       private String bucketFileUrl;
       private String checkCode;
       private String currency;
       private String currencyName;
       private String digest;
       private String electronicBillStatus;
       private String electronicBillType;
       private String loanType;
       private String openingBank;
       private String oppositeAccount;
       private String oppositeAccountName;
       private String oppositeOpeningBank;
       private String oppositeOpeningPlace;
       private String printInstanceNumber;
       private String purpose;
       private String settleBusinessReferenceCode;
       private BigDecimal transactionAmount;
       private LocalDateTime transactionDate;
       private String unitCode;
    
}
