 package com.labway.business.center.finance.repository.center;

import java.util.List;

import javax.annotation.Resource;

import com.labway.business.center.core.config.Pager;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.labway.business.center.finance.persistence.entity.Accass;
import com.labway.business.center.finance.persistence.mapper.AccassMapper;

/**
 * 处理NCC辅助核算信息资源类
 * <AUTHOR>
 * @date 2023/03/21
 */
@Repository
@RefreshScope
 public class AccassRepository {
    
    @Resource
    private AccassMapper accassMapper;
    
    @Value(value = "${defaultChartId:10010110000000000BCB}")
    private String defaultChartId;
    
    public Integer batchInsertAccass(List<Accass> accasses) {
        if (CollectionUtils.isEmpty(accasses)) {
            return 0 ;
        }
        return accassMapper.insertBatchSomeColumn(accasses);
    }
    
    public List<Accass> getAccassByAccassIds(List<String> ids){
        LambdaQueryWrapper<Accass> queryWrapper = new  LambdaQueryWrapper<>();
        queryWrapper.in(Accass::getAccassId, ids);
        queryWrapper.select(Accass::getAccassId);
        return accassMapper.selectList(queryWrapper);
    }
    
    public List<Accass> getAccassByAccasoaIds(List<String> ids){
        LambdaQueryWrapper<Accass> queryWrapper = new  LambdaQueryWrapper<>();
        queryWrapper.in(Accass::getAccasoaId, ids);
        return accassMapper.selectList(queryWrapper);
    }
    
    public List<Accass> getAccassByAccasoaCodes(List<String> codes){
        LambdaQueryWrapper<Accass> queryWrapper = new  LambdaQueryWrapper<>();
        queryWrapper.in(Accass::getAccasoaCode, codes);
        queryWrapper.eq(Accass::getAccchartId, defaultChartId);
        return accassMapper.selectList(queryWrapper);
    }
    
    public List<Accass> getAccassByAccasoaId( String id){
        LambdaQueryWrapper<Accass> queryWrapper = new  LambdaQueryWrapper<>();
        queryWrapper.eq(Accass::getAccasoaId, id);
        return accassMapper.selectList(queryWrapper);
    }
    
    
    public List<Accass> getAccassByAccasoaCode( String code){
        LambdaQueryWrapper<Accass> queryWrapper = new  LambdaQueryWrapper<>();
        queryWrapper.eq(Accass::getAccasoaCode, code);
        queryWrapper.eq(Accass::getAccchartId, defaultChartId);
        return accassMapper.selectList(queryWrapper);
    }
    
    
    public Long countAccass() {
        LambdaQueryWrapper<Accass> queryWrapper = new  LambdaQueryWrapper<>();
        return accassMapper.selectCount(queryWrapper);
    }
    
    /**
     *
     * @param centerPage
     * @return
     */
    public List<Accass> getAccassByPager(Pager<String> centerPage) {
        return accassMapper.selectAccassByPager(centerPage);
    }
    
    public Integer removeByAccassIds(List<String> itemIds) {
        LambdaQueryWrapper<Accass> queryWrapper = new  LambdaQueryWrapper<>();
        queryWrapper.in(Accass::getAccassId,itemIds);
        return accassMapper.delete(queryWrapper);
    }
}
