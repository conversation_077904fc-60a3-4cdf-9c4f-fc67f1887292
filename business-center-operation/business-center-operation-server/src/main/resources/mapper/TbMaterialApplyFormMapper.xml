<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.business.center.operation.persistence.mapper.TbMaterialApplyFormMapper">

    <resultMap type="com.labway.business.center.operation.persistence.entity.TbMaterialApplyForm" id="TbMaterialApplyFormMap">
        <result property="itemId" column="item_id" jdbcType="VARCHAR"/>
        <result property="applyCode" column="apply_code" jdbcType="VARCHAR"/>
        <result property="applyTime" column="apply_time" jdbcType="TIMESTAMP"/>
        <result property="applyUserId" column="apply_user_id" jdbcType="VARCHAR"/>
        <result property="appleUserName" column="apple_user_name" jdbcType="VARCHAR"/>
        <result property="applyOrgCode" column="apply_org_code" jdbcType="VARCHAR"/>
        <result property="applyOrgName" column="apply_org_name" jdbcType="VARCHAR"/>
        <result property="applyLisCode" column="apply_lis_code" jdbcType="VARCHAR"/>
        <result property="applyLisName" column="apply_lis_name" jdbcType="VARCHAR"/>
        <result property="applyStatus" column="apply_status" jdbcType="INTEGER"/>
        <result property="applyCommitTimes" column="apply_commit_times" jdbcType="INTEGER"/>
        <result property="approvalUserId" column="approval_user_id" jdbcType="VARCHAR"/>
        <result property="approvalUserName" column="approval_user_name" jdbcType="VARCHAR"/>
        <result property="approvalTime" column="approval_time" jdbcType="TIMESTAMP"/>
        <result property="enabled" column="enabled" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="applyRemark" column="apply_remark" jdbcType="VARCHAR"/>
        <result property="rejectReason" column="reject_reason" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 批量更新 -->
    <update id="updateBatch">
        update tb_material_apply_form

        <trim prefix="set" suffixOverrides=",">
            <trim prefix="apply_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when item_id = #{item.itemId} then #{item.applyCode}
                </foreach>
            </trim>

            <trim prefix="apply_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when item_id = #{item.itemId} then #{item.applyTime}
                </foreach>
            </trim>

            <trim prefix="apply_user_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when item_id = #{item.itemId} then #{item.applyUserId}
                </foreach>
            </trim>

            <trim prefix="apple_user_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when item_id = #{item.itemId} then #{item.appleUserName}
                </foreach>
            </trim>

            <trim prefix="apply_org_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when item_id = #{item.itemId} then #{item.applyOrgCode}
                </foreach>
            </trim>

            <trim prefix=" apply_org_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when item_id = #{item.itemId} then #{item.applyOrgName}
                </foreach>
            </trim>

            <trim prefix="apply_lis_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when item_id = #{item.itemId} then #{item.applyLisCode}
                </foreach>
            </trim>

            <trim prefix="apply_lis_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when item_id = #{item.itemId} then #{item.applyLisName}
                </foreach>
            </trim>

            <trim prefix="apply_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when item_id = #{item.itemId} then #{item.applyStatus}
                </foreach>
            </trim>

            <trim prefix="apply_commit_times = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when item_id = #{item.itemId} then #{item.applyCommitTimes}
                </foreach>
            </trim>

            <trim prefix="approval_user_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when item_id = #{item.itemId} then #{item.approvalUserId}
                </foreach>
            </trim>

            <trim prefix="approval_user_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when item_id = #{item.itemId} then #{item.approvalUserName}
                </foreach>
            </trim>

            <trim prefix="approval_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when item_id = #{item.itemId} then #{item.approvalTime}
                </foreach>
            </trim>

            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when item_id = #{item.itemId} then #{item.enabled}
                </foreach>
            </trim>

            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when item_id = #{item.itemId} then #{item.createTime}
                </foreach>
            </trim>

            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when item_id = #{item.itemId} then #{item.updateTime}
                </foreach>
            </trim>

            <trim prefix="apply_remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when item_id = #{item.itemId} then #{item.applyRemark}
                </foreach>
            </trim>

            <trim prefix="reject_reason = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when item_id = #{item.itemId} then #{item.rejectReason}
                </foreach>
            </trim>

        </trim>

        where item_id in
        <foreach close=")" collection="list" item="item" open="(" separator=",">
            #{item.itemId}
        </foreach>

    </update>

    <update id="approveApplyFormBath">

        update tb_material_apply_form
        <trim prefix="set" suffixOverrides=",">
            <trim prefix=" apply_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when item_id = #{item.applyItemId} then #{item.applyStatus}
                </foreach>
            </trim>

            <trim prefix=" approval_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when item_id = #{item.applyItemId} then #{item.approvalTime}
                </foreach>
            </trim>

            <trim prefix=" approval_user_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when item_id = #{item.applyItemId} then #{item.optUserCode}
                </foreach>
            </trim>

            <trim prefix=" approval_user_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when item_id = #{item.applyItemId} then #{item.optUserName}
                </foreach>
            </trim>

            <trim prefix=" update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when item_id = #{item.applyItemId} then #{item.approvalTime}
                </foreach>
            </trim>

            <trim prefix=" approval_remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when item_id = #{item.applyItemId} then #{item.approvalRemark}
                </foreach>
            </trim>

            <trim prefix=" reject_reason = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when item_id = #{item.applyItemId} then #{item.rejectReason}
                </foreach>
            </trim>

        </trim>

        where item_id in
        <foreach close=")" collection="list" item="item" open="(" separator=",">
            #{item.applyItemId}
        </foreach>

    </update>

    <update id="stockOutApplyFormBath">
        update tb_material_apply_form
        <trim prefix="set" suffixOverrides=",">
            <trim prefix=" apply_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when item_id = #{item.applyItemId} then #{item.applyStatus}
                </foreach>
            </trim>

            <trim prefix=" update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when item_id = #{item.applyItemId} then #{item.updateTime}
                </foreach>
            </trim>

        </trim>

        where item_id in
        <foreach close=")" collection="list" item="item" open="(" separator=",">
            #{item.applyItemId}
        </foreach>
    </update>



</mapper>

