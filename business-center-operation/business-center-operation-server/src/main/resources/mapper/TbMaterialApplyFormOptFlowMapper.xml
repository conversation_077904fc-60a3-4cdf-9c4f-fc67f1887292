<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.business.center.operation.persistence.mapper.TbMaterialApplyFormOptFlowMapper">

    <resultMap type="com.labway.business.center.operation.persistence.entity.TbMaterialApplyFormOptFlow" id="TbMaterialApplyFormOptFlowMap">
        <result property="itemId" column="item_id" jdbcType="VARCHAR"/>
        <result property="applyFormId" column="apply_form_id" jdbcType="VARCHAR"/>
        <result property="applyFormCode" column="apply_form_code" jdbcType="VARCHAR"/>
        <result property="optStatus" column="opt_status" jdbcType="INTEGER"/>
        <result property="enabled" column="enabled" jdbcType="VARCHAR"/>
        <result property="optUserCode" column="opt_user_code" jdbcType="VARCHAR"/>
        <result property="optUserAccount" column="opt_user_account" jdbcType="VARCHAR"/>
        <result property="optUserName" column="opt_user_name" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="itemId" useGeneratedKeys="true">
        insert into tb_material_apply_form_opt_flow(item_id,apply_form_id, apply_form_code, opt_status, enabled, opt_user_code, opt_user_account, opt_user_name, create_time, update_time)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.itemId},#{entity.applyFormId}, #{entity.applyFormCode}, #{entity.optStatus}, #{entity.enabled}, #{entity.optUserCode}, #{entity.optUserAccount}, #{entity.optUserName}, #{entity.createTime}, #{entity.updateTime})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="itemId" useGeneratedKeys="true">
        insert into tb_material_apply_form_opt_flow(apply_form_id, apply_form_code, opt_status, enabled, opt_user_code, opt_user_account, opt_user_name, create_time, update_time)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.applyFormId}, #{entity.applyFormCode}, #{entity.optStatus}, #{entity.enabled}, #{entity.optUserCode}, #{entity.optUserAccount}, #{entity.optUserName}, #{entity.createTime}, #{entity.updateTime})
        </foreach>
        on duplicate key update
         apply_form_id = values(apply_form_id) , apply_form_code = values(apply_form_code) , opt_status = values(opt_status) , enabled = values(enabled) , opt_user_code = values(opt_user_code) , opt_user_account = values(opt_user_account) , opt_user_name = values(opt_user_name) , create_time = values(create_time) , update_time = values(update_time)     </insert>

</mapper>

