<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.business.center.operation.persistence.mapper.TbInstitutionCompanyMapper">


    <select id="selectCompanyList" resultType="com.labway.business.center.operation.dto.user.TbInstitutionCompanyDto">
        select tic.*
        from tb_institution_company tic
        <where>
            <if test="userId != null and userId != ''">
                and tic.item_id in
                (
                select tiur.allot_type_value
                from tb_institution_user_relation tiur
                where tiur.user_id = #{userId}
                and tiur.allot_type = 1
                and tiur.enabled = 1
                )
            </if>
            <if test="companyId != null and companyId != ''">
                and tic.item_id = #{companyId}
            </if>
            <if test="companyNameLike != null and companyNameLike != ''">
                <bind name="companyNameLikeTemp" value="'%'+companyNameLike+'%'"/>
                and company_name like #{companyNameLikeTemp}
            </if>
            and tic.status = 1
            and tic.enabled = 1
        </where>
    </select>
</mapper>

