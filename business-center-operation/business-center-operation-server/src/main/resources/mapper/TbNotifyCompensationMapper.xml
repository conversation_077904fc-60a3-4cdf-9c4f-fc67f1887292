<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.business.center.operation.persistence.mapper.TbNotifyCompensationMapper">

    <resultMap type="com.labway.business.center.operation.persistence.entity.TbNotifyCompensation" id="TbNotifyCompensationMap">
        <result property="itemId" column="item_id" jdbcType="VARCHAR"/>
        <result property="requestUrl" column="request_url" jdbcType="VARCHAR"/>
        <result property="requestHeard" column="request_heard" jdbcType="VARCHAR"/>
        <result property="requestBody" column="request_body" jdbcType="VARCHAR"/>
        <result property="requestType" column="request_type" jdbcType="INTEGER"/>
        <result property="requestTimes" column="request_times" jdbcType="INTEGER"/>
        <result property="isSendSuccess" column="is_send_success" jdbcType="INTEGER"/>
        <result property="enabled" column="enabled" jdbcType="VARCHAR"/>
        <result property="optUserCode" column="opt_user_code" jdbcType="VARCHAR"/>
        <result property="optUserAccount" column="opt_user_account" jdbcType="VARCHAR"/>
        <result property="optUserName" column="opt_user_name" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertList" keyProperty="itemId" useGeneratedKeys="true">
        insert into tb_notify_compensation(item_id,request_url, request_heard, request_body, request_type, request_times, is_send_success, enabled, opt_user_code, opt_user_account, opt_user_name, create_time, update_time)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.itemId}, #{entity.requestUrl}, #{entity.requestHeard}, #{entity.requestBody}, #{entity.requestType}, #{entity.requestTimes}, #{entity.isSendSuccess}, #{entity.enabled}, #{entity.optUserCode}, #{entity.optUserAccount}, #{entity.optUserName}, #{entity.createTime}, #{entity.updateTime})
        </foreach>
    </insert>

    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="itemId" useGeneratedKeys="true">
        insert into tb_notify_compensation(request_url, request_heard, request_body, request_type, request_times, is_send_success, enabled, opt_user_code, opt_user_account, opt_user_name, create_time, update_time)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.requestUrl}, #{entity.requestHeard}, #{entity.requestBody}, #{entity.requestType}, #{entity.requestTimes}, #{entity.isSendSuccess}, #{entity.enabled}, #{entity.optUserCode}, #{entity.optUserAccount}, #{entity.optUserName}, #{entity.createTime}, #{entity.updateTime})
        </foreach>
        on duplicate key update
         request_url = values(request_url) , request_heard = values(request_heard) , request_body = values(request_body) , request_type = values(request_type) , request_times = values(request_times) , is_send_success = values(is_send_success) , enabled = values(enabled) , opt_user_code = values(opt_user_code) , opt_user_account = values(opt_user_account) , opt_user_name = values(opt_user_name) , create_time = values(create_time) , update_time = values(update_time)     </insert>

    <update id="updateList">
        update tb_notify_compensation

        <trim prefix="set" suffixOverrides=",">
            <trim prefix="request_times = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when item_id = #{item.itemId} then #{item.requestTimes}
                </foreach>
            </trim>

            <trim prefix="is_send_success = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when item_id = #{item.itemId} then #{item.isSendSuccess}
                </foreach>
            </trim>

            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when item_id = #{item.itemId} then #{item.updateTime}
                </foreach>
            </trim>
        </trim>

        where item_id in
        <foreach close=")" collection="list" item="item" open="(" separator=",">
            #{item.itemId}
        </foreach>

    </update>

</mapper>

