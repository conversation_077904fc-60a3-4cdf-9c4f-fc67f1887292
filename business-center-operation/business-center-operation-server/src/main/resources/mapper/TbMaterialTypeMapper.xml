<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.business.center.operation.persistence.mapper.TbMaterialTypeMapper">
    <insert id="saveMaterialTypeDtoList">
        INSERT INTO tb_material_type (
        item_id,
        info_hash,
        from_no,
        material_type_code,
        material_type_name,
        enable_state,
        enabled,
        opt_user_code,
        opt_user_account,
        opt_user_name,
        create_time,
        update_time
        )
        values
        <foreach collection="conditions" item="item" index="index" separator=",">
            (
            #{item.itemId},
            #{item.infoHash},
            #{item.fromNo},
            #{item.materialTypeCode},
            #{item.materialTypeName},
            #{item.enableState},
            #{item.enabled},
            #{item.optUserCode},
            #{item.optUserAccount},
            #{item.optUserName},
            #{item.createTime},
            #{item.updateTime}
            )
        </foreach>
    </insert>
    <update id="invalidMaterialTypeDtoByFromNo">
        update tb_material_type
        set enabled = false
        where from_no in
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </update>

    <select id="selectTbMaterialTypeDto"
            resultType="com.labway.business.center.operation.dto.material.TbMaterialTypeDto">
        select *
        from tb_material_type
    </select>
    <select id="selectMaterialTypeList"
            resultType="com.labway.business.center.operation.dto.material.TbMaterialTypeDto">
        select *
        from tb_material_type
        <where>
            <if test="enableState != null">
                and enable_state = #{enableState}
            </if>
            <if test="materialTypeNameLike != null and materialTypeNameLike != ''">
                <bind name="materialTypeNameLikeTemp" value="'%'+materialTypeNameLike+'%'"/>
                and material_type_name like #{materialTypeNameLikeTemp}
            </if>
            and enabled = 1
        </where>
        order by material_type_code
    </select>
</mapper>

