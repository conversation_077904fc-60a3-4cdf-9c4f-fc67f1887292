package com.labway.business.center.operation.service.impl;


import com.labway.business.center.core.user.LoginUserInfoUtil;
import com.labway.business.center.operation.constants.CommonConstant;
import com.labway.business.center.operation.dto.material.SaveTbMaterialSyncFlowDto;
import com.labway.business.center.operation.dto.material.TbMaterialSyncFlowDto;
import com.labway.business.center.operation.exception.OperationResultCode;
import com.labway.business.center.operation.repository.TbMaterialSyncFlowDal;
import com.labway.business.center.operation.service.material.TbMaterialSyncFlowService;
import com.labway.business.center.operation.util.JacksonUtil;
import com.labway.business.center.operation.util.SerialIdUtil;
import com.swak.frame.dto.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.ParseException;
import java.util.Date;
import java.util.List;

/**
 * 物料同步流水表 Service impl
 * 
 * <AUTHOR>
 * @since 2023/3/14 15:47
 */
@Slf4j
@DubboService
public class TbMaterialSyncFlowServiceImpl implements TbMaterialSyncFlowService {

    @Resource
    private TbMaterialSyncFlowDal tbMaterialSyncFlowDal;
    @Resource
    private SerialIdUtil serialIdUtil;


    @Override
    public TbMaterialSyncFlowDto recentFlowRecord() {
        return tbMaterialSyncFlowDal.recentFlowRecord();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveFlowRecord(SaveTbMaterialSyncFlowDto condition) {
        String syncFlowId = condition.getSyncFlowId();
        TbMaterialSyncFlowDto tbMaterialSyncFlowDto = JacksonUtil.convertValue(condition, TbMaterialSyncFlowDto.class);
        if (StringUtils.isBlank(syncFlowId)) {
            tbMaterialSyncFlowDto.setItemId(serialIdUtil.getSerialNumber());
            tbMaterialSyncFlowDto.setEnabled(Boolean.TRUE);
            tbMaterialSyncFlowDto.setOptUserCode(LoginUserInfoUtil.getUserId());
            tbMaterialSyncFlowDto.setOptUserAccount(LoginUserInfoUtil.getUserLoginName());
            tbMaterialSyncFlowDto.setOptUserName(LoginUserInfoUtil.getUserUserName());
            tbMaterialSyncFlowDto.setCreateTime(new Date());
            tbMaterialSyncFlowDto.setUpdateTime(new Date());

            tbMaterialSyncFlowDal.saveFlowRecord(tbMaterialSyncFlowDto);
            return;
        }
        String str = "xxl_job";
        tbMaterialSyncFlowDto.setItemId(syncFlowId);
        tbMaterialSyncFlowDto.setEnabled(Boolean.TRUE);
        tbMaterialSyncFlowDto.setOptUserCode(str);
        tbMaterialSyncFlowDto.setOptUserAccount(str);
        tbMaterialSyncFlowDto.setOptUserName(str);
        tbMaterialSyncFlowDto.setUpdateTime(new Date());
        tbMaterialSyncFlowDal.updateFlowRecord(tbMaterialSyncFlowDto);
    }

    @Override
    public Response<List<TbMaterialSyncFlowDto>> recentFailureFlowRecord() {
        // 前天 凌晨
        Date afterTime;
        try {
            afterTime = DateUtils
                .parseDate(DateFormatUtils.format(DateUtils.addDays(new Date(), CommonConstant.INT_NEGATIVE_TWO),
                    CommonConstant.DATE_PATTERN_DAY), CommonConstant.DATE_PATTERN_DAY);
        } catch (ParseException e) {
            return Response.fail(OperationResultCode.E100001);
        }
        List<TbMaterialSyncFlowDto> tbMaterialSyncFlowDtos = tbMaterialSyncFlowDal.recentFailureFlowRecord(afterTime);
        return Response.success(tbMaterialSyncFlowDtos);
    }
}
