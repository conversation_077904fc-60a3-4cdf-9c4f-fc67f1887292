package com.labway.business.center.operation.persistence.mapper;

import com.labway.business.center.core.injector.ExtBaseMapper;
import com.labway.business.center.operation.dto.material.TbMaterialSyncFlowDto;
import com.labway.business.center.operation.persistence.entity.TbMaterialSyncFlow;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 物料同步流水表 mapper
 * 
 * <AUTHOR>
 * @since 2023/3/14 15:07
 */
public interface TbMaterialSyncFlowMapper extends ExtBaseMapper<TbMaterialSyncFlow> {
    /**
     * 获取 最近一条成功的流水记录
     */

    TbMaterialSyncFlowDto recentFlowRecord();

    /**
     * 保存 流水记录
     *
     * @param condition 参数
     */
    void saveFlowRecord(TbMaterialSyncFlowDto condition);

    /**
     * 获取 失败物料流水记录
     *
     * @param afterTime 此时间之后
     * @return list
     */
    List<TbMaterialSyncFlowDto> recentFailureFlowRecord(@Param("afterTime") Date afterTime);

    /**
     * 更新 流水记录
     *
     * @param condition 参数
     */
    void updateFlowRecord(TbMaterialSyncFlowDto condition);
}
