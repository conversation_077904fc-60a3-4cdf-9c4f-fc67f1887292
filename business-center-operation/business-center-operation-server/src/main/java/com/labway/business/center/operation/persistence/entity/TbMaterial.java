package com.labway.business.center.operation.persistence.entity;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 物料信息表
 * 
 * <AUTHOR>
 * @since 2023/3/13 9:50
 */

@Getter
@Setter
public class TbMaterial extends CommonFieldEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 信息hash
     */
    private String infoHash;
    /**
     * 同步唯一码
     */
    private String fromNo;
    /**
     * 物料编码
     */
    private String materialCode;
    /**
     * 物料名称
     */
    private String materialName;
    /**
     * 物料基本分类编码
     */
    private String materialTypeCode;
    /**
     * 物料基本分类名称
     */
    private String materialTypeName;
    /**
     * 物料规格
     */
    private String specification;
    /**
     * 物料型号
     */
    private String model;
    /**
     * 主单位
     */
    private String primaryUnit;
    /**
     * 主单位ncc关联code
     */
    private String primaryUnitCode;
    /**
     * 辅单位
     */
    private String secondaryUnit;
    /**
     * 辅单位ncc关联code
     */
    private String secondaryUnitCode;
    /**
     * 主辅单位换算率
     */
    private String unitTransRate;
    /**
     * 厂商
     */
    private String manufacturer;
    /**
     * 储存条件
     */
    private String storageRequirement;
    /**
     * 注册证号码
     */
    private String registrationNo;
    /**
     * 注册证名称
     */
    private String registrationName;
    /**
     * 注册证名称/备案人名称
     */
    private String registrant;
    /**
     * 注册证名称/备案人名称code
     */
    private String registrantCode;
    /**
     * 启用状态:1未启用，2已启用，3已停用
     */
    private Integer enableState;

}
