package com.labway.business.center.operation.exception;


import com.swak.frame.enums.IResultCode;
import lombok.Getter;

/**
 * 物料服务错误码
 *
 * <AUTHOR>
 * @since 2023/3/9 16:58
 */
@Getter
public enum MaterialErrorCode  implements IResultCode {

    ME100001(100001, "对应客商不存在"),

    ME100002(100002, "对应客商不存在或存在多个"),

    ME100003(100003, "存在无效物料"),

    ME100004(100004, "存在无效客商"),

    ME100005(100005, "请求主数据接口报错"),

    ME100006(100006, "获取物料库存数量失败"),
    U100007(100007, "公司不存在或已停用"),

    ;

    private final Integer code;
    private final String msg;

    MaterialErrorCode(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }
}
