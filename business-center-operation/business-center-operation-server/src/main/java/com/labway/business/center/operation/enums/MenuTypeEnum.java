package com.labway.business.center.operation.enums;

/**
 * @author: zhangyang
 * date:2022/11/24 11:17
 **/
public enum MenuTypeEnum {

    /**
     *  菜单类型
     */
    CATALOGUE(1,"目录"),
    MENU(2,"菜单"),
    BUTTON(3,"按钮");

    private final Integer menuType;

    private final String menuTypeDesc;

    MenuTypeEnum(Integer menuType, String menuTypeDesc) {
        this.menuType = menuType;
        this.menuTypeDesc = menuTypeDesc;
    }


    public static MenuTypeEnum getMenuTypeDes(Integer menuType) {
        for (MenuTypeEnum menuTypeEnum : MenuTypeEnum.values()) {
            if (menuTypeEnum.getMenuType().equals(menuType)) {
                return menuTypeEnum;
            }
        }
        throw new IllegalArgumentException("错误类型");
    }


    public Integer getMenuType() {
        return menuType;
    }

    public String getMenuTypeDesc() {
        return menuTypeDesc;
    }
}
