package com.labway.business.center.operation.service.impl;

import com.labway.business.center.core.enums.ResultCode;
import com.labway.business.center.operation.dto.material.ApplyFormDetailByApplyIdQueryDto;
import com.labway.business.center.operation.dto.material.ApplyFormDetailByIdsQueryDto;
import com.labway.business.center.operation.dto.material.ApplyFormDetailQueryDto;
import com.labway.business.center.operation.dto.material.TbMaterialDto;
import com.labway.business.center.operation.exception.OperationResultCode;
import com.labway.business.center.operation.repository.TbMaterialApplyFormDetailDal;
import com.labway.business.center.operation.repository.TbMaterialDal;
import com.labway.business.center.operation.persistence.entity.TbMaterial;
import com.labway.business.center.operation.service.material.TbMaterialApplyFormDetailServiceOperation;
import com.labway.business.center.operation.vo.material.ApplyFormDetailVo;
import com.labway.business.center.operation.vo.material.MaterialApplyFormDetailVo;
import com.swak.frame.dto.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 物料申领单明细表(TbMaterialApplyFormDetail)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-03-13 11:15:50
 */
@Slf4j
@DubboService
public class TbMaterialApplyFormDetailServiceOperationImpl implements TbMaterialApplyFormDetailServiceOperation {

    @Resource
    private TbMaterialApplyFormDetailDal tbMaterialApplyFormDetailDal;
    @Resource
    private TbMaterialDal tbMaterialDal;

    /**
     * 根据申领单id查询申领单明细信息
     * 
     * @param queryDto
     * @return
     */
    @Override
    public Response<List<ApplyFormDetailVo>> queryApplyFormDetailByApplyFormId(ApplyFormDetailQueryDto queryDto) {
        if (!StringUtils.hasText(queryDto.getApplyFormId())){
            return Response.fail(ResultCode.PARAMS_NOT_VALIDATE);
        }

        List<ApplyFormDetailVo> applyFormDetailVos = tbMaterialApplyFormDetailDal.queryApplyFormDetailByApplyFormId(queryDto.getApplyFormId());
        if (CollectionUtils.isEmpty(applyFormDetailVos)){
            log.info("申领单物料明细为空！");
            return Response.fail(OperationResultCode.E900028);
        }

        // 查询物料信息，根据换算率填充审批数量
        List<String> materialCodes = applyFormDetailVos.stream().map(ApplyFormDetailVo::getMaterialCode).collect(Collectors.toList());
        List<TbMaterialDto> tbMaterialDtos = tbMaterialDal.queryMaterialByCodes(materialCodes);
        if (CollectionUtils.isEmpty(tbMaterialDtos)){
            log.info("未查询到申领单物料信息！");
            return Response.fail(OperationResultCode.E900030);
        }

        // 分组
        Map<String, List<TbMaterialDto>> materialGroup = tbMaterialDtos.stream().collect(Collectors.groupingBy(TbMaterialDto::getMaterialCode));

        for (ApplyFormDetailVo applyFormDetailVo : applyFormDetailVos) {
            List<TbMaterialDto> tempMaterial = materialGroup.get(applyFormDetailVo.getMaterialCode());
            if (CollectionUtils.isEmpty(tempMaterial)){
                continue;
            }

            BigDecimal multiply = null;
            try {
                TbMaterialDto tbMaterialDto = tempMaterial.get(0);
                String unitTransRate = tbMaterialDto.getUnitTransRate();

                String[] splitUnit = unitTransRate.split("/");
                BigDecimal materialAssistCountApply = applyFormDetailVo.getMaterialAssistCountApply();
                //主单位数量
                multiply = materialAssistCountApply.divide(new BigDecimal(splitUnit[1])).multiply(new BigDecimal(splitUnit[0]));
                applyFormDetailVo.setMaterialAssistCountApproval(applyFormDetailVo.getMaterialAssistCountApply());
                // 四舍五入取整
                applyFormDetailVo.setMaterialCountApproval(multiply.setScale(0,BigDecimal.ROUND_HALF_UP));
            } catch (Exception e) {
                log.error("主辅单位换算异常，物料编码是：{}",applyFormDetailVo.getMaterialCode());
            }

        }

        return Response.success(applyFormDetailVos);
    }

    /**
     * 根据申领明细id查询申领单明细信息
     * @param queryDto
     * @return
     */
    @Override
    public Response<List<MaterialApplyFormDetailVo>> queryApplyFormDetailByIds(ApplyFormDetailByIdsQueryDto queryDto) {
        if (CollectionUtils.isEmpty(queryDto.getDetailIds())){
            return Response.fail(ResultCode.PARAMS_NOT_VALIDATE);
        }
        List<MaterialApplyFormDetailVo> materialApplyFormDetailVos = tbMaterialApplyFormDetailDal.queryApplyFormDetailByIds(queryDto.getDetailIds());
        return Response.success(materialApplyFormDetailVos);
    }

    /**
     * 根据申领单id查询申领单物料申领明细信息--业务中台调用
     * @param queryDto
     * @return
     */
    @Override
    public Response<List<MaterialApplyFormDetailVo>> queryApplyFormDetailByApplyId(ApplyFormDetailByApplyIdQueryDto queryDto) {
        if (org.springframework.util.CollectionUtils.isEmpty(queryDto.getApplyIds())){
            log.info("申领物料查询，申领单id为空!");
            return Response.fail(OperationResultCode.E900025);
        }

        List<MaterialApplyFormDetailVo> resultList = tbMaterialApplyFormDetailDal.queryApplyFormDetailByApplyId(queryDto);
        return Response.success(resultList);
    }


    private List<TbMaterial> getMaterialDetail(List<String> materCodeList) {
        return tbMaterialDal.getMaterialDetail(materCodeList);
    }
    
    /**
     * 获取物料的注册证号码
     * @param materials 物料详情
     * @return 编码 key  注册证号 value
     */
    private Map<String,String> getMaterialRegistrationNo(List<TbMaterial> materials) {
         return materials.stream()
                 .collect(Collectors.toMap(TbMaterial::getMaterialCode, TbMaterial::getRegistrationNo));
    }
    
    /**
     * 获取物料的注册证名称
     * @param materials 物料编码
     * @return 编码 key  注册证号 value
     */
    private Map<String,String> getMaterialRegistrationName(List<TbMaterial> materials) {
        return materials.stream()
                .collect(Collectors.toMap(TbMaterial::getMaterialCode, TbMaterial::getRegistrationName));
    }
    
    /**
     * 获取物料的注册人
     * @param materials 物料编码
     * @return 编码 key  注册人 value
     */
    private Map<String,String> getMaterialRegistrant(List<TbMaterial> materials) {
        return materials.stream()
                .collect(Collectors.toMap(TbMaterial::getMaterialCode, TbMaterial::getRegistrant));
    }
    
    /**
     * 获取物料的注册人code
     * @param materials 物料编码
     * @return 编码 key  注册人code value
     */
    private Map<String,String> getMaterialRegistrantCode(List<TbMaterial> materials) {
        return materials.stream()
                .collect(Collectors.toMap(TbMaterial::getMaterialCode, TbMaterial::getRegistrantCode));
    }
    
    /**
     * 获取物料的主单位code
     * @param materials 物料编码
     * @return 编码 key  主单位code value
     */
    private Map<String,String> getMaterialMeasnameCode(List<TbMaterial> materials) {
        return materials.stream()
                .collect(Collectors.toMap(TbMaterial::getMaterialCode, TbMaterial::getPrimaryUnitCode));
    }
    
    /**
     * 获取物料的主单位code
     * @param materials 物料编码
     * @return 编码 key  主单位code value
     */
    private Map<String,String> getMaterialMeasnamefCode(List<TbMaterial> materials) {
        return materials.stream()
                .collect(Collectors.toMap(TbMaterial::getMaterialCode, TbMaterial::getSecondaryUnitCode));
    }
    
    /**
     * 获取物料的存储方式
     * @param materials 物料编码
     * @return 编码 key  存储方式 value
     */
    private Map<String,String> getMaterialStorageRequirement(List<TbMaterial> materials) {
        return materials.stream()
                .collect(Collectors.toMap(TbMaterial::getMaterialCode, TbMaterial::getStorageRequirement));
    }
}
