
package com.labway.business.center.operation.persistence.entity;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 物料类型表
 * 
 * <AUTHOR>
 * @since 2023/3/13 9:50
 */

@Getter
@Setter
public class TbMaterialType extends CommonFieldEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 信息hash
     */
    private String infoHash;
    /**
     * 同步唯一码
     */
    private String fromNo;
    /**
     * 物料基本分类编码
     */
    private String materialTypeCode;
    /**
     * 物料基本分类名称
     */
    private String materialTypeName;
    /**
     * 启用状态:1未启用，2已启用，3已停用
     */
    private Integer enableState;

}
