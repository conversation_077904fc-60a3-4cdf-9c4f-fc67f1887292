package com.labway.business.center.operation.persistence.mapper;

import com.labway.business.center.core.injector.ExtBaseMapper;
import com.labway.business.center.operation.dto.material.MaterialTypeCountQueryDto;
import com.labway.business.center.operation.dto.material.StockOutDetailDto;
import com.labway.business.center.operation.persistence.entity.TbMaterialApplyFormDetail;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 物料申领单明细表(TbMaterialApplyFormDetail)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-03-13 11:15:43
 */
public interface TbMaterialApplyFormDetailMapper extends ExtBaseMapper<TbMaterialApplyFormDetail> {

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<TbMaterialApplyFormDetail> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<TbMaterialApplyFormDetail> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<TbMaterialApplyFormDetail> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<TbMaterialApplyFormDetail> entities);

    /**
     * 查询申领单的物料种类数量
     *
     * @param applyItemIds
     * @return
     */
    List<MaterialTypeCountQueryDto> queryMaterialTypeCountByApplyIds(@Param("list") List<String> applyItemIds);

    /**
     * 更新物料明细出库数量-批量
     *
     * @param stockOutDetailDtos
     * @return
     */
    int updateStockOutCountBatch(@Param("list") List<StockOutDetailDto> stockOutDetailDtos);

}

