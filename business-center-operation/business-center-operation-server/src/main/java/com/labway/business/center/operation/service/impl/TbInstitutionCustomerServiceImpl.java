package com.labway.business.center.operation.service.impl;

import com.labway.business.center.operation.converter.InstitutionCustomerConverter;
import com.labway.business.center.operation.dto.user.SelectCompanyListDto;
import com.labway.business.center.operation.dto.user.SelectCustomerListDto;
import com.labway.business.center.operation.dto.user.TbInstitutionCompanyDto;
import com.labway.business.center.operation.dto.user.TbInstitutionCustomerDto;
import com.labway.business.center.operation.exception.MaterialErrorCode;
import com.labway.business.center.operation.repository.TbInstitutionCompanyDal;
import com.labway.business.center.operation.repository.TbInstitutionCustomerDal;
import com.labway.business.center.operation.service.user.TbInstitutionCustomerService;
import com.labway.business.center.operation.vo.user.InstitutionCustomerVo;
import com.swak.frame.dto.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 客商信息表 service impl
 * 
 * <AUTHOR>
 * @since 2023/3/13 19:27
 */
@Slf4j
@DubboService
public class TbInstitutionCustomerServiceImpl implements TbInstitutionCustomerService {

    @Resource
    private TbInstitutionCustomerDal tbCustomerDal;
    @Resource
    private TbInstitutionCompanyDal tbInstitutionCompanyDal;
    @Resource
    private InstitutionCustomerConverter institutionCustomerConverter;

    @Override
    public Response<List<InstitutionCustomerVo>> selectCustomerList(SelectCustomerListDto condition) {
        var companyId = condition.getCompanyId();

        // 检查公司
        if (StringUtils.isNotBlank(companyId)) {
            SelectCompanyListDto selectCompanyListDto = new SelectCompanyListDto();
            selectCompanyListDto.setCompanyId(companyId);
            List<TbInstitutionCompanyDto> tbInstitutionCompanyDtos =
                tbInstitutionCompanyDal.selectCompanyList(selectCompanyListDto);
            if (CollectionUtils.isEmpty(tbInstitutionCompanyDtos)) {
                Response.fail(MaterialErrorCode.U100007);
            }
        }
        List<TbInstitutionCustomerDto> tbInstitutionCustomerDtos = tbCustomerDal.selectTbCustomerInfoDto(condition);
        if (CollectionUtils.isEmpty(tbInstitutionCustomerDtos)) {
            return Response.success();
        }
        return Response.success(tbInstitutionCustomerDtos.stream().map(item -> institutionCustomerConverter.convertInstitutionCustomerdto2vo(item)).collect(Collectors.toList()));
    }

}
