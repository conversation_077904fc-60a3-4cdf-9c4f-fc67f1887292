package com.labway.business.center.operation.persistence.mapper;

import com.labway.business.center.core.injector.ExtBaseMapper;
import com.labway.business.center.operation.dto.material.SelectMaterialTypeListDto;
import com.labway.business.center.operation.dto.material.TbMaterialTypeDto;
import com.labway.business.center.operation.persistence.entity.TbMaterialType;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 物料类型表 mapper
 * 
 * <AUTHOR>
 * @since 2023/3/13 17:23
 */
public interface TbMaterialTypeMapper extends ExtBaseMapper<TbMaterialType> {
    /**
     * 查询 所有 物料类型
     *
     * @return list
     */
    List<TbMaterialTypeDto> selectTbMaterialTypeDto();

    /**
     * 根据 物料类型 主数据 唯一码 失效 数据
     *
     * @param fromNoList ncc 唯一码
     */
    void invalidMaterialTypeDtoByFromNo(@Param("list") List<String> fromNoList);

    /**
     * 保存 物料类型 信息
     *
     * @param conditions list {@link TbMaterialTypeDto}
     */
    void saveMaterialTypeDtoList(List<TbMaterialTypeDto> conditions);

    /**
     * 物料类型 list
     *
     * @param condition 参数
     * @return list
     */
    List<TbMaterialTypeDto> selectMaterialTypeList(SelectMaterialTypeListDto condition);
}
