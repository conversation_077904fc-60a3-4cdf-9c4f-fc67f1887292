package com.labway.business.center.operation.util;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jdk8.Jdk8Module;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.module.paramnames.ParameterNamesModule;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Objects;

/**
 * JacksonUtil
 *
 * <AUTHOR>
 * @since 2023/1/28 11:52
 */
@Slf4j
public class JacksonUtil {
    private JacksonUtil() {}

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();
    static {
        /*
          允许有不存在的字段
          默认非空不输出，时间格式
         */
        OBJECT_MAPPER.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        OBJECT_MAPPER.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        OBJECT_MAPPER.setDateFormat(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
        OBJECT_MAPPER.registerModule(new ParameterNamesModule()).registerModule(new Jdk8Module())
            .registerModule(new JavaTimeModule()).disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
    }

    /**
     * 将 JSON 对象转换为 JavaBean
     *
     * @param obj
     * @param clazz
     * @return
     */
    public static <T> T convertValue(Object obj, Class<T> clazz) {
        return OBJECT_MAPPER.convertValue(obj, clazz);
    }

    /**
     * 转换为 JSON 字符串
     *
     * @param obj 转换对象
     * @return 结果
     */
    public static String objToJson(Object obj) {
        try {
            return OBJECT_MAPPER.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            log.info("转换数据异常 >>>>>> ", e);
            return null;
        }
    }

    /**
     * 转换为 JavaBean
     *
     * @param jsonString json字符传
     * @param clazz 类文件
     * @return 结果
     */
    public static <T> T jsonToPojo(String jsonString, Class<T> clazz) {
        if (Objects.isNull(jsonString) || Objects.isNull(clazz)) {
            return null;
        }
        try {
            OBJECT_MAPPER.configure(DeserializationFeature.ACCEPT_SINGLE_VALUE_AS_ARRAY, true);
            return OBJECT_MAPPER.readValue(jsonString, clazz);
        } catch (IOException e) {
            log.info("解析json格式文件 {} 异常 >>>>>> {} ", jsonString, e);
            return null;
        }
    }

}
