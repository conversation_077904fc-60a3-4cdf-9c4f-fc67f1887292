package com.labway.business.center.operation.persistence.mapper;


import com.labway.business.center.core.injector.ExtBaseMapper;
import com.labway.business.center.operation.dto.user.ApplyCommunityDto;
import com.labway.business.center.operation.dto.user.SelectApplyCommunityListDto;
import com.labway.business.center.operation.persistence.entity.TbInstitutionCustomerMapping;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 客商映射第三方信息 Mapper
 * 
 * <AUTHOR>
 * @since 2023/3/15 14:39
 */
@Repository
public interface TbInstitutionCustomerMappingMapper extends ExtBaseMapper<TbInstitutionCustomerMapping> {
    /**
     * 申领社区 list
     *
     * @return list {@link ApplyCommunityDto}
     */
    List<ApplyCommunityDto> selectApplyCommunityList(SelectApplyCommunityListDto condition);
}
