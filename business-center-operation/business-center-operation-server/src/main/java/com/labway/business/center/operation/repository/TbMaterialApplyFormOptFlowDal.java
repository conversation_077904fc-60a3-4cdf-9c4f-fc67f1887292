package com.labway.business.center.operation.repository;

import com.alibaba.fastjson.JSON;
import com.labway.business.center.operation.constants.CommonConstant;
import com.labway.business.center.operation.dto.material.MaterialApplyFormOptFlowDto;
import com.labway.business.center.operation.persistence.entity.TbMaterialApplyFormOptFlow;
import com.labway.business.center.operation.persistence.mapper.TbMaterialApplyFormOptFlowMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2023/03/15 09:04
 * @description 审批单操作记录dal
 **/
@Repository
public class TbMaterialApplyFormOptFlowDal {

    @Resource
    private TbMaterialApplyFormOptFlowMapper tbMaterialApplyFormOptFlowMapper;


    /**
     * 批量新增申领单操作记录
     * @param batchDtos
     * @return
     */
    public int insertApplyFormOptFlowBatch(List<MaterialApplyFormOptFlowDto> batchDtos){
        if (CollectionUtils.isEmpty(batchDtos)){
            return CommonConstant.INT_ZERO;
        }
        return tbMaterialApplyFormOptFlowMapper.insertBatch(JSON.parseArray(JSON.toJSONString(batchDtos), TbMaterialApplyFormOptFlow.class));
    }


}


