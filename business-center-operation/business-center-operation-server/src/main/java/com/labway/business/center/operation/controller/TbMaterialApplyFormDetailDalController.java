package com.labway.business.center.operation.controller;

import com.labway.business.center.operation.dto.material.ApplyFormDetailByApplyIdQueryDto;
import com.labway.business.center.operation.dto.material.ApplyFormDetailByIdsQueryDto;
import com.labway.business.center.operation.service.material.TbMaterialApplyFormDetailServiceOperation;
import com.labway.business.center.operation.vo.material.MaterialApplyFormDetailVo;
import com.swak.frame.dto.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 申请单详细信息
 * <AUTHOR>
 * @Description 申请单详细信息
 * @Date 2023/3/14 9:37
 */
@Slf4j
@RestController
@RequestMapping("/material-detail")
public class TbMaterialApplyFormDetailDalController {
    
    @Resource
    private TbMaterialApplyFormDetailServiceOperation tbMaterialApplyFormDetailServiceOperation;

    /**
     * 根据申领明细id查询申领单明细信息
     */
    @PostMapping("/queryApplyFormDetailByIds")
    public Response<List<MaterialApplyFormDetailVo>> queryApplyFormDetailByIds(@RequestBody @Validated ApplyFormDetailByIdsQueryDto queryDto){
        return tbMaterialApplyFormDetailServiceOperation.queryApplyFormDetailByIds(queryDto);
    }

    /**
     * 根据申领单id查询申领单物料申领明细信息--业务中台调用
     */
    @PostMapping("/queryApplyFormDetailByApplyId")
    public Response<List<MaterialApplyFormDetailVo>> queryApplyFormDetailByApplyId(@RequestBody @Validated ApplyFormDetailByApplyIdQueryDto queryDto){
        return tbMaterialApplyFormDetailServiceOperation.queryApplyFormDetailByApplyId(queryDto);
    }
}
