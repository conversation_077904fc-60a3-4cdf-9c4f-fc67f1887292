package com.labway.business.center.operation.service.material;

import com.labway.business.center.operation.dto.material.SelectMaterialTypeListDto;
import com.labway.business.center.operation.dto.material.TbMaterialTypeDto;

import java.util.List;

/**
 * 物料类型表 service
 * 
 * <AUTHOR>
 * @since 2023/3/13 10:05
 */
public interface TbMaterialTypeService {

    /**
     * 全量从主数据获取物料类型调度
     */
    void syncAllMaterialType();

    /**
     * 物料类型 list
     * 
     * @param condition 参数
     * @return list
     */
    List<TbMaterialTypeDto> selectMaterialTypeList(SelectMaterialTypeListDto condition);
}
