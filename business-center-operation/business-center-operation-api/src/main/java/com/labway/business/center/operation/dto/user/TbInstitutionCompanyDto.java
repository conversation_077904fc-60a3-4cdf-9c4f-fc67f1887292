package com.labway.business.center.operation.dto.user;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 公司信息 Dto
 * 
 * <AUTHOR>
 * @since 2023/3/15 10:41
 */
@Getter
@Setter
public class TbInstitutionCompanyDto {

    /**
     * 公司编码全局唯一
     */
    private String companyCode;
    /**
     * 公司名称
     */
    private String companyName;
    /**
     * 状态:0停用,1启用
     */
    private Integer status;
    /**
     * 雪花生成唯一id
     */
    private String itemId;

    /**
     * 是否有效
     */
    private Boolean enabled;
    /**
     * 操作人编码
     */
    private String optUserCode;
    /**
     * 操作人账号
     */
    private String optUserAccount;
    /**
     * 操作人姓名
     */
    private String optUserName;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 修改时间
     */
    private Date updateTime;
}
