package com.labway.business.center.operation.service.material;

import com.labway.business.center.operation.dto.material.CustomerMaterialAllotDto;
import com.labway.business.center.operation.dto.material.CustomerMaterialCopyDto;
import com.labway.business.center.operation.dto.material.MaterialStockDto;
import com.labway.business.center.operation.dto.material.TbMaterialDto;
import com.labway.business.center.operation.dto.material.community.CommunitySelectMaterialDto;
import com.labway.business.center.operation.vo.material.MaterialStockVo;
import com.labway.business.center.operation.vo.material.MaterialVo;
import com.swak.frame.dto.Response;

import java.util.List;

/**
 * 物料客商表 Service
 * 
 * <AUTHOR>
 * @since 2023/3/13 18:58
 */
public interface TbMaterialCustomerService {

    /**
     * 社区 查看 对应客商下 物料信息
     *
     * @param condition 参数
     * @return list
     */
    Response<List<MaterialVo>> communitySelectCustomerMaterialList(CommunitySelectMaterialDto condition);

    /**
     * 社区 查看 对应客商下 物料信息 包含库存
     * 
     * @param condition 参数
     * @return list
     */
    Response<List<MaterialStockVo>> communitySelectCustomerMaterialStockList(CommunitySelectMaterialDto condition);

    /**
     * 物料下发
     *
     * @param condition 参数 {@link CustomerMaterialAllotDto}
     */
    void customerMaterialAllot(CustomerMaterialAllotDto condition);

    /**
     * 物料复制
     * 
     * @param condition 参数 {@link CustomerMaterialCopyDto}
     */
    void customerMaterialCopy(CustomerMaterialCopyDto condition);
}
