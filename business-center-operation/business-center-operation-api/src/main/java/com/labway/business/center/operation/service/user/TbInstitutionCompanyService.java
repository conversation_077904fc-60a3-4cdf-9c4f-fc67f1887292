package com.labway.business.center.operation.service.user;

import com.labway.business.center.operation.dto.user.SelectCompanyListDto;
import com.labway.business.center.operation.vo.user.InstitutionCompanyVo;
import com.swak.frame.dto.Response;

import java.util.List;

/**
 * 公司信息 service
 * 
 * <AUTHOR>
 * @since 2023/3/15 10:38
 */
public interface TbInstitutionCompanyService {

    /**
     * 公司信息 list
     *
     * @param condition 参数 {@link SelectCompanyListDto}
     * @return list {@link InstitutionCompanyVo}
     */
    Response<List<InstitutionCompanyVo>> selectCompanyList(SelectCompanyListDto condition);
}
