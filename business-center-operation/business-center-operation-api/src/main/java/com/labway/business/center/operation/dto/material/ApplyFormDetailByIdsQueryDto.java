package com.labway.business.center.operation.dto.material;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2023/03/22 17:23
 * @description
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ApplyFormDetailByIdsQueryDto implements Serializable {

    @NotEmpty(message = "明细id不能为空！")
    private List<String> detailIds;

}