package com.labway.business.center.operation.service.material;


import com.labway.business.center.operation.dto.material.SaveTbMaterialSyncFlowDto;
import com.labway.business.center.operation.dto.material.TbMaterialSyncFlowDto;
import com.swak.frame.dto.Response;

import java.util.List;

/**
 * 物料同步流水表 Service
 * 
 * <AUTHOR>
 * @since 2023/3/14 15:47
 */
public interface TbMaterialSyncFlowService {

    /**
     * 获取 最近一条成功的流水记录
     * 
     * @return 记录
     */
    TbMaterialSyncFlowDto recentFlowRecord();

    /**
     * 添加 流水记录
     * 
     * @param condition 参数
     */
    void saveFlowRecord(SaveTbMaterialSyncFlowDto condition);

    /**
     * 获取 近两天失败物料流水记录
     *
     * @return 记录
     */
    Response<List<TbMaterialSyncFlowDto>> recentFailureFlowRecord();
}
