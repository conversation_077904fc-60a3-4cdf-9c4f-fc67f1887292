package com.labway.business.center.operation.dto.user;

import lombok.Getter;
import lombok.Setter;

/**
 * 权限资源 Dto
 * 
 * <AUTHOR>
 * @since 2023/3/10 13:23
 */
@Getter
@Setter
public class PermissionResourceDto {
    /**
     * 雪花生成唯一id
     */
    private String itemId;
    /**
     * 资源编码（全局唯一）
     */
    private String resourceNo;
    /**
     * 资源类型:1菜单目录
     */
    private String resourceType;
    /**
     * 资源所属服务
     */
    private String resourceServer;
    /**
     * 资源值
     */
    private String value;
    /**
     * 资源描述
     */
    private String description;
    /**
     * 对应的图标
     */
    private String icon;
    /**
     * 对应前端路由地址
     */
    private String routerUrl;
    /**
     * 上级资源ID
     */
    private String parentId;
    /**
     * 显示顺序
     */
    private Integer displayIndex;
    /**
     * 页签是否展示
     */
    private Boolean tagFlag;
}
