package com.labway.business.center.operation.service.material;

import com.labway.business.center.operation.dto.material.TbNotifyCompensationDto;
import com.labway.business.center.operation.vo.material.TbNotifyCompensationVo;
import com.swak.frame.dto.Response;

import java.util.List;

/**
 * 社区lis回调通知补偿表(TbNotifyCompensation)表服务接口
 *
 * <AUTHOR>
 * @since 2023-03-20 11:44:54
 */
public interface TbNotifyCompensationService {

    /**
     * 查询需要回调统治的数据集合
     * @return
     */
    Response<List<TbNotifyCompensationVo>> queryListForNotify();

    /**
     * 批量更新补偿信息
     * @param updateList
     * @return
     */
    int updateList(List<TbNotifyCompensationDto> updateList);

    void lisNotifyCompensation();
}

