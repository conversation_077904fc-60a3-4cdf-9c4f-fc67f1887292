package com.labway.business.center.operation.dto.material;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 2023/03/20 12:00
 * @description
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TbNotifyCompensationDto implements Serializable {
    //通知补偿id 雪花生成唯一id
    private String itemId;
    //请求地址
    private String requestUrl;
    //请求头信息
    private String requestHeard;
    //请求体信息
    private String requestBody;
    //请求方式 1get 2post
    private Integer requestType;
    //请求次数
    private Integer requestTimes;
    //是否执行成功 0否1是
    private Integer isSendSuccess;
    //是否有效
    private Boolean enabled;
    //操作人编码
    private String optUserCode;
    //操作人账号
    private String optUserAccount;
    //操作人姓名
    private String optUserName;
    //创建时间
    private Date createTime;
    //修改时间
    private Date updateTime;



}