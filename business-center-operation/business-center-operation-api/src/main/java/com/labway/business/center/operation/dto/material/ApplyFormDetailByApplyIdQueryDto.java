package com.labway.business.center.operation.dto.material;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2023/03/28 09:26
 * @description
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ApplyFormDetailByApplyIdQueryDto implements Serializable {

    @NotEmpty(message = "申领单id不能为空！")
    private List<String> applyIds;

}