package com.labway.business.center.operation.dto.material.mdm;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 试剂耗材信息 Vo
 *
 * <AUTHOR>
 * @since 2023/3/2 10:11
 */
@Getter
@Setter
public class ReagentVo {

    /**
     * 信息hash
     */
    private String infoHash;
    /**
     * ncc唯一码
     */
    private String fromNo;
    /**
     * 所属组织ncc关联id
     */
    private String ncOrgId;
    /**
     * 所属组织名称
     */
    private String orgName;
    /**
     * NC试剂编码
     */
    private String ncReagentNo;
    /**
     * 试剂名称
     */
    private String reagentName;
    /**
     * 英文名称
     */
    private String englishReagentName;
    /**
     * 试剂类型ncc关联id
     */
    private String ncReagentTypeId;
    /**
     * 试剂类型编码
     */
    private String reagentTypeCode;
    /**
     * 试剂类型
     */
    private String reagentType;
    /**
     * 试剂规格
     */
    private String specification;
    /**
     * 英文规格
     */
    private String englishSpecification;
    /**
     * 型号
     */
    private String reagentModel;
    /**
     * 物料简称
     */
    private String reagentAlias;
    /**
     * 助记码
     */
    private String mnemonicCode;
    /**
     * 图号
     */
    private String graphId;
    /**
     * 条形码
     */
    private String barcode;
    /**
     * 主单位ncc关联id
     */
    private String primaryUnitId;
    /**
     * 主单位
     */
    private String primaryUnit;
    /**
     * 主单位ncc关联code
     */
    private String primaryUnitCode;
    /**
     * 产品线ncc关联id
     */
    private String productLineId;
    /**
     * 产品线
     */
    private String productLineName;
    /**
     * 品牌ncc关联id
     */
    private String brandId;
    /**
     * 品牌
     */
    private String brandName;
    /**
     * 服务类
     */
    private Boolean fee;
    /**
     * 价格折扣
     */
    private Boolean discountFlag;
    /**
     * 助促销品
     */
    private Boolean promotionFlag;
    /**
     * 成套件
     */
    private Boolean partsFlag;
    /**
     * 物料税率ncc关联id
     */
    private String reagentTaxesId;
    /**
     * 物料税率
     */
    private String reagentTaxesName;
    /**
     * 临检类存货分类
     */
    private String def1;
    /**
     * 是否低值易燃品
     */
    private String def2;
    /**
     * 专业组
     */
    private String def3;
    /**
     * 储存条件
     */
    private String def6;
    /**
     * 注册证号码
     */
    private String def7;
    /**
     * 注册证号有效期
     */
    private String def8;
    /**
     * 注册证名称
     */
    private String def9;
    /**
     * 注册证名称/备案人名称
     */
    private String def10;
    /**
     * 注册证名称/备案人名称code
     */
    private String def10Code;
    /**
     * 仪器平台
     */
    private String def11;
    /**
     * 标识
     */
    private String def13;
    /**
     * 管理类别
     */
    private String def14;
    /**
     * 经营范围
     */
    private String def15;
    /**
     * 采购分配
     */
    private String def18;
    /**
     * 生产许可证号/备案号
     */
    private String def19;
    /**
     * 生产企业
     */
    private String def20;
    /**
     * 启用状态:1未启用，2已启用，3已停用
     */
    private Integer enableState;
    /**
     * 创建人
     */
    private String creator;
    /**
     * 创建时间
     */
    private String creationTime;
    /**
     * 最后修改人
     */
    private String modifier;
    /**
     * 最后修改时间
     */
    private String modifiedTime;
    /**
     * 雪花生成唯一id
     */
    private String itemId;

    /**
     * 试剂耗材 辅计量信息
     */
    private List<ReagentAssistVo> reagentAssistVoList;

}
