package com.labway.business.center.operation.dto.material;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MaterialApplyFormDetailAddDto implements Serializable {
    //申领单明细id 雪花生成唯一id
    private String itemId;
    //物料申领单id
    private String applyFormId;
    //物料申领单编码-申请方单据标识
    private String applyFormCode;
    //申领物料编码
    private String materialCode;
    //申领物料名称
    private String materialName;
    //申领物料辅单位
    private String materialAssistUnit;
    //物料辅单位数量(申请数量)
    private BigDecimal materialAssistCountApply;
    //物料辅单位数量(审批数量)
    private BigDecimal materialAssistCountApproval;
    //申领物料主单位
    private String materialUnit;
    //物料主单位数量(申请数量)
    private BigDecimal materialCountApply;
    //物料主单位数量(审批数量)
    private BigDecimal materialCountApproval;
    //物料规格
    private String materialSpecification;
    //是否有效
    private Boolean enabled;
    //操作人编码
    private String optUserCode;
    //操作人账号
    private String optUserAccount;
    //操作人姓名
    private String optUserName;
    //创建时间
    private Date createTime;
    //修改时间
    private Date updateTime;



}
