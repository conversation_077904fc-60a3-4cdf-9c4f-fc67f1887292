package com.labway.business.center.operation.dto.material;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2023/03/20 19:12
 * @description 申领单出库 dto
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StockOutNotifyLisDto implements Serializable {

    /**
     * 申领单id
     */
    @NotBlank(message = "请选择申领单！")
    private String applyId;

    /**
     * 出库单信息
     */
    @NotNull(message = "出库单信息不能为空！")
    TbMaterialSupplyDto tbMaterialSupplyDto;

    /**
     * 出库单详情
     */
    @NotNull(message = "出库单详情不能为空！")
    List<TbMaterialSupplyDetailDto> materialSupplyDetailDtos;

    /**
     * 出库标识 0部分出库 1全部出库
     */
    @NotNull(message = "出库标识不能为空!")
    private Integer stockOutFlag;

    /**
     * 操作人编码
     */
    private String optUserCode;

    /**
     * 操作人账户
     */
    private String optUserAccount;

    /**
     * 操作人名称
     */
    private String optUserName;


}