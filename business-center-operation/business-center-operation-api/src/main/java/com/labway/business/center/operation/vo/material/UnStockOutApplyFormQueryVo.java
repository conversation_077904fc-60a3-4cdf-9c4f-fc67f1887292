package com.labway.business.center.operation.vo.material;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UnStockOutApplyFormQueryVo implements Serializable {

    //申领单id 雪花生成唯一id
    private String itemId;
    //申领单编码-申请方唯一标识
    private String applyCode;
    //申领时间
    private Date applyTime;
    //申领人id
    private String applyUserId;
    //申领人名称
    private String appleUserName;
    //申领机构编码
    private String applyOrgCode;
    //申领机构名称
    private String applyOrgName;
    //申领社区编码
    private String applyLisCode;
    //申领社区名称
    private String applyLisName;
    //申领单状态；0待二审 1待出库 2部分出库 3已出库 4已驳回
    private Integer applyStatus;
    //申领单提交次数
    private Integer applyCommitTimes;
    //申领单审批人id
    private String approvalUserId;
    //申领单审批人姓名
    private String approvalUserName;
    //申领单审批时间
    private Date approvalTime;
    //是否有效
    private Boolean enabled;
    //操作人编码
    private String optUserCode;
    //操作人账号
    private String optUserAccount;
    //操作人姓名
    private String optUserName;
    //创建时间
    private Date createTime;
    //修改时间
    private Date updateTime;
    //申清单备注
    private String applyRemark;
    //申请单驳回原因
    private String rejectReason;

    // 物料明细
    List<UnStockOutApplyFormDetailQueryVo> detailQueryDtos;

}
