<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.center.material.persistence.mapper.TbMaterialSupplyMapper">

    <select id="selectSupplyPage" resultType="com.labway.business.center.third.ncc.dto.material.SupplyInfoDto">
        select tms.*
        from tb_material_supply tms
        <where>
            supply_warehouse_org_id = #{condition.orgCode}
            <if test="condition.supplyCustomerId != null and condition.supplyCustomerId != ''">
                and tms.supply_customer_id = #{condition.supplyCustomerId}
            </if>
            <if test="condition.supplyTimeStart != null and condition.supplyTimeEnd != null ">
                and
                (
                tms.supply_time &gt;= #{condition.supplyTimeStart} and tms.supply_time &lt;= #{condition.supplyTimeEnd}
                )
            </if>
            and tms.enabled = 1
        </where>
        order by tms.create_time desc
    </select>
</mapper>
