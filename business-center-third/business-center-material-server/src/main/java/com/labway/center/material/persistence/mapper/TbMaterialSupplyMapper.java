package com.labway.center.material.persistence.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.labway.business.center.third.ncc.dto.material.SelectSupplyPageDto;
import com.labway.business.center.third.ncc.dto.material.SupplyInfoDto;
import com.labway.business.center.third.ncc.dto.material.TbMaterialDto;
import com.labway.center.material.persistence.entity.TbMaterialSupply;
import org.apache.ibatis.annotations.Param;

/**
 * 物料出库信息 Mapper 接口
 * 
 * <AUTHOR>
 * @since 2023-03-14
 */
public interface TbMaterialSupplyMapper extends BaseMapper<TbMaterialSupply> {

    /**
     * 物料信息 分页查看
     *
     * @param page 分页参数
     * @param condition 条件参数
     * @return Page
     */
    Page<SupplyInfoDto> selectSupplyPage(Page<TbMaterialDto> page, @Param("condition") SelectSupplyPageDto condition);

}
