package com.labway.center.material.repository;

import com.labway.business.center.operation.dto.user.ApplyCommunityDto;
import com.labway.business.center.third.ncc.dto.center.SelectApplyCommunityListDto;
import com.labway.center.material.persistence.mapper.TbInstitutionCustomerMappingMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * 客商映射第三方信息 Dal
 * 
 * <AUTHOR>
 * @since 2023/3/15 14:38
 */
@Slf4j
@Repository
public class TbInstitutionCustomerMappingDal {

    @Resource
    private TbInstitutionCustomerMappingMapper tbInstitutionCustomerMappingMapper;

    /**
     * 申领社区 list
     *
     * @return list {@link ApplyCommunityDto}
     */
    public List<ApplyCommunityDto> selectApplyCommunityList(SelectApplyCommunityListDto condition) {
        return tbInstitutionCustomerMappingMapper.selectApplyCommunityList(condition);
    }
}
