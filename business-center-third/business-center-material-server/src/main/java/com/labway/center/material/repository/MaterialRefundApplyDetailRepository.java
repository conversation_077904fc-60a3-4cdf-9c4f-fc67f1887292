package com.labway.center.material.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.labway.business.center.third.ncc.request.CheckMaterialRefundRequest;
import com.labway.center.material.persistence.entity.MaterialRefundApplyDetail;
import com.labway.center.material.persistence.mapper.MaterialRefundApplyDetailMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2024/03/07 14:33
 **/
@Repository
public class MaterialRefundApplyDetailRepository {
    @Resource
    private MaterialRefundApplyDetailMapper materialRefundApplyDetailMapper;

    public List<MaterialRefundApplyDetail> searchBySupplyNos(List<String> refundSupplyNos) {
        if (CollectionUtils.isEmpty(refundSupplyNos)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<MaterialRefundApplyDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(MaterialRefundApplyDetail::getRefundSupplyNo, refundSupplyNos);
        return materialRefundApplyDetailMapper.selectList(queryWrapper);
    }

    public void saveBatch(List<MaterialRefundApplyDetail> insertRefundDetails) {
        if (CollectionUtils.isEmpty(insertRefundDetails)) {
            return;
        }
        materialRefundApplyDetailMapper.insertBatchSomeColumn(insertRefundDetails);
    }

    public List<MaterialRefundApplyDetail> searchByRefundSupplyNos(List<String> refundSupplyNos) {
        if (CollectionUtils.isEmpty(refundSupplyNos)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<MaterialRefundApplyDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(MaterialRefundApplyDetail::getRefundSupplyNo, refundSupplyNos);
        return materialRefundApplyDetailMapper.selectList(queryWrapper);
    }

    /**
     * 根据出库单号 物料 批次 查询退库信息
     * @param supplyNo
     * @param materialCode
     * @param batchNo
     * @return
     */
    public List<MaterialRefundApplyDetail> searchBySupplyNoAndMaterialInfo(String supplyNo, String materialCode, String batchNo) {
        if (StringUtils.isAnyBlank(supplyNo, materialCode, batchNo)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<MaterialRefundApplyDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MaterialRefundApplyDetail::getSupplyNo, supplyNo)
                .eq(MaterialRefundApplyDetail::getMaterialCode, materialCode)
                .eq(MaterialRefundApplyDetail::getBatchNo, batchNo);
        return materialRefundApplyDetailMapper.selectList(queryWrapper);
    }

    /**
     * 根据退库单号 查询退库信息
     * @param refundSupplyNo
     * @return
     */
    public List<MaterialRefundApplyDetail> searchByRefundSupplyNo(String refundSupplyNo) {
        if (StringUtils.isBlank(refundSupplyNo)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<MaterialRefundApplyDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MaterialRefundApplyDetail::getRefundSupplyNo, refundSupplyNo);
        return materialRefundApplyDetailMapper.selectList(queryWrapper);
    }

    public void updateNumberBatch(List<CheckMaterialRefundRequest.CheckDetailRequest> detailRequests, String refundSupplyNo) {
        materialRefundApplyDetailMapper.updateNumberBatch(detailRequests, refundSupplyNo);
    }
}