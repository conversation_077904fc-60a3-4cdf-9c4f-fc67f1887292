package com.labway.center.material.converter;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.labway.business.center.core.config.PageResponse;
import com.labway.business.center.third.ncc.dto.material.SupplyInfoDto;
import com.labway.business.center.third.ncc.response.SupplyDetailResponse;
import com.labway.business.center.third.ncc.response.SupplyPageResponse;
import com.labway.business.center.third.ncc.vo.material.SupplyInfoVo;
import com.labway.center.material.persistence.entity.TbMaterialSupply;
import com.labway.center.material.persistence.entity.TbMaterialSupplyDetail;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2023/05/22 16:58
 **/
@Mapper(componentModel = "spring")
public interface SupplyInfoConvert {
    SupplyInfoVo convertDTO2VO(SupplyInfoDto dto);

	PageResponse<SupplyPageResponse> convertEntityPage2ResponsePage(Page<TbMaterialSupply> page);

	@Mapping(target = "supplyId", source = "itemId")
	SupplyPageResponse tbMaterialSupplyToSupplyPageResponse(TbMaterialSupply tbMaterialSupply);

	List<SupplyDetailResponse> convertDetailEntityList2ResponseList(List<TbMaterialSupplyDetail> detailResponses);
}