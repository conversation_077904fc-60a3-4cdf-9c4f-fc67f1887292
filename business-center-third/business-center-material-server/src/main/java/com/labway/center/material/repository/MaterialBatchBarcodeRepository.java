package com.labway.center.material.repository;

import com.labway.business.center.third.ncc.dto.lims.BusinessCenterMaterialBarcodeSearchDto;
import com.labway.business.center.third.ncc.vo.lims.BizMaterialBarcodeVo;
import com.labway.center.material.persistence.entity.MaterialBatchBarcode;
import com.labway.center.material.persistence.mapper.MaterialBatchBarcodeMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @Description
 * @Date 2024/11/25 17:21
 */
@Repository
public class MaterialBatchBarcodeRepository {

	@Resource
	private MaterialBatchBarcodeMapper materialBatchBarcodeMapper;


	public List<BizMaterialBarcodeVo> searchListByMaterialCodesAndBatchNos(List<BusinessCenterMaterialBarcodeSearchDto.MaterialInfoDto> materialInfoDtos,  String orgId) {
		return materialBatchBarcodeMapper.searchListByMaterialCodesAndBatchNos(materialInfoDtos, orgId);
	}

	public void saveBatch(List<MaterialBatchBarcode> batchBarcodes, String orgCode) {
		if (CollectionUtils.isEmpty(batchBarcodes)) {
			return;
		}
		batchBarcodes.forEach(item -> item.setOrgCode(orgCode));
		materialBatchBarcodeMapper.insertBatchSomeColumn(batchBarcodes);
	}
}
