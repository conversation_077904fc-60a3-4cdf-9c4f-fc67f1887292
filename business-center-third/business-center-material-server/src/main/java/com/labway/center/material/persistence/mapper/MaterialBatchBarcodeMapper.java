package com.labway.center.material.persistence.mapper;

import com.labway.business.center.core.injector.ExtBaseMapper;
import com.labway.business.center.third.ncc.dto.lims.BusinessCenterMaterialBarcodeSearchDto;
import com.labway.business.center.third.ncc.vo.lims.BizMaterialBarcodeVo;
import com.labway.center.material.persistence.entity.MaterialBatchBarcode;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @Description
 * @Date 2024/11/25 17:20
 */
public interface MaterialBatchBarcodeMapper extends ExtBaseMapper<MaterialBatchBarcode> {

	List<BizMaterialBarcodeVo> searchListByMaterialCodesAndBatchNos(@Param("list") List<BusinessCenterMaterialBarcodeSearchDto.MaterialInfoDto> materialInfoDtos,@Param("orgId") String orgId);
}
