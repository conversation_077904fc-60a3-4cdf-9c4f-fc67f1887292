package com.labway.center.material.util;

import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import com.labway.business.center.core.enums.ResultCode;
import com.labway.business.center.mdm.api.reagent.param.reagent.ReagentAssistVo;
import com.labway.business.center.mdm.api.reagent.param.reagent.ReagentVo;
import com.labway.business.center.mdm.api.reagent.param.reagent.SelectReagentListVi;
import com.labway.business.center.mdm.api.reagent.service.ReagentMaterialService;
import com.labway.business.center.mdm.common.util.BeanUtils;
import com.labway.business.center.third.ncc.dto.material.TbMaterialDto;
import com.labway.center.material.config.MaterialMdmConfig;
import com.labway.center.material.constants.CommonErrorCode;
import com.swak.frame.dto.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;

/**
 * MdmUtil 调用主数据工具类
 *
 * <AUTHOR>
 * @version 2023/03/21 19:15
 **/
@Slf4j
@Component
public class MdmUtil {
    @Resource
    private MaterialMdmConfig materialMdmConfig;
    @DubboReference
    private ReagentMaterialService reagentMaterialService;
    
    public Response<List<TbMaterialDto>> getMaterialDetail(Collection<String> materCodeList) {
        log.info("开始调用主数据，获取物料详情，物料编码：{}",materCodeList);
        try {
            Response<List<ReagentVo>> mdmResponse = reagentMaterialService.selectReagentListByNos(new HashSet<>(materCodeList));
            log.info("请求主数据，响应信息：code:{},msg:{},data:{}",mdmResponse.getCode(),mdmResponse.getMsg(),mdmResponse.getData());

            if (!mdmResponse.isSuccess()) {
                return  Response.fail(CommonErrorCode.E000500.getCode(),"查询主数据失败，请稍后再试");
            }
            List<TbMaterialDto> tbMaterialDtos = convertToMdmMaterialDto(BeanUtils.beanToArray(mdmResponse.getData(), ReagentVo.class));
            return Response.success(tbMaterialDtos);
        } catch (Exception e) {
            log.error("请求主数据发生异",e );
        }
        return Response.fail(ResultCode.MDM_DEPT_REQUEST_FAIL);
    }

    public Response<List<ReagentVo>> selectReagentList(SelectReagentListVi request) {
        request.validityCheck();
        log.info("开始调用主数据，获取物料详情，参数：{}", request);
        return reagentMaterialService.selectReagentList(request);
    }
    
    /**
     * 主数据 请求结果 结构转为 MdmMaterialDto
     *
     * @return list 物料信息
     */
    private List<TbMaterialDto> convertToMdmMaterialDto(List<ReagentVo> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        List<TbMaterialDto> targetList = Lists.newArrayListWithCapacity(list.size());
        for (int i = 0; i < list.size(); i++) {
            ReagentVo temp = list.get(i);
    
            TbMaterialDto target = new TbMaterialDto();
            target.setInfoHash(temp.getInfoHash());
            target.setFromNo(temp.getFromNo());
            target.setMaterialCode(temp.getNcReagentNo());
            target.setMaterialName(temp.getReagentName());
            target.setMaterialTypeCode(temp.getReagentTypeCode());
            target.setMaterialTypeName(temp.getReagentType());
            target.setSpecification(temp.getSpecification());
            target.setModel(temp.getReagentModel());
            target.setPrimaryUnit(temp.getPrimaryUnit());
            target.setPrimaryUnitCode(temp.getPrimaryUnitCode());
            target.setSecondaryUnit(StringUtils.EMPTY);
            target.setSecondaryUnitCode(StringUtils.EMPTY);
            target.setUnitTransRate(StringUtils.EMPTY);
            target.setManufacturer(temp.getDef20());
            target.setStorageRequirement(temp.getDef6());
            target.setRegistrationNo(temp.getDef7());
            target.setRegistrationName(temp.getDef9());
            target.setRegistrant(temp.getDef10());
            target.setRegistrantCode(temp.getDef10Code());
            target.setEnableState(temp.getEnableState());
            
            if (CollectionUtils.isNotEmpty(temp.getReagentAssistVoList())) {
                ReagentAssistVo assistVo = temp.getReagentAssistVoList().get(0);
                target.setSecondaryUnit(assistVo.getUnitName());
                target.setSecondaryUnitCode(assistVo.getUnitNameCode());
                target.setUnitTransRate(assistVo.getUnitTransRate());
            }
            
            targetList.add(target);
        }
        return targetList;
    }
}