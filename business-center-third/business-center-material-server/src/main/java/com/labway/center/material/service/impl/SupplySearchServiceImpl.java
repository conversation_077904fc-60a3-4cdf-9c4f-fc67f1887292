package com.labway.center.material.service.impl;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.labway.business.center.core.config.PageResponse;
import com.labway.business.center.third.ncc.dto.lims.BusinessCenterMaterialBarcodeSearchDto;
import com.labway.business.center.third.ncc.dto.material.TbMaterialDto;
import com.labway.business.center.third.ncc.request.SupplyDetailRequest;
import com.labway.business.center.third.ncc.request.SupplyPageRequest;
import com.labway.business.center.third.ncc.response.SupplyDetailResponse;
import com.labway.business.center.third.ncc.response.SupplyPageResponse;
import com.labway.business.center.third.ncc.service.SupplySearchService;
import com.labway.business.center.third.ncc.vo.lims.BizMaterialBarcodeVo;
import com.labway.center.material.converter.SupplyInfoConvert;
import com.labway.center.material.persistence.entity.MaterialApplyForm;
import com.labway.center.material.persistence.entity.MaterialApplyFormDetail;
import com.labway.center.material.persistence.entity.TbMaterialSupply;
import com.labway.center.material.persistence.entity.TbMaterialSupplyDetail;
import com.labway.center.material.repository.*;
import com.labway.center.material.util.MdmUtil;
import com.swak.frame.dto.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 *
 * <AUTHOR> Tianhao
 * @Description
 * @Date 2024/11/26 14:07
 */
@Slf4j
@Service
public class SupplySearchServiceImpl implements SupplySearchService {

	@Resource
	private TbMaterialSupplyDal tbMaterialSupplyDal;

	@Resource
	private TbMaterialSupplyDetailDal tbMaterialSupplyDetailDal;

	@Resource
	private MaterialApplyFormRepository materialApplyFormRepository;

	@Resource
	private MaterialApplyFormDetailRepository materialApplyFormDetailRepository;

	@Resource
	private MaterialBatchBarcodeRepository materialBatchBarcodeRepository;

	@Resource
	private SupplyInfoConvert supplyInfoConvert;

	@Resource
	private MdmUtil mdmUtil;


	/**
	 * 分页查询物料出库信息
	 *
	 * @param request 查询条件
	 */
	@Override
	public Response<PageResponse<SupplyPageResponse>> searchSupplyPage(SupplyPageRequest request) {
		// 根据 物料编码/条码查询出库单号
		List<String> supplyNos = tbMaterialSupplyDetailDal.searchSupplyNosByMaterialInfos(request.getMaterialBarcode(), request.getMaterialInfo(), request.getPageSize());
		if (CollectionUtils.isEmpty(supplyNos) && (StringUtils.isNotBlank(request.getMaterialBarcode()) || StringUtils.isNotBlank(request.getMaterialInfo()))) {
			return Response.success(PageResponse.ofEmptyList(request.getPage(), request.getPage()));
		}
		Page<TbMaterialSupply> page = tbMaterialSupplyDal.searchMaterialSupplyPage(supplyNos,request);
		if (CollectionUtils.isEmpty(page.getRecords())) {
			return Response.success(PageResponse.ofEmptyList(request.getPage(), request.getPage()));
		}
		// 查询对应的申请单
		Set<String> itemIds = page.getRecords().stream().map(TbMaterialSupply::getApplyId).collect(Collectors.toSet());
		List<MaterialApplyForm> materialApplyForms = materialApplyFormRepository.searchApplyNosByApplyId(itemIds);
		Map<String, String> applyNoMap = materialApplyForms.stream().collect(Collectors.toMap(MaterialApplyForm::getItemId, MaterialApplyForm::getApplyCode));
		PageResponse<SupplyPageResponse> responsePage = supplyInfoConvert.convertEntityPage2ResponsePage(page);
		responsePage.getRecords().forEach(item -> item.setApplyNo(applyNoMap.get(item.getApplyId())));
		return Response.success(responsePage);
	}

	/**
	 * 查询出库单详情
	 *
	 * @param request 查询条件
	 */
	@Override
	public Response<List<SupplyDetailResponse>> detail(SupplyDetailRequest request) throws ExecutionException, InterruptedException, TimeoutException {
		List<TbMaterialSupplyDetail> detailResponses = tbMaterialSupplyDetailDal.searchDetail(request.getSupplyId());
		List<SupplyDetailResponse> details = supplyInfoConvert.convertDetailEntityList2ResponseList(detailResponses);

		CompletableFuture<Map<String, MaterialApplyFormDetail>> applyDetailFuture = CompletableFuture.supplyAsync(() -> {
			List<String> formIds = detailResponses.stream().map(TbMaterialSupplyDetail::getApplyDetailId).collect(Collectors.toList());
			List<MaterialApplyFormDetail> materialApplyFormDetails = materialApplyFormDetailRepository.queryDetailByDetailIds(formIds);
			return materialApplyFormDetails.stream().collect(Collectors.toMap(MaterialApplyFormDetail::getItemId, Function.identity()));
		});

		CompletableFuture<Response<List<TbMaterialDto>>> mdmMaterialFuture = CompletableFuture.supplyAsync(() -> {
			return mdmUtil.getMaterialDetail(detailResponses.stream().map(TbMaterialSupplyDetail::getMaterialCode).collect(Collectors.toSet()));
		});

		CompletableFuture<List<BizMaterialBarcodeVo>> materialBarcodeFuture = CompletableFuture.supplyAsync(() -> {
			List<BusinessCenterMaterialBarcodeSearchDto.MaterialInfoDto> materialInfoDtos = details.stream().map(item -> {
				BusinessCenterMaterialBarcodeSearchDto.MaterialInfoDto info = new BusinessCenterMaterialBarcodeSearchDto.MaterialInfoDto();
				info.setBatchNo(item.getBatchNo());
				info.setMaterialCode(item.getMaterialCode());
				return info;
			}).collect(Collectors.toList());
			return materialBatchBarcodeRepository.searchListByMaterialCodesAndBatchNos(materialInfoDtos, request.getOrgId());
		});

		CompletableFuture.allOf(applyDetailFuture, mdmMaterialFuture, materialBarcodeFuture).get(10, TimeUnit.SECONDS);

		Map<String, MaterialApplyFormDetail> applyDetailMap = applyDetailFuture.get();
		Response<List<TbMaterialDto>> mdmResponse = mdmMaterialFuture.get();
		if (!mdmResponse.isSuccess()) {
			log.error("调用主数据出现异常：{}", JSONUtil.toJsonStr(mdmResponse));
			return Response.fail(mdmResponse.getCode(), mdmResponse.getMsg());
		}
		Map<String, TbMaterialDto> materialDtoMap = mdmResponse.getData().stream().collect(Collectors.toMap(TbMaterialDto::getMaterialCode, Function.identity()));
		List<BizMaterialBarcodeVo> bizMaterialBarcodeVos = materialBarcodeFuture.get();
		Map<String, String> materialBarcodeMap = bizMaterialBarcodeVos.stream().collect(Collectors.toMap(item -> item.getMaterialCode() + item.getBatchNo(), BizMaterialBarcodeVo::getMaterialBarcode));

		details.forEach(item -> {
			MaterialApplyFormDetail materialApplyFormDetail = applyDetailMap.get(item.getApplyDetailId());
			if (Objects.nonNull(materialApplyFormDetail)) {
				item.setMaterialPrimaryUnitNumber(materialApplyFormDetail.getMaterialCountApply());
				item.setMaterialSecondaryUnitNumber(materialApplyFormDetail.getMaterialAssistCountApply());
			}
			TbMaterialDto tbMaterialDto = materialDtoMap.get(item.getMaterialCode());
			if (Objects.nonNull(tbMaterialDto)) {
				item.setSpecification(tbMaterialDto.getSpecification());
				item.setSecondaryUnit(tbMaterialDto.getSecondaryUnit());
				item.setPrimaryUnit(tbMaterialDto.getPrimaryUnit());
				item.setUnitTransRate(tbMaterialDto.getUnitTransRate());
				item.setManufacturer(tbMaterialDto.getManufacturer());
				item.setStorageRequirement(tbMaterialDto.getStorageRequirement());
				item.setRegistrant(tbMaterialDto.getRegistrant());
				item.setMaterialName(tbMaterialDto.getMaterialName());
			}
			item.setMaterialBarcode(materialBarcodeMap.get(item.getMaterialCode() + item.getBatchNo()));
		});
		return Response.success(details);
	}
}
