package com.labway.center.material.service.impl;


import com.labway.business.center.core.enums.ResultCode;
import com.labway.business.center.third.ncc.dto.material.LimsNccDeptMappingDTO;
import com.labway.business.center.third.ncc.request.LimsNccDeptMappingRequest;
import com.labway.business.center.third.ncc.service.LimsNccDeptMappingService;
import com.labway.center.material.converter.LimsNccDeptMappingConvert;
import com.labway.center.material.persistence.entity.LimsNccDeptMapping;
import com.labway.center.material.repository.LimsNccDeptMappingRepository;
import com.swak.frame.dto.Response;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【tb_lims_ncc_dept_mapping】的数据库操作Service实现
 * @createDate 2023-05-30 13:58:45
 */
@DubboService
public class LimsNccDeptMappingServiceImpl implements LimsNccDeptMappingService {
    @Resource
    private LimsNccDeptMappingRepository limsNccDeptMappingRepository;
    @Resource
    private LimsNccDeptMappingConvert limsNccDeptMappingConvert;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Response<String> setMapping(LimsNccDeptMappingRequest request) {
        String nccDeptId = request.getNccDeptId();
        Long groupId = request.getGroupId();
        String orgId = request.getOrgId();
        if (Objects.isNull(groupId)) {
            return Response.fail(ResultCode.PARAMS_NOT_VALIDATE);
        }

        // 删除已有的对照关系
        limsNccDeptMappingRepository.deleteMappingByLimsId(groupId,orgId);
        if (StringUtils.isNotBlank(nccDeptId)) {
            // 判断ncc部门是否有对照关系
            LimsNccDeptMapping mapping = limsNccDeptMappingRepository.countByNccDeptId(nccDeptId,orgId);
            if (Objects.nonNull(mapping)) {
                return Response.fail(ResultCode.LIMS_NCC_DEPT_EXIST.getCode(),String.format(ResultCode.LIMS_NCC_DEPT_EXIST.getMsg(),mapping.getNccName(),mapping.getLimsName()));
            }
            // 新增对照关系
            LimsNccDeptMapping limsNccDeptMapping = new LimsNccDeptMapping();
            limsNccDeptMapping.setLimsId(groupId).setLimsName(request.getGroupName())
                    .setNccId(request.getNccDeptId()).setNccName(request.getNccDeptName()).setOrgId(request.getOrgId());
            limsNccDeptMappingRepository.saveMapping(limsNccDeptMapping);
        }
        return Response.success();
    }

    /**
     * 查询所有的对照关系 按照业务单元
     *
     * @param orgId
     * @return
     */
    @Override
    public Response<List<LimsNccDeptMappingDTO>> getAllMapping(String groupId) {
        if (StringUtils.isBlank(groupId)) {
            return Response.fail(ResultCode.PARAMS_NOT_VALIDATE);
        }
        List<LimsNccDeptMapping> limsNccDeptMappings = limsNccDeptMappingRepository.getAllMapping(groupId);
        if (CollectionUtils.isEmpty(limsNccDeptMappings)) {
            return Response.success();
        }
        List<LimsNccDeptMappingDTO> resultList = limsNccDeptMappings.stream().map(item -> limsNccDeptMappingConvert.convertLimsNccDeptMapping2DTO(item)).collect(Collectors.toList());
        return Response.success(resultList);
    }

    @Override
    public Response<LimsNccDeptMappingDTO> countByNccDeptId(String orgId, String nccId) {

        if (StringUtils.isBlank(orgId) || StringUtils.isBlank(nccId)) {
            return Response.fail(ResultCode.PARAMS_NOT_VALIDATE);
        }

        LimsNccDeptMapping mapping = limsNccDeptMappingRepository.countByNccDeptId(nccId, orgId);

        return Response.success(limsNccDeptMappingConvert.convertLimsNccDeptMapping2DTO(mapping));
    }
}




