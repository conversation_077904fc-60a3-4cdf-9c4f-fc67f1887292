package com.labway.center.material.repository;

import cn.hutool.core.date.DateTime;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.business.center.core.util.BeanCopyUtils;
import com.labway.business.center.third.ncc.dto.material.SelectSupplyDetailDto;
import com.labway.business.center.third.ncc.dto.material.SupplyDetailInfoDto;
import com.labway.business.center.third.ncc.dto.ncc.NccBodyDto;
import com.labway.business.center.third.ncc.dto.ncc.NccResponseDto;
import com.labway.business.center.third.ncc.dto.out.OutDto;
import com.labway.business.center.third.ncc.vo.material.MaterialSupplyDetailVo;
import com.labway.center.material.constants.CommonErrorCode;
import com.labway.center.material.constants.CommonConstant;
import com.labway.center.material.persistence.entity.TbMaterialSupply;
import com.labway.center.material.persistence.entity.TbMaterialSupplyDetail;
import com.labway.center.material.persistence.mapper.TbMaterialSupplyDetailMapper;
import com.labway.center.material.persistence.mapper.TbMaterialSupplyMapper;
import com.labway.business.center.third.ncc.dto.out.OutBodyDto;
import com.swak.frame.dto.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 物料出库明细 Dal
 * 
 * <AUTHOR>
 * @since 2023/3/15 17:27
 */
@Slf4j
@Repository
public class TbMaterialSupplyDetailDal {

    @Resource
    private TbMaterialSupplyDetailMapper tbMaterialSupplyDetailMapper;
    @Resource
    private TbMaterialSupplyMapper tbMaterialSupplyMapper;

    /**
     * 出库信息明细 查看
     *
     * @param condition 参数 {@link SelectSupplyDetailDto}
     * @return list {@link SupplyDetailInfoDto}
     */
    public List<SupplyDetailInfoDto> selectSupplyDetail(SelectSupplyDetailDto condition) {
        return tbMaterialSupplyDetailMapper.selectSupplyDetail(condition);
    }
    
    /**
     * 插入 出库单详情
     * @param supplyId 出库单id
     * @param outDto 入库信息
     * @param nccResponseDto 入库信息
     * @return list 出库单详情
     */
    public Response<List<TbMaterialSupplyDetail>> insertMaterialSupplyDetail(String supplyId, OutDto outDto, NccResponseDto nccResponseDto) {
        if (StringUtils.isBlank(supplyId)) {
            return Response.fail(CommonErrorCode.E100002.getCode(),"申领单出库失败");
        }
        Map<String, String> applyDetailIdMap = outDto.getOutBody().stream().collect(Collectors.toMap(item ->item.getRowNo() + item.getMaterialCode() + item.getBatchCode(), OutBodyDto::getApplyDetailId));
        Map<String, String> remarkMap = outDto.getOutBody().stream()
                .collect(Collectors.toMap(
                        item ->item.getRowNo() + item.getMaterialCode() + item.getBatchCode(),
                        remark -> Optional.ofNullable(remark.getRemark()).orElse(StringUtils.EMPTY)));

        Map<String, OutBodyDto> outBodyMap = outDto.getOutBody().stream()
                .collect(Collectors.toMap(
                        item -> item.getRowNo() + item.getMaterialCode() + item.getBatchCode(),
                        Function.identity()));
        List<TbMaterialSupplyDetail> list = new ArrayList<>();
        for (int i = 0; i < nccResponseDto.getBodyDtoList().size(); i++) {
            NccBodyDto item = nccResponseDto.getBodyDtoList().get(i);
            TbMaterialSupplyDetail detail = new TbMaterialSupplyDetail();
            detail.setItemId(CommonConstant.STOCK_OUT_DETAIL_ID + IdWorker.getId());
            detail.setSupplyId(supplyId);
            // 申领单详细id
            detail.setApplyDetailId(applyDetailIdMap.get((i + 1) + item.getMaterialCode() + item.getBatchNo()));
            detail.setMaterialCode(item.getMaterialCode());
            detail.setBatchNo(item.getBatchNo());
            detail.setInvalidTime(item.getInvalidTime());
            detail.setPrimaryNumber(item.getPrimaryNumber());
            detail.setAssistNumber(new BigDecimal(item.getAssistNumber()));
            detail.setEnabled(Boolean.TRUE);
            detail.setCreateTime(DateTime.now());
            detail.setUpdateTime(DateTime.now());

            // 获取出库单详情
            OutBodyDto outBodyDto = outBodyMap.get((i + 1) + item.getMaterialCode() + item.getBatchNo());
            if (Objects.nonNull(outBodyDto)) {
                // 出库备注
                detail.setRemark(Optional.ofNullable(outBodyDto.getRemark()).orElse(StringUtils.EMPTY));
                // 货位
                detail.setRackCode(outBodyDto.getCscode());
				// 储存条件
	            detail.setStorageRequirement(outBodyDto.getStorageRequirement());
	            detail.setMaterialBarcode(outBodyDto.getMaterialBarcode());
            }
            list.add(detail);
        }
        
        
        int i = tbMaterialSupplyDetailMapper.insertBatch(list);
        if (i <= 0) {
            log.error("出库单详情入库失败===>{}",list);
            return Response.fail(CommonErrorCode.E000500.getCode(),"出库单详情入库失败，请查看原因");
        }
        return Response.success(list);
        
    }
    
    /**
     * 根据物料编码查询出库详情
     * @param materialCodes 物料编码信息
     * @param supplyId 出库单id
     * @return 物料信息
     */
    public List<TbMaterialSupplyDetail> selectDetailByMaterialCodes(List<String> materialCodes,String supplyId) {
        QueryWrapper<TbMaterialSupplyDetail> wrapper = new QueryWrapper<>();
        wrapper.in("material_code",materialCodes);
        List<TbMaterialSupplyDetail> supplyDetails = tbMaterialSupplyDetailMapper.selectList(wrapper);
        
        return supplyDetails.stream().filter(item -> Objects.equals(item.getSupplyId(),supplyId))
                .collect(Collectors.toList());
    }

    /**
     * 根据出库单id查询出库单详情
     * @param supplyId
     * @return
     */
    public List<MaterialSupplyDetailVo> querySupplyDetailBySupplyId(String supplyId) {
        if (!org.springframework.util.StringUtils.hasText(supplyId)){
            log.info("出库单id为空！");
            return Collections.emptyList();
        }
        List<TbMaterialSupplyDetail> tbMaterialSupplyDetails = tbMaterialSupplyDetailMapper.selectList(Wrappers.lambdaQuery(TbMaterialSupplyDetail.class)
                .eq(TbMaterialSupplyDetail::getSupplyId,supplyId)
                .eq(TbMaterialSupplyDetail::getEnabled, Boolean.TRUE));
        if (CollectionUtils.isEmpty(tbMaterialSupplyDetails)){
            return Collections.emptyList();
        }

        return BeanCopyUtils.copyList(tbMaterialSupplyDetails,MaterialSupplyDetailVo.class);
    }


    /**
     * 查询出库单关联的退库单信息
     * @param supplyId
     * @return
     */
    public List<MaterialSupplyDetailVo> queryRollbackDetailBySupplyId(String supplyId) {
        if (!org.springframework.util.StringUtils.hasText(supplyId)){
            log.info("出库单id为空！");
            return Collections.emptyList();
        }

        List<TbMaterialSupply> tbMaterialSupplies = tbMaterialSupplyMapper.selectList(Wrappers.lambdaQuery(TbMaterialSupply.class)
                .eq(TbMaterialSupply::getApplyId, supplyId)
                .eq(TbMaterialSupply::getEnabled, Boolean.TRUE));
        if (CollectionUtils.isEmpty(tbMaterialSupplies)){
            log.info("未查询到出库单信息！");
            return Collections.emptyList();
        }

        TbMaterialSupply tbMaterialSupply = tbMaterialSupplies.get(0);
        String supplyNo = tbMaterialSupply.getSupplyNo();

        List<TbMaterialSupply> tbMaterialRollbacks = tbMaterialSupplyMapper.selectList(Wrappers.lambdaQuery(TbMaterialSupply.class)
                .eq(TbMaterialSupply::getType, Integer.valueOf(2))
                .eq(TbMaterialSupply::getRelationSupplyNo, supplyNo)
                .eq(TbMaterialSupply::getEnabled, Boolean.TRUE));

        if (CollectionUtils.isEmpty(tbMaterialRollbacks)){
            log.info("未查询到退库单信息！");
            return Collections.emptyList();
        }

        List<String> rollbackSupplyIds = tbMaterialRollbacks.stream().map(TbMaterialSupply::getApplyId).collect(Collectors.toList());
        List<TbMaterialSupplyDetail> tbMaterialSupplyDetails = tbMaterialSupplyDetailMapper.selectList(Wrappers.lambdaQuery(TbMaterialSupplyDetail.class)
                .in(TbMaterialSupplyDetail::getSupplyId, rollbackSupplyIds).eq(TbMaterialSupplyDetail::getEnabled, Boolean.TRUE));
        if (CollectionUtils.isEmpty(tbMaterialSupplyDetails)){
            log.info("未查询到退库单明细！");
            return Collections.emptyList();
        }

        return BeanCopyUtils.copyList(tbMaterialSupplyDetails,MaterialSupplyDetailVo.class);
    }


    public List<TbMaterialSupplyDetail> searchBySupplyId(String itemId) {
        return tbMaterialSupplyDetailMapper.selectList(Wrappers.lambdaQuery(TbMaterialSupplyDetail.class)
                .eq(TbMaterialSupplyDetail::getSupplyId, itemId));
    }

    public TbMaterialSupplyDetail searchBySupplyIdAndMateriInfo(String itemId, String materialCode, String batchNo) {
        if (StringUtils.isAnyBlank(itemId, materialCode, batchNo)) {
            return null;
        }
        return tbMaterialSupplyDetailMapper.selectOne(Wrappers.lambdaQuery(TbMaterialSupplyDetail.class)
                .eq(TbMaterialSupplyDetail::getSupplyId, itemId)
                .eq(TbMaterialSupplyDetail::getMaterialCode, materialCode)
                .eq(TbMaterialSupplyDetail::getBatchNo, batchNo));
    }

    /**
     * 查询物料的出库详情
     * @param itemId
     * @param materialCodes
     * @return
     */
    public List<TbMaterialSupplyDetail> searchBySupplyIdAndMaterialCodes(String itemId, Set<String> materialCodes) {
        if (StringUtils.isBlank(itemId)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<TbMaterialSupplyDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TbMaterialSupplyDetail::getSupplyId, itemId)
                .in(TbMaterialSupplyDetail::getMaterialCode, materialCodes);
        return tbMaterialSupplyDetailMapper.selectList(queryWrapper);
    }

	/**
	 * 根据物料信息查询出库单
	 * @param materialBarcode 物料条码号
	 * @param materialInfo 物料信息
	 * @param pageSize 数量
	 * @return
	 */
	public List<String> searchSupplyNosByMaterialInfos(String materialBarcode, String materialInfo, Integer pageSize) {
		if (StringUtils.isAllBlank(materialBarcode, materialInfo)) {
			return Collections.emptyList();
		}

		return tbMaterialSupplyDetailMapper.searchSupplyNosByMaterialInfos(materialBarcode, materialInfo, pageSize);
	}

	public List<TbMaterialSupplyDetail> searchDetail(String supplyId) {
		if (StringUtils.isBlank(supplyId)) {
			return Collections.emptyList();
		}
		return tbMaterialSupplyDetailMapper.selectList(Wrappers.lambdaQuery(TbMaterialSupplyDetail.class)
				.eq(TbMaterialSupplyDetail::getSupplyId, supplyId));
	}
}
