package com.labway.center.material.util;

import com.labway.center.material.constants.CommonConstant;
import com.labway.center.material.constants.RedisConstant;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 出库id生成工具
 *
 * <AUTHOR>
 * @since 2023-03-15
 */
@Service
public class OutIdGeneratorService {
    private static final String PREFIX = RedisConstant.OUT_KEY;
    private static final int DIGIT_COUNT = 4;
    private static final String PATTERN = "%s" + "%s%0" + DIGIT_COUNT + "d";

    @Resource
    private StringRedisTemplate stringRedisTemplate;
    
    /**
     * 生成出库id
     * @return 出库id
     */
    public String generateId(String idType) {
        String today = DateFormatThreadLocal.getDateFormat().format(new Date());
        DateFormatThreadLocal.remove();
        String key = PREFIX + idType + today;
        Long code = stringRedisTemplate.opsForValue().increment(key);
        if (Objects.equals(code, 1L)) {
            stringRedisTemplate.expire(key, 1, TimeUnit.DAYS);
        }
        return String.format(PATTERN,idType, today, code);
    }

	/**
	 * 生成新条码号/自增条码
	 *
	 * @param originalString 物料code
	 * @param numberString   物料条码号
	 * @return
	 */
	public String incrementAndAppend(String originalString, String numberString) {
		String lastSixDigits;
		if (StringUtils.isNotBlank(numberString)) {
			lastSixDigits = numberString.substring(Math.max(numberString.length() - 6, 0)); // 取最后6位字符串
		} else {
			lastSixDigits = "100000";
		}
		int number = Integer.parseInt(lastSixDigits); // 解析为整数
		number++; // 自增
		String formattedNumber = String.format("%06d", number); // 格式化为6位数的字符串
		return originalString + formattedNumber; // 拼接到原始字符串后面
	}

	public String genMaterialBarcode(String materialCode) {
		String key = "LABWAY:BUSINESS:CENTER:MATERIAL:BARCODE:" + materialCode;
		Long code = stringRedisTemplate.opsForValue().increment(key);
		String formatCode = String.format("%05d", code);
		int prefix = Integer.parseInt(formatCode.substring(0, 1)) + 1;
		return materialCode + prefix + formatCode;
	}
}
