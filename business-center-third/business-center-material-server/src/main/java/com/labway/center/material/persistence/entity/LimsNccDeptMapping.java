package com.labway.center.material.persistence.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 
 * @TableName tb_lims_ncc_dept_mapping
 */
@TableName(value ="tb_lims_ncc_dept_mapping")
@Data
@Accessors(chain = true)
public class LimsNccDeptMapping implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * lims专业组主键id
     */
    @TableField(value = "lims_id")
    private Long limsId;

    /**
     * lims专业组名称
     */
    @TableField(value = "lims_name")
    private String limsName;

    /**
     * ncc部门id
     */
    @TableField(value = "ncc_id")
    private String nccId;

    /**
     * ncc部门名称
     */
    @TableField(value = "ncc_name")
    private String nccName;

    /**
     * 所属的业务单元
     */
    @TableField(value = "org_id")
    private String orgId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}