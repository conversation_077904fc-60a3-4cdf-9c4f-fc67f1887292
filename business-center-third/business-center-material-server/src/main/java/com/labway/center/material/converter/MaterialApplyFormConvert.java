package com.labway.center.material.converter;

import com.labway.business.center.third.ncc.request.MaterialApplyFormRequest;
import com.labway.business.center.third.ncc.vo.applyform.ApplyCommunityVo;
import com.labway.business.center.third.ncc.vo.applyform.QueryCancellationApplyFormVo;
import com.labway.business.center.third.ncc.vo.applyform.UnStockOutApplyFormQueryVo;
import com.labway.center.material.persistence.entity.MaterialApplyForm;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * 申领单
 *
 * <AUTHOR>
 * @version 2023/06/06 11:33
 **/
@Mapper(componentModel = "spring")
public interface MaterialApplyFormConvert {
    MaterialApplyForm convertRequest2MaterialApplyForm(MaterialApplyFormRequest materialApplyFormRequest);
    @Mapping(source = "applyOrgCode",target = "applyLisCode")
    @Mapping(source = "applyOrgName",target = "applyLisName")
    @Mapping(source = "applyCustomerPk",target = "nccCustomerId")
    @Mapping(source = "applyCustomerName",target = "nccCustomerName")
    ApplyCommunityVo convertMaterialApplyForm2ApplyCommunityVo(MaterialApplyForm materialApplyForm);
    @Mapping(source = "applyOrgCode",target = "applyLisCode")
    @Mapping(source = "applyOrgName",target = "applyLisName")
    UnStockOutApplyFormQueryVo convertMaterialApplyForm2UnStockOutApplyFormQueryVo(MaterialApplyForm materialApplyForm);
    @Mapping(source = "applyOrgCode",target = "applyLisCode")
    @Mapping(source = "applyOrgName",target = "applyLisName")
    QueryCancellationApplyFormVo convertMaterialApplyForm2QueryCancellationApplyFormVo(MaterialApplyForm materialApplyForm);
}