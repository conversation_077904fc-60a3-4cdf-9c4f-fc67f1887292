package com.labway.center.material.controller;

import com.labway.business.center.third.ncc.dto.center.OrgsMappingDTO;
import com.labway.business.center.third.ncc.service.OrgsMappingService;
import com.swak.frame.dto.Response;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2023/05/29 20:18
 **/
@RestController
@RequestMapping("/user")
public class UserInfoController {
    @Resource
    private OrgsMappingService orgsMappingService;

    @GetMapping("/orgs")
    public Response<List<OrgsMappingDTO>> getUserHasOrgs() {
        return orgsMappingService.getUserHasOrgs();
    }
}