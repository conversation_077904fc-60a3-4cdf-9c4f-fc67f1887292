package com.labway.center.material.constants;

/**
 * <AUTHOR>
 * @version 2023/03/15 12:02
 * @description
 **/
public enum EnabledStatusEnum {

    UN_ENABLE(Boolean.FALSE,"无效"),
    ENABLE(Boolean.TRUE,"有效"),
    ;

    /**
     * 操作状态值
     */
    private Boolean value;
    /**
     * 操作状态描述
     */
    private String description;

    public Boolean getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }

    EnabledStatusEnum(Boolean value, String description) {
        this.value = value;
        this.description = description;
    }

    public static EnabledStatusEnum getEnumDes(Integer menuType) {
        for (EnabledStatusEnum tempEnumValue : EnabledStatusEnum.values()) {
            if (tempEnumValue.getValue().equals(menuType)) {
                return tempEnumValue;
            }
        }
        throw new IllegalArgumentException("错误类型");
    }

}
