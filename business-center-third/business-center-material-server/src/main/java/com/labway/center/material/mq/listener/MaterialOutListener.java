package com.labway.center.material.mq.listener;

import cn.hutool.json.JSONUtil;
import com.labway.business.center.third.ncc.dto.mq.MessagedDto;
import com.labway.business.center.third.ncc.service.TbMaterialSupplyService;
import com.labway.center.material.constants.RabbitConstant;
import com.labway.center.material.constants.RedisConstant;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @since 2023/3/2 15:56
 */
@Slf4j
@Component
public class MaterialOutListener implements ChannelAwareMessageListener {
    @Resource
    private RedisTemplate redisTemplate;
    @Resource
    private TbMaterialSupplyService tbMaterialSupplyService;
    
    @Override
    @RabbitListener(bindings = @QueueBinding(value = @Queue(),
            exchange = @Exchange(value = RabbitConstant.MATERIAL_OUT_EXCHANGE, type = ExchangeTypes.FANOUT)))
    public void onMessage(Message message, Channel channel) throws Exception {
        long deliveryTag = message.getMessageProperties().getDeliveryTag();
        String body = new String(message.getBody(), "UTF-8");
        MessagedDto messagedDto = JSONUtil.toBean(body, MessagedDto.class);
        if (!redisTemplate.opsForValue().setIfAbsent(RedisConstant.MQ_KEY + messagedDto.getMessageId(),"",1,TimeUnit.HOURS)) {
            return;
        }
        try
        {
            log.info("--------------监听到出库通知时发生异常--------------");
            /**
             * 确认消息，参数说明：
             * long deliveryTag：唯一标识 ID。
             * boolean multiple：是否批处理，当该参数为 true 时，
             * 则可以一次性确认 deliveryTag 小于等于传入值的所有消息。
             */
            tbMaterialSupplyService.notifySystem(messagedDto.getMessage());
            channel.basicAck(deliveryTag, true);
            /**
             * 否定消息，参数说明：
             * long deliveryTag：唯一标识 ID。
             * boolean multiple：是否批处理，当该参数为 true 时，
             * 则可以一次性确认 deliveryTag 小于等于传入值的所有消息。
             * boolean requeue：如果 requeue 参数设置为 true，
             * 则 RabbitMQ 会重新将这条消息存入队列，以便发送给下一个订阅的消费者；
             * 如果 requeue 参数设置为 false，则 RabbitMQ 立即会还把消息从队列中移除，
             * 而不会把它发送给新的消费者。
             */
        }
        catch (Exception e) {
            
            /**
             * 拒绝消息，参数说明：
             * long deliveryTag：唯一标识 ID。
             * boolean requeue：如果 requeue 参数设置为 true，
             * 则 RabbitMQ 会重新将这条消息存入队列，以便发送给下一个订阅的消费者；
             * 如果 requeue 参数设置为 false，则 RabbitMQ 立即会还把消息从队列中移除，
             * 而不会把它发送给新的消费者。
             */
            channel.basicReject(deliveryTag, true);
        }
    }
}
