package com.labway.center.material.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.labway.business.center.core.enums.DeleteFlagEnum;
import com.labway.business.center.core.enums.ResultCode;
import com.labway.business.center.core.user.LoginUserInfoUtil;
import com.labway.business.center.core.util.RedisUtils;
import com.labway.business.center.third.ncc.dto.center.MaterialRefundDeptDTO;
import com.labway.business.center.third.ncc.dto.material.MaterialRefundApplyDTO;
import com.labway.business.center.third.ncc.dto.material.MaterialRefundApplyDetailDTO;
import com.labway.business.center.third.ncc.dto.material.TbMaterialDto;
import com.labway.business.center.third.ncc.dto.ncc.NccResponseDto;
import com.labway.business.center.third.ncc.dto.out.*;
import com.labway.business.center.third.ncc.request.CheckMaterialRefundRequest;
import com.labway.business.center.third.ncc.request.MaterialRefundApplyDetailRequest;
import com.labway.business.center.third.ncc.request.MaterialRefundApplyRequest;
import com.labway.business.center.third.ncc.request.MaterialRefundSearchRequest;
import com.labway.business.center.third.ncc.service.MaterialRefundApplyService;
import com.labway.business.center.third.ncc.service.NccHttpServer;
import com.labway.center.material.constants.CommonConstant;
import com.labway.center.material.constants.RefundApplyStatus;
import com.labway.center.material.converter.MaterialRefundApplyConvert;
import com.labway.center.material.converter.MaterialRefundDetailApplyConvert;
import com.labway.center.material.persistence.entity.MaterialRefundApply;
import com.labway.center.material.persistence.entity.MaterialRefundApplyDetail;
import com.labway.center.material.persistence.entity.TbMaterialSupply;
import com.labway.center.material.persistence.entity.TbMaterialSupplyDetail;
import com.labway.center.material.repository.MaterialRefundApplyDetailRepository;
import com.labway.center.material.repository.MaterialRefundApplyRepository;
import com.labway.center.material.repository.TbMaterialSupplyDal;
import com.labway.center.material.repository.TbMaterialSupplyDetailDal;
import com.labway.center.material.util.MdmUtil;
import com.labway.center.material.util.OutIdGeneratorService;
import com.swak.frame.dto.Response;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Tianhao
 * @version 2024/03/07 14:35
 **/
@Slf4j
@DubboService
public class MaterialRefundApplyServiceImpl implements MaterialRefundApplyService {

    @Resource
    private TbMaterialSupplyDal materialSupplyDal;
    @Resource
    private TbMaterialSupplyDetailDal materialSupplyDetailDal;
    @Resource
    private MaterialRefundApplyRepository materialRefundApplyRepository;
    @Resource
    private MaterialRefundApplyDetailRepository materialRefundApplyDetailRepository;

    @Resource
    private MaterialRefundApplyConvert materialRefundApplyConvert;
    @Resource
    private MaterialRefundDetailApplyConvert materialRefundDetailApplyConvert;

    @Resource
    private OutIdGeneratorService outIdGeneratorService;
    @Resource
    private MdmUtil mdmUtil;

    @Resource
    private RedisUtils redisUtils;

    @DubboReference
    private NccHttpServer nccHttpServer;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Response<String> createRefundApply(MaterialRefundApplyRequest applyRequest) {
        log.info("lims创建退库申请单：{}", JSONUtil.toJsonStr(applyRequest));
        String md5Key = DigestUtil.md5Hex(JSONUtil.toJsonStr(applyRequest));
        if (!redisUtils.lock(md5Key, "1", 1, TimeUnit.MINUTES)) {
            return Response.fail(500,"请勿重复提交");
        }

        LocalDateTime now = LocalDateTime.now();
        String refundSupplyNo = outIdGeneratorService.generateId(CommonConstant.REFUND_OUT_ID);

        String supplyNo = applyRequest.getSupplyNo();
        List<MaterialRefundApplyDetailRequest> detailRequests = applyRequest.getDetailRequests();
        Response<String> validResponse = validInsertRefund(applyRequest, detailRequests);
        if (!validResponse.isSuccess()) {
            return validResponse;
        }

        // 数量校验
        // 规则：查询出原出库单，以及出库单下的退库单，计算数量为最大退库数量，且申请退库数量不得为0
        Map<String, MaterialRefundApplyDetailRequest> refundMaterialMap = detailRequests.stream()
                .collect(Collectors.toMap(key -> key.getMaterialCode() + ":" + key.getBatchNo(), Function.identity()));
        TbMaterialSupply materialSupply = materialSupplyDal.searchMaterialSupplyBySupplyNo(supplyNo);
        if (Objects.isNull(materialSupply)) {
            return Response.fail(ResultCode.SUPPLY_DONT_EXIST.getCode(), String.format(ResultCode.SUPPLY_DONT_EXIST.getMsg(),applyRequest.getRefundApplyNo()));
        }
        if (StringUtils.isAnyBlank(materialSupply.getSupplyTypeId(),materialSupply.getSupplyCustomerId(),materialSupply.getSupplyDeptId(),materialSupply.getSupplyWarehouseId())) {
            return Response.fail(500,"历史数据无法进行退库");
        }

        List<TbMaterialSupplyDetail> materialSupplyDetails = materialSupplyDetailDal.searchBySupplyId(materialSupply.getItemId());
        Map<String, TbMaterialSupplyDetail> supplyDetailMap = materialSupplyDetails.stream()
                .collect(Collectors.toMap(key -> key.getMaterialCode() + ":" + key.getBatchNo(), Function.identity()));

        // 查询物料基础信息
        Set<String> materialCodes = applyRequest.getDetailRequests().stream().map(MaterialRefundApplyDetailRequest::getMaterialCode).collect(Collectors.toSet());
        Response<List<TbMaterialDto>> mdmResponse = mdmUtil.getMaterialDetail(Lists.newArrayList(materialCodes));
        if (!mdmResponse.isSuccess()) {
            return Response.fail(500,mdmResponse.getMsg());
        }
        List<TbMaterialDto> materialInfos = mdmResponse.getData();
        Map<String, TbMaterialDto> materialInfoMap = materialInfos.stream().collect(Collectors.toMap(TbMaterialDto::getMaterialCode, Function.identity()));

        List<MaterialRefundApplyDetail> insertRefundDetails = Lists.newArrayList();
        for (Map.Entry<String, MaterialRefundApplyDetailRequest> materialRefundApplyDetailRequestEntry : refundMaterialMap.entrySet()) {
            MaterialRefundApplyDetailRequest materialRefundApplyDetailRequest = materialRefundApplyDetailRequestEntry.getValue();
            TbMaterialSupplyDetail tbMaterialSupplyDetail = supplyDetailMap.get(materialRefundApplyDetailRequestEntry.getKey());

            if (Objects.isNull(tbMaterialSupplyDetail)) {
                return Response.fail(ResultCode.MATERIAL_BATCH_NOT_EXIST.getCode(), String.format(ResultCode.MATERIAL_BATCH_NOT_EXIST.getMsg(),applyRequest.getSupplyNo(),materialRefundApplyDetailRequestEntry.getKey()));
            }
			if (Objects.isNull(materialRefundApplyDetailRequest.getRefundNumber()) && Objects.isNull(materialRefundApplyDetailRequest.getRefundNumberDecimal())) {
				return Response.fail(500, "请输入退库主数量");
			}
            // 物料信息
            TbMaterialDto materialDto = materialInfoMap.get(materialRefundApplyDetailRequest.getMaterialCode());
            if (Objects.isNull(materialDto)) {
                throw new RuntimeException("物料编码"+materialRefundApplyDetailRequest.getMaterialCode()+"信息不存在");
            }
            MaterialRefundApplyDetail materialRefundApplyDetail = materialRefundDetailApplyConvert.convertMdmInfo2InsertParam(materialDto);

            materialRefundApplyDetail.setSupplyNumber(tbMaterialSupplyDetail.getPrimaryNumber());
            materialRefundApplyDetail.setSupplyAssistNumber(tbMaterialSupplyDetail.getAssistNumber());
            materialRefundApplyDetail.setAlreadyRefundNumber(BigDecimal.ZERO);
            materialRefundApplyDetail.setAlreadyRefundAssistNumber(BigDecimal.ZERO);
            materialRefundApplyDetail.setRowNo(materialRefundApplyDetailRequest.getRowNo());
            materialRefundApplyDetail.setAssistNumber(materialRefundApplyDetailRequest.getAssistNumber());

			// 1.0.6 数量支持小数 同时要兼容未更新1.1.3.9版本的lims
	        if(Objects.nonNull(materialRefundApplyDetailRequest.getRefundNumberDecimal()) &&
			        materialRefundApplyDetailRequest.getRefundNumberDecimal().compareTo(BigDecimal.ZERO) > 0){
		        materialRefundApplyDetail.setRefundNumber(materialRefundApplyDetailRequest.getRefundNumberDecimal());

	        }else {
		        materialRefundApplyDetail.setRefundNumber(new BigDecimal(materialRefundApplyDetailRequest.getRefundNumber()));
	        }

            materialRefundApplyDetail.setBatchNo(materialRefundApplyDetailRequest.getBatchNo());
            materialRefundApplyDetail.setSupplyNo(materialSupply.getSupplyNo());
            materialRefundApplyDetail.setExpirationDate(materialRefundApplyDetailRequest.getExpirationDate());
            materialRefundApplyDetail.setCreateTime(now);
            materialRefundApplyDetail.setModifyTime(now);


            // 最大主辅数量

            MaxNumberObj maxNumberObj = getMaxRefundNumber(materialSupply,materialRefundApplyDetail.getMaterialCode(),materialRefundApplyDetail.getBatchNo());

            BigDecimal maxRefundMainNum = maxNumberObj.getMaxRefundMainNum();
            BigDecimal maxRefundAssistNum = maxNumberObj.getMaxRefundAssistNum();

            BigDecimal applyRefundNumber = materialRefundApplyDetail.getRefundNumber();
            BigDecimal applyAssistNumber = materialRefundApplyDetail.getAssistNumber();
            if (maxRefundMainNum.compareTo(applyRefundNumber) < 0 || maxRefundAssistNum.compareTo(applyAssistNumber) < 0) {
                return Response.fail(500,String.format("物料编码：%s，批次号：%s，剩余最大可退辅数量为：%s", tbMaterialSupplyDetail.getMaterialCode(), tbMaterialSupplyDetail.getBatchNo(), maxRefundMainNum));
            }
            materialRefundApplyDetail.setRefundSupplyNo(refundSupplyNo);
            // 数量校验通过
            insertRefundDetails.add(materialRefundApplyDetail);
        }

        MaterialRefundApply materialRefundApply = materialRefundApplyConvert.convertInsertRequest2Entity(applyRequest);
        materialRefundApply.setStatus(RefundApplyStatus.WAIT.getStatus());
        materialRefundApply.setRefundSupplyNo(refundSupplyNo);

        materialRefundApply.setSupplyTypeId(materialSupply.getSupplyTypeId());
        materialRefundApply.setSupplyTypeName(materialSupply.getSupplyType());
        materialRefundApply.setCustomerId(materialSupply.getSupplyCustomerId());
        materialRefundApply.setCustomerName(materialSupply.getSupplyCustomerName());
        materialRefundApply.setSupplyDeptId(materialSupply.getSupplyDeptId());
        materialRefundApply.setSupplyDeptName(materialSupply.getSupplyDeptName());
        materialRefundApply.setSupplyWarehouseId(materialSupply.getSupplyWarehouseId());
        materialRefundApply.setSupplyWarehouseName(materialSupply.getSupplyWarehouseName());
        materialRefundApply.setSupplyWarehouseOrgId(materialSupply.getSupplyWarehouseOrgId());
        materialRefundApply.setSupplyNo(materialSupply.getSupplyNo());
        materialRefundApply.setModifyTime(now);
        materialRefundApply.setCreateTime(now);
        materialRefundApply.setIsDelete(DeleteFlagEnum.NO_DELETE.getCode());

        materialRefundApplyRepository.save(materialRefundApply);
        materialRefundApplyDetailRepository.saveBatch(insertRefundDetails);
        return Response.success();
    }

    /**
     * 查询退库申请单
     *
     * @param materialRefundSearchRequest
     * @return
     */
    @Override
    public Response searchRefundPage(MaterialRefundSearchRequest materialRefundSearchRequest) {
        Page<MaterialRefundApply> materialRefundApplyPage = new Page<>(materialRefundSearchRequest.getCurrent(), materialRefundSearchRequest.getSize());
        materialRefundApplyPage = materialRefundApplyRepository.searchPageByInfo(materialRefundApplyPage,materialRefundSearchRequest.getOrgId(),materialRefundSearchRequest.getStatus(),materialRefundSearchRequest.getSupplyDeptId());
        Page<MaterialRefundApplyDTO> resultPage = materialRefundApplyConvert.convertEntityPage2PageDTO(materialRefundApplyPage);
        if (CollectionUtils.isEmpty(resultPage.getRecords())) {
            return Response.success();
        }
        List<String> refundSupplyNos = resultPage.getRecords().stream().map(MaterialRefundApplyDTO::getRefundSupplyNo).collect(Collectors.toList());
        List<MaterialRefundApplyDetail> materialRefundApplyDetails = materialRefundApplyDetailRepository.searchByRefundSupplyNos(refundSupplyNos);
        if (CollectionUtils.isEmpty(materialRefundApplyDetails)) {
            return Response.fail(500,"数据存在异常");
        }
        List<MaterialRefundApplyDetailDTO> materialRefundApplyDetailDTO = materialRefundDetailApplyConvert.convertEntityList2DTO(materialRefundApplyDetails);
        Map<String, List<MaterialRefundApplyDetailDTO>> detailMap = materialRefundApplyDetailDTO.stream().collect(Collectors.groupingBy(MaterialRefundApplyDetailDTO::getRefundSupplyNo));
        resultPage.getRecords().forEach(materialRefundApplyDTO -> {
            materialRefundApplyDTO.setRefundApplyDetails(detailMap.get(materialRefundApplyDTO.getRefundSupplyNo()));
        });
        return Response.success(resultPage);
    }

    /**
     * 查询所有的领料部门
     *
     * @param orgId
     * @return
     */
    @Override
    public Response<List<MaterialRefundDeptDTO>> getAllRefundDept(String orgId) {
        if (StringUtils.isBlank(orgId)) {
            return Response.fail(ResultCode.PARAMS_NOT_VALIDATE);
        }
        List<MaterialRefundApply> supplyDeptList = materialRefundApplyRepository.getAllSupplyDeptIds(orgId);
        List<MaterialRefundDeptDTO> supplyDeptDTO = materialRefundApplyConvert.convertEntityList2SupplyDetpDTO(supplyDeptList);
        return Response.success(supplyDeptDTO);
    }

    /**
     * 退货审核
     *
     * @param checkRequest
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Response<String> checkRefundApply(CheckMaterialRefundRequest checkRequest) {
        String supplyNo = checkRequest.getSupplyNo();
        TbMaterialSupply materialSupply = materialSupplyDal.searchMaterialSupplyBySupplyNo(supplyNo);
        if (Objects.isNull(materialSupply)) {
            return Response.fail(ResultCode.SUPPLY_DONT_EXIST.getCode(), String.format(ResultCode.SUPPLY_DONT_EXIST.getMsg(),checkRequest.getRefundApplyNo()));
        }
        // 查询相关的出库详情
        List<CheckMaterialRefundRequest.CheckDetailRequest> detailRequests = checkRequest.getDetailRequests();
        Set<String> materialCodes = detailRequests.stream().map(CheckMaterialRefundRequest.CheckDetailRequest::getMaterialCode).collect(Collectors.toSet());
        Response<List<TbMaterialDto>> materialDetailResponse = mdmUtil.getMaterialDetail(new ArrayList<>(materialCodes));
        if (!materialDetailResponse.isSuccess()) {
            return Response.fail(500,"物料编码：" + materialCodes.toString() + "，获取物料详情失败");
        }
        List<TbMaterialSupplyDetail> materialSupplyDetails = materialSupplyDetailDal.searchBySupplyIdAndMaterialCodes(materialSupply.getItemId(), materialCodes);
        // 数量校验
        for (CheckMaterialRefundRequest.CheckDetailRequest detail : detailRequests) {
            if (detail.getRefundNumber().compareTo(BigDecimal.ZERO) <= 0 || detail.getRefundAssistNumber().compareTo(BigDecimal.ZERO) <= 0) {
                return Response.fail(500,"物料编码：" + detail.getMaterialCode() + "，批次号：" + detail.getBatchNo() + "，退库数量不可小于等于0");
            }
            MaxNumberObj maxRefundNumber = getMaxRefundNumber(materialSupply, detail.getMaterialCode(), detail.getBatchNo());
            BigDecimal maxRefundAssistNum = maxRefundNumber.getMaxRefundAssistNum();
            BigDecimal maxRefundMainNum = maxRefundNumber.getMaxRefundMainNum();
            if (maxRefundMainNum.compareTo(detail.getRefundNumber()) < 0 || maxRefundAssistNum.compareTo(detail.getRefundAssistNumber()) < 0) {
                return Response.fail(500,String.format("物料编码：%s，批次号：%s，剩余最大可退辅数量为：%s", detail.getMaterialCode(), detail.getBatchNo(), maxRefundMainNum));
            }
        }
        // 查询退库单
        String refundApplyNo = checkRequest.getRefundApplyNo();
        MaterialRefundApply materialRefundApply = materialRefundApplyRepository.searchByRefundApplyNo(refundApplyNo);
        if (Objects.isNull(materialRefundApply)) {
            return Response.fail(ResultCode.REFUND_APPLY_NOT_EXIST.getCode(),String.format(ResultCode.REFUND_APPLY_NOT_EXIST.getMsg(),checkRequest.getRefundApplyNo()));
        }
        List<MaterialRefundApplyDetail> materialRefundApplyDetails = materialRefundApplyDetailRepository.searchByRefundSupplyNo(materialRefundApply.getRefundSupplyNo());
        OutDto outDto = buildRefundParam(materialRefundApply , materialRefundApplyDetails,detailRequests ,materialSupplyDetails ,materialDetailResponse.getData());
        Response<NccResponseDto> nccResult = invokeOut(outDto);
        if (!nccResult.isSuccess()) {
            return Response.fail(500,nccResult.getMsg());
        }
        // 出库成功
        String nccRefundSupplyNo = nccResult.getData().getHeadDto().getNccSupplyNo();
        materialRefundApplyRepository.updateStatus(refundApplyNo, RefundApplyStatus.ALREADY.getStatus(), LoginUserInfoUtil.getLoginUser(), nccRefundSupplyNo);
        materialRefundApplyDetailRepository.updateNumberBatch(detailRequests,materialRefundApply.getRefundSupplyNo());
        return Response.success();
    }

    private Response<NccResponseDto> invokeOut(OutDto outDto) {
        try {
            log.info("开始调用nc进行出库，参数：{}", JSONUtil.toJsonStr(Lists.newArrayList(outDto)));
            Response<NccResponseDto> nccResult = nccHttpServer.wareHousing(outDto);
            log.info("nc返回结果：{}", JSONUtil.toJsonStr(nccResult));
            return nccResult;
        } catch (Exception e) {
            return Response.fail(500, "nc出库出现异常失败");
        }
    }

    private OutDto buildRefundParam(MaterialRefundApply materialRefundApply, List<MaterialRefundApplyDetail> materialRefundApplyDetails,
                                    List<CheckMaterialRefundRequest.CheckDetailRequest> detailRequests,
                                    List<TbMaterialSupplyDetail> materialSupplyDetails,List<TbMaterialDto> materialDtos) {
        OutDto outDto = new OutDto();
        outDto.setStorDocId(materialRefundApply.getSupplyWarehouseId());
        outDto.setOrgId(materialRefundApply.getSupplyWarehouseOrgId());
        outDto.setDeptId(materialRefundApply.getSupplyDeptId());
        outDto.setDeptvId(materialRefundApply.getSupplyDeptId());
        outDto.setOutId(materialRefundApply.getRefundSupplyNo());
        outDto.setCustomerId(materialRefundApply.getCustomerId());
        outDto.setTypeId(materialRefundApply.getSupplyTypeId());
        outDto.setMakeDate(DateUtil.date());

        List<OutBodyDto> outBodyDtos = new ArrayList<>();
        Map<String, TbMaterialDto> materialMap = materialDtos.stream().collect(Collectors.toMap(TbMaterialDto::getMaterialCode, Function.identity()));
        Map<String, CheckMaterialRefundRequest.CheckDetailRequest> detailRequestMap = detailRequests.stream()
                .collect(Collectors.toMap(key -> key.getMaterialCode() + key.getBatchNo(), Function.identity()));
        Map<String, TbMaterialSupplyDetail> supplyDetailMap = materialSupplyDetails.stream()
                .collect(Collectors.toMap(key -> key.getMaterialCode() + key.getBatchNo(), Function.identity()));

        for (MaterialRefundApplyDetail item : materialRefundApplyDetails) {
            TbMaterialDto tbMaterialDto = materialMap.get(item.getMaterialCode());
            CheckMaterialRefundRequest.CheckDetailRequest checkDetailRequest = detailRequestMap.get(item.getMaterialCode() + item.getBatchNo());
            TbMaterialSupplyDetail materialSupplyDetail = supplyDetailMap.get(item.getMaterialCode() + item.getBatchNo());
            OutBodyDto outBodyDto = new OutBodyDto();
            outBodyDto.setMaterialCode(tbMaterialDto.getMaterialCode());
            outBodyDto.setStorageRequirement(tbMaterialDto.getStorageRequirement());
            outBodyDto.setExpirationDate(item.getExpirationDate());
            outBodyDto.setBatchCode(item.getBatchNo());
            outBodyDto.setRegistrationName(tbMaterialDto.getRegistrationName());
            outBodyDto.setRegistrationNo(tbMaterialDto.getRegistrationNo());
            outBodyDto.setRegistrant(tbMaterialDto.getRegistrantCode());
            outBodyDto.setMaterialAssistCountApply(checkDetailRequest.getRefundAssistNumber().negate());
            outBodyDto.setNshouldnum(checkDetailRequest.getRefundNumber().negate());
            outBodyDto.setNassistnum(checkDetailRequest.getRefundAssistNumber().negate());
            outBodyDto.setNnum(checkDetailRequest.getRefundNumber().negate());
            outBodyDto.setSecondaryUnitCode(tbMaterialDto.getSecondaryUnitCode());
            outBodyDto.setPrimaryUnitCode(tbMaterialDto.getPrimaryUnitCode());
            outBodyDto.setCscode(materialSupplyDetail.getRackCode());
            outBodyDto.setMainmeasrate(tbMaterialDto.getUnitTransRate());
            outBodyDtos.add(outBodyDto);
        }
        outDto.setOutBody(outBodyDtos);
        return outDto;
    }


    private Response<String> validInsertRefund(MaterialRefundApplyRequest applyRequest,List<MaterialRefundApplyDetailRequest> detailRequests) {
        // 基础信息校验
        String supplyNo = applyRequest.getSupplyNo();
        if (StringUtils.isAnyBlank(applyRequest.getLimsOrgId(),applyRequest.getRefundApplyNo(), supplyNo,applyRequest.getApplyNo()) || CollectionUtils.isEmpty(detailRequests)) {
            return Response.fail(ResultCode.REQUEST_INFO_ERR);
        }
        // 单号校验
        MaterialRefundApply materialRefundApply = materialRefundApplyRepository.searchByLimsOrgAndRefundApplyNo(applyRequest.getLimsOrgId(), applyRequest.getRefundApplyNo());
        if (Objects.nonNull(materialRefundApply)) {
            return Response.fail(ResultCode.REFUND_APPLY_EXIST.getCode(),String.format(ResultCode.REFUND_APPLY_EXIST.getMsg(),applyRequest.getLimsOrgId(),applyRequest.getRefundApplyNo()));
        }
        // 物料 + 批次 重复校验
        Map<String, Long> materialCountMap = detailRequests.stream().
                collect(Collectors.groupingBy(key -> key.getMaterialCode() + ":" + key.getBatchNo(), Collectors.counting()));
        List<String> duplicateMaterialAndBatch = materialCountMap.entrySet().stream().filter(entry -> entry.getValue() > 1).map(Map.Entry::getKey).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(duplicateMaterialAndBatch)) {
            String errorInfo = duplicateMaterialAndBatch.stream()
                    .map(key -> "物料编码：" + key.split(":")[0] + "批次号：" + key.split(":")[1] + "发生重复")
                    .collect(Collectors.joining(","));
            return Response.fail(ResultCode.REFUND_APPLY_MATERIAL_DUPLICATE.getCode(),errorInfo);
        }
        return Response.success();
    }

    /**
     * 获取最大的可退数量
     * @param materialSupply
     * @param materialCode
     * @param batchNo
     * @return
     */
    private MaxNumberObj getMaxRefundNumber(TbMaterialSupply materialSupply, String materialCode, String batchNo) {
        // 查询出库数量
        TbMaterialSupplyDetail tbMaterialSupplyDetail = materialSupplyDetailDal.searchBySupplyIdAndMateriInfo(materialSupply.getItemId(), materialCode, batchNo);
        if (Objects.isNull(tbMaterialSupplyDetail)) {
            throw new RuntimeException("物料编码：" + materialCode + "，批次号：" + batchNo + "，未找到出库信息");
        }
        BigDecimal maxRefundMainNum = tbMaterialSupplyDetail.getPrimaryNumber();
        BigDecimal maxRefundAssistNum = tbMaterialSupplyDetail.getAssistNumber();
        // 查询已退数量
        List<MaterialRefundApplyDetail> materialRefundApplyDetails = materialRefundApplyDetailRepository.searchBySupplyNoAndMaterialInfo(materialSupply.getSupplyNo(),materialCode,batchNo);
        if (CollectionUtils.isNotEmpty(materialRefundApplyDetails)) {
            BigDecimal alreadyRefundNumber = materialRefundApplyDetails.stream().map(MaterialRefundApplyDetail::getAlreadyRefundNumber).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal alreadyAssistNumber = materialRefundApplyDetails.stream().map(MaterialRefundApplyDetail::getAlreadyRefundAssistNumber).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            maxRefundMainNum = maxRefundMainNum.subtract(alreadyRefundNumber);
            maxRefundAssistNum = maxRefundAssistNum.subtract(alreadyAssistNumber);
        }
        MaxNumberObj maxNumberObj = new MaxNumberObj();
        maxNumberObj.setMaxRefundMainNum(maxRefundMainNum);
        maxNumberObj.setMaxRefundAssistNum(maxRefundAssistNum);
        return maxNumberObj;
    }
}
@Data
class MaxNumberObj{
    BigDecimal maxRefundMainNum = BigDecimal.ZERO;
    BigDecimal maxRefundAssistNum = BigDecimal.ZERO;
}