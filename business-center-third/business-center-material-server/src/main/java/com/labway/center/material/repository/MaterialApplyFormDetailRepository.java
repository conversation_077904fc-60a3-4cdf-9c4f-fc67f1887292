package com.labway.center.material.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.google.common.collect.Lists;
import com.labway.business.center.core.enums.DeleteFlagEnum;
import com.labway.business.center.core.enums.YesOrNoEnum;
import com.labway.center.material.persistence.entity.MaterialApplyFormDetail;
import com.labway.center.material.persistence.mapper.MaterialApplyFormDetailMapper;
import com.labway.sso.core.user.SsoUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2023/06/06 10:14
 **/
@Slf4j
@Repository
public class MaterialApplyFormDetailRepository {
    @Resource
    private MaterialApplyFormDetailMapper materialApplyFormDetailMapper;

    /**
     * 插入申领详情
     * @param materialApplyFormDetails
     * @return
     */
    public int insertBatch(List<MaterialApplyFormDetail> materialApplyFormDetails) {
        return materialApplyFormDetailMapper.insertBatchSomeColumn(materialApplyFormDetails);
    }

    /**
     * 本地库中查询申领单的详情
     * @param applyFormId
     * @return
     */
    public List<MaterialApplyFormDetail> queryDetailByFormId(String applyFormId) {
        LambdaQueryWrapper<MaterialApplyFormDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MaterialApplyFormDetail::getApplyFormId,applyFormId)
                .eq(MaterialApplyFormDetail::getRejectStatus,DeleteFlagEnum.NO_DELETE.getCode());
        return materialApplyFormDetailMapper.selectList(queryWrapper);
    }

    public int updateBatch(List<MaterialApplyFormDetail> materialApplyFormDetails) {
        // 批量修改

        return materialApplyFormDetailMapper.updateBatch(materialApplyFormDetails);
    }

    public List<MaterialApplyFormDetail> queryDetailByDetailIds(List<String> applyDetailIds) {
        if (CollectionUtils.isEmpty(applyDetailIds)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<MaterialApplyFormDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(MaterialApplyFormDetail::getItemId,applyDetailIds)
                .eq(MaterialApplyFormDetail::getRejectStatus,DeleteFlagEnum.NO_DELETE.getCode());
        return materialApplyFormDetailMapper.selectList(queryWrapper);
    }

    public List<MaterialApplyFormDetail> queryDetailByApplyCode(String applyCode) {
        LambdaQueryWrapper<MaterialApplyFormDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(MaterialApplyFormDetail::getApplyFormCode,applyCode)
                .eq(MaterialApplyFormDetail::getRejectStatus,DeleteFlagEnum.NO_DELETE.getCode());
        return materialApplyFormDetailMapper.selectList(queryWrapper);
    }

    /**
     * 根据申领单号和物料编码查询详情
     * @param applyNo
     * @param materialCodes
     * @return
     */
    public List<MaterialApplyFormDetail> getMaterialApplyFormDetailByApplyIdAndMaterialCodes(String applyNo, List<String> materialCodes) {
        if (StringUtils.isBlank(applyNo) || CollectionUtils.isEmpty(materialCodes)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<MaterialApplyFormDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MaterialApplyFormDetail::getApplyFormCode,applyNo)
                .in(MaterialApplyFormDetail::getMaterialCode,materialCodes)
                .eq(MaterialApplyFormDetail::getRejectStatus, DeleteFlagEnum.NO_DELETE.getCode());
        return materialApplyFormDetailMapper.selectList(queryWrapper);
    }

    public void rejectApplyMaterial(String applyNo, List<String> materialCodes) {
        if (StringUtils.isBlank(applyNo) || CollectionUtils.isEmpty(materialCodes)) {
            return ;
        }
        LambdaUpdateWrapper<MaterialApplyFormDetail> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(MaterialApplyFormDetail::getRejectStatus, DeleteFlagEnum.DELETED.getCode())
                .in(MaterialApplyFormDetail::getMaterialCode,materialCodes)
                .eq(MaterialApplyFormDetail::getApplyFormCode,applyNo);
        materialApplyFormDetailMapper.update(null,updateWrapper);
    }

    public List<MaterialApplyFormDetail> countNotRejectByApplyNo(String applyNo) {
        if (StringUtils.isBlank(applyNo)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<MaterialApplyFormDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MaterialApplyFormDetail::getApplyFormCode,applyNo)
                .eq(MaterialApplyFormDetail::getRejectStatus, DeleteFlagEnum.NO_DELETE.getCode());
        return materialApplyFormDetailMapper.selectList(queryWrapper);
    }

    /**
     * 批量作废申请单详情
     * @param itemIds
     * @param loginUser
     * @return
     */
    public Integer cancellationMaterialApplyFormDetail(List<String> itemIds, SsoUser loginUser) {
        if (CollectionUtils.isEmpty(itemIds)){
            return 0;
        }

        return materialApplyFormDetailMapper.update(null,new LambdaUpdateWrapper<MaterialApplyFormDetail>()
                .set(MaterialApplyFormDetail::getUpdateTime,new Date())
                .set(MaterialApplyFormDetail::getIsCancellation, YesOrNoEnum.YES.getCode())
                .set(MaterialApplyFormDetail::getCancellationUserId,loginUser.getUserId())
                .set(MaterialApplyFormDetail::getCancellationUserName,loginUser.getUserName())
                .set(MaterialApplyFormDetail::getCancellationTime,new Date())
                .in(MaterialApplyFormDetail::getItemId,itemIds));
    }

    /**
     * 批量恢复申请单物料详情
     * @param itemIds
     * @param loginUser
     * @return
     */
    public Integer renewMaterialApplyFormDetail(List<String> itemIds, SsoUser loginUser) {
        if (CollectionUtils.isEmpty(itemIds)){
            return 0;
        }

        return materialApplyFormDetailMapper.update(null,new LambdaUpdateWrapper<MaterialApplyFormDetail>()
                .set(MaterialApplyFormDetail::getUpdateTime,new Date())
                .set(MaterialApplyFormDetail::getIsCancellation, YesOrNoEnum.NO.getCode())
                .set(MaterialApplyFormDetail::getCancellationUserId,loginUser.getUserId())
                .set(MaterialApplyFormDetail::getCancellationUserName,loginUser.getUserName())
                .in(MaterialApplyFormDetail::getItemId,itemIds));
    }

    /**
     * 根据申领单id查询作废的物料
     */
    public List<MaterialApplyFormDetail> queryCancellationMaterialApplyFormDetail(String applyFormId) {
        LambdaQueryWrapper<MaterialApplyFormDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MaterialApplyFormDetail::getApplyFormId,applyFormId)
                .eq(MaterialApplyFormDetail::getIsCancellation,YesOrNoEnum.YES.getCode())
                .eq(MaterialApplyFormDetail::getRejectStatus,DeleteFlagEnum.NO_DELETE.getCode());
        return materialApplyFormDetailMapper.selectList(queryWrapper);
    }


}