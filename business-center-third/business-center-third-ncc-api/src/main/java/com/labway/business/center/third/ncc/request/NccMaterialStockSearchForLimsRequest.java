package com.labway.business.center.third.ncc.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Description nc物料现存量查询
 * @Date 2024/7/15 10:28
 */
@Data
public class NccMaterialStockSearchForLimsRequest implements Serializable {

	@NotBlank(message = "请选择公司")
	private String orgId;

	@NotNull(message = "请选择物料")
	private List<String> materialCodes;
}
