package com.labway.business.center.third.ncc.request;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @Description 计划采购详情列表查询
 * @Date 2024/7/17 13:33
 */
@Data
public class MaterialPurchasePlanDetailListSearchRequest {

	@Length(max = 20, message = "计划单号长度不能超过20")
	@NotBlank(message = "计划单号不能为空")
	private String planNo;

}
