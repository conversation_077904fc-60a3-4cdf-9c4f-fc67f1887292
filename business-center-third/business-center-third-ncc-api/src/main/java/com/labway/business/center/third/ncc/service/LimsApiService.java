package com.labway.business.center.third.ncc.service;

import com.labway.business.center.third.ncc.request.MaterialPurchasePlanCreateRequest;
import com.labway.business.center.third.ncc.request.NccMaterialStockSearchForLimsRequest;
import com.labway.business.center.third.ncc.vo.material.LimsMaterialStockVo;
import com.swak.frame.dto.Response;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 对lims暴露的接口
 * @Date 2024/7/15 9:48
 */
public interface LimsApiService {

	/**
	 * 查询物料库存 - lims
	 * @param request {@link NccMaterialStockSearchForLimsRequest}
	 * @return List {@link LimsMaterialStockVo}
	 */
	Response<List<LimsMaterialStockVo>> searchMaterialStockForLims(NccMaterialStockSearchForLimsRequest request);

	/**
	 * 创建计划采购单
	 * @param request
	 * @return
	 */
	Response<String> createMaterialPurchasePlan(MaterialPurchasePlanCreateRequest request);
}
