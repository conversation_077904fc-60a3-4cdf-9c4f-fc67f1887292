package com.labway.business.center.third.ncc.vo.supply;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 2023/03/27 13:26
 * @description
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class LisMaterialStockQueryVo implements Serializable {

    /**
     * 物料编码
     */
    private String materialCode;

    /**
     * 物料批次号
     */
    private String batchNo;

    /**
     * 辅单位数量
     */
    private BigDecimal assistNumber;



}