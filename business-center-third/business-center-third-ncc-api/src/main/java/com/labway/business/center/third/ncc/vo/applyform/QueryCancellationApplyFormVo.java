package com.labway.business.center.third.ncc.vo.applyform;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 申领单信息
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class QueryCancellationApplyFormVo implements Serializable {

    //申领单id 雪花生成唯一id
    private String itemId;
    //申领单编码-申请方唯一标识
    private String applyCode;
    //申领时间
    private Date applyTime;
    //申领人id
    private String applyUserId;
    //申领人名称
    private String appleUserName;
    //申领机构编码
    private String applyOrgCode;
    //申领机构名称
    private String applyOrgName;
    //申领社区编码
    private String applyLisCode;
    //申领社区名称
    private String applyLisName;
    //申领单状态；0待二审 1待出库 2部分出库 3已出库 4已驳回
    private Integer applyStatus;
    //是否有效
    private Boolean enabled;
    //操作人编码
    private String optUserCode;
    //操作人账号
    private String optUserAccount;
    //操作人姓名
    private String optUserName;
    //创建时间
    private Date createTime;
    //修改时间
    private Date updateTime;
    //申清单备注
    private String applyRemark;
    //申请单驳回原因
    private String rejectReason;
    // 系统名称
    private String systemName;
    // 系统编码
    private String systemCode;
    /**
     * 申领单作废状态0为作废，1作废
     */
    private Integer isCancellation;

    /**
     * 申领单作废状态0为作废，1作废
     */
    private String cancellationUserId;

    /**
     * 申领单作废状态0为作废，1作废
     */
    private String cancellationUserName;

    /**
     * 申领单作废状态0为作废，1作废
     */
    private Date  cancellationTime;


    // 物料明细
    List<UnStockOutApplyFormDetailQueryVo> detailQueryDtos;

}
