package com.labway.business.center.third.ncc.dto.ncc;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * NccInventorySplitDto
 *
 * <AUTHOR>
 * @version 2023/03/20 20:17
 **/
@Data
public class NccInventorySplitDto implements Serializable {
    /**
     * 主数量
     */
    private String nonhandnum;
    /**
     * 辅数量
     */
    private BigDecimal nonhandastnum;
    private String materialCode;
    private String materialName;
    /**
     * pici
     */
    private String batchNo;
    private String warehouseId;
    private String warehouseName;
    /**
     * 失效日期
     */
    private String dvalidate;
    /**
     * 货位编码 货位名称
     */
    private String cscode;
    private String csname;
}