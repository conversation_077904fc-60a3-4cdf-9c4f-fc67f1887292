package com.labway.business.center.third.ncc.request.ncc.purchase;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 * @Description
 * @Date 2024/8/7 10:33
 */
@Data
public class PoPraybillB implements Serializable {

	/**
	 * 主单位
	 */
	private String cunitid;

	/**
	 * 辅数量
	 */
	private BigDecimal nastnum;

	/**
	 * 主数量
	 */
	private BigDecimal nnum;

	/**
	 * 物料
	 */
	private String pk_material;

	/**
	 * 换算率
	 */
	private String vchangerate;

	/**
	 * 计划到货日期
	 */
	private String dreqdate;

	private String cordertrantypecode = "00010110000000001LJP";

}
