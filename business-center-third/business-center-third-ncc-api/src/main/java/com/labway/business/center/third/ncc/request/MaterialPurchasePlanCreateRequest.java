package com.labway.business.center.third.ncc.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 创建计划采购单
 * <AUTHOR>
 * @Description
 * @Date 2024/7/16 13:07
 */
@Data
public class MaterialPurchasePlanCreateRequest implements Serializable {
	/**
	 * 业务单元
	 */
	@Length(max = 20, message = "业务单元的长度不能超过20")
	@NotBlank(message = "请选择业务单元")
	private String orgId;

	/**
	 * 请购部门id（专业组id）
	 */
	@NotNull(message = "请选择请购的专业组")
	private Long purchaseRequestDeptId;

	/**
	 * 请购部门名称（专业组名称）
	 */
	@Length(max = 20, message = "请购部门的长度不能超过20")
	@NotBlank(message = "请选择请购的专业组")
	private String purchaseRequestDeptName;

	/**
	 * 请购人id（lims人员id）
	 */
	@Length(max = 20, message = "请购人员ID的长度不能超过20")
	@NotBlank(message = "请填写请购人员ID")
	private String purchaseRequestUserId;

	/**
	 * 请购人名称
	 */
	@Length(max = 20, message = "请购人员的长度不能超过20")
	@NotBlank(message = "请填写请购人员")
	private String purchaseRequestUserName;

	/**
	 * 请购时间
	 */
	@NotNull(message = "请选择请购时间")
	private LocalDateTime purchaseRequestTime;

	/**
	 * 关联的请购单号（lims的请购单号）
	 */
	@Length(max = 20, message = "关联的请购单号的长度不能超过20")
	@NotBlank(message = "请填写关联的请购单号")
	private String limsPlanNo;

	@Valid
	@NotNull(message = "请选择需要请购的物料")
	private List<MaterialPurchasePlanCreateDetailRequest> detailRequests;

	@Data
	public static class MaterialPurchasePlanCreateDetailRequest {

		@Length(max = 20, message = "物料编码的长度不能超过20")
		@NotBlank(message = "请选择计划请购物料")
		private String materialCode;

		/**
		 * 请购辅数量
		 */
		@DecimalMin(value = "0.0001", message = "请购辅数量不能小于0.0001")
		@NotNull(message = "请输入计划请购的辅数量")
		private BigDecimal planAssistNumber;

		/**
		 * 请购主数量
		 */
//		@NotNull(message = "请输入计划请购的主数量")
		@Deprecated(since = "1.0.6")
		private Integer planPrimaryNumber;

		/**
		 * 请购主数量
		 */
		@DecimalMin(value = "0.0001", message = "请购主数量不能小于0.0001")
//		@NotNull(message = "请输入计划请购的主数量")
		private BigDecimal planPrimaryNumberDecimal;
	}
}
