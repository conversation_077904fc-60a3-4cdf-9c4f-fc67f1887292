package com.labway.business.center.third.ncc.dto.material;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 专业组
 */
@Getter
@Setter
public class ProfessionalGroupDTO implements Serializable {
    /**
     * 专业组ID
     */
    private String groupId;

    /**
     * 专业组名称
     */
    private String groupName;

    /**
     * 专业组说明
     */
    private String groupRemark;

    /**
     * 专业组编码
     */
    private String groupCode;

    /**
     * 专业组类别ID
     */
    private String groupTypeCode;

    /**
     * 专业组类别名称
     */
    private String groupTypeName;

    /**
     * 批准者名字
     */
    private String approverName;

    /**
     * 批准者签名图片
     */
    private String approverSign;

    /**
     * 是否启动(0未启动 1启动)
     */
    private Integer enable;

    /**
     * 机构ID
     */
    private String orgId;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 创建人ID
     */
    private String creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 更新人ID
     */
    private String updaterId;

    /**
     * 更新人名称
     */
    private String updaterName;
}
