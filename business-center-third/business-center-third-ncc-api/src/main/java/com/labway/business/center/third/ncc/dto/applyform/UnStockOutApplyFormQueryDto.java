package com.labway.business.center.third.ncc.dto.applyform;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 导入申领单 查询对象
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UnStockOutApplyFormQueryDto implements Serializable {

    /**
     * 申领社区编码
     */
    private String lisCode;

    /**
     * 申领单开始时间
     */
    private Date applyTimeBegin;

    /**
     * 申领单结束时间
     */
    private Date applyTimeEnd;


}
