package com.labway.business.center.third.ncc.service;

import com.labway.business.center.third.ncc.request.ncc.purchase.PoPrayRequestBody;
import com.swak.frame.dto.Response;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @Description ncc请购单服务
 * @Date 2024/8/7 9:48
 */
public interface NccPurchaseRequestService {

	/**
	 * 新增请购单
	 * @param requestBody {@link PoPrayRequestBody}
	 * @return 成功时返回的是nc的单号，失败时msg中是异常信息
	 */
	Response<String> createPurchaseRequest(List<PoPrayRequestBody> requestBody);
}
