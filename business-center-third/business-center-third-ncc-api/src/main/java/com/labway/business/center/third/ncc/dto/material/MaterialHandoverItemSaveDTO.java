package com.labway.business.center.third.ncc.dto.material;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 无聊配送交接单明细保存实体类
 */
@Data
public class MaterialHandoverItemSaveDTO implements Serializable {

    private static final long serialVersionUID = 7038279155329992502L;
    /**
     * 物料交接单编号
     */
    private String materialHandoverNo;
    /**
     * 物流交接明细编号
     */
    private String materialHandoverItemNo;

    /**
     * 包裹号
     */
    private String packageNo;

    /**
     * 物料名称
     */
    private String materialName;

    /**
     * 物料编号
     */
    private String materialNo;

    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 规格
     */
    private String specification;

    /**
     * 型号
     */
    private String model;

    /**
     * 辅单位
     */
    private String subUnit;

    /**
     * 单位
     */
    private String unit;

    /**
     * 换算率
     */
    private String exchangeRate;

    /**
     * 辅数量
     */
    private Integer subCount;

    /**
     * 数量
     */
    private Integer count;

    /**
     * 期望到货期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
  private Date expectDate;

    /**
     * 失效日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
  private Date expireDate;

    /**
     * 备注
     */
    private String memo;

    /**
     * 证书URL
     */
    private String certificationUrl;
}