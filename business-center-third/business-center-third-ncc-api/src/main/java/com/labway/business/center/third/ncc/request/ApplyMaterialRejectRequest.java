package com.labway.business.center.third.ncc.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2024/03/11 10:23
 **/
@Data
public class ApplyMaterialRejectRequest implements Serializable {

    @NotBlank(message = "业务单元不能为空")
    private String orgCode;
    /**
     * 申请单号
     */
    @NotBlank(message = "申请单号不能为空")
    private String applyNo;

    @NotNull(message = "请选择物料")
    private List<String> materialCodes;
}