package com.labway.business.center.third.ncc.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 *
 * <AUTHOR>
 * @Description 出库单查询
 * @Date 2024/11/26 13:19
 */
@Data
public class SupplyPageRequest implements Serializable {

	@NotBlank(message = "请选择业务单元")
	private String orgId;

	/**
	 * 出库部门
	 */
	private String supplyDeptName;

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date starDate;

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date endDate;

	/**
	 * 物料条码号
	 */
	private String materialBarcode;

	/**
	 * 物料名称 / 编码
	 */
	private String materialInfo;

	/**
	 * 当前页数.
	 */
	@Min(value = 1, message = "当前页码必须大于0")
	@NotNull
	private Integer page;

	/**
	 * 分页条数.
	 */
	@Range(min = 1, max = 10000, message = "页大小应在1~10000之间")
	@NotNull
	private Integer pageSize;
}
