package com.labway.business.center.third.ncc.service;

import com.labway.business.center.core.config.PageResponse;
import com.labway.business.center.third.ncc.request.SupplyDetailRequest;
import com.labway.business.center.third.ncc.request.SupplyPageRequest;
import com.labway.business.center.third.ncc.response.SupplyDetailResponse;
import com.labway.business.center.third.ncc.response.SupplyPageResponse;
import com.swak.frame.dto.Response;

import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeoutException;

/**
 *
 * <AUTHOR>
 * @Description
 * @Date 2024/11/26 14:06
 */
public interface SupplySearchService {

	/**
	 * 分页查询物料出库信息
	 *
	 * @param request 查询条件
	 */
	Response<PageResponse<SupplyPageResponse>> searchSupplyPage(SupplyPageRequest request);

	/**
	 * 查询出库单详情
	 * @param request 查询条件
	 */
	Response<List<SupplyDetailResponse>> detail(SupplyDetailRequest request) throws ExecutionException, InterruptedException, TimeoutException;
}
