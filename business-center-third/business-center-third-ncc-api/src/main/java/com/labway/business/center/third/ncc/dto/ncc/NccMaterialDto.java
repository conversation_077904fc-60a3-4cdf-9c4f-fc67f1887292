package com.labway.business.center.third.ncc.dto.ncc;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Description ncc物料对象vo
 * @Date 2023/3/13 17:40
 */
@Data
public class NccMaterialDto {
    /**
     * 申领详情id
     */
    private String itemId;
    
    /**
     * 物料编码
     */
    private String invcode;
    
    /**
     * 物料名称
     */
    private String invname;
    
    /**
     * 生产企业
     */
    private String vfree3;
    
    /**
     * 规格
     */
    private String materialSpecification;
    
    /**
     * 备注
     */
    private String remark;
    
    /**
     * 申请主数量
     */
    private BigDecimal materialCountApply;
    
    /**
     * 主单位名称
     */
    private String measname;
    
    /**
     * 主单位code
     */
    private String measCode;
    
    /**
     * 已出主数量
     */
    private BigDecimal materialCountApproval;
    
    /**
     * 辅主单位换算率
     */
    private String mainmeasrate;
    
    /**
     * 申请辅数量
     */
    private BigDecimal materialAssistCountApply;
    
    /**
     * 辅单位名称
     */
    private String measnamef;
    
    /**
     * 辅单位code
     */
    private String measCodef;
    
    /**
     * 已出辅数量
     */
    private BigDecimal materialAssistCountApproval;
    
    /**
     * 批号
     */
    private String vbatchcode;
    
    /**
     * 失效日期
     */
    private String dvalidate;
    
    /**
     * 货位编码
     */
    private String cscode;
    
    /**
     * 货位名称
     */
    private String csname;
    
    /**
     * 主单位数量
     */
    private String nonhandnum;
    
    /**
     * 辅单位数量
     */
    private String nonhandastnum;

    /**
     * 出库对象 tb_material_supply表
     */
    private String supplyObject;

    /**
     * 物料类别
     */
    private String type;
    
    /**
     * 申请单号
     */
    private String applyCode;
    
    /**
     * 存储方式
     */
    private String storageRequirement;
    
    /**
     * 注册证号
     */
    private String registrationNo;
    
    /**
     * 注册证名称
     */
    private String registrationName;
    
    /**
     * 注册人
     */
    private String registrant;
    
    /**
     * 备案人code
     */
    private String registrantCode;

    /**
     * 备案人code
     */
    private String registrantNo;
    
    /**
     * 出库对象 id
     */
    private String costumerId;
    
    /**
     * 出库对象名称
     */
    private String costumerName;
    
    /**
     * 可以出库的辅数量
     */
    private BigDecimal nassistnum;
    
    /**
     * 可以出库的主数量
     */
    private String nnum;

    /**
     * 业务标识
     */
    private String materialFlag;
}
