package com.labway.business.center.third.ncc.dto.applyform;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ApplyFormDetailQueryDto implements Serializable {

    /**
     * 申请单id
     */
    @NotBlank(message = "申请单ID不能为空")
    private String applyFormId;
    
    /**
     * 出库仓库id
     */
    @NotBlank(message = "仓库ID不能为空")
    private String storDocId;
    
    /**
     * 出库对象id
     */
    @NotBlank(message = "出库对象不能为空")
    private String costumerId;

    public ApplyFormDetailQueryDto(String applyFormId){
        this.applyFormId = applyFormId;
    }
}
