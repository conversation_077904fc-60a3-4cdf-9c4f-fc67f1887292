package com.labway.business.center.third.ncc.dto.material;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 出库信息 Dto
 * 
 * <AUTHOR>
 * @since 2023/3/15 16:56
 */
@Getter
@Setter
public class SupplyInfoDto {

    /**
     * 申领信息id
     */
    private String applyId;
    /**
     * ncc出库类别名称
     */
    private String supplyType;
    /**
     * 出库单号
     */
    private String supplyNo;
    /**
     * 出库对象id
     */
    private String supplyCustomerId;
    /**
     * 出库对象名称
     */
    private String supplyCustomerName;
    /**
     * 出库人
     */
    private String supplyPerson;
    /**
     * 出库日期
     */
    private Date supplyTime;
    /**
     * 状态
     */
    private String status;
    /**
     * ncc出库单号
     */
    private String nccSupplyNo;

    /**
     * 雪花生成唯一id
     */
    private String itemId;

    // ---------------*--------------
    /**
     * 申领单号
     */
    private String applyCode;
}
