package com.labway.business.center.third.sccp.service;

import com.labway.business.center.core.config.PageRequest;
import com.labway.business.center.core.config.PageResponse;
import com.labway.business.center.third.sccp.request.ReturnOrderAuditRequest;
import com.labway.business.center.third.sccp.request.ReturnOrderPageSearchRequest;
import com.labway.business.center.third.sccp.response.SccpReturnOrderResponse;
import com.swak.frame.dto.Response;

/**
 * 退货订单
 *
 * <AUTHOR> on 2025/2/24.
 */
public interface ReturnOrderService {

	/**
	 * 退货审核
	 * @param request {@link ReturnOrderAuditRequest 退货订单审核}
	 */
	Response<String> auditReturnOrder(ReturnOrderAuditRequest request);

	/**
	 * 退货分页查询
	 * @param request {@link ReturnOrderPageSearchRequest 退货分页查询}
	 */
	Response<PageResponse<SccpReturnOrderResponse>> searchReturnOrderPage(PageRequest<ReturnOrderPageSearchRequest> request);
}
