package com.labway.business.center.third.sccp.request.sccp;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 同步SCCP订单请求
 *
 * <AUTHOR> on 2025/2/6.
 */
@Data
public class SyncSccpOrdersRequest implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 订单号 为空下载所有订单
	 */
	private String orderno;

	@JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
	private Date startdate;

	@JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
	private Date enddate;
}
