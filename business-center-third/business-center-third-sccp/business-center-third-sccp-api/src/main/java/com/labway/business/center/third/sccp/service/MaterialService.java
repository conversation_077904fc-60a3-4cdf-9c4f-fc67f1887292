package com.labway.business.center.third.sccp.service;

import com.labway.business.center.third.sccp.response.MaterialResponse;
import com.swak.frame.dto.Response;

import java.util.List;

/**
 * 试剂耗材服务
 *
 * <AUTHOR> on 2025/1/24.
 */
public interface MaterialService {

	void syncOrgCustomerMaterial2Local(String orgId, String customerCode);

	Response<List<MaterialResponse>> searchNeedUploadMaterial();

	/**
	 * 更新物料的上传状态
	 * @param materialCodes {需要更新的物料编码}
	 */
	Response<String> uploadMaterialsUploadStatus(List<String> materialCodes);
}
