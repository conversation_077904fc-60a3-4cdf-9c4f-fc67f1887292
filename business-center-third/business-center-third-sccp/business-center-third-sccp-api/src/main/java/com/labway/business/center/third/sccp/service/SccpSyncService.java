package com.labway.business.center.third.sccp.service;

import com.labway.business.center.third.sccp.request.sccp.SyncSccpOrdersRequest;
import com.swak.frame.dto.Response;

/**
 * 万序sccp同步服务
 *
 * <AUTHOR> on 2025/2/6.
 */
public interface SccpSyncService {

	Response<String> uploadMaterial2SCCP();

	/**
	 * 同步万序的采购订单
	 * @param syncRequest {@link SyncSccpOrdersRequest 同步请求}
	 * @return
	 */
	Response<String> syncSCCPOrders(SyncSccpOrdersRequest syncRequest);

	/**
	 * 上传配送单到SCCP
	 * @return
	 */
	Response<String> uploadSupply2SCCP();

	/**
	 * 同步万序的退货订单
	 * @param syncRequest {@link SyncSccpOrdersRequest 同步请求}
	 * @return
	 */
	Response<String> syncSCCPReOrders(SyncSccpOrdersRequest syncRequest);

	/**
	 * 手动上传配送单
	 * @param nccSupplyNo nc的销售出库单号
	 */
	Response<String> uploadSupply2SCCPByNo(String nccSupplyNo);
}
