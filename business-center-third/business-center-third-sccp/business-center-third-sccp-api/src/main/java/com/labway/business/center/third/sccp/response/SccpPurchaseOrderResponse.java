package com.labway.business.center.third.sccp.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 实体类 - sccp系统的采购单
 */
@Data
public class SccpPurchaseOrderResponse extends BaseEntityResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 采购订单主键
     */
    private String purchaseId;

	/**
	 * 业务单元id
	 */
	private String orgId;

    /**
     * 医院编码
     */
    private String hspCode;

    /**
     * 医院名称
     */
    private String hspName;

    /**
     * 医院收货部门编码
     */
    private String hspDeptCode;

    /**
     * 医院收货部门名称
     */
    private String hspDeptName;

    /**
     * 医院收货地址
     */
    private String hspAddress;

    /**
     * 医院订单直送科室编码
     */
    private String hspOrderDeptCode;

    /**
     * 医院订单直送科室名称
     */
    private String hspOrderDeptName;

    /**
     * sccp订单号
     */
    private String sccpOrderNo;

    /**
     * sccp订单号主键
     */
    private String sccpOrderCd;

    /**
     * 订单备注
     */
    private String sccpOrderRemark;

    /**
     * 供应商编码
     */
    private String supplierCode;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 平台订单数量
     */
    private BigDecimal purchqty;

    /**
     * sccp订单总金额
     */
    private BigDecimal sccpOrderPrice;

    /**
     * sccp平台订单日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date sccpOrderDate;

    /**
     * 确认状态 0: 未确认, 1: 已确认
     */
    private Boolean confirmStatus;

    /**
     * 对应中台的单号
     */
    private String bizNo;

    /**
     * 取消原因
     */
    private String cancelReason;

	/**
	 * 客商编码
	 */
	private String customerCode;

	/**
	 * 客商名称
	 */
	private String customerName;

    /**
     * 采购单详情
     */
    private List<SccpPurchaseOrderDetailResponse> detailResponses;
}
