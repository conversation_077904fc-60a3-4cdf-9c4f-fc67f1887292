<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.labway.business.center.third.sccp.persistence.mapper.ncc.NcBaseInfoMapper">

    <select id="getBdSaleBillTypes"
            resultType="com.labway.business.center.third.sccp.persistence.entity.ncc.baseinfo.BdBilltype">
        select pk_billtypecode as billtypecode, billtypename as billtypename, pk_billtypeid as billtypeid
        from bd_billtype
        where (
                  istransaction = 'Y' and pk_group = '000101100000000004NT' and nvl ( islock, 'N' ) = 'N'
                      AND
                  ( ( parentbilltype = '30' and pk_billtypecode in ( '30-03', '30-Cxx-01', '30-Cxx-02', '30-Cxx-03', '30-Cxx-04', '30-Cxx-05', '30-Cxx-06', '30-Cxx-07') ) )
                  )
        order by pk_billtypecode
    </select>
</mapper>