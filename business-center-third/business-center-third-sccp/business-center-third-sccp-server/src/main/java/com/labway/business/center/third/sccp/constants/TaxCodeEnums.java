package com.labway.business.center.third.sccp.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.math.BigDecimal;
import java.util.Arrays;

/**
 * 税码
 *
 * <AUTHOR> on 2025/6/12.
 */
@Getter
@AllArgsConstructor
public enum TaxCodeEnums {

	TAX_13(new BigDecimal("13"), "CN01", "10010110000000001ICK"),
	TAX_65(new BigDecimal("6.5"), "CN02", "10010110000000001IBS"),
	TAX_0(new BigDecimal("0"), "CN03", "1001Z01000000003W0X1"),
	TAX_3(new BigDecimal("3"), "CN05", "1002Z010000000002QHC"),
	TAX_16(new BigDecimal("16"), "CN06", "1002Z010000000002QHK"),
	TAX_6(new BigDecimal("6"), "CN07", "1002Z010000000002QHG"),
	TAX_9(new BigDecimal("9"), "CN08", "10010110000000001IDQ"),
	TAX_10(new BigDecimal("10"), "CN09", "1002Z010000000002QHI"),
	TAX_1(new BigDecimal("1"), "CN10", "1001011000000000W7G0"),
	;

	private final BigDecimal tax;

	private final String code;

	private final String pk;

	public static TaxCodeEnums getCodeByTax(BigDecimal tax) {
		return Arrays.stream(values()).filter(taxEnum -> taxEnum.getTax().compareTo(tax) == 0)
				.findAny().orElse(null);
	}
}
