package com.labway.business.center.third.sccp.aop;

import com.swak.frame.dto.Response;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

/**
 *
 *
 * <AUTHOR> on 2025/1/26.
 */
@Aspect
@Slf4j
@Component
public class RecordAop {

	@Pointcut("@annotation(com.labway.business.center.third.sccp.aop.Record)")
	public void annotationPointCut() {}

	@Around("annotationPointCut()")
	public Object around(ProceedingJoinPoint point) throws Throwable {
		// 类型装换
		MethodSignature methodSignature = (MethodSignature) point.getSignature();

		// 方法对象
		Method method = methodSignature.getMethod();
		Record annotation = method.getAnnotation(Record.class);
		String operationUser = annotation.operationUser();
		String operationContent = annotation.operationContent();
		Object proceed = point.proceed();
		if (proceed instanceof Response) {
			Response response = (Response) proceed;
			// 操作结果
			String contentResult = response.getMsg();
		}
		return proceed;
	}
}
