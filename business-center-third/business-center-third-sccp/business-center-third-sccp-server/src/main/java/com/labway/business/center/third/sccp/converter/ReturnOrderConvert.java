package com.labway.business.center.third.sccp.converter;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.labway.business.center.core.config.PageResponse;
import com.labway.business.center.third.sccp.persistence.entity.SccpReturnOrder;
import com.labway.business.center.third.sccp.persistence.entity.SccpReturnOrderDetail;
import com.labway.business.center.third.sccp.response.SccpReturnOrderDetailResponse;
import com.labway.business.center.third.sccp.response.SccpReturnOrderResponse;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 退货单
 *
 * <AUTHOR> on 2025/2/24.
 */
@Mapper(componentModel = "spring")
public interface ReturnOrderConvert {

	PageResponse<SccpReturnOrderResponse> convertReturnOrderPage(Page<SccpReturnOrder> page);

	List<SccpReturnOrderDetailResponse> convertSccpReturnOrderDetailList(List<SccpReturnOrderDetail> detaiList);
}
