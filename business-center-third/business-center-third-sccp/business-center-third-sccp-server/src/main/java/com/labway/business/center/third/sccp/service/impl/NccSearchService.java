package com.labway.business.center.third.sccp.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import com.labway.business.center.core.enums.DeleteFlagEnum;
import com.labway.business.center.core.third.ncc.DefaultNccInvoker;
import com.labway.business.center.third.sccp.config.SccpNccConfig;
import com.labway.business.center.third.sccp.constants.Constants;
import com.labway.business.center.third.sccp.constants.RedisConstants;
import com.labway.business.center.third.sccp.persistence.entity.*;
import com.labway.business.center.third.sccp.persistence.entity.ncc.IcSaleoutB;
import com.labway.business.center.third.sccp.persistence.entity.ncc.IcSaleoutH;
import com.labway.business.center.third.sccp.persistence.params.ncc.CtSale;
import com.labway.business.center.third.sccp.persistence.params.ncc.CtSaleB;
import com.labway.business.center.third.sccp.repositoiry.MaterialRepository;
import com.labway.business.center.third.sccp.repositoiry.SaleRepository;
import com.labway.business.center.third.sccp.repositoiry.SupplyRepository;
import com.labway.business.center.third.sccp.repositoiry.ncc.SaleOutRepository;
import com.labway.business.center.third.sccp.request.ncc.SearchNccCustomerSaleDailyRequest;
import com.labway.business.center.third.sccp.request.ncc.SearchNccSaleOutOrdersRequest;
import com.swak.frame.dto.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 查询ncc服务
 *
 * <AUTHOR> Tianhao on 2025/1/23.
 */
@Slf4j
@Service
public class NccSearchService {

	@Resource
	private SccpNccConfig sccpNccConfig;

	@Resource
	private SaleRepository saleRepository;

	@Resource
	private SupplyRepository supplyRepository;

	@Resource
	private SaleOutRepository saleOutRepository;

	@Resource
	private MaterialRepository materialRepository;

	@Resource
	private StringRedisTemplate stringRedisTemplate;

	@Resource
	private ThreadPoolTaskExecutor sccpExecutor;


	/**
	 * 查询nc 客商对应的销售合同
	 * @param searchRequest {@link SearchNccCustomerSaleDailyRequest}
	 * @return {@link CtSale 销售合同}
	 */
	public Response<List<CtSale>> searchNccCustomerSaleDaily(SearchNccCustomerSaleDailyRequest searchRequest) {
		// {"pk_customer.code": ["Z000020626000001"],"pk_org": ["00010110000000001V20"],"fstatusflag":["1"]}

		DefaultNccInvoker nccInvoker = new DefaultNccInvoker();
		String body = nccInvoker.doInvoke(sccpNccConfig, sccpNccConfig.getSaleDaily(), null, JSONUtil.toJsonStr(searchRequest));

		JSONObject jsonObject = JSONUtil.parseObj(body);
		Integer code = jsonObject.getInt("code");
		if (Objects.isNull(code) || code != 0) {
			log.error("查询ncc客商对应的销售合同失败，ncc返回信息：{}", jsonObject.getStr("message"));
			return Response.fail(500, jsonObject.getStr("message"));
		}

		JSONArray array = jsonObject.getJSONArray("data");
		List<CtSale> sales = new ArrayList<>();
		array.forEach(item -> {
			JSONObject itemObj = JSONUtil.parseObj(item);
			CtSale sale = itemObj.getBean("ct.ct_sale", CtSale.class);
			JSONArray jsonArray = itemObj.getJSONArray("ct.ct_sale_b");
			List<CtSaleB> ctSaleBList = jsonArray.toList(CtSaleB.class);
			sale.setSaleBList(ctSaleBList);
			sales.add(sale);
		});
		log.debug("查询ncc客商对应的销售合同结果={}", sales);
		return Response.success(sales);
	}

	/**
	 * 查询ncc销售出库单
	 * @return
	 */
	public Response<String> searchNccSaleOutOrders(SearchNccSaleOutOrdersRequest request) {
		StopWatch stopWatch = new StopWatch("同步NC销售订单");
		stopWatch.start();

		Date startDate = request.getStartDate();
		Date endDate = request.getEndDate();
		if (Objects.isNull(startDate)) {
			startDate = supplyRepository.getMaxSupplyDate();
		}
		if (Objects.isNull(endDate)) {
			endDate = new Date();
		}

		// 去nc查询这个时间范围内的出库单并且过滤掉已经处理过的
		log.info("查询NC销售出库单参数：[{}]-[{}]-[{}]", DateUtil.formatDateTime(startDate), DateUtil.formatDateTime(endDate), request.getSupplyNo());
		List<IcSaleoutH> orderHs = saleOutRepository.searchOrdersByDateOrSupplyNo(startDate, endDate, request.getSupplyNo())
				.stream().filter(id ->
						Boolean.FALSE.equals(stringRedisTemplate.hasKey(RedisConstants.formatKey(RedisConstants.HANDLED_S_H_KEY, id)))).collect(Collectors.toList());
		// 收集所有的id
		List<String> hIds = orderHs.stream().map(IcSaleoutH::getCgeneralhid).collect(Collectors.toList());
		log.info("查询NC销售出库单结果：{}", JSONUtil.toJsonStr(hIds));

		if (CollectionUtils.isEmpty(hIds)) {
			log.info("当前没有需要同步的销售出库单");
			return Response.success();
		}
		List<List<String>> partition = Lists.partition(hIds, 100);
		partition.forEach(ids -> {

			sccpExecutor.execute(() -> {
				RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
				RequestContextHolder.setRequestAttributes(requestAttributes);

				// 查询对应的详情
				Map<String, List<IcSaleoutB>> orderBMap = saleOutRepository.searchOrdersByHIds(hIds).stream().collect(Collectors.groupingBy(IcSaleoutB::getCgeneralhid));
				for (IcSaleoutH orderH : orderHs) {
					List<IcSaleoutB> orderBs = orderBMap.get(orderH.getCgeneralhid());
					if (CollectionUtils.isEmpty(orderBs)) {
						log.error("查询NC销售出库单详情失败，订单号：{}", orderH.getVbillcode());
						continue;
					}
					IcSaleoutB next = orderBs.iterator().next();
					String saleOrderCode = next.getVfirstbillcode();

					// 查询对应的销售单
					SaleOrder saleOrder = saleRepository.searchSaleOrderByNcSaleOrderNo(saleOrderCode);
					if (Objects.isNull(saleOrder)) {
						log.error("查询NC销售出库单对应的销售单失败，订单号：{}", orderH.getVbillcode());
						continue;
					}
					// 查询销售单详情
					List<SaleOrderDetail> saleOrderDetails = saleRepository.searchSaleOrderDetailBySaleOrderId(saleOrder.getSaleOrderId());

					// 数据保存入库 为了避免，拉到一批单子因为某一条导致批量失败，这里使用单挑数据落库
					Supply supply = handlerSupplyOrder(orderH, saleOrder);
					List<SupplyDetail> supplyDetails = handlerSupplyOrderDetail(orderBs, supply, saleOrderDetails);

					supplyRepository.insertSupplyOrder(supply);
					supplyRepository.insertSupplyOrderDetailBatch(supplyDetails);
				}
				RequestContextHolder.resetRequestAttributes();
			});
		});


		return Response.success();
	}

	private List<SupplyDetail> handlerSupplyOrderDetail(List<IcSaleoutB> orderBs, Supply supply, List<SaleOrderDetail> saleOrderDetails) {
		List<SupplyDetail> supplyDetails = new ArrayList<>();
		Map<String, Material> materialMap = materialRepository.searchLocalMaterialsByMaterialCodes(orderBs.stream().map(IcSaleoutB::getMaterialCode).collect(Collectors.toList()))
				.stream().collect(Collectors.toMap(Material::getMaterialCode, Function.identity()));
		Map<String, String> purchaseMaterialMap = saleOrderDetails.stream().collect(Collectors.toMap(SaleOrderDetail::getMaterialCode, SaleOrderDetail::getSourceOrderId));

		BigDecimal totalMoney = BigDecimal.ZERO;
		for (IcSaleoutB orderB : orderBs) {
			SupplyDetail supplyDetail = new SupplyDetail();
			supplyDetail.setSupplyDetailId(IdUtil.getSnowflake().nextId());
			supplyDetail.setSupplyId(supply.getSupplyId());
			supplyDetail.setPurchaseOrderDetailId(purchaseMaterialMap.get(orderB.getMaterialCode()));
			supplyDetail.setMaterialCode(orderB.getMaterialCode());
			supplyDetail.setMaterialName(orderB.getMaterialName());
			supplyDetail.setBatchNo(orderB.getVbatchcode());
			supplyDetail.setInvalidTime(orderB.getDvalidate());
			supplyDetail.setProducedDate(orderB.getDproducedate());
			supplyDetail.setPrimaryUnitNumber(orderB.getNnum());
			supplyDetail.setSecondaryUnitNumber(orderB.getNassistnum());
			supplyDetail.setMaterialPrice(orderB.getNqtorigtaxprice());
			supplyDetail.setTotalPrice(orderB.getNorigtaxmny());
			supplyDetail.setMaterialBarcode("");
			supplyDetail.setSupplierCode(Constants.DEFAULT_SUPPLIER_CODE);
			supplyDetail.setSupplierName(Constants.DEFAULT_SUPPLIER_NAME);
			supplyDetail.setOutStatus(0);
			supplyDetail.setOutNumber(BigDecimal.ZERO);


			Material material = materialMap.getOrDefault(orderB.getMaterialCode(), new Material());
			// 从本地查
			supplyDetail.setRegistrationNumber(material.getRegistrationNumber());
			supplyDetail.setPrimaryUnitCode(material.getPrimaryUnitCode());
			supplyDetail.setPrimaryUnitName(material.getPrimaryUnitName());
			supplyDetail.setSecondaryUnitCode(material.getSecondaryUnitCode());
			supplyDetail.setSecondaryUnitName(material.getSecondaryUnitName());
			supplyDetail.setCreateUser("默认用户");
			supplyDetail.setCreateTime(LocalDateTime.now());
			supplyDetail.setUpdateUser("默认用户");
			supplyDetail.setUpdateTime(LocalDateTime.now());
			supplyDetail.setIsDelete(DeleteFlagEnum.NO_DELETE.getCode());

			totalMoney = totalMoney.add(supplyDetail.getTotalPrice());
			supplyDetails.add(supplyDetail);
		}
		supply.setTotalPrice(totalMoney);
		return supplyDetails;
	}

	private Supply handlerSupplyOrder(IcSaleoutH orderH, SaleOrder saleOrder) {

		Supply supply = new Supply();
		supply.setSupplyId(IdUtil.getSnowflake().nextId());
		supply.setBizSupplyNo(orderH.getVbillcode());
		supply.setBizSaleOrder(saleOrder.getNcSaleOrderNo());
		supply.setPurchaseOrderId(saleOrder.getSourceOrderId());
		supply.setSupplyNum(orderH.getNtotalnum());
		supply.setRemark(orderH.getVnote());
		supply.setSupplyTime(DateUtil.parseDateTime(orderH.getDbilldate()));
		supply.setUploadStatus(Boolean.FALSE);
		supply.setCreateUser("默认用户");
		supply.setCreateTime(LocalDateTime.now());
		supply.setUpdateUser("默认用户");
		supply.setUpdateTime(LocalDateTime.now());
		supply.setIsDelete(DeleteFlagEnum.NO_DELETE.getCode());
		return supply;
	}
}
