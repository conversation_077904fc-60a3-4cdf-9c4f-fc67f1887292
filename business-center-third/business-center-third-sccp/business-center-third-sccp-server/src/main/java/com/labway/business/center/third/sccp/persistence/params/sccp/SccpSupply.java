package com.labway.business.center.third.sccp.persistence.params.sccp;

import lombok.Data;

import java.io.Serializable;

/**
 * SCCP配送单
 *
 * <AUTHOR> on 2025/2/13.
 */
@Data
public class SccpSupply implements Serializable {
	/**
	 * SCCP订单主单号
	 */
	private String ordercd;

	/**
	 * SCCP订单明细号
	 */
	private String orderdcd;

	/**
	 * 供应商配送单单据号
	 */
	private String suporderno;

	/**
	 * 供应商配送单主单号
	 */
	private String supordercd;

	/**
	 * 供应商配送单明细号
	 */
	private String suporderdcd;

	/**
	 * SCCP平台商品索引号
	 */
	private String goodscd;

	/**
	 * SCCP平台商品编码
	 */
	private String goodscode;

	/**
	 * 供应商商品编码
	 */
	private String uigoodscd;

	/**
	 * 生产批号
	 */
	private String lotno;

	/**
	 * 有效期
	 */
	private String expire;

	/**
	 * 生产日期
	 */
	private String mfdate;

	/**
	 * 批准文号/注册证号
	 */
	private String approvalnumber;

	/**
	 * 配货数量
	 */
	private String supqty;

	/**
	 * 单位
	 */
	private String unit;

	/**
	 * 配货单价
	 */
	private String supprice;

	/**
	 * 金额
	 */
	private String supmoney;

	/**
	 * 供应商编码
	 */
	private String supcode;

	/**
	 * 供应商名称
	 */
	private String supname;

	/**
	 * 配送时间
	 */
	private String suporderdate;

	/**
	 * 装箱条码
	 */
	private String barcode;

	/**
	 * 配货人
	 */
	private String username;

	/**
	 * 配货人联系方式
	 */
	private String tel;

	/**
	 * 备注
	 */
	private String remarks;
}
