package com.labway.business.center.third.sccp.job.sccp;

import com.labway.business.center.third.sccp.service.SccpJobService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

/**
 * sccp定时任务
 *
 * <AUTHOR> on 2025/2/25.
 */
@Slf4j
@Component
public class SccpJob {

	@DubboReference
	private SccpJobService sccpJobService;


	/**
	 * 同步nc物料
	 */
	@XxlJob("syncNccMaterial")
	public void syncNccMaterial() {
		log.info("自动执行定时任务,同步nc物料:syncNccMaterial");
		try {
			sccpJobService.syncNccMaterial();
		} catch (Exception e) {
			log.error("同步nc物料失败", e);
		}

	}

	/**
	 * 上传物料到sccp
	 */
	@XxlJob("syncLocalMaterial2SCCP")
	public void syncLocalMaterial2SCCP() {
		log.info("自动执行定时任务,上传物料到sccp:syncLocalMaterial2SCCP");
		try {
			sccpJobService.syncLocalMaterial2SCCP();
		} catch (Exception e) {
			log.error("上传物料到sccp失败", e);
		}
	}

	/**
	 * 同步万序采购单
	 */
	@XxlJob("syncSCCPPurchaseOrder")
	public void syncSCCPPurchaseOrder() {
		log.info("自动执行定时任务,同步万序采购单:syncSCCPPurchaseOrder");
		try {
			sccpJobService.syncSCCPPurchaseOrder();
		} catch (Exception e) {
			log.error("同步万序采购单失败", e);
		}
	}

	/**
	 * 同步万序退货单
	 */
	@XxlJob("syncSCCPReturnOrder")
	public void syncSCCPReturnOrder() {
		log.info("自动执行定时任务,同步万序退货单:syncSCCPReturnOrder");
		try {
			sccpJobService.syncSCCPReturnOrder();
		} catch (Exception e) {
			log.error("同步万序退货单失败", e);
		}
	}

	/**
	 * 同步nc销售订单
	 */
	@XxlJob("syncNccSaleOrders")
	public void syncNccSaleOrders() {
		log.info("自动执行定时任务,同步nc销售订单:syncNccSaleOrders");
		try {
			sccpJobService.syncNccSaleOrders();
		} catch (Exception e) {
			log.error("同步nc销售订单失败", e);
		}
	}

	/**
	 * 上传配送单
	 */
	@XxlJob("syncSupply2SCCP")
	public void syncSupply2SCCP() {
		log.info("自动执行定时任务,上传配送单:syncSupply2SCCP");
		try {
			sccpJobService.syncSupply2SCCP();
		} catch (Exception e) {
			log.error("上传配送单失败", e);
		}
	}
}
