package com.labway.business.center.third.sccp.persistence.params.sccp;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 上传sccp的物料
 *
 * <AUTHOR> on 2025/1/26.
 */
@Data
public class UploadMaterialParams implements Serializable {

	private SendDetails senddetails;

	@Data
	public static class SendDetails {
		private List<DetailLine> detailline;
	}

	@Data
	public static class DetailLine {
		/**
		 * 供应商商品编码(nc物料编码)
		 */
		private String uigoodscd;

		/**
		 * 商品名称(物料名称)
		 */
		private String ulname;

		/**
		 * 商品规格
		 */
		private String ulspec;

		/**
		 * 包装单位
		 */
		private String ulunit;

		/**
		 * 厂家
		 */
		private String ulcompyname;

		/**
		 * 批准文号/注册证号
		 */
		private String ulappdno;

		/**
		 * 销售价格
		 */
		private BigDecimal sellprice;

		/**
		 * 供应商编码
		 */
		private String supcode;

		/**
		 * 供应商名称
		 */
		private String supname;

		private String cwcatgcode;
		private String cwcatgname;

		private String typename = "试剂";

		/**
		 * 属性
		 */
		private String property = "低值";

		private String gs1;

		/**
		 * 1在用、2停用
		 */
		private String isreleaseflg;
	}
}
