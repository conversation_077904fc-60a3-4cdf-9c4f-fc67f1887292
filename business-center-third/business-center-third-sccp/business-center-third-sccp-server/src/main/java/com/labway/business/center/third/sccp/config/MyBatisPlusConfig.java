package com.labway.business.center.third.sccp.config;

import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.labway.business.center.core.injector.EasySqlInjector;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

@Configuration
public class MyBatisPlusConfig {
	@Bean
	public MybatisPlusInterceptor mybatisPlusInterceptor() {
		MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
		interceptor.addInnerInterceptor(paginationInterceptor());
		return interceptor;
	}

	private PaginationInnerInterceptor paginationInterceptor() {
		PaginationInnerInterceptor page = new PaginationInnerInterceptor();
		return page;
	}
	@Bean
	@Primary//批量插入配置
	public EasySqlInjector easySqlInjector() {
		return new EasySqlInjector();
	}
}