package com.labway.business.center.third.sccp.service.impl;

import com.google.common.collect.Lists;
import com.labway.business.center.third.ncc.dto.material.TbMaterialDto;
import com.labway.business.center.third.sccp.aop.Record;
import com.labway.business.center.third.sccp.converter.MaterialConverter;
import com.labway.business.center.third.sccp.persistence.entity.Material;
import com.labway.business.center.third.sccp.persistence.params.ncc.CtSale;
import com.labway.business.center.third.sccp.persistence.params.ncc.CtSaleB;
import com.labway.business.center.third.sccp.repositoiry.MaterialRepository;
import com.labway.business.center.third.sccp.request.ncc.SearchNccCustomerSaleDailyRequest;
import com.labway.business.center.third.sccp.response.MaterialResponse;
import com.labway.business.center.third.sccp.service.MaterialService;
import com.labway.business.center.third.sccp.util.MdmUtil;
import com.swak.frame.dto.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.ImmutableTriple;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 试剂耗材服务
 *
 * <AUTHOR> Tianhao on 2025/1/24.
 */
@Slf4j
@Service
public class MaterialServiceImpl implements MaterialService {

	@Resource
	private MaterialRepository materialRepository;

	@Resource
	private NccSearchService nccSearchService;

	@Resource
	private MaterialConverter materialConverter;

	@Resource
	private StringRedisTemplate stringRedisTemplate;

	@Resource
	private MdmUtil mdmUtil;


	/**
	 * 同步xx组织下的xx客商签订合同的物料同步到本地库中
	 * @param orgId 业务单元id
	 * @param customerCode 客商编码
	 */
	@Override
	@Record(operationContent = "本地同步销售物料")
	@Transactional(rollbackFor = Exception.class)
	public void syncOrgCustomerMaterial2Local(String orgId, String customerCode) {
		SearchNccCustomerSaleDailyRequest searchNccCustomerSaleDailyRequest = new SearchNccCustomerSaleDailyRequest();
		searchNccCustomerSaleDailyRequest.setPk_org(List.of(orgId));
		searchNccCustomerSaleDailyRequest.setCustomer(List.of(customerCode));
		// 销售合同
		Response<List<CtSale>> nccSaleDailyResponse = nccSearchService.searchNccCustomerSaleDaily(searchNccCustomerSaleDailyRequest);
		if (!nccSaleDailyResponse.isSuccess()) {
			return;
		}
		List<CtSale> data = nccSaleDailyResponse.getData();
		if (data.isEmpty()) {
			return;
		}
		// 销售合同下的物料
		Map<String, List<CtSaleB>> saleMap = data.stream().collect(Collectors.toMap(CtSale::getCtname, CtSale::getSaleBList));

		saleMap.forEach((k,v) -> {
			Map<String, CtSaleB> saleMaterialMap = v.stream().collect(Collectors.toMap(item -> item.getPk_material().getCode(), Function.identity(), (v1, v2) -> v2));
			Set<String> materialCodes = saleMaterialMap.keySet();

			Response<List<TbMaterialDto>> mdmResponse = mdmUtil.getMaterialDetail(materialCodes);
			if (!mdmResponse.isSuccess()) {
				log.error("查询主数据出现异常：{}", mdmResponse.getMsg());
				return;
			}
			List<TbMaterialDto> mdmMaterials = mdmResponse.getData();

			Map<String, Material> materialMap = materialRepository.searchLocalMaterialsByMaterialCodes(materialCodes)
					.stream().collect(Collectors.toMap(Material::getMaterialCode, Function.identity(), (v1, v2) -> v2));

			Triple<List<Material>, List<Material>, List<String>> materialInfos = buildSaveMaterialInfos(saleMaterialMap, mdmMaterials,
					materialMap, searchNccCustomerSaleDailyRequest, k);

			materialRepository.saveBatchMaterial(materialInfos.getLeft());
			materialRepository.updateBatchMaterial(materialInfos.getMiddle());
			materialRepository.deleteBatchMaterial(materialInfos.getRight());
		});
	}

	@Override
	public Response<List<MaterialResponse>> searchNeedUploadMaterial() {
		return Response.success(materialConverter.convert2ResponseMaterialList(materialRepository.searchNeedUploadMaterial()));
	}

	/**
	 * 更新物料的上传状态
	 *
	 * @param materialCodes {需要更新的物料编码}
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public Response<String> uploadMaterialsUploadStatus(List<String> materialCodes) {
		materialRepository.uploadMaterialsUploadStatus(materialCodes);
		return Response.success();
	}

	/**
	 * 构建保存物料信息
	 *
	 * @param saleMaterialMap                   销售合同的物料
	 * @param mdmMaterials                      主数据的物料
	 * @param materialMap                       本地的物料
	 * @param searchNccCustomerSaleDailyRequest 组织信息
	 * @param k 合同名称
	 * @return 保存物料信息 {L:新增的m, M:修改的, R: 删除的物料编码}
	 */
	private Triple<List<Material>, List<Material>, List<String>> buildSaveMaterialInfos(Map<String, CtSaleB> saleMaterialMap,
	                                                                                    List<TbMaterialDto> mdmMaterials,
	                                                                                    Map<String, Material> materialMap,
	                                                                                    SearchNccCustomerSaleDailyRequest searchNccCustomerSaleDailyRequest,
	                                                                                    String k) {
		List<Material> insertMaterials = Lists.newArrayList();
		List<Material> updateMaterials = Lists.newArrayList();
		List<String> deleteMaterialCodes = Lists.newArrayList();

		for (TbMaterialDto mdmMaterial : mdmMaterials) {
			CtSaleB saleInfo = saleMaterialMap.get(mdmMaterial.getMaterialCode());
			if (Objects.isNull(saleInfo)) {
				continue;
			}

			Material material = materialMap.getOrDefault(mdmMaterial.getMaterialCode(), new Material());
			if (material.equalsMdm(mdmMaterial, saleInfo)) {
				continue;
			}

			boolean hasMaterial = materialMap.containsKey(mdmMaterial.getMaterialCode());
			Material newMaterial = materialConverter.convert2LocalMaterial(saleInfo, mdmMaterial, searchNccCustomerSaleDailyRequest, k);
			if (hasMaterial) {
				updateMaterials.add(newMaterial);
			} else {
				insertMaterials.add(newMaterial);
			}
		}
		// 将不存在的删除
		deleteMaterialCodes.addAll(
			materialMap.keySet().stream()
				.filter(code -> mdmMaterials.stream()
					.noneMatch(mdm -> Objects.equals(mdm.getMaterialCode(), code)))
				.collect(Collectors.toList())
		);

		return ImmutableTriple.of(insertMaterials, updateMaterials, deleteMaterialCodes);
	}
}
