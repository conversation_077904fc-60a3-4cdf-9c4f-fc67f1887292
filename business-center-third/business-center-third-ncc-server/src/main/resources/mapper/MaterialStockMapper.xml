<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.business.center.third.ncc.persistence.mapper.MaterialStockMapper">

    <select id="selectMaterialStock"
            resultType="com.labway.business.center.third.ncc.dto.ncc.NccMaterialStockDto">
        select a.*
        from (select INVCODE as materialCode,
        sum(nonhandnum) as primaryInventoryNumber,
        sum(nonhandastnum) as secondInventoryNumber
        from view_inv
        where UNITCODE = '01'
        group by INVCODE) a
        where a.materialCode in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>

