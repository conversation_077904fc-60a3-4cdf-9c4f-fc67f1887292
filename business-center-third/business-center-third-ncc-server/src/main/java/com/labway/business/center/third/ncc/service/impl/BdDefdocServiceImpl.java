package com.labway.business.center.third.ncc.service.impl;

import com.labway.business.center.core.enums.ResultCode;
import com.labway.business.center.third.ncc.converter.BdDefdocConverter;
import com.labway.business.center.third.ncc.dto.material.BdDefdocDTO;
import com.labway.business.center.third.ncc.persistence.entity.BdDefdoc;
import com.labway.business.center.third.ncc.repository.BdDefdocRepository;
import com.labway.business.center.third.ncc.service.BdDefdocService;
import com.swak.frame.dto.Response;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 2023/11/01 19:12
 **/
@DubboService
public class BdDefdocServiceImpl implements BdDefdocService {
    @Resource
    private BdDefdocRepository bdDefdocRepository;

    @Resource
    private BdDefdocConverter bdDefdocConverter;


    /**
     * 查询物料储存条件主键
     *
     * @param storageRequirements
     * @return
     */
    @Override
    public Response<List<BdDefdocDTO>> searchMaterialStorageRequirement(Set<String> storageRequirements) {
        if (CollectionUtils.isEmpty(storageRequirements)) {
            return Response.fail(ResultCode.PARAMS_NOT_VALIDATE);
        }
        List<BdDefdoc> bdDefdocs = bdDefdocRepository.searchStorageList(storageRequirements);
        return Response.success(bdDefdocConverter.convertBdDefdoc2DTOList(bdDefdocs));
    }
}