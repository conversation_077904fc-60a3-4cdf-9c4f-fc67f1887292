package com.labway.business.center.third.ncc.persistence.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 自定义档案
 * <AUTHOR>
 * @version 2023/10/11 09:29
 **/
@Data
@TableName(value = "bd_defdoc")
public class BdDefdoc {

    /**
     * 主键
     */
    @TableField("pk_defdoc")
    private String pkDefdoc;

    /**
     * 名称
     */
    @TableField("name")
    private String name;
}