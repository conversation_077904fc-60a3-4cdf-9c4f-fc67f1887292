package com.labway.business.center.third.ncc.persistence.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * ncc仓库
 * <AUTHOR>
 * @since 2023-03-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("BD_STORDOC")
public class BdStordoc implements Serializable {

    private static final long serialVersionUID = 1L;
    
    /**
     * 仓库编码
     */
    @TableField("CODE")
    private String code;
    
    /**
     * 创建时间
     */
    @TableField("CREATIONTIME")
    private String creationtime;
    
    /**
     * 创建人
     */
    @TableField("CREATOR")
    private String creator;
    
    /**
     * 	货位管理
     */
    @TableField("CSFLAG")
    private String csflag;

    @TableField("DATAORIGINFLAG")
    private Long dataoriginflag;

    @TableField("DEF1")
    private String def1;

    @TableField("DEF2")
    private String def2;

    @TableField("DEF3")
    private String def3;

    @TableField("DEF4")
    private String def4;

    @TableField("DEF5")
    private String def5;

    @TableField("DR")
    private Long dr;
    
    /**
     * 启用状态
     * 1=未启用;
     * 2=已启用;
     * 3=已停用;
     */
    @TableField("ENABLESTATE")
    private Long enablestate;
    
    /**
     * 废品库
     */
    @TableField("GUBFLAG")
    private String gubflag;
    
    /**
     * 代储仓
     */
    @TableField("ISAGENTSTORE")
    private String isagentstore;
    
    /**
     * 影响可用量
     */
    @TableField("ISATPAFFECTED")
    private String isatpaffected;
    
    /**
     * 	进行存货成本计算
     */
    @TableField("ISCALCULATEDINVCOST")
    private String iscalculatedinvcost;
    
    /**
     * 委外仓
     */
    @TableField("ISCOMMISSIONOUT")
    private String iscommissionout;
    
    /**
     * 	直运仓
     */
    @TableField("ISDIRECTSTORE")
    private String isdirectstore;
    
    /**
     * 	保税仓
     */
    @TableField("ISKPTAXSTORE")
    private String iskptaxstore;
    
    /**
     * 可预留
     */
    @TableField("ISOBLIGATE")
    private String isobligate;

    @TableField("ISRETAIL")
    private String isretail;

    @TableField("ISSHOPSTORE")
    private String isshopstore;

    @TableField("ISSTOREONTHEWAY")
    private String isstoreontheway;
    
    /**
     * 备注
     */
    @TableField("MEMO")
    private String memo;
    
    /**
     * 最后修改时间
     */
    @TableField("MODIFIEDTIME")
    private String modifiedtime;
    
    /**
     * 修改人
     */
    @TableField("MODIFIER")
    private String modifier;

    @TableField("MRPFLAG")
    private String mrpflag;
    
    /**
     * 仓库名称
     */
    @TableField("NAME")
    private String name;

    @TableField("NAME2")
    private String name2;

    @TableField("NAME3")
    private String name3;

    @TableField("NAME4")
    private String name4;

    @TableField("NAME5")
    private String name5;

    @TableField("NAME6")
    private String name6;
    
    /**
     * 加工商
     */
    @TableField("OPERATESUPPLIER")
    private String operatesupplier;
    
    /**
     * 	电话号码
     */
    @TableField("PHONE")
    private String phone;
    
    /**
     * 所属地点
     */
    @TableField("PK_ADDRESS")
    private String pkAddress;
    
    /**
     * 所属集团
     */
    @TableField("PK_GROUP")
    private String pkGroup;
    
    /**
     * 所属库存组织
     */
    @TableField("PK_ORG")
    private String pkOrg;
    
    /**
     * 主键
     */
    @TableId("PK_STORDOC")
    private String pkStordoc;
    
    /**
     * 	负责人
     */
    @TableField("PRINCIPALCODE")
    private String principalcode;
    
    /**
     * 所属利润中心
     */
    @TableField("PROFITCENTRE")
    private String profitcentre;
    
    /**
     * 	生产仓库
     */
    @TableField("PROFLAG")
    private String proflag;
    
    /**
     * 仓库地址
     */
    @TableField("STORADDR")
    private String storaddr;

    @TableField("TS")
    private String ts;

    @TableField(exist = false)
    private String orgName;
}
