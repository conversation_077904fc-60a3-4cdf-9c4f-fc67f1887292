package com.labway.business.center.third.ncc.repository;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.labway.business.center.third.ncc.persistence.entity.BdRack;
import com.labway.business.center.third.ncc.persistence.mapper.BdRackMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2023/09/22 18:59
 **/
@DS("ncc")
@Repository
public class BdRackRepository {
    @Resource
    private BdRackMapper bdRackMapper;

    /**
     * 查询仓库下的货位
     * @param storId
     * @return
     */
    public List<BdRack> searchBdRackListByStorId(String storId) {
        if (StringUtils.isBlank(storId)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<BdRack> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BdRack::getPkStordoc,storId);
        return bdRackMapper.selectList(queryWrapper);
    }
}