package com.labway.business.center.third.ncc.service.impl;

import com.labway.business.center.third.ncc.converter.BdcurrtypeConverter;
import com.labway.business.center.third.ncc.dto.ncc.BdCurrtypeDTO;
import com.labway.business.center.third.ncc.persistence.entity.BdCurrtype;
import com.labway.business.center.third.ncc.repository.BdCurrtypeRepository;
import com.labway.business.center.third.ncc.service.BdCurrtypeService;
import com.swak.frame.dto.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2023/10/18 09:41
 **/
@Slf4j
@DubboService
public class BdCurrtypeServiceImpl implements BdCurrtypeService {
    @Resource
    private BdCurrtypeRepository bdCurrtypeRepository;

    @Resource
    private BdcurrtypeConverter bdcurrtypeConverter;

    /**
     * 查询所有的币种
     *
     * @return
     */
    @Override
    public Response<List<BdCurrtypeDTO>> searchAllBdCurrtype() {
        List<BdCurrtype> bdCurrtypes = bdCurrtypeRepository.searchAllBdCurrtype();
        return Response.success(bdcurrtypeConverter.converterEntitys2DTOList(bdCurrtypes));
    }
}