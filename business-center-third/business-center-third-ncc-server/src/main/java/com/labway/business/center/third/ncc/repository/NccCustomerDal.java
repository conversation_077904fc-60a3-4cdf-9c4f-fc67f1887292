package com.labway.business.center.third.ncc.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.labway.business.center.third.ncc.constants.CommonErrorCode;
import com.labway.business.center.third.ncc.dto.ncc.NccCostumerDto;
import com.labway.business.center.third.ncc.dto.ncc.NccCustomerPageDto;
import com.labway.business.center.third.ncc.dto.ncc.SimpleNccCustomerListDto;
import com.labway.business.center.third.ncc.persistence.entity.BdCustomer;
import com.labway.business.center.third.ncc.persistence.mapper.BdCustomerMapper;
import com.swak.frame.dto.Response;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * NccCustomerDal 客户dal
 *
 * <AUTHOR>
 * @version 2023/03/16 13:02
 **/
@Repository
public class NccCustomerDal {
    @Resource
    private BdCustomerMapper bdCustomerMapper;
    
    /**
     * 分页 根据名称 模糊查询
     * @param pageDto 分页对象
     * @return 客户信息
     */
    public Page<BdCustomer> selectPageByName(NccCustomerPageDto pageDto) {
        Page<BdCustomer> page = new Page<>();
        page.setCurrent(pageDto.getCurrent());
        page.setSize(pageDto.getSize());
        LambdaQueryWrapper<BdCustomer> wrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(pageDto.getCustomerName())) {
            wrapper.like(BdCustomer::getName,pageDto.getCustomerName());
        }
        wrapper.eq(BdCustomer::getEnablestate,2).eq(BdCustomer::getPkOrg,"000101100000000004NT");
        wrapper.select(BdCustomer::getName,BdCustomer::getPkCustomer,BdCustomer::getCode);
        return bdCustomerMapper.selectPage(page,wrapper);
    }
    
    /**
     * 根据客户id获取客户code
     *
     * @param customerId 客户id
     * @return 客户的code
     */
    public Response<String> getNccCustomerCodeByCustomerId(String customerId) {
        LambdaQueryWrapper<BdCustomer> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(BdCustomer::getCode).eq(BdCustomer::getPkCustomer, customerId);
        BdCustomer bdCustomer = bdCustomerMapper.selectOne(wrapper);
        if (StringUtils.isBlank(bdCustomer.getCode())) {
            return Response.fail(CommonErrorCode.E000500.getCode(),"该客商不存在");
        }
        return Response.success(bdCustomer.getCode());
    }
    
    /**
     * 根据出库对象id查询出库对象
     * @param costumerId 出库对象id
     * @return 出库对象
     */
    public NccCostumerDto getNccCostumerById(String costumerId) {
        LambdaQueryWrapper<BdCustomer> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(BdCustomer::getCode,BdCustomer::getName).eq(BdCustomer::getPkCustomer, costumerId);
        BdCustomer bdCustomer = bdCustomerMapper.selectOne(wrapper);
        NccCostumerDto nccCostumerDto = new NccCostumerDto();
        nccCostumerDto.setCostumerId(costumerId);
        nccCostumerDto.setCostumerName(bdCustomer.getName());
        return nccCostumerDto;
    }

    public Response<String> getNccCustomerPkByCustomerCode(String customerCode) {
        LambdaQueryWrapper<BdCustomer> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(BdCustomer::getPkCustomer).eq(BdCustomer::getCode, customerCode);
        BdCustomer bdCustomer = bdCustomerMapper.selectOne(wrapper);
        if (StringUtils.isBlank(bdCustomer.getPkCustomer())) {
            return Response.fail(CommonErrorCode.E000500.getCode(),"该客商不存在");
        }
        return Response.success(bdCustomer.getPkCustomer());
    }


    /**
     * select name, code
     * from BD_CUSTOMER where pk_customer in(
     * select pk_customer from bd_custorg where pk_org = '00010110000000001V0O' and enablestate = 2) ;
     * @param dto
     * @return
     */
    public List<BdCustomer> getSimpleNccCustomerList(SimpleNccCustomerListDto dto) {
            return bdCustomerMapper.getSimpleNccCustomerList(dto.getOrgCode());
    }


}