package com.labway.business.center.third.ncc.service.impl;

import cn.hutool.json.JSONUtil;
import com.labway.business.center.core.enums.ResultCode;
import com.labway.business.center.core.third.ncc.DefaultNccInvoker;
import com.labway.business.center.core.util.NcResponseUtil;
import com.labway.business.center.third.ncc.config.ThridNccConfig;
import com.labway.business.center.third.ncc.request.ncc.purchase.PoPrayRequestBody;
import com.labway.business.center.third.ncc.service.NccPurchaseRequestService;
import com.swak.frame.dto.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 * @Description ncc请购单服务
 * @Date 2024/8/7 9:48
 */
@Slf4j
@DubboService
public class NccPurchaseRequestServiceImpl implements NccPurchaseRequestService {

	@Resource
	private ThridNccConfig thridNccConfig;

	@Override
	public Response<String> createPurchaseRequest(List<PoPrayRequestBody> requestBody) {
		String jsonBody = JSONUtil.toJsonStr(requestBody);
		log.info("调用NCC创建请购单：{}",jsonBody);

		String response = null;
		try {
			DefaultNccInvoker defaultNccInvoker = new DefaultNccInvoker();
			response = defaultNccInvoker.doInvoke(thridNccConfig, thridNccConfig.getPurchaseRequestUrl(), null, jsonBody);
		} catch (Exception e) {
			return Response.fail(ResultCode.NCC_INVOKE_FAIL);
		}

		log.info("NCC创建请购单响应内容：{}",response);
		Response<String> ncResponse = NcResponseUtil.isSuccess(response);
		if (!ncResponse.isSuccess()) {
			return ncResponse;
		}

		Map<String, String> vbillcodeMap = NcResponseUtil.getIndexDataAndField(response, 0, "pu.po_praybill", "vbillcode");
		if (MapUtils.isEmpty(vbillcodeMap) || StringUtils.isBlank(vbillcodeMap.get("vbillcode"))) {
			return Response.fail(ResultCode.NCC_RESPONSE_DATA_ERROR);
		}
		return Response.success(vbillcodeMap.get("vbillcode"));
	}
}
