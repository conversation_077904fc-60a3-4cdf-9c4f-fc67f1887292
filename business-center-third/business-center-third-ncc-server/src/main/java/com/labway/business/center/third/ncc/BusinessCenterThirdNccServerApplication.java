package com.labway.business.center.third.ncc;

import com.github.jaemon.dinger.config.DingerAutoConfiguration;
import com.labway.business.center.core.enums.Constants;
import com.labway.business.center.third.ncc.config.CustomApplicationContextInitializer;
import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;

/**
 * <AUTHOR>
 * @version 2023/05/22 09:55
 **/
@SpringBootApplication(exclude = DingerAutoConfiguration.class)
@MapperScan( basePackages = {"com.labway.business.center.third.**.mapper.**"})
@ComponentScan(Constants.BASE_PACKAGE)
@EnableDubbo
@EnableWebMvc
public class BusinessCenterThirdNccServerApplication {
    public static void main(String[] args) {
        SpringApplication application = new SpringApplication(BusinessCenterThirdNccServerApplication.class);
        application.addInitializers(new CustomApplicationContextInitializer());
        application.run(args);
        
    }
}