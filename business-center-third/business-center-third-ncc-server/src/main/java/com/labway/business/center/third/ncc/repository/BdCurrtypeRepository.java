package com.labway.business.center.third.ncc.repository;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.business.center.third.ncc.persistence.entity.BdCurrtype;
import com.labway.business.center.third.ncc.persistence.mapper.BdCurrtypeMapper;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * 币种
 *
 * <AUTHOR>
 * @version 2023/10/18 09:40
 **/
@DS("ncc")
@Repository
public class BdCurrtypeRepository {
    @Resource
    private BdCurrtypeMapper bdCurrtypeMapper;

    public List<BdCurrtype> searchAllBdCurrtype() {
        return bdCurrtypeMapper.selectList(Wrappers.lambdaQuery(BdCurrtype.class).orderByAsc(BdCurrtype::getPkCurrtype));
    }
}