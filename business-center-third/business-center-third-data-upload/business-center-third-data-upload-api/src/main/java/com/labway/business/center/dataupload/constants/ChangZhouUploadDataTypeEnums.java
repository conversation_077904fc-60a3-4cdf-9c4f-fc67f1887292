package com.labway.business.center.dataupload.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 *
 * <AUTHOR>
 * @Description excel表格导入枚举
 * @Date 2024/9/2 10:34
 */
@Getter
@AllArgsConstructor
public enum ChangZhouUploadDataTypeEnums {
	DeviceManageRecords,
	DeviceManageReturns,
	InspectionRecords,
	PurchaseReturnsRecorded,
	StockRecords,
	SubstandardDrugsRecords;

	public static ChangZhouUploadDataTypeEnums getByName(String name) {
		return ChangZhouUploadDataTypeEnums.valueOf(name);
	}
}
