package com.labway.business.center.dataupload.response.chagnzhou;

import lombok.Data;

import java.lang.String;

/**
 *
 * <AUTHOR>
 * @Description 医疗器械经营单位批发销售记录
 * @Date 2024/9/3 13:41
 */
@Data
public class DeviceManageRecordsPageResponse extends CommonResponse {
	/**
	 * 主键Id
	 */
	private String id;

	/**
	 * 业务单元
	 */
	private String orgId;

	/**
	 * 商品名
	 */
	private String commodityName;

	/**
	 * 别名
	 */
	private String aliases;

	/**
	 * 规格
	 */
	private String specification;

	/**
	 * 计量单位
	 */
	private String measure;

	/**
	 * 生产企业
	 */
	private String productSurvey;

	/**
	 * 产品注册号
	 */
	private String productRegistNum;

	/**
	 * 储存条件
	 */
	private String storageConditions;

	/**
	 * 有效期
	 */
	private String validityDate;

	/**
	 * 生产日期
	 */
	private String productDate;

	/**
	 * 生产批号/编号
	 */
	private String productBatch;

	/**
	 * 数量
	 */
	private String number;

	/**
	 * 单价
	 */
	private String unitPrice;

	/**
	 * 金额
	 */
	private String amount;

	/**
	 * 销售日期
	 */
	private String saleDate;

	/**
	 * 销往单位
	 */
	private String saleUnit;

	/**
	 * 经手人
	 */
	private String handler;

	/**
	 * 说明
	 */
	private String description;
}
