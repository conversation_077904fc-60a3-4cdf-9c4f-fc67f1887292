package com.labway.business.center.dataupload.request.changzhou;

import com.labway.business.center.dataupload.constants.ChangZhouUploadDataTypeEnums;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 *
 * <AUTHOR>
 * @Description 导入excel
 * @Date 2024/9/2 11:10
 */
@Data
public class ExcelImportRequest {

	@NotBlank(message = "文件路径不能为空")
	private String fileUrl;

	@NotBlank(message = "机构id不能为空")
	private String orgId;

	@NotNull(message = "导入类型不能为空")
	private ChangZhouUploadDataTypeEnums type;
}
