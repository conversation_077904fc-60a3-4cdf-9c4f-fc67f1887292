package com.labway.business.center.dataupload.response.chagnzhou;

import lombok.Data;

import java.lang.String;

/**
 * 医疗器械经营企业采购退货记录
 */
@Data
public class PurchaseReturnsRecordedPageResponse extends CommonResponse {

    /**
     * 主键Id
     */
    private String id;

    /**
     * 业务单元
     */
    private String orgId;

    /**
     * 商品名
     */
    private String commodityName;

    /**
     * 别名
     */
    private String aliases;

    /**
     * 规格
     */
    private String standards;

    /**
     * 计量单位
     */
    private String measure;

    /**
     * 生产批号/编号
     */
    private String productBatch;

    /**
     * 数量
     */
    private String number;

    /**
     * 供货单位
     */
    private String supplyUnit;

    /**
     * 购进日期
     */
    private String purchaseDate;

    /**
     * 退货日期
     */
    private String returnDate;

    /**
     * 审核人
     */
    private String auditPeople;

    /**
     * 经手人
     */
    private String handlePeople;

    /**
     * 退货原因
     */
    private String returnReason;

    /**
     * 处理结果
     */
    private String handleResult;

    /**
     * 说明
     */
    private String introduction;
}
