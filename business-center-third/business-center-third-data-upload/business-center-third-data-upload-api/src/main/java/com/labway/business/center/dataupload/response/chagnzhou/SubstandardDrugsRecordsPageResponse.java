package com.labway.business.center.dataupload.response.chagnzhou;

import lombok.Data;

import java.lang.String;

/**
 * 医疗器械经营企业不合格药品记录
 */
@Data
public class SubstandardDrugsRecordsPageResponse extends CommonResponse {

    /**
     * 主键Id
     */
    private String id;

    /**
     * 业务单元
     */
    private String orgId;

    /**
     * 商品名
     */
    private String commodityName;

    /**
     * 生产批号/编号
     */
    private String productBatch;

    /**
     * 数量
     */
    private String number;

    /**
     * 生产企业
     */
    private String productionEnterprises;

    /**
     * 供货单位
     */
    private String supplyUnit;

    /**
     * 不合格原因
     */
    private String failureReason;

    /**
     * 处理意见
     */
    private String handleAdvice;

    /**
     * 处理情况
     */
    private String handleContent;

    /**
     * 处理说明
     */
    private String handleIntroduction;

    /**
     * 审核人
     */
    private String handler;

    /**
     * 销毁人
     */
    private String destroyHuman;

    /**
     * 销毁日期
     */
    private String destroyDate;

    /**
     * 销毁方式
     */
    private String destroyWay;

    /**
     * 销毁说明
     */
    private String destroyIntroduction;

    /**
     * 时间
     */
    private String time;
}
