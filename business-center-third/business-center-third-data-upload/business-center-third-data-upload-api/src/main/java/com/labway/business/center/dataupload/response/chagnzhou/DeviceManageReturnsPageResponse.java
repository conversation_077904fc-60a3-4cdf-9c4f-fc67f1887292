package com.labway.business.center.dataupload.response.chagnzhou;


import lombok.Data;

import java.math.BigDecimal;
import java.lang.String;

/**
 * 医疗器械经营单位销售退货上传记录
 */
@Data
public class DeviceManageReturnsPageResponse extends CommonResponse {

    /**
     * 主键Id
     */
    private String id;

    /**
     * 业务单元
     */
    private String orgId;

    /**
     * 商品名
     */
    private String commodityName;

    /**
     * 别名
     */
    private String aliases;

    /**
     * 规格
     */
    private String specification;

    /**
     * 计量单位
     */
    private String measure;

    /**
     * 生产企业
     */
    private String productSurvey;

    /**
     * 储存条件
     */
    private String storageConditions;

    /**
     * 有效期
     */
    private String validityDate;

    /**
     * 生产日期
     */
    private String productDate;

    /**
     * 生产批号/编号
     */
    private String productBatch;

    /**
     * 数量
     */
    private String num;

    /**
     * 退货日期
     */
    private String returnDate;

    /**
     * 退货单位
     */
    private String returnUnit;

    /**
     * 退回原因
     */
    private String returnReason;

    /**
     * 处理结果
     */
    private String processResult;

    /**
     * 重新验收结论
     */
    private String reinspectionConclusion;

    /**
     * 验收日期
     */
    private String inspectionDate;

    /**
     * 重新入库数量
     */
    private String incomedNumber;

    /**
     * 验收人
     */
    private String checkOperater;

    /**
     * 经手人
     */
    private String handler;

    /**
     * 说明
     */
    private String description;
}
