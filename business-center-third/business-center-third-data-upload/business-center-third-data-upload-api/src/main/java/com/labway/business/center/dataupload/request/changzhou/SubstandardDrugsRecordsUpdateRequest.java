package com.labway.business.center.dataupload.request.changzhou;

import com.fasterxml.jackson.annotation.JsonTypeName;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

/**
 * 医疗器械经营企业不合格药品记录
 */
@Data
@JsonTypeName("SubstandardDrugsRecords")
public class SubstandardDrugsRecordsUpdateRequest extends CommonUpdateRequest {

	/**
	 * 主键Id
	 */
	@NotBlank(message = "主键Id不能为空")
	@Length(max = 255, message = "主键Id长度不能超过255个字符")
	private String id;

	/**
	 * 商品名
	 */
	@Length(max = 255, message = "商品名长度不能超过255个字符")
	private String commodityName;

	/**
	 * 生产批号/编号
	 */
	@Length(max = 255, message = "生产批号/编号长度不能超过255个字符")
	private String productBatch;

	/**
	 * 数量
	 */
	@Length(max = 255, message = "数量长度不能超过255个字符")
	private String number;

	/**
	 * 生产企业
	 */
	@Length(max = 255, message = "生产企业长度不能超过255个字符")
	private String productionEnterprises;

	/**
	 * 供货单位
	 */
	@Length(max = 255, message = "供货单位长度不能超过255个字符")
	private String supplyUnit;

	/**
	 * 不合格原因
	 */
	@Length(max = 255, message = "不合格原因长度不能超过255个字符")
	private String failureReason;

	/**
	 * 处理意见
	 */
	@Length(max = 255, message = "处理意见长度不能超过255个字符")
	private String handleAdvice;

	/**
	 * 处理情况
	 */
	@Length(max = 255, message = "处理情况长度不能超过255个字符")
	private String handleContent;

	/**
	 * 处理说明
	 */
	@Length(max = 255, message = "处理说明长度不能超过255个字符")
	private String handleIntroduction;

	/**
	 * 审核人
	 */
	@Length(max = 255, message = "审核人长度不能超过255个字符")
	private String handler;

	/**
	 * 销毁人
	 */
	@Length(max = 255, message = "销毁人长度不能超过255个字符")
	private String destroyHuman;

	/**
	 * 销毁日期
	 */
	@Length(max = 255, message = "销毁日期长度不能超过255个字符")
	private String destroyDate;

	/**
	 * 销毁方式
	 */
	@Length(max = 255, message = "销毁方式长度不能超过255个字符")
	private String destroyWay;

	/**
	 * 销毁说明
	 */
	@Length(max = 255, message = "销毁说明长度不能超过255个字符")
	private String destroyIntroduction;

	/**
	 * 时间
	 */
	@Length(max = 255, message = "时间长度不能超过255个字符")
	private String time;
}
