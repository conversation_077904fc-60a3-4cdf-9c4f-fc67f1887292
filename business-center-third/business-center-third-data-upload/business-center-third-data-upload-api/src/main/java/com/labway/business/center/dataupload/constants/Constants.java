package com.labway.business.center.dataupload.constants;

import lombok.experimental.UtilityClass;

/**
 *
 * <AUTHOR>
 * @Description 常量
 * @Date 2024/9/2 10:24
 */
@UtilityClass
public class Constants {

	// ------------对接常州市监局数据导入bean名称-------------
	public static final String PURCHASE_RETURNS_RECORDED_REPOSITORY_BEAN_NAME = "purchaseReturnsRecordedRepository";

	public static final String INSPECTION_RECORDS_REPOSITORY_BEAN_NAME = "inspectionRecordsRepository";

	public static final String STOCK_RECORDS_REPOSITORY_BEAN_NAME = "stockRecordsRepository";

	public static final String SUBSTANDARD_DRUGS_RECORDS_REPOSITORY_BEAN_NAME = "substandardDrugsRecordsRepository";

	public static final String DEVICE_MANAGE_RECORDS_REPOSITORY_BEAN_NAME = "deviceManageRecordsRepository";

	public static final String DEVICE_MANAGE_RETURNS_REPOSITORY_BEAN_NAME = "deviceManageReturnsRepository";

	// ------------对接常州市监局数据导入MapKey-------------
	public static final String PURCHASE_RETURNS_RECORDED = "PurchaseReturnsRecorded";

	public static final String INSPECTION_RECORDS = "InspectionRecords";

	public static final String STOCK_RECORDS = "StockRecords";

	public static final String SUBSTANDARD_DRUGS_RECORDS = "SubstandardDrugsRecords";

	public static final String DEVICE_MANAGE_RECORDS = "DeviceManageRecords";

	public static final String DEVICE_MANAGE_RETURNS = "DeviceManageReturns";

	public static final String REPORT_LOCK_KEY = "labway:data:upload:lock:%s";

	/**
	 * 获取锁的key
	 * @param key eg: #STOCK_RECORDS
	 * @return
	 */
	public static String getReportLockKey(String key){
		return String.format(REPORT_LOCK_KEY, key);
	}
}
