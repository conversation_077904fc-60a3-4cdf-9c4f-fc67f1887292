package com.labway.business.center.dataupload.changzhou.params.webservice;

import lombok.Data;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 *
 * @<PERSON> <PERSON>
 * @Description 退货记录
 * @Date 2024/9/9 11:49
 */
@Data
@XmlRootElement(name = "devicemanagereturn")
public class DeviceManageReturnsBody {

	private BaseData base;

	private DeviceManageReturnsEntity deviceManageReturnsEntity;

	@XmlElement(name = "basedata")
	public BaseData getBase() {
		return base;
	}

	public void setBase(BaseData base) {
		this.base = base;
	}

	@XmlElement(name = "entity")
	public DeviceManageReturnsEntity getDeviceManageReturnsEntity() {
		return deviceManageReturnsEntity;
	}

	public void setDeviceManageReturnsEntity(DeviceManageReturnsEntity deviceManageReturnsEntity) {
		this.deviceManageReturnsEntity = deviceManageReturnsEntity;
	}
}
