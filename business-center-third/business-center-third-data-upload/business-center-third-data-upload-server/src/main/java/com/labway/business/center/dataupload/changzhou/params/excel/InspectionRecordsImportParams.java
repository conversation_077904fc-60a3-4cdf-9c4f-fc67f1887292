package com.labway.business.center.dataupload.changzhou.params.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.labway.business.center.core.util.CheckParamUtils;
import lombok.Data;

import java.util.List;

import static com.labway.business.center.core.util.CheckParamUtils.isLengthGt;

/**
 * 医疗器械经营企业验收记录
 */
@Data
public class InspectionRecordsImportParams {

	/**
	 * 商品类别
	 */
	@ExcelProperty( value = "商品类别")
	private String type;

	/**
	 * 商品名
	 */
	@ExcelProperty( value = "商品名")
	private String commodityName;

	/**
	 * 别名
	 */
	@ExcelProperty( value = "别名")
	private String aliases;

	/**
	 * 采购数量
	 */
	@ExcelProperty( value = "采购数量")
	private String purchaseNum;

	/**
	 * 实收数量
	 */
	@ExcelProperty( value = "实收数量")
	private String paidNum;

	/**
	 * 生产批号/编号
	 */
	@ExcelProperty( value = "生产批号/编号")
	private String productBatch;

	/**
	 * 规格
	 */
	@ExcelProperty( value = "规格")
	private String standards;

	/**
	 * 计量单位
	 */
	@ExcelProperty( value = "计量单位")
	private String measure;

	/**
	 * 供货单位
	 */
	@ExcelProperty( value = "供货单位")
	private String supplyUnit;

	/**
	 * 生产企业
	 */
	@ExcelProperty( value = "生产企业")
	private String productionEnterprises;

	/**
	 * 生产日期
	 */
	@ExcelProperty( value = "生产日期")
	private String productDate;

	/**
	 * 剂型
	 */
	@ExcelProperty( value = "剂型")
	private String dosageForms;

	/**
	 * 有效期
	 */
	@ExcelProperty( value = "有效期")
	private String validityDate;

	/**
	 * 仓储条件
	 */
	@ExcelProperty( value = "仓储条件")
	private String storageConditions;

	/**
	 * 产品注册号
	 */
	@ExcelProperty( value = "产品注册号")
	private String registrationNum;

	/**
	 * 合格证
	 */
	@ExcelProperty( value = "合格证")
	private String certification;

	/**
	 * 装量
	 */
	@ExcelProperty( value = "装量")
	private String packingRate;

	/**
	 * 注册商标
	 */
	@ExcelProperty( value = "注册商标")
	private String registerTrademark;

	/**
	 * 进货时间
	 */
	@ExcelProperty( value = "进货时间")
	private String stockGoodsDate;

	/**
	 * 抽检数量
	 */
	@ExcelProperty( value = "抽检数量")
	private String inspectNum;

	/**
	 * 质量情况
	 */
	@ExcelProperty( value = "质量情况")
	private String qualitySituation;

	/**
	 * 验收结论
	 */
	@ExcelProperty( value = "验收结论")
	private String result;

	/**
	 * 备注
	 */
	@ExcelProperty( value = "备注")
	private String content;

	/**
	 * 验收时间
	 */
	@ExcelProperty( value = "验收时间")
	private String checkDate;

	/**
	 * 验收人
	 */
	@ExcelProperty( value = "验收人")
	private String checkName;

	/**
	 * 国家
	 */
	@ExcelProperty( value = "国家")
	private String country;

	/**
	 * 主要成份
	 */
	@ExcelProperty( value = "主要成份")
	private String mainComponents;

	/**
	 * 口岸检验所
	 */
	@ExcelProperty( value = "口岸检验所")
	private String portCheckInstitute;

	/**
	 * 报告单编号
	 */
	@ExcelProperty( value = "报告单编号")
	private String reportNum;

	/**
	 * 注册证编号
	 */
	@ExcelProperty( value = "注册证编号")
	private String certificateNum;

	public static CheckParamUtils<InspectionRecordsImportParams> check(List<?> importList) {
		List<InspectionRecordsImportParams> validLst = (List<InspectionRecordsImportParams>) importList;
		return CheckParamUtils.build(validLst, 2)
				.check(data -> isLengthGt(data.getType(), 255), "[商品类别]长度不能超过255")
				.check(data -> isLengthGt(data.getCommodityName(), 255), "[商品名]长度不能超过255")
				.check(data -> isLengthGt(data.getAliases(), 255), "[别名]长度不能超过255")
				.check(data -> isLengthGt(data.getStandards(),255), "[规格]长度不能超过255")
				.check(data -> isLengthGt(data.getMeasure(), 255), "[计量单位]长度不能超过255")
				.check(data -> isLengthGt(data.getSupplyUnit(), 255), "[供货单位]长度不能超过255")
				.check(data -> isLengthGt(data.getProductionEnterprises(), 255), "[生产企业]长度不能超过255")
				.check(data -> isLengthGt(data.getProductBatch(), 255), "[生产批号/编号]长度不能超过255")
				.check(data -> isLengthGt(data.getDosageForms(), 255), "[剂型]长度不能超过255")
				.check(data -> isLengthGt(data.getStorageConditions(), 255), "[仓储条件]长度不能超过255")
				.check(data -> isLengthGt(data.getRegistrationNum(), 255), "[产品注册号]长度不能超过255")
				.check(data -> isLengthGt(data.getCertification(), 255), "[合格证]长度不能超过255")
				.check(data -> isLengthGt(data.getPackingRate(), 255), "[装量]长度不能超过255")
				.check(data -> isLengthGt(data.getRegisterTrademark(), 255), "[注册商标]长度不能超过255")
				.check(data -> isLengthGt(data.getStockGoodsDate(), 255), "[进货时间]长度不能超过255")
				.check(data -> isLengthGt(data.getQualitySituation(), 255), "[质量情况]长度不能超过255")
				.check(data -> isLengthGt(data.getResult(), 255), "[验收结论]长度不能超过255")
				.check(data -> isLengthGt(data.getContent(), 255), "[备注]长度不能超过255")
				.check(data -> isLengthGt(data.getCheckDate(), 255), "[验收时间]长度不能超过255")
				.check(data -> isLengthGt(data.getCheckName(), 255), "[验收人]长度不能超过255")
				.check(data -> isLengthGt(data.getCountry(), 255), "[国家]长度不能超过255")
				.check(data -> isLengthGt(data.getMainComponents(), 255), "[主要成份]长度不能超过255")
				.check(data -> isLengthGt(data.getPortCheckInstitute(), 255), "[口岸检验所]长度不能超过255")
				.check(data -> isLengthGt(data.getReportNum(), 255), "[报告单编号]长度不能超过255")
				.check(data -> isLengthGt(data.getCertificateNum(), 255), "[注册证编号]长度不能超过255");
	}
}
