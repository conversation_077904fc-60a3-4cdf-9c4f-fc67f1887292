package com.labway.business.center.dataupload.changzhou.params.webservice;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;

/**
 *
 * <AUTHOR>
 * @Description 基类
 * @Date 2024/9/5 14:17
 */
@Data
@XmlAccessorType(XmlAccessType.FIELD)
public class BaseData {
	/**
	 * 企业唯一验证码
	 */
	@XmlElement(name = "checkcode")
	private String checkCode;

	/**
	 * 医疗器械经营单位
	 */
	@XmlElement(name = "devicemanageunit")
	private String deviceManageUnit;
}
