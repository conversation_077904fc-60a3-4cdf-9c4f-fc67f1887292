package com.labway.business.center.dataupload.changzhou.params.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.labway.business.center.core.util.CheckParamUtils;
import lombok.Data;

import java.util.List;

import static com.labway.business.center.core.util.CheckParamUtils.isLengthGt;


/**
 * 医疗器械经营单位批发销售记录
 */
@Data
public class DeviceManageRecordsImportParams {

	/**
	 * 商品名
	 */
	@ExcelProperty( value = "商品名")
	private String commodityName;

	/**
	 * 别名
	 */
	@ExcelProperty( value = "别名")
	private String aliases;

	/**
	 * 规格
	 */
	@ExcelProperty( value = "规格")
	private String specification;

	/**
	 * 计量单位
	 */
	@ExcelProperty( value = "计量单位")
	private String measure;

	/**
	 * 生产企业
	 */
	@ExcelProperty( value = "生产企业")
	private String productSurvey;

	/**
	 * 产品注册号
	 */
	@ExcelProperty( value = "产品注册号")
	private String productRegistNum;

	/**
	 * 储存条件
	 */
	@ExcelProperty( value = "储存条件")
	private String storageConditions;

	/**
	 * 有效期
	 */
	@ExcelProperty( value = "有效期")
	private String validityDate;

	/**
	 * 生产日期
	 */
	@ExcelProperty( value = "生产日期")
	private String productDate;

	/**
	 * 生产批号/编号
	 */
	@ExcelProperty( value = "生产批号/编号")
	private String productBatch;

	/**
	 * 数量
	 */
	@ExcelProperty( value = "数量")
	private String number;

	/**
	 * 单价
	 */
	@ExcelProperty( value = "单价")
	private String unitPrice;

	/**
	 * 金额
	 */
	@ExcelProperty( value = "金额")
	private String amount;

	/**
	 * 销售日期
	 */
	@ExcelProperty( value = "销售日期")
	private String saleDate;

	/**
	 * 销往单位
	 */
	@ExcelProperty( value = "销往单位")
	private String saleUnit;

	/**
	 * 经手人
	 */
	@ExcelProperty( value = "经手人")
	private String handler;

	/**
	 * 说明
	 */
	@ExcelProperty( value = "说明")
	private String description;

	public static CheckParamUtils<DeviceManageRecordsImportParams> check(List<?> importList){
		List<DeviceManageRecordsImportParams> validLst = (List<DeviceManageRecordsImportParams>) importList;
		return CheckParamUtils.build(validLst, 2)
				.check(data -> isLengthGt(data.getCommodityName(), 255), "[商品名]长度不能超过255")
				.check(data -> isLengthGt(data.getAliases(), 255), "[别名]长度不能超过255")
				.check(data -> isLengthGt(data.getSpecification(), 255), "[规格]长度不能超过255")
				.check(data -> isLengthGt(data.getMeasure(), 255), "[计量单位]长度不能超过255")
				.check(data -> isLengthGt(data.getProductSurvey(), 255), "[生产企业]长度不能超过255")
				.check(data -> isLengthGt(data.getProductRegistNum(), 255), "[产品注册号]长度不能超过255")
				.check(data -> isLengthGt(data.getStorageConditions(), 255), "[储存条件]长度不能超过255")
				.check(data -> isLengthGt(data.getValidityDate(), 255), "[有效期]长度不能超过255")
				.check(data -> isLengthGt(data.getProductDate(), 255), "[生产日期]长度不能超过255")
				.check(data -> isLengthGt(data.getProductBatch(), 255), "[生产批号/编号]长度不能超过255")
				.check(data -> isLengthGt(data.getNumber(), 255), "[数量]长度不能超过255")
				.check(data -> isLengthGt(data.getUnitPrice(), 255), "[单价]长度不能超过255")
				.check(data -> isLengthGt(data.getAmount(), 255), "[金额]长度不能超过255")
				.check(data -> isLengthGt(data.getSaleDate(), 255), "[销售日期]长度不能超过255")
				.check(data -> isLengthGt(data.getSaleUnit(), 255), "[销往单位]长度不能超过255")
				.check(data -> isLengthGt(data.getHandler(), 255), "[经手人]长度不能超过255")
				.check(data -> isLengthGt(data.getDescription(), 255), "[说明]长度不能超过255");
	}
}
