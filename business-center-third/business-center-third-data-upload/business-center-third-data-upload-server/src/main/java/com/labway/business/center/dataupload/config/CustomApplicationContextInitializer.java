package com.labway.business.center.dataupload.config;


import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContextInitializer;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class CustomApplicationContextInitializer implements ApplicationContextInitializer<ConfigurableApplicationContext> {
	private static ConfigurableApplicationContext context = null;

	@Override
	public void initialize(ConfigurableApplicationContext applicationContext) {
		context = applicationContext;
	}

	public static ConfigurableApplicationContext getContext() {
		return context;
	}


}