package com.labway.business.center.dataupload.changzhou.params.webservice;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * 验收记录实际参数
 * <AUTHOR>
 * @Description
 * @Date 2024/9/5 15:02
 */
@Data
@XmlRootElement(name = "inspectionrecords")
@XmlAccessorType(XmlAccessType.FIELD)
public class InspectionRecordsBody {

	@XmlElement(name = "basedata")
	private BaseData base;

	@XmlElement(name = "entity")
	private InspectionRecordsEntity inspectionRecords;

}

