package com.labway.business.center.dataupload.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 *
 * <AUTHOR>
 * @Description 常州市监局soap接口配置
 * @Date 2024/9/9 9:54
 */
@Getter
@Setter
@Component
@RefreshScope
@ConfigurationProperties(prefix= "soap.chang-zhou")
public class ChangZhouSoapConfig {

	private String url;

	private String namespaceURI;

	private String checkCode;

	private String deviceManageUnit;

	private Map<String, String> methodNameMap;

	/**
	 *
	 * @param key {@link Constants.对接常州市监局数据导入MapKey}
	 * @return
	 */
	public String getMethodName(String key) {
		return methodNameMap.get(key);
	}

}
