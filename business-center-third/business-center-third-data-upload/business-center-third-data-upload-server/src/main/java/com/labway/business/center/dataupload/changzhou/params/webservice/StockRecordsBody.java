package com.labway.business.center.dataupload.changzhou.params.webservice;

import lombok.Data;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 *
 * @<PERSON> <PERSON>
 * @Description 库存
 * @Date 2024/9/9 11:52
 */
@XmlRootElement(name = "stockrecords")
public class StockRecordsBody {

	private BaseData base;

	private StockRecordsEntity stockRecordsEntity;

	@XmlElement(name = "basedata")
	public BaseData getBase() {
		return base;
	}

	public void setBase(BaseData base) {
		this.base = base;
	}

	@XmlElement(name = "entity")
	public StockRecordsEntity getStockRecordsEntity() {
		return stockRecordsEntity;
	}

	public void setStockRecordsEntity(StockRecordsEntity stockRecordsEntity) {
		this.stockRecordsEntity = stockRecordsEntity;
	}
}
