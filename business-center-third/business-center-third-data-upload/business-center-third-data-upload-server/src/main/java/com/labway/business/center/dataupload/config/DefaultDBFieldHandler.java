package com.labway.business.center.dataupload.config;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.labway.business.center.core.enums.DeleteFlagEnum;
import com.labway.business.center.core.user.LoginUserInfoUtil;
import com.labway.business.center.dataupload.changzhou.persistence.entity.BaseEntity;
import com.labway.business.center.dataupload.constants.UploadStatusEnums;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.reflection.MetaObject;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 通用参数填充实现类
 *
 * 如果没有显式的对通用参数进行赋值，这里会对通用参数进行填充、赋值
 *
 * <AUTHOR>
 */
public class DefaultDBFieldHandler implements MetaObjectHandler {

    @Override
    public void insertFill(MetaObject metaObject) {
        if (Objects.nonNull(metaObject) && metaObject.getOriginalObject() instanceof BaseEntity) {
            BaseEntity baseEntity = (BaseEntity) metaObject.getOriginalObject();

	        baseEntity.setDeleted(DeleteFlagEnum.NO_DELETE.getCode());

            LocalDateTime current = LocalDateTime.now();
	        if (Objects.isNull(baseEntity.getUploadStatus())) {
				baseEntity.setUploadStatus(UploadStatusEnums.UN_UPLOAD.getStatus());
	        }
            // 创建时间为空，则以当前时间为插入时间
            if (Objects.isNull(baseEntity.getCreateTime())) {
                baseEntity.setCreateTime(current);
            }
            // 更新时间为空，则以当前时间为更新时间
            if (Objects.isNull(baseEntity.getUpdateTime())) {
                baseEntity.setUpdateTime(current);
            }

            String userName = LoginUserInfoUtil.getUserUserName();
            // 当前登录用户不为空，创建人为空，则当前登录用户为创建人
            if (StringUtils.isNotBlank(userName) && Objects.isNull(baseEntity.getCreateUser())) {
                baseEntity.setCreateUser(userName);
            }
            // 当前登录用户不为空，更新人为空，则当前登录用户为更新人
            if (StringUtils.isNotBlank(userName) && Objects.isNull(baseEntity.getUpdateUser())) {
                baseEntity.setUpdateUser(userName);
            }
        }
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        // 更新时间为空，则以当前时间为更新时间
        Object modifyTime = getFieldValByName("updateTime", metaObject);
        if (Objects.isNull(modifyTime)) {
            setFieldValByName("updateTime", LocalDateTime.now(), metaObject);
        }

        // 当前登录用户不为空，更新人为空，则当前登录用户为更新人
        Object modifier = getFieldValByName("updateUser", metaObject);
        String userName = LoginUserInfoUtil.getUserUserName();
        if (Objects.nonNull(userName) && Objects.isNull(modifier)) {
            setFieldValByName("updateUser", userName, metaObject);
        }
    }
}
