package com.labway.business.center.dataupload.changzhou.persistence.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;


/**
 * 库存数据
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("stock_records")
public class StockRecords extends BaseEntity {

    /**
     * 主键Id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 业务单元
     */
    @TableField("org_id")
    private String orgId;

    /**
     * 商品名
     */
    @TableField("commodity_name")
    private String commodityName;

    /**
     * 别名
     */
    @TableField("aliases")
    private String aliases;

    /**
     * 规格
     */
    @TableField("specification")
    private String specification;

    /**
     * 数量
     */
    @TableField("num")
    private String num;

    /**
     * 计量单位
     */
    @TableField("measure")
    private String measure;

    /**
     * 生产企业
     */
    @TableField("product_survey")
    private String productSurvey;

    /**
     * 批准文号
     */
    @TableField("registration_num")
    private String registrationNum;

    /**
     * 有效期
     */
    @TableField("validity_date")
    private String validityDate;

    /**
     * 生产日期
     */
    @TableField("product_date")
    private String productDate;

    /**
     * 生产批号
     */
    @TableField("product_batch")
    private String productBatch;

    /**
     * 供货单位
     */
    @TableField("supply_unit")
    private String supplyUnit;
}
