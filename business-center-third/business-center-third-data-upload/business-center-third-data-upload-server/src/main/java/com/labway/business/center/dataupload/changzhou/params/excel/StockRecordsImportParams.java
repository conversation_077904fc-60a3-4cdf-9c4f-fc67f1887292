package com.labway.business.center.dataupload.changzhou.params.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.labway.business.center.core.util.CheckParamUtils;
import lombok.Data;

import java.util.List;

import static com.labway.business.center.core.util.CheckParamUtils.isLengthGt;

/**
 * 库存数据
 */
@Data
public class StockRecordsImportParams {

	/**
	 * 商品名
	 */
	@ExcelProperty( value = "商品名")
	private String commodityName;

	/**
	 * 别名
	 */
	@ExcelProperty( value = "别名")
	private String aliases;

	/**
	 * 规格
	 */
	@ExcelProperty( value = "规格")
	private String specification;

	/**
	 * 数量
	 */
	@ExcelProperty( value = "数量")
	private String num;

	/**
	 * 计量单位
	 */
	@ExcelProperty( value = "计量单位")
	private String measure;

	/**
	 * 生产企业
	 */
	@ExcelProperty( value = "生产企业")
	private String productSurvey;

	/**
	 * 批准文号
	 */
	@ExcelProperty( value = "批准文号")
	private String registrationNum;

	/**
	 * 有效期
	 */
	@ExcelProperty( value = "有效期")
	private String validityDate;

	/**
	 * 生产日期
	 */
	@ExcelProperty( value = "生产日期")
	private String productDate;

	/**
	 * 生产批号
	 */
	@ExcelProperty( value = "生产批号")
	private String productBatch;

	/**
	 * 供货单位
	 */
	@ExcelProperty( value = "供货单位")
	private String supplyUnit;

	public static CheckParamUtils<StockRecordsImportParams> check(List<?> importList){
		List<StockRecordsImportParams> validLst = (List<StockRecordsImportParams>) importList;
		return CheckParamUtils.build(validLst, 2)
				.check(data -> isLengthGt(data.getCommodityName(), 255), "[商品名]长度不能超过255")
				.check(data -> isLengthGt(data.getAliases(), 255), "[别名]长度不能超过255")
				.check(data -> isLengthGt(data.getSpecification(), 255), "[规格]长度不能超过255")
				.check(data -> isLengthGt(data.getNum(), 255), "[数量]长度不能超过255")
				.check(data -> isLengthGt(data.getMeasure(), 255), "[计量单位]长度不能超过255")
				.check(data -> isLengthGt(data.getProductSurvey(), 255), "[生产企业]长度不能超过255")
				.check(data -> isLengthGt(data.getRegistrationNum(), 255), "[批准文号]长度不能超过255")
				.check(data -> isLengthGt(data.getValidityDate(), 255), "[有效期]长度不能超过255")
				.check(data -> isLengthGt(data.getProductDate(), 255), "[生产日期]长度不能超过255")
				.check(data -> isLengthGt(data.getProductBatch(), 255), "[生产批号]长度不能超过255")
				.check(data -> isLengthGt(data.getSupplyUnit(), 255), "[供货单位]长度不能超过255");
	}
}
