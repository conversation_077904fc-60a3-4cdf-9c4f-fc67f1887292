package com.labway.business.center.dataupload.changzhou.convert;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.labway.business.center.dataupload.changzhou.params.excel.SubstandardDrugsRecordsImportParams;
import com.labway.business.center.dataupload.changzhou.params.webservice.SubstandardDrugsRecordsEntity;
import com.labway.business.center.dataupload.changzhou.persistence.entity.SubstandardDrugsRecords;
import com.labway.business.center.dataupload.request.changzhou.SubstandardDrugsRecordsUpdateRequest;
import com.labway.business.center.dataupload.response.Pager;
import com.labway.business.center.dataupload.response.chagnzhou.SubstandardDrugsRecordsPageResponse;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @Description
 * @Date 2024/9/2 17:11
 */
@Mapper(componentModel = "spring")
public interface SubstandardDrugsRecordsConvert extends BaseConvert {

	@Mapping(target = "bigDecimalField", source = "stringField", qualifiedByName = "stringToBigDecimal")
	List<SubstandardDrugsRecords> convertExcelInsertDataEntityList(List<SubstandardDrugsRecordsImportParams> insertData);

	Pager<SubstandardDrugsRecordsPageResponse> convertEntityPage2ResponsePage(Page<SubstandardDrugsRecords> substandardDrugsRecordsPage);

	@Mapping(target = "uploadStatus", source = "uploadStatus")
	@Mapping(target = "uploadTime", source = "uploadTime")
	@Mapping(target = "uploadUser", source = "uploadUser")
	@Mapping(target = "createUser", source = "createUser")
	@Mapping(target = "createTime", source = "createTime")
	@Mapping(target = "updateUser", source = "updateUser")
	@Mapping(target = "updateTime", source = "updateTime")
	SubstandardDrugsRecordsPageResponse toResponse(SubstandardDrugsRecords substandardDrugsRecords);

	SubstandardDrugsRecords convertUpdateRequest2Entity(SubstandardDrugsRecordsUpdateRequest substandardDrugsRecordsUpdateRequest);

	List<SubstandardDrugsRecordsEntity> convertEntityList2XmlList(List<SubstandardDrugsRecords> substandardDrugsRecordList);
}
