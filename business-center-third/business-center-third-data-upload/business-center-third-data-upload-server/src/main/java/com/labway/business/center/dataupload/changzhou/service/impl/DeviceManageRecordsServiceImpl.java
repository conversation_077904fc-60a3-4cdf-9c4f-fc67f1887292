package com.labway.business.center.dataupload.changzhou.service.impl;

import com.google.common.collect.Lists;
import com.labway.business.center.core.enums.ResultCode;
import com.labway.business.center.dataupload.changzhou.convert.DeviceManageRecordsConvert;
import com.labway.business.center.dataupload.changzhou.params.webservice.*;
import com.labway.business.center.dataupload.changzhou.persistence.entity.DeviceManageRecords;
import com.labway.business.center.dataupload.changzhou.repository.DeviceManageRecordsRepository;
import com.labway.business.center.dataupload.config.ChangZhouSoapConfig;
import com.labway.business.center.dataupload.constants.Constants;
import com.labway.business.center.dataupload.log.Log;
import com.labway.business.center.dataupload.service.changzhou.ChangZhouPushService;
import com.labway.business.center.dataupload.service.changzhou.DeviceManageRecordsService;
import com.labway.business.center.dataupload.utils.LabXmlUtil;
import com.swak.frame.dto.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

/**
 *
 * <AUTHOR> Tianhao
 * @Description 医疗器械经营单位批发销售记录
 * @Date 2024/8/30 13:28
 */
@Slf4j
@DubboService
public class DeviceManageRecordsServiceImpl implements DeviceManageRecordsService {

	@Resource
	private DeviceManageRecordsRepository deviceManageRecordsRepository;

	@Resource
	private DeviceManageRecordsConvert deviceManageRecordsConvert;

	@Resource
	private ChangZhouSoapConfig changZhouSoapConfig;

	@Resource
	private ChangZhouPushService changZhouPushService;

	@Resource
	private ThreadPoolTaskExecutor changeZhouExecutor;

	@Resource
	private StringRedisTemplate stringRedisTemplate;

	@Override
	@Log("数据上报-批发销售记录")
	public Response<String> reportData() {

		String reportLockKey = Constants.getReportLockKey(Constants.DEVICE_MANAGE_RECORDS);

		if (Boolean.FALSE.equals(stringRedisTemplate.opsForValue().setIfAbsent(reportLockKey, "1"))) {
			return Response.fail(ResultCode.IS_PUSHING);
		}

		try {

			if (this.countNotReportData() <= 0) {
				return Response.fail(500,"没有需要推送的数据");
			}
			List<DeviceManageRecords> deviceManageRecordsList = deviceManageRecordsRepository.searchNotReportData();
			List<DeviceManageRecordsEntity> xmlEntityList = deviceManageRecordsConvert.convertEntityList2XmlList(deviceManageRecordsList);

			BaseData baseData = new BaseData();
			baseData.setCheckCode(changZhouSoapConfig.getCheckCode());
			baseData.setDeviceManageUnit(changZhouSoapConfig.getDeviceManageUnit());

			StringBuilder successInfo = new StringBuilder();
			StringBuilder errorInfo = new StringBuilder();
			List<Long> successIds = Lists.newArrayList();
			List<Long> errorIds = Lists.newArrayList();

			List<List<DeviceManageRecordsEntity>> partition = Lists.partition(xmlEntityList, 500);

			CountDownLatch countDownLatch = new CountDownLatch(partition.size());

			for (List<DeviceManageRecordsEntity> deviceManageRecordsEntities : partition) {
				changeZhouExecutor.execute(() -> {

					for (DeviceManageRecordsEntity entity : deviceManageRecordsEntities) {
						try {

							DeviceManageRecordsBody body = buildInvokeParams(baseData, entity);
							Response<String> soapResponse = changZhouPushService.pushData(changZhouSoapConfig.getUrl(), changZhouSoapConfig.getNamespaceURI(), Constants.DEVICE_MANAGE_RECORDS, LabXmlUtil.toXml(body));
							if (!soapResponse.isSuccess()) {
								errorInfo.append(String.format("数据id:[%s],推送失败，soap接口返回信息：[%s]\n", entity.getId(), soapResponse.getMsg()));
								log.error("DeviceManageRecords数据id:[{}],推送失败，soap接口返回信息：[{}]", entity.getId(), soapResponse.getMsg());
								errorIds.add(entity.getId());
							} else {
								successInfo.append(String.format("数据id:[%s],推送成功\n", entity.getId()));
								log.info("DeviceManageRecords数据id:[{}],推送成功", entity.getId());
								successIds.add(entity.getId());
							}
						} catch (Exception e) {
							errorInfo.append(String.format("数据id:[%s],推送失败，异常信息：[%s]\n", entity.getId(), e.getMessage()));
							log.error("DeviceManageRecords数据id:[{}],推送失败，异常信息：[{}]", entity.getId(), e.getMessage());
						}
						countDownLatch.countDown();
					}
				});
			}

			countDownLatch.await();

			if (CollectionUtils.isNotEmpty(successIds)) {
				deviceManageRecordsRepository.pushSuccessData(successIds);
			}

			stringRedisTemplate.expire(reportLockKey, 1, TimeUnit.MICROSECONDS);

			if (CollectionUtils.isNotEmpty(errorIds)) {
				return Response.fail(500, String.format("本次推送条数为[%s]条，成功：[%s]条，失败[%s]条", xmlEntityList.size(), successIds.size(), errorIds.size()));
			}

			return Response.success(successInfo.toString());
		} catch (Exception e) {
			log.error("DeviceManageRecords数据推送异常", e);
			return Response.fail(ResultCode.PUSH_ERROR);
		} finally {
			stringRedisTemplate.expire(reportLockKey, 1, TimeUnit.MICROSECONDS);
		}
	}

	@Override
	public Long countNotReportData() {
		return deviceManageRecordsRepository.countNotReportData();
	}

	private DeviceManageRecordsBody buildInvokeParams(BaseData baseData, DeviceManageRecordsEntity entity) {
		DeviceManageRecordsBody body = new DeviceManageRecordsBody();
		body.setBase(baseData);
		body.setDeviceManageRecordsEntity(entity);
		return body;
	}
}
