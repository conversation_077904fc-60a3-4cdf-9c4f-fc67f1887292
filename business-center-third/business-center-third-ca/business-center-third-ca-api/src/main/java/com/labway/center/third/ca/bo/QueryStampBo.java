package com.labway.center.third.ca.bo;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class QueryStampBo implements Serializable {

    private String appId;

    private String bussNo;

    private String signInfo;

    private String timestamp;

    private QueryStampData data;

    @Data
    @NoArgsConstructor
    public static class QueryStampData implements Serializable{

        //证书用户类型 0:医生,1:患者
        private String bindType;

        // 工号/健康卡号
        private String bizNum;

    }


}
