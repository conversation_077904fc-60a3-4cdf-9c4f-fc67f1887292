package com.labway.center.third.ca.request;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class QueryStampRequest implements Serializable {

    // 实验室用户账号
    @NotBlank(message = "用户标识不能为空！！")
    private String limsUser;

    // 实验室编码
    @NotBlank(message = "实验室编码不能为空！！")
    private String limsCode;

    // 业务流水号
    @NotBlank(message = "业务流水号不能为空！！")
    private String bussNo;

}
