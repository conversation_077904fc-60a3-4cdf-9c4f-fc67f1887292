package com.labway.center.third.ca.service.impl;

import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONObject;
import com.labway.center.third.ca.bo.QueryStampBo;
import com.labway.center.third.ca.config.CAConfig;
import com.labway.center.third.ca.request.QueryStampRequest;
import com.labway.center.third.ca.service.CAService;
import com.swak.frame.dto.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;
import java.util.TreeMap;
import java.util.UUID;

/**
 * <AUTHOR>
 */
@Slf4j
@RefreshScope
@DubboService
public class CAServiceImpl implements CAService {

    @Resource
    private CAConfig caConfig;


    /**
     * 查询用户的CA认证签名外观
     * @param request
     * @return
     */
    @Override
    public Response<?> queryStamp(QueryStampRequest request) {

        String timestamp = String.valueOf(System.currentTimeMillis());
        String bussNo = request.getBussNo();
        String bizNum = caConfig.getUserNo().get(request.getLimsUser());

        QueryStampBo bo = new QueryStampBo();
        bo.setAppId(caConfig.getAppId());
        bo.setBussNo(bussNo);
        bo.setTimestamp(timestamp);
        QueryStampBo.QueryStampData data = new QueryStampBo.QueryStampData();
        data.setBindType(String.valueOf(0));
        data.setBizNum(bizNum);
        bo.setData(data);

        TreeMap<String,String> params = new TreeMap<>();
        params.put("appId",caConfig.getAppId());
        params.put("bindType",String.valueOf(0));
        params.put("bussNo", bussNo);
        params.put("bizNum",bizNum);
        params.put("timestamp",timestamp);
        String signInfo = signInfo(params);
        bo.setSignInfo(signInfo);

        HttpResponse execute = HttpRequest.post(caConfig.getUrl()+caConfig.getApi().get("queryStamp"))
                .body(JSONObject.toJSONString(bo))
                .execute();
        JSONObject jsonObject = JSONObject.parseObject(execute.body());
        Integer code = jsonObject.getInteger("code");
        String msg = jsonObject.getString("message");

        if (!Objects.equals(code,0)){
            return Response.fail(code,msg);
        }

        JSONObject dataObject = jsonObject.getJSONObject("data");

        return Response.success(dataObject);
    }


    //==================================================================================================================

    // 加密signInfo
    private String signInfo(TreeMap<String,String> params){
        StringBuilder paramStr = new StringBuilder();
        for (Map.Entry<String, String> stringStringEntry : params.entrySet()) {
            String key = stringStringEntry.getKey();
            String value = stringStringEntry.getValue();
            if (StringUtils.isNotBlank(paramStr)){
                paramStr.append("&");
            }
            paramStr.append(key).append("=").append(value);
        }
        paramStr.append("&").append("APP=").append(caConfig.getAppSecret());

        String base64Str = Base64.encodeBase64String(paramStr.toString().getBytes());
        String sha256HexStr = DigestUtil.sha256Hex(base64Str, "utf-8");
        return sha256HexStr;
    }


}
