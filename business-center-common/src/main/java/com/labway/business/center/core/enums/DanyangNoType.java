package com.labway.business.center.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <p>
 * DanyangNoType
 * 丹阳两癌接口参数类型
 * </p>
 *
 * <AUTHOR>
 * @since 2023/11/8 19:37
 */
@Getter
@AllArgsConstructor
public enum DanyangNoType {

    HPV("hpvNo", "HPV"),

    TCT("tctNo", "TCT"),

    ;

    private final String noType;
    private final String name;

    public static boolean isHPV(String noType) {
        return HPV.getNoType().equals(noType);
    }
    public static boolean isTCT(String noType) {
        return TCT.getNoType().equals(noType);
    }

}
