package com.labway.business.center.core.enums;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @description:
 * @date 2021-07-12 15:35
 */
public enum CardTypeEnum {

    IDCARD("1", "身份证", "1"),
    HUREN_IDCARD("01","荔湾互认平台身份证","01"),
    PASSPORT("3", "护照", "2"),
    HKANDMACAO("6", "港澳居民通行证", "3"),
    TAIWAN("7", "台湾居民通行证", "14"),
    OTHER("99", "其他", "99");

    private String code;

    private String desc;

    private String innerCode;

    CardTypeEnum(String code, String innerCode, String desc) {
        this.code = code;
        this.innerCode = innerCode;
        this.desc = desc;
    }

    public static String getCodeByInnerCode(String innerCode) {
        for (CardTypeEnum cardType : CardTypeEnum.values()) {
            if (cardType.getInnerCode().equals(innerCode)) {
                return cardType.getCode();
            }
        }
        return "99";
    }

    /**
     * 有效状态
     *
     * @param code 状态编码
     * @return 状态枚举值
     */
    public static CardTypeEnum getEnumByStr(String code) {
        return Arrays.stream(CardTypeEnum.values()).filter(item -> item.getCode().equals(code)).findFirst().orElse(IDCARD);
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getInnerCode() {
        return innerCode;
    }

    public void setInnerCode(String innerCode) {
        this.innerCode = innerCode;
    }
}
