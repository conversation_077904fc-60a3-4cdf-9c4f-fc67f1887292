package com.labway.business.center.core.util;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.MutableTriple;
import org.apache.commons.lang3.tuple.Triple;

import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <p>
 * AgeUtil
 * 处理年龄
 * </p>
 *
 * <AUTHOR>
 * @since 2023/11/14 11:30
 */
@Slf4j
public final class AgeUtil {
    static final Pattern AGE_PATTERN = Pattern.compile("(\\d+)(岁+|年+)|(\\d+)(月+)|(\\d+)(天+)|(\\d+)(时+)");
    static final Pattern AGE_NUMBER = Pattern.compile("^\\d+$");

    /**
     * <pre>
     *  left    ==> patientAge
     *  mkddle: ==> subage
     *  right:  ==> subageUnit {@link AgeUnit}
     * </pre>
     * @param age 1岁3月
     * @return
     */
    public static Triple<Integer, Integer, String> parse(String age) {
        int patientAge = 0;
        int subage = 0;
        String subageUnit = AgeUnit.MONTH.getUnit();
        MutableTriple<Integer, Integer, String> triple = MutableTriple.of(patientAge, subage, subageUnit);

        try {
            if (StringUtils.isEmpty(age)) {
                return triple;
            }
            if (AGE_NUMBER.matcher(age).find()) {
                triple.setLeft(Integer.parseInt(age));
                return triple;
            }
            String zero = "0";
            Matcher matcher = AGE_PATTERN.matcher(age);
            while (matcher.find()) {
                int year = Integer.parseInt(Objects.requireNonNullElse(matcher.group(1), zero));
                if (year > 0) {
                    patientAge = year;
                }
                int month = Integer.parseInt(Objects.requireNonNullElse(matcher.group(3), zero));
                if (month > 0) {
                    subage = month;
                    subageUnit = AgeUnit.MONTH.getUnit();
                }
                int day = Integer.parseInt(Objects.requireNonNullElse(matcher.group(5), zero));
                if (day > 0) {
                    subage = day;
                    subageUnit = AgeUnit.DAY.getUnit();
                }
                if (StringUtils.isNotBlank(matcher.group(7))) {
                    int hour = Integer.parseInt(matcher.group(7));
                    subage = (hour > 0) ? 1 : 0;
                    subageUnit = AgeUnit.DAY.getUnit();
                }
            }
        } catch (Exception e) {
            log.error("解析年龄异常【PatientAge：{}】", age);
        }

        triple.setLeft(patientAge); // 年龄 xx岁
        triple.setMiddle(subage);   // 子年龄    xxx天|xxx周|xxx月
        triple.setRight(subageUnit);// 子年龄单位

        return triple;
    }

    @Getter
    @AllArgsConstructor
    enum AgeUnit {
        YEAR("年"),
        MONTH("月"),
        DAY("天"),
        HOUR("时"),
        ;

        private final String unit;
    }

}
