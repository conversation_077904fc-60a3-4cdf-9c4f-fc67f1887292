package com.labway.business.center.core.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 中间库模式样本 lis状态
 */
@AllArgsConstructor
@Getter
public enum MiddleSampleLisStatus {

    SIGN("1", "已签收", 10),
    CONFIRM("2", "已确认", 21),
    SEND("3", "已发送", 35),
    ;

    private String code;
    private String desc;
    private Integer lisCode;

    public static String parseStatus(String lisStatus) {
        for (MiddleSampleLisStatus value : MiddleSampleLisStatus.values()) {
            if (value.getCode().equals(lisStatus)) {
                return value.getDesc();
            }
        }
        return StringUtils.EMPTY;
    }

    public static MiddleSampleLisStatus getByCode(String code) {
        for (MiddleSampleLisStatus value : MiddleSampleLisStatus.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
