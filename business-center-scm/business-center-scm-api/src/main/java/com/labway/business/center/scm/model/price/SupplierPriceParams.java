 package com.labway.business.center.scm.model.price;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

import lombok.Data;

/**
  * 供应商价目表
  * <AUTHOR>
  * @date 2023/08/01
  */

@Data
 public class SupplierPriceParams implements Serializable {
     
     /**
      * 组织ID编码
      */
     private String orgId;
     
     /**
      * 供应商ID
      */
     private String supplierId;

    /**
     * 币种
     */
    private String currtype;

     /**
      * 物料编号
      */
     private Collection<String> matIds;

}
