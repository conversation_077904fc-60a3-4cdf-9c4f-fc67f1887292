package com.labway.business.center.scm.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 采购政策tab
 * <AUTHOR>
 * @version 2023/10/27 09:48
 **/
@Data
public class PurchaseNewOrderPolicyDTO implements Serializable {

    /**
     * 主键
     */
    private String id;

    /**
     * 物料名称
     */
    private String materialName;


    /**
     * 物料名称
     */
    private String materialCode;

    /**
     * 结算财务组织 / 应付组织
     */
    private String financeOrgName;

    /**
     * 注册证号码
     */
    private String registrationNo;

    /**
     * 注册证名称
     */
    private String registrationName;

    /**
     * 厂商/生产企业
     */
    private String manufacturer;

    /**
     * 主辅单位换算率
     */
    private String unitTransRate;

    /**
     * 折本汇率
     */
    private BigDecimal parities;
}