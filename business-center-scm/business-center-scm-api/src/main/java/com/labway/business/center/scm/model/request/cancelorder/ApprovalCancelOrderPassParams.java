package com.labway.business.center.scm.model.request.cancelorder;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
public class ApprovalCancelOrderPassParams implements Serializable {
    private static final long serialVersionUID = -7614873591026588467L;

    // 业务单元ID
    @NotBlank(message = "业务单元id不能为空！")
    private String businessId;

    // 取消采购记录ID
    @NotBlank(message = "取消采购记录ID不能为空！")
    private String cancelId;
}
