package com.labway.business.center.scm.model.response.sale;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;



/**
 *销售订单物料
 */
@Data
public class WaitSaleOrderMaterialVo implements Serializable {
    /**
     * 销售订单表的订单ID
     */
     private String saleOrderId;
     /**
     * 物料编码
     */
     private String materialCode;
     /**
     * 商品名称
     */
     private String materialName;
     /**
     * 商品编号
     */
     private String materialId;
     /**
     * 生产企业/厂商
     */
     private String manufacturer;
     /**
     * 物料规格
     */
     private String specification;
     /**
     * 注册证号
     */
     private String registrationNo;
     /**
     * 注册证名称
     */
     private String registrationName;
     /**
     * 注册证名称/备案人名称
     */
     private String registrant;
     /**
     * 注册证名称/备案人名称code
     */
     private String registrantCode;
     /**
     * 预销售订单ID
     */
     private String saleRequestOrderId;
     /**
     * 源订单ID
     */
     private String sourceOrderId;
     /**
     * 含税单价
     */
     private BigDecimal taxPrice;
     /**
     * 无税单价
     */
     private BigDecimal noTaxPrice;
     /**
     * 销售数量
     */
     private Integer plainSaleNum;
     
     private Integer saleNum;
     
     private Integer plainSendTime;
     
     private Integer plainReceiveTime;
     /**
     * 创建时间
     */
     private LocalDateTime createTime;
     /**
     * 销售单状态详情0-已销售，1-已取消
     */
     private Integer status;


}