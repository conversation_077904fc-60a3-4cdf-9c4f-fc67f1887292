package com.labway.business.center.scm.model.request.approval;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
public class QueryApprovalUserParams implements Serializable {
    private static final long serialVersionUID = 6235030491370573814L;

    // 业务单元ID
    @NotBlank
    private String businessId;

    // 单据类型
    private Integer approvalType;

    // 用户姓名
    private String userName;
}
