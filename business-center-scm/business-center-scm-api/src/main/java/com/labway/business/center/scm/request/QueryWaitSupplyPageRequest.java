package com.labway.business.center.scm.request;

import com.labway.business.center.scm.request.base.PageRequest;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class QueryWaitSupplyPageRequest extends PageRequest implements Serializable {

    // 业务单元编码
    @NotBlank(message = "业务单元编码不能为空！")
    private String orgCode;

}
