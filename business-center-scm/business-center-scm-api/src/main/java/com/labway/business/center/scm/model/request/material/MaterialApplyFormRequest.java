package com.labway.business.center.scm.model.request.material;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;

/**
 * 查询未出库完成的申领单
 * <AUTHOR>
 * @version 2023/08/10 15:24
 **/
@Data
public class MaterialApplyFormRequest implements Serializable {

    /**
     * 申领社区编码
     */
    private String customerCode;

    /**
     * 申领单开始时间
     */
    private Date applyTimeBegin;

    /**
     * 申领单结束时间
     */
    private Date applyTimeEnd;

    /**
     * 业务单元
     */
    @NotBlank(message = "请选择业务单元")
    private String orgCode;
}