package com.labway.business.center.scm.model.ncc.stock;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class SaveNccStockInDto implements Serializable {

    // 请求体-h
    private IcPurchaseinH ic_purchasein_h;

    // 请求体-B
    private List<IcPurchaseinB> ic_purchasein_b;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class IcLocation implements Serializable {
        /**
         * 货位编码
         */
        private String clocationid;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class IcPurchaseinH implements Serializable {

        private String pk_org;          //	是	库存组织最新版本
        private String cwarehouseid;    //	是	仓库
        private String approver;        //	否	签字人
        private String billmaker;       //	否	制单人
        private String bitinbill;       //	否	进口入库单
        private String btriatradeflag;  //	否	三角贸易
        private String cbizid;          //	否	采购员
        private String cbiztype;        //	否	业务流程
        private String ccostdomainid;   //	否	结算成本域
        private String ccustomerid;     //	否	收货客户
        private String cdptid;          //	否	采购部门最新版本
        private String cdptvid;         //	否	采购部门
        private String cfanaceorgoid;   //	否	结算财务组织最新版本
        private String cfanaceorgvid;   //	否	结算财务组织
        private String cgeneralbid;     //	否	入库单表体主键
        private String cgeneralhid;     //	否	入库单表头主键
        private String corpoid;         //	否	公司最新版本
        private String corpvid;         //	否	公司
        private String cpayfinorgoid;   //	否	应付财务组织最新版本
        private String cpayfinorgvid;   //	否	应付财务组织
        private String cpurorgoid;      //	否	采购组织
        private String cpurorgvid;      //	否	采购组织最新版本
        private Date creationtime;      //	否	创建时间
        private String creator;         //	否	创建人
        private String crececountryid;  //	否	收货国
        private String csendcountryid;  //	否	发货国
        private String csendtypeid;     //	否	运输方式
        private String ctaxcountryid;   //	否	报税国
        private String ctradewordid;    //	否	贸易术语
        private String ctrantypeid;     //	否	出入库类型
        private String cvendorid;       //	否	供应商
        private String cwhsmanagerid;   //	否	库管员
        private String dbilldate;         //	否	单据日期
        private String dmakedate;         //	否	制单日期
        private Integer fbillflag;      //	否	单据状态
        private Integer fbuysellflag;   //	否	购销类型
        private Integer freplenishflag; //	否	采购退库
        private Integer iprintcount;    //	否	打印次数
        private Date modifiedtime;      //	否	最后修改时间
        private String modifier;        //	否	最后修改人
        private BigDecimal ntotalnum;   //	否	总数量
        private BigDecimal ntotalpiece; //	否	总件数
        private BigDecimal ntotalvolume;//	否	总体积
        private BigDecimal ntotalweight;//	否	总重量
        private String pk_group;        //	否	集团
        private String pk_measware;     //	否	计量器具
        private String pk_org_v;        //	否	库存组织
        private Date taudittime;        //	否	签字日期
        private String vbillcode;       //	否	单据号
        private String vdef1;           //	否	表头自定义项1
        private String vdef10;          //	否	表头自定义项10
        private String vdef11;          //	否	表头自定义项11
        private String vdef12;          //	否	表头自定义项12
        private String vdef13;          //	否	表头自定义项13
        private String vdef14;          //	否	表头自定义项14
        private String vdef15;          //	否	表头自定义项15
        private String vdef16;          //	否	表头自定义项16
        private String vdef17;          //	否	表头自定义项17
        private String vdef18;          //	否	表头自定义项18
        private String vdef19;          //	否	表头自定义项19
        private String vdef2;           //	否	表头自定义项2
        private String vdef20;          //	否	表头自定义项20
        private String vdef3;           //	否	表头自定义项3
        private String vdef4;           //	否	表头自定义项4
        private String vdef5;           //	否	表头自定义项5
        private String vdef6;           //	否	表头自定义项6
        private String vdef7;           //	否	表头自定义项7
        private String vdef8;           //	否	表头自定义项8
        private String vdef9;           //	否	表头自定义项9
        private String vnote;           //	否	备注
        private String vreturnreason;   //	否	退库理由
        private String vtrantypecode;   //	否	出入库类型编码

    }


    @Data
    public static class IcPurchaseinB implements Serializable {

        private String cmaterialvid;//	是	物料编码
        private BigDecimal nshouldassistnum;//	是	应收数量
        private String bassetcard;//	否	已生成设备卡片
        private String bfixedasset;//	否	已转固
        private String bopptaxflag;//	否	逆向征税标志
        private String borrowinflag;//	否	借入转采购
        private String bsourcelargess;//	否	上游赠品行
        private String carriveorder_bbid;//	否	来源到货单质检明细主键
        private Boolean bbarcodeclose;//	否	单据行是否条码关闭
        private Boolean bhasiabill;//	否	是否已传存货核算
        private Boolean bonroadflag;//	否	是否在途
        private String casscustid;//	否	客户
        private String castunitid;//	否	单位
        private String cbodytranstypecode;//	否	出入库类型
        private String cbodywarehouseid;//	否	库存仓库
        private String ccheckstateid;//	否	质检状态
        private String ccorrespondbid;//	否	对应入库单表体主键
        private String ccorrespondcode;//	否	对应入库单单据号
        private String ccorrespondhid;//	否	对应入库单主键
        private String ccorrespondrowno;//	否	对应入库单行号
        private String ccorrespondtranstype;//	否	对应入库单交易类型
        private String ccorrespondtype;//	否	对应入库单类型
        private String ccurrencyid;//	否	本位币
        private String cdestiareaid;//	否	目的地区
        private String cdesticountryid;//	否	目的国
        private String cdetailbid;//	否	进口明细单行主键
        private String cdetailid;//	否	进口明细单主键
        private String cdetailrowno;//	否	进口明细单行号
        private String cetdetlpickbid;//	否	出口明细拣配主键
        private String cfanaceorgoid;//	否	结算财务组织
        private String cffileid;//	否	特征码
        private String cfirstbillbid;//	否	源头单据表体主键
        private String cfirstbillhid;//	否	源头单据表头主键
        private String cfirsttranstype;//	否	源头单据交易类型
        private String cfirsttype;//	否	源头单据类型
        private String cgeneralbid;//	否	入库单表体主键
        private String cglobalcurrencyid;//	否	全局本位币
        private String cgroupcurrencyid;//	否	集团本位币
        private String cioliabilityoid;//	否	收货利润中心
        private String cioliabilityvid;//	否	收货利润中心
        private String cliabilityoid;//	否	结算利润中心
        private String cliabilityvid;//	否	结算利润中心
        private String clocationid;//	否	货位
        private IcLocation ic_location;//	否	货位
        private String corder_bb1id;//	否	源头采购单据到货计划
        private String corigareaid;//	否	原产地区
        private String corigcountryid;//	否	原产国
        private String corpoid;//	否	公司最新版本
        private String corpvid;//	否	公司
        private String cproductorid;//	否	生产厂商
        private String cprojectid;//	否	项目
        private String cprojecttaskid;//	否	项目任务
        private String cqtunitid;//	否	报价单位
        private String cqualitylevelid;//	否	质量等级
        private String creqstoorgoid;//	否	需求库存组织最新版本
        private String creqstoorgvid;//	否	需求库存组织
        private String crowno;//	否	行号
        private String cselastunitid;//	否	选择拆解单位
        private String csettlecurrencyid;//	否	结算币种
        private String csnqualitylevelid;//	否	序列号质量等级
        private String csnunitid;//	否	序列号单位
        private String csourcebillbid;//	否	来源单据表体主键
        private String csourcebillhid;//	否	来源单据主键
        private String csourcetranstype;//	否	来源单据交易类型
        private String csourcetype;//	否	来源单据类型
        private String csrc2billbid;//	否	其他来源单行主键
        private String csrc2billhid;//	否	其他来源单主键
        private String csrc2billtype;//	否	其他来源单据类型编码
        private String csrc2transtype;//	否	其他来源交易类型编码
        private String csrcmaterialoid;//	否	来源物料
        private String csrcmaterialvid;//	否	来源物料编码
        private String cstateid;//	否	库存状态
        private String ctaxcodeid;//	否	税码
        private String ctplcustomerid;//	否	货主客户
        private String cunitid;//	否	主单位
        private String cvendorid;//	否	供应商
        private String cvmivenderid;//	否	寄存供应商
        private Date dbizdate;//	否	入库日期
        private Date dinbounddate;//	否	首次入库日期
        private String dproducedate;//	否	生产日期
        private String dvalidate;//	否	失效日期
        private Integer fbillrowflag;//		否	配套标志
        private Integer fchecked;//		否	待检标志
        private String flargess;//	否	赠品
        private Integer ftaxtypeflag;//	否	扣税类别
        private Integer ibcversion;//	否	批次版本
        private Integer idesatype;//	否	拆解类型
        private BigDecimal naccumsettlenum;//	;//	否	累计结算数量
        private BigDecimal naccumtonum;//	否	累计转调拨数量
        private BigDecimal naccumvminum;//	否	累计汇总匹配主数量
        private BigDecimal nassistnum;//	需要	实收数量
        private BigDecimal nbarcodenum;//	否	条码主数量
        private BigDecimal ncalcostmny;//	否	计成本金额
        private BigDecimal ncaltaxmny;//	否	计税金额
        private BigDecimal ncanreplnum;//	否	可补货主数量
        private BigDecimal nchangestdrate;//	否	折本汇率
        private BigDecimal ncorrespondastnum;//	否	累计出库数量
        private BigDecimal ncorrespondgrsnum;//	否	累计出库毛重主数量
        private BigDecimal ncorrespondnum;//	否	累计出库主数量
        private BigDecimal ncountnum;//	否	箱数
        private BigDecimal nglobalexchgrate;//	否	全局本位币汇率
        private BigDecimal nglobalmny;//	否	全局本币无税金额
        private BigDecimal ngroupexchgrate;//	否	全局本位币汇率
        private BigDecimal ngroupmny;//	否	集团本币无税金额
        private BigDecimal ninvoicemny;//	否	可开票金额
        private BigDecimal ninvoicenum;//	否	可开票数量
        private BigDecimal nitemdiscountrate;//	否	折扣
        private BigDecimal nkdnum;//	否	扣吨主数量
        private BigDecimal nmny;//	否	本币无税金额
        private BigDecimal nnetprice;//	否	主本币无税净价
        private BigDecimal nnosubtax;//	否	不可抵扣税额
        private BigDecimal nnosubtaxrate;//	否	不可抵扣税率
        private BigDecimal norigmny;//	否	无税金额
        private BigDecimal norignetprice;//	否	主无税净价
        private BigDecimal norigprice;//	否	主无税单价
        private BigDecimal norigtaxmny;//	否	价税合计
        private BigDecimal norigtaxnetprice;//	否	主含税净价
        private BigDecimal npickupnum;//	否	主含税单价
        private BigDecimal norigtaxprice;//	否	累计拣配主数量 (箱数)
        private BigDecimal nprice;//	否	主本币无税单价
        private BigDecimal nqtnetprice;//	否	本币无税净价
        private BigDecimal nqtorignetprice;//	否	无税净价
        private BigDecimal nqtorigprice;//	否	无税单价
        private BigDecimal nqtorigtaxnetprice;//	否	含税净价
        private BigDecimal nqtorigtaxprice;//	否	含税单价
        private BigDecimal nqtprice;//	否	本币无税单价
        private BigDecimal nqttaxnetprice;//	否	本币含税净价
        private BigDecimal nqttaxprice;//	否	本币含税单价
        private BigDecimal nqtunitnum;//	否	报价数量
        private BigDecimal nreplenishedastnum;//	否	累计退库补货数量
        private BigDecimal nreplenishednum;//	否	累计退库补货主数量
        private BigDecimal nsettleprice;//	否	结算价格
        private BigDecimal nsettlepricenotax;//	否	结算无税价格
        private BigDecimal ncostmny;//	否	金额
        private BigDecimal ncostprice;//	否	单价
        private Integer nfeesettlecount;//	否	费用结算次数
        private BigDecimal ngrossnum;//	否	毛重主数量
        private BigDecimal nnum;//	否	实收主数量
        private BigDecimal npiece;//	否	件数
        private BigDecimal nplannedmny;//	否	计划金额
        private BigDecimal nplannedprice;//	否	计划单价
        private BigDecimal nshouldnum;//	否	应收主数量
        private BigDecimal ntarenum;//	否	皮重主数量
        private BigDecimal ntotalpicknum;//	否	累计拣配主数量
        private BigDecimal nvolume;//	否	体积
        private BigDecimal nweight;//	否	重量
        private String pk_batchcode;//	否	批次主键
        private String pk_group;//	否	集团
        private String pk_measware;//	否	计量器具
        private String pk_org;//	否	库存组织最新版本
        private String pk_org_v;//	否	库存组织
        private String pk_packsort;//	否	包装类型
        private String pk_serialcode;//	否	序列号主键
        private Date tbcts;//	否	批次时间戳
        private Date tchecktime;//	否	检验时间
        private Date tsourcebodyts;//	否	来源表体时间戳
        private Date tsourceheadts;//	否	来源表头时间戳
        private String vbatchcode;//	否	批次号
        private String vbatchcodenote;//	否	批次备注
        private String vbcdef1;//	否	批次自定义项1
        private String vbcdef10;//	否	批次自定义项10
        private String vbcdef11;//	否	批次自定义项11
        private String vbcdef12;//	否	批次自定义项12
        private String vbcdef13;//	否	批次自定义项13
        private String vbcdef14;//	否	批次自定义项14
        private String vbcdef15;//	否	批次自定义项15
        private String vbcdef16;//	否	批次自定义项16
        private String vbcdef17;//	否	批次自定义项17
        private String vbcdef18;//	否	批次自定义项18
        private String vbcdef19;//	否	批次自定义项19
        private String vbcdef2;//	否	批次自定义项2
        private String vbcdef20;//	否	批次自定义项20
        private String vbcdef3;//	否	批次自定义项3
        private String vbcdef4;//	否	批次自定义项4
        private String vbcdef5;//	否	批次自定义项5
        private String vbcdef6;//	否	批次自定义项6
        private String vbcdef7;//	否	批次自定义项7
        private String vbcdef8;//	否	批次自定义项8
        private String vbcdef9;//	否	批次自定义项9
        private String vbdef1;//	否	表体自定义项1(注册证)
        private String vbdef10;//	否	表体自定义项10
        private String vbdef11;//	否	表体自定义项11
        private String vbdef12;//	否	表体自定义项12
        private String vbdef13;//	否	表体自定义项13
        private String vbdef14;//	否	表体自定义项14
        private String vbdef15;//	否	表体自定义项15
        private String vbdef16;//	否	表体自定义项16
        private String vbdef17;//	否	表体自定义项17
        private String vbdef18;//	否	表体自定义项18
        private String vbdef19;//	否	表体自定义项19
        private String vbdef2;//	否	表体自定义项2(注册证号)
        private String vbdef20;//	否	表体自定义项20
        private String vbdef3;//	否	表体自定义项3(注册人/备案人code)
        private String vbdef4;//	否	表体自定义项4
        private String vbdef5;//	否	表体自定义项5
        private String vbdef6;//	否	表体自定义项6
        private String vbdef7;//	否	表体自定义项7
        private String vbdef8;//	否	表体自定义项8
        private String vbdef9;//	否	表体自定义项9
        private String vbillbarcode;//	否	单据条码
        private String vbilltypeu8rm;//	否	来自于零售之单据类型
        private String vchangerate;//	否	换算率
        private String vdetailbillcode;//	否	进口明细单号
        private String vexigencybid;//	否	紧急放行申请单行主键
        private String vexigencycode;//	否	紧急放行申请单号
        private String vexigencyhid;//	否	紧急放行申请单主键
        private String vexigencyrowno;//	否	紧急放行申请单行号
        private String vfirstbillcode;//	否	源头单据号
        private String vfirstrowno;//	否	源头单据行号
        private String vfree1;//	否	自由辅助属性1
        private String vfree10;//	否	自由辅助属性10
        private String vfree2;//	否	自由辅助属性2
        private String vfree3;//	否	自由辅助属性3
        private String vfree4;//	否	自由辅助属性4
        private String vfree5;//	否	自由辅助属性5
        private String vfree6;//	否	自由辅助属性6
        private String vfree7;//	否	自由辅助属性7
        private String vfree8;//	否	自由辅助属性8
        private String vfree9;//	否	自由辅助属性9
        private String vnotebody;//	否	行备注
        private String vqtunitrate;//	否	报价换算率
        private String vitcontractbillcode;//	否	进口合同号
        private String vreturnreason;//	否	退库理由
        private String vserialcode;//	否	序列号
        private String vsndef1;//	否	序列号自定义项1
        private String vsndef10;//	否	序列号自定义项10
        private String vsndef11;//	否	序列号自定义项11
        private String vsndef12;//	否	序列号自定义项12
        private String vsndef13;//	否	序列号自定义项13
        private String vsndef14;//	否	序列号自定义项14
        private String vsndef15;//	否	序列号自定义项15
        private String vsndef16;//	否	序列号自定义项16
        private String vsndef17;//	否	序列号自定义项17
        private String vsndef18;//	否	序列号自定义项18
        private String vsndef19;//	否	序列号自定义项19
        private String vsndef2;//	否	序列号自定义项2
        private String vsndef20;//	否	序列号自定义项20
        private String vsndef21;//	否	序列号自定义项21
        private String vsndef22;//	否	序列号自定义项22
        private String vsndef23;//	否	序列号自定义项23
        private String vsndef24;//	否	序列号自定义项24
        private String vsndef25;//	否	序列号自定义项25
        private String vsndef26;//	否	序列号自定义项26
        private String vsndef27;//	否	序列号自定义项27
        private String vsndef28;//	否	序列号自定义项28
        private String vsndef29;//	否	序列号自定义项29
        private String vsndef3;//	否	序列号自定义项3
        private String vsndef30;//	否	序列号自定义项30
        private String vsndef31;//	否	序列号自定义项31
        private String vsndef32;//	否	序列号自定义项32
        private String vsndef33;//	否	序列号自定义项33
        private String vsndef34;//	否	序列号自定义项34
        private String vsndef35;//	否	序列号自定义项35
        private String vsndef36;//	否	序列号自定义项36
        private String vsndef37;//	否	序列号自定义项37
        private String vsndef38;//	否	序列号自定义项38
        private String vsndef39;//	否	序列号自定义项39
        private String vsndef4;//	否	序列号自定义项4
        private String vsndef40;//	否	序列号自定义项40
        private String vsndef5;//	否	序列号自定义项5
        private String vsndef6;//	否	序列号自定义项6
        private String vsndef7;//	否	序列号自定义项7
        private String vsndef8;//	否	序列号自定义项8
        private String vsndef9;//	否	序列号自定义项9
        private String vsourcebillcode;//	否	来源单据号
        private String vsourcerowno;//	否	来源单据行号
        private String vsrc2billcode;//	否	其他来源单据号
        private String vsrc2billrowno;//	否	其他来源单行号
        private String vvendbatchcode;//	否	供应商批次号
    }

}
