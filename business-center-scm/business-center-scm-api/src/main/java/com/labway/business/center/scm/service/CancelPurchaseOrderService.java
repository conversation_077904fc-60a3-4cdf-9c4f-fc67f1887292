package com.labway.business.center.scm.service;

import com.labway.business.center.scm.model.request.cancelorder.ApprovalCancelOrderPassParams;
import com.labway.business.center.scm.model.request.cancelorder.ApprovalCancelOrderRejectParams;
import com.labway.business.center.scm.model.request.cancelorder.QueryCancelOrderMaterialParams;
import com.labway.business.center.scm.model.request.cancelorder.QueryCancelOrderParams;
import com.labway.business.center.scm.model.response.cancelorder.CancelOrderMaterialDTO;
import com.labway.business.center.scm.model.response.cancelorder.CancelPurchaseOrderDTO;
import com.labway.business.center.scm.request.AddCancelPurchaseParams;
import com.swak.frame.dto.Response;

import java.util.List;

/**
 * 取消采购服务
 */
public interface CancelPurchaseOrderService {

    /**
     * 添加取消采购单
     */
    Response<?> cancelPurchase(AddCancelPurchaseParams params);

    /**
     * 查询采购单
     */
    Response<List<CancelPurchaseOrderDTO>> listOrder(QueryCancelOrderParams params);

    /**
     * 查询采购单下物料
     */
    Response<List<CancelOrderMaterialDTO>> listMaterial(QueryCancelOrderMaterialParams params);

    /**
     * 审批通过
     */
    Response<?> approvalPass(ApprovalCancelOrderPassParams params);

    /**
     * 驳回
     */
    Response<?> approvalReject(ApprovalCancelOrderRejectParams params);
}
