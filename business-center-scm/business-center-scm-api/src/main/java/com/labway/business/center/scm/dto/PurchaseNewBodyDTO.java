package com.labway.business.center.scm.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 采购单重构表体
 *
 * <AUTHOR>
 * @version 2023/10/27 11:48
 **/
@Data
public class PurchaseNewBodyDTO implements Serializable {

    /**
     * 物料信息
     */
    private List<PurchaseNewOrderMaterialDTO> orderMaterials;

    /**
     * 到货信息
     */
    private List<PurchaseNewOrderArrivalDTO> orderArrivals;

    /**
     * 采购政策
     */
    private List<PurchaseNewOrderPolicyDTO> orderPolicies;

    /**
     * 执行结果
     */
    private List<PurchaseNewOrderExeResultDTO> orderExeResults;
}