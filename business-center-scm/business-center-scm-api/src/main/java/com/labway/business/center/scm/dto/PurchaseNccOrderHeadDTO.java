package com.labway.business.center.scm.dto;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.labway.business.center.scm.model.response.address.AddressVo;
import com.labway.business.center.scm.model.response.purchase.PayAgreementVo;
import com.labway.business.center.scm.model.response.purchase.RevenueTypeVo;
import com.labway.business.center.third.ncc.dto.ncc.BdCurrtypeDTO;
import com.labway.business.center.third.ncc.dto.ncc.PurchaseNatureDTO;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 请购单头部
 *
 * <AUTHOR>
 * @version 2023/10/26 15:05
 **/
@Data
public class PurchaseNccOrderHeadDTO implements Serializable {
    /**
     * 订单类型id
     */
    private String orderTypeId;

    /**
     * 订单类型名称
     */
    private String orderTypeName;

    /**
     * 制单/订单日期
     */
    @JsonFormat(pattern = DatePattern.NORM_DATE_PATTERN)
    private Date orderMakeDate;

    /**
     * 开票供应商id
     */
    private String supplierIdKp;

    /**
     * 开票供应商名称
     */
    private String supplierNameKp;

    /**
     * 供应商id
     */
    private String supplierId;

    /**
     * 供应商
     */
    private String supplierName;

    /**
     * 付款协议id
     */
    private String payAgreement;

    /**
     * 付款协议
     */
    private String payAgreementName;

    /**
     * 结算方式 编码
     */
    private String revenueId;

    /**
     * 结算方式名称
     */
    private String revenueName;

    /**
     * 币种
     *
     */
    private String currtypeCode;

    /**
     * 币种
     */
    private String currtypeName;

    /**
     * 整单扣税类别
     */
    private String taxType;

    /**
     * 整单税率
     */
    private BigDecimal tax;

    /**
     * 采购员code
     */
    private String purchaseUserCode;

    /**
     * 采购员名称
     */
    private String purchaseUserName;

    /**
     * 采购部门code
     */
    private String purchaseDeptId;

    /**
     * 采购部门名称
     */
    private String purchaseDeptName;

    /**
     * 供应商发货地址
     */
    private String supplierAddressId;

    /**
     * 供应商发货地址
     */
    private String supplierAddressName;

    /**
     * 对方订单号
     */
    private String targetOrderNumber;

    /**
     * 收货地址
     */
    private String addressId;

    /**
     * 收货地址
     */
    private String addressName;

    /**
     * 请购性质编码
     */
    private String purchaseNaturesCode;

    /**
     * 请购性质
     */
    private String purchaseNaturesName;

    /**
     * 总数量
     */
    private Integer totalNumber;

    /**
     * 价税合计
     */
    private BigDecimal totalMoney;

    /**
     * 备注
     */
    private String remark;
}