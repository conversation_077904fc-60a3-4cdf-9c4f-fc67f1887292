package com.labway.business.center.scm.request.purchasenew;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

/**
 * 切换供应商
 *
 * <AUTHOR>
 * @version 2023/10/31 14:50
 **/
@Data
public class ChangeSupplierRequest implements Serializable {
    @NotBlank(message = "请选择业务单元")
    private String orgId;
    /**
     * 供应商编码
     */
    @NotBlank(message = "请选择供应商")
    private String supplierId;
    /**
     * 币种编码
     */
    @NotBlank(message = "请选择币种")
    private String currtypeCode;
    private BigDecimal orderTax;
    /**
     * 物料
     */
    @Valid
    @NotNull(message = "请选择物料")
    private List<MaterialInfo> materialInfos;

    @Data
    public static class MaterialInfo{
        private String id;
        /**
         * 物料
         */
        @NotBlank(message = "请选择物料")
        private String materialCode;
        /**
         * 数量
         */
        private BigDecimal number;
        /**
         * 折扣
         */
        @NotNull(message = "折扣不能为空")
        private BigDecimal discount;
        /**
         * 税率
         */
        private List<String> tax;
    }
}