 package com.labway.business.center.scm.model.request.sale;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;

 /**
 * 销售订单
 * <AUTHOR>
 * @date 2023/08/09
 */
@Data
public class SaleOrderParams implements Serializable {
  /**
   * 请购销售单的id
   */
  @NotBlank(message = "待销售订单编码不能为空")
    private String requestOrderId;

  @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
  @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
  private LocalDateTime omsOrderCreateTime;
    /**
     * 订单类型名称
     */
     private String orderTypeName;
     /**
     * 订单类型ID
     */
     @NotBlank(message = "订单类型不能为空")
     private String orderTypeId;
     /**
     * 付款协议ID
     */
     @NotBlank(message = "付款协议不能为空")
     private String payAgreementId;
     /**
     * 付款协议名称
     */
     private String payAgreementName;
     /**
     * 收入类别名称
     */
     private String revenueTypeName;
     /**
     * 收入类别ID
     */
     @NotBlank(message = "收入类别不能为空")
     private String revenueTypeId;

     @NotBlank(message = "客户不能为空")
     private String customerId;
     
     private String customerName;
     /**
     * 币种
     */
     private String currency;
     
     private String currencyId;
    
     /**
     * 制单时间
     */
     @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
     @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
     private LocalDateTime makeTime;

     @NotBlank(message = "请选择业务单元")
    private String orgId;

     /**
      * oms原始订单id
      */
     private String omsOrderCode;
     /**
      * web源订单id
      */
     private String sourceOrderId;
    /**
     * 物料列表
     */
    private List<SaleOrderMaterialParams> materials;

}
