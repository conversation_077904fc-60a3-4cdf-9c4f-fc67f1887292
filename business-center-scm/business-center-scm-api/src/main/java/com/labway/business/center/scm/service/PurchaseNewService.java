package com.labway.business.center.scm.service;

import com.labway.business.center.core.config.BaseRequest;
import com.labway.business.center.scm.dto.*;
import com.labway.business.center.scm.request.PurchaseNewBodyRequest;
import com.labway.business.center.scm.request.purchasenew.*;
import com.swak.frame.dto.Response;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2023/10/26 14:01
 **/
public interface PurchaseNewService {
    /**
     * 获取 待采购单头部
     * @param baseRequest
     * @return
     */
    Response<PurchaseNewHeadDTO> getPurchaseHead(BaseRequest baseRequest, HttpServletRequest request);

    /**
     * 获取待采购单表体
     * @param purchaseNewBodyRequest
     * @return
     */
    Response<PurchaseNewBodyDTO> getPurchaseBody(PurchaseNewBodyRequest purchaseNewBodyRequest);

    /**
     * 待采购单保存
     * @param purchaseOrderNewRequest
     * @return
     */
    Response<String> createPurchaseOrder(PurchaseOrderNewRequest purchaseOrderNewRequest);

    /**
     * 更换供应商
     * @param changeSupplierRequest
     * @return
     */
    Response<List<PurchaseNewOrderMaterialDTO>> changePurchaseOrder(ChangeSupplierRequest changeSupplierRequest);

    /**
     * 查询折本汇率
     * @param currTypeInfoRequest
     * @return
     */
    Response<BigDecimal> getRageByCurrtype(CurrTypeInfoRequest currTypeInfoRequest);

    /**
     * 查询详情
     * @param purchaseOrderId
     * @return
     */
    Response<PurchaseNewOrder> searchNccPurchase(String purchaseOrderId);

    /**
     * 查询供应商收货地址
     * @param supplierId
     * @return
     */
    Response<List<SupplierAddressDTO>> searchSupplierAddress(String supplierId);

    /**
     * 作废请购单
     * @param cancelPurchaseRequestMaterialRequest
     * @return
     */
    Response<String> cancelPurchase(CancelPurchaseRequestMaterialRequest cancelPurchaseRequestMaterialRequest);


    /**
     * 新增物料
     * @param selectMaterialRequest
     * @return
     */
    Response<PurchaseNewBodyDTO> selectMaterial(SelectMaterialRequest selectMaterialRequest);
}