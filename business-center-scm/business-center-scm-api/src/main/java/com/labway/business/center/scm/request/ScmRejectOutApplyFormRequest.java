package com.labway.business.center.scm.request;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 出库单驳回
 * <AUTHOR>
 * @version 2023/08/23 14:54
 **/
@Data
public class ScmRejectOutApplyFormRequest implements Serializable {
    private static final long serialVersionUID = -1L;

    /**
     * 审批人id
     */
    private String userId;

    /**
     * 审批人名称
     */
    private String userName;

    /**
     * 申领单号
     */
    @NotBlank(message = "申领单编码不能为空")
    private String applyCode;

    /**
     * 订单编码
     */
    @NotBlank(message = "订单编码不能为空")
    private String orderCode;

    /**
     * 驳回原因
     */
    @NotBlank(message = "驳回原因不能为空")
    private String remark;

    @NotNull(message = "驳回的信息不能为空")
    private List<RejectMaterial> rejectMaterials;
    @Data
    @Valid
    public static class RejectMaterial{
        /**
         * 物料编码
         */
        @NotBlank(message = "物料编码不能为OK那个")
        private String materialCode;
        @NotNull(message = "驳回数量不能为空")
        private BigDecimal rejectNumber;
    }
}