 package com.labway.business.center.scm.model.response.purchase;

import java.io.Serializable;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.List;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;


@Data
public class PurchaseReqestOrderVo implements Serializable {

    /**
     * 数据唯一标识
     */
    private String uid;

    private String purchaseRequestOrderId;
    
    private String customId;
    
    private String customName;

    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private LocalDateTime orderTime;
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Timestamp createTime;

    private List<PurchaseReqestOrderMaterialVo> orderMaterials;
    
}
