 package com.labway.business.center.scm.service.ncc;

import java.util.List;

import com.labway.business.center.scm.model.control.NccControlCheckRequest;
import com.swak.frame.dto.Response;

/**
 * 销售订单出库管控单逻辑
 * <AUTHOR>
 * @date 2023/07/27
 */
 public interface NccSaleOutBoundControlFormService {
     
     /**
      * 销售出库单管控校验
      * @param request
      * @return
      */
     public Response<List<String>> checkNccSaleOutBound(NccControlCheckRequest request);

}
