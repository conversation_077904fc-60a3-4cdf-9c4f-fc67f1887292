package com.labway.business.center.scm.model.response.usermaterial;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UserMaterialDTO implements Serializable {
    private static final long serialVersionUID = -6867082390748137419L;

    // 业务标识
    private String materialFlag;

    // 是否勾选
    private boolean check;
}
