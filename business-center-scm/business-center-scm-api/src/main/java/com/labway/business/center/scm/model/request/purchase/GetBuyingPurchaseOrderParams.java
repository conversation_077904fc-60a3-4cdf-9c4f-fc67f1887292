 package com.labway.business.center.scm.model.request.purchase;

import java.io.Serializable;

import lombok.Data;

import javax.validation.constraints.NotBlank;

 /**
 * 获取采购订单参数
 * <AUTHOR>
 * @date 2023/08/10
 */

@Data
public class GetBuyingPurchaseOrderParams implements Serializable {
    
    @NotBlank(message = "请选择业务单元")
    private String orgId;
     /**
      * 供应商
      */
    private String supplierId;

     /**
      * 订单类型
      */
    private String orderTypeId;

     /**
      * 开始时间
      */
    private String startTime;

     /**
      * 结束时间
      */
    private String endTime;

     /**
      * 订单编码
      */
    private String orderId;

     /**
      * 页码
      */
    private Integer page;

     /**
      * 每页的条数
      */
    private Integer pageSize;

}
