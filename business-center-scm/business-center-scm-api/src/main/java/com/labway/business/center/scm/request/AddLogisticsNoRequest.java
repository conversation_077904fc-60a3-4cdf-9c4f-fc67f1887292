package com.labway.business.center.scm.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AddLogisticsNoRequest implements Serializable {

    // 物流单号
    private String logisticsNo;

    // 物流名称
    private String logisticsName;

    // 物流员
    private String staffName;

    /**
     * 物流出单号时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime logisticsCreateTime;

    // 出库单号
    @NotBlank(message = "出库单号不能为空！")
    private String supplyNo;

    // 出库单id
    private String itemId;

}
