package com.labway.business.center.scm.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class passArrivalDocumentRequest implements Serializable {

    // 到货单id
    @NotNull(message = "到货单id不能为空！")
    private String id;

    // 到货单编号
    private String arrivalNo;


}
