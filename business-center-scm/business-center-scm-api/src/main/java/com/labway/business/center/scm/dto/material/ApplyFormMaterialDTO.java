package com.labway.business.center.scm.dto.material;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 申请单物料
 *
 * <AUTHOR>
 * @version 2023/08/10 15:35
 **/
@Data
public class ApplyFormMaterialDTO implements Serializable {
    /**
     * 主键 -> id
     */
    private String itemId;
    /**
     * 申请单编码 -> orderCode
     */
    private String applyFormCode;
    /**
     * 申领物料编码
     */
    private String materialCode;
    /**
     * 申领物料名称
     */
    private String materialName;
    /**
     * 申领物料辅单位
     */
    private String materialAssistUnit;
    /**
     * 物料辅单位数量(申请数量)
     */
    private BigDecimal materialAssistCountApply;
    /**
     * 申领物料主单位
     */
    private String materialUnit;
    /**
     * 物料主单位数量(申请数量)
     */
    private Integer materialCountApply;
    /**
     * 物料规格
     */
    private String materialSpecification;
    /**
     * 物料辅单位出库数量
     */
    private BigDecimal materialAssistCountStockOut;
    /**
     * 物料主单位出库数量
     */
    private Integer materialCountStockOut;
    /**
     * 厂商信息
     */
    private String manufacturer;
    /**
     * 状态
     */
    private String status;
}