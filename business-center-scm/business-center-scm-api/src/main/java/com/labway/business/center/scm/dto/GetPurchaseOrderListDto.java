package com.labway.business.center.scm.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Date;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GetPurchaseOrderListDto implements Serializable {

    /**
     * ID
     */
    private Long id;
    /**
     * 业务单元id
     */
    private String businessId;
    /**
     * 采购订单ID
     */
    private String purchaseOrderId;
    /**
     * 采购订单状态0-待采购，1-已采购，2-采购单驳回，3-请购单，4-请购单驳回
     */
    private Integer purchaseOrderStatus;

    private String supplierId;

    /**
     * 供应商名称
     */
    private String supplierName;
    /**
     * 客户编码
     */
    private String customerCode;
    /**
     * 客户名称
     */
    private String customerName;
    /**
     * 采购人id
     */
    private String purchaseUser;
    /**
     * 付款协议
     */
    private String payAgreement;
    /**
     * 币种
     */
    private String currency;
    /**
     * 收货地址（关联收货地址表）
     */
    private String addrId;
    /**
     * 是否直线
     */
    private Integer directed;
    /**
     * 删除标志0-未删除，1-已删除
     */
    private Integer deleted;
    /**
     * ncc销售订单号
     */
    private String nccSalesOrderId;
    /**
     * ncc采购订单号
     */
    private String nccPurchaseOrderId;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    private String orderTypeId;

    private String orderTypeName;

    // 审核人id
    private String auditUserId;

    // 审核人名称
    private String auditUserName;

    // 审核时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime auditTime;

    // 原订单号
    private String sourceOrderId;

    /**
     * 采购人
     */
    private String makeUserName;
    // 采购单物料信息
    private List<PurchaseOrderMaterialList> purchaseOrderMaterialLists;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PurchaseOrderMaterialList implements Serializable{
        /**
         * 序列号
         */
        private Long id;
        /**
         * 采购订单表的订单ID
         */
        private String purchaseOrderId;
        /**
         * 物料编码
         */
        private String materialCode;
        /**
         * 商品名称
         */
        private String materialName;
        /**
         * 商品编号
         */
        private String materialId;
        /**
         * 生产企业/厂商
         */
        private String manufacturer;
        /**
         * 物料规格
         */
        private String specification;
        /**
         * 注册证号
         */
        private String registrationNo;
        /**
         * 注册证名称
         */
        private String registrationName;
        /**
         * 注册证名称/备案人名称
         */
        private String registrant;
        /**
         * 注册证名称/备案人名称code
         */
        private String registrantCode;
        /**
         * NCC 采购订单ID
         */
        private String nccPurchaseOrderId;

        // 请购订单ID
        private String purchaseRequestOrderId;
        /**
         * 含税单价
         */
        // 含税单价
        private BigDecimal taxPrice;
        /**
         * 无税单价
         */
        private BigDecimal noTaxPrice;
        /**
         * 请购数量
         */
        private BigDecimal purchaseNumber;
        /**
         * 商品货品的购买数量
         */
        private BigDecimal number;
        /**
         * 创建时间
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime createTime;
        /**
         * 更新时间
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime updateTime;
        /**
         * 是否直销
         */
        private Integer directed;
        /**
         * 逻辑删除0-未删除，1-已删除
         */
        private Integer deleted;
        /**
         * 采购单状态详情0-已采购，1-已取消
         */
        private Integer status;

        // 源订单id
        private String sourceOrderId;

        // 物料到货数量（到货入库单的总数量）
        private BigDecimal hasArrivalNumber;

        /**
         * 供应商编码
         */
        private String customerCode;
        /**
         * 供应商名称
         */
        private String customerName;

        /**
         * 原始订单创建时间
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime orderCreateTime;

        /**
         * 辅单位
         */
        private String secondaryUnit;

        // 存储条件
        private String materialStorageRequirement;

    }


}
