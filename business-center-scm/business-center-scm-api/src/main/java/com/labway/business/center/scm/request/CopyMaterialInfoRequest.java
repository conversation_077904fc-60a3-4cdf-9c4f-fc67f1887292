package com.labway.business.center.scm.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 2023/12/07 09:51
 **/
@Data
public class CopyMaterialInfoRequest implements Serializable {
    /**
     * 物料编码
     */
    @NotBlank(message = "请选择物料")
    private String materialCode;

    /**
     * 仓库id
     */
    @NotBlank(message = "请选择仓库")
    private String storDocId;

    /**
     * 订单编号
     */
    @NotBlank(message = "请选择订单")
    private String orderCode;
}