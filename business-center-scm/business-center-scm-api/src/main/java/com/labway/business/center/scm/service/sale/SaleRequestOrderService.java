package com.labway.business.center.scm.service.sale;

import java.util.List;

import com.labway.business.center.core.config.Pager;
import com.labway.business.center.scm.model.request.purchase.GetBuyingPurchaseOrderParams;
import com.labway.business.center.scm.model.request.purchase.PurchaseRequestSearchParams;
import com.labway.business.center.scm.model.request.sale.GetSaledOrderParams;
import com.labway.business.center.scm.model.response.purchase.WaitPurchaseOrderVo;
import com.labway.business.center.scm.model.response.sale.SaleReqestOrderVo;
import com.labway.business.center.scm.model.response.sale.SaleReqestOrders;
import com.swak.frame.dto.Response;

/**
 * 预销售订单服务
 * <AUTHOR>
 * @date 2023/08/11
 */
public interface SaleRequestOrderService {
    
  
    
    /**
     * 获取预售订单列表
     * @return
     */
    public Response<Pager<List<SaleReqestOrderVo>>> getSaleRequestOrders(GetSaledOrderParams params);
}

