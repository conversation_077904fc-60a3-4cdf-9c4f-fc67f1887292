package com.labway.business.center.scm.service;


import com.labway.business.center.core.config.Pager;
import com.labway.business.center.scm.dto.MaterialDTO;
import com.labway.business.center.scm.request.MaterialSearchRequest;
import com.labway.business.center.third.ncc.request.NccMaterialStockPageRequest;
import com.labway.business.center.third.ncc.vo.NccMaterialStockPageVo;
import com.swak.frame.dto.Response;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2023/10/30 09:53
 **/
public interface MaterialService {
    /**
     * 物料搜索
     * @param materialSearchRequest
     * @return
     */
    Response<List<MaterialDTO>> searchMaterial(MaterialSearchRequest materialSearchRequest);

    /**
     * 物料库存分页
     * @param stockPageRequest
     * @return
     */
    Response<Pager<List<NccMaterialStockPageVo>>> searchMaterialStockPage(NccMaterialStockPageRequest stockPageRequest);
}