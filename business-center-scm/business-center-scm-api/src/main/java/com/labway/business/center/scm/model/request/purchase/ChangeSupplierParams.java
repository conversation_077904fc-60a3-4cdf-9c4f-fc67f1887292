 package com.labway.business.center.scm.model.request.purchase;

import java.io.Serializable;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

 /**
 * 改变供应商获取价格
 * <AUTHOR>
 * @date 2023/08/04
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ChangeSupplierParams implements Serializable {
    @NotBlank(message = "请选择业务单元")
    private String orgId;
    @NotBlank(message = "请选择供应商")
    private String supplierId;
    
    private String matId;

}
