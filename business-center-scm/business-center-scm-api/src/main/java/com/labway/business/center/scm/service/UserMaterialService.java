package com.labway.business.center.scm.service;

import com.labway.business.center.scm.model.request.usermaterial.QueryOrgUserParams;
import com.labway.business.center.scm.model.request.usermaterial.QueryUserMaterialParams;
import com.labway.business.center.scm.model.request.usermaterial.SaveUserMaterialParams;
import com.labway.business.center.scm.model.response.usermaterial.OrgUserInfoDTO;
import com.labway.business.center.scm.model.response.usermaterial.UserMaterialDTO;
import com.swak.frame.dto.Response;

import java.util.List;

/**
 * 用户物料维护
 */
public interface UserMaterialService {

    /**
     * 查询业务单元下的用户列表
     */
    Response<List<OrgUserInfoDTO>> queryOrgUser(QueryOrgUserParams params);

    /**
     * 查询物料的业务标识，并根据用户打标
     */
    Response<List<UserMaterialDTO>> queryUserMaterial(QueryUserMaterialParams params);

    /**
     * 保存用户物料关联关系
     */
    Response<?> saveUserMaterial(SaveUserMaterialParams params);
}
