package com.labway.business.center.scm.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DeleteArrivalDocumentMaterialDetailRequest implements Serializable {

    // 到货单号
    @NotBlank(message = "到货单号不能为空！")
    private String arrivalNo;

    // 到货单物料id
    @NotEmpty(message = "到货单物料id不能为空！")
    private List<Integer> ids;


}
