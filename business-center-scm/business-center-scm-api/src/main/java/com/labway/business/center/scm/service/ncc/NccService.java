package com.labway.business.center.scm.service.ncc;

import com.labway.business.center.core.config.BaseRequest;
import com.labway.business.center.scm.dto.BdRackDTO;
import com.labway.business.center.scm.dto.OrderTypeDto;
import com.labway.business.center.scm.dto.material.MaterialPlanDateDTO;
import com.labway.business.center.scm.model.response.purchase.PayAgreementVo;
import com.labway.business.center.scm.model.response.purchase.RevenueTypeVo;
import com.labway.business.center.scm.request.purchasenew.CurrTypeInfoRequest;
import com.labway.business.center.third.ncc.dto.material.BdDefdocDTO;
import com.labway.business.center.third.ncc.dto.ncc.BdCurrtypeDTO;
import com.labway.business.center.third.ncc.dto.ncc.NccCustomerPageDto;
import com.labway.business.center.third.ncc.dto.ncc.NccMaterialCodeDto;
import com.labway.business.center.third.ncc.dto.ncc.OrgDeptPageDto;
import com.swak.frame.dto.Response;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;


/**
 * 调用ncc模块
 * <AUTHOR> Tianhao
 * @version 2023/08/10 17:37
 **/
public interface NccService {
    /**
     * 根据业务单元查询仓库
     * @param orgCode
     * @return
     */
    Response searchNccWareHouse(String orgCode);

    Response getNccOutType();

    Response getNccInType();

    Response<?> getNccToken();

     Response<?> getNccCustomer(NccCustomerPageDto pageDto);

    Response getNccDept(OrgDeptPageDto pageDto);

    /**
     * 查询仓库货位
     * @param storId
     * @return
     */
    List<BdRackDTO> searchBdRackListByStorId(String storId);

    /**
     * 获取物料批次号
     * @param nccMaterialCodeDto
     * @return
     */
    Response getBatchCodes(NccMaterialCodeDto nccMaterialCodeDto);

    Response<List<BdCurrtypeDTO>> getCurrencys();

    /**
     * 查询所有的订单类型
     * @return
     */
    Response<List<OrderTypeDto>> getOrderTypeDtos();

    /**
     * 待采购-付款协议
     * @param baseRequest
     * @return
     */
    Response<List<PayAgreementVo>> getPayAgreementList(BaseRequest baseRequest);

    /**
     * 待采购-结算方式
     * @return
     */
    Response<List<RevenueTypeVo>> getRevenueTypeList();

    /**
     * 折本汇率查询
     * @param currTypeInfoRequest
     * @return
     */
    Response<BigDecimal> getRageByCurrtype(CurrTypeInfoRequest currTypeInfoRequest);

    /**
     * 查询物料的储存条件
     * @param materialCodes
     * @return
     */
    Response<List<BdDefdocDTO>> searchMaterialStorageRequirement(Set<String> materialCodes);

    /**
     * 查询物料的固定提前量
     * @param materialCodes
     * @return
     */
    Response<List<MaterialPlanDateDTO>> searchMaterialPlanDate(Set<String> materialCodes);

    /**
     * 采购入库单跟采购单关联
     * @param cgeneralhid
     * @param ncPuOrder
     * @param orderTypeId
     * @param nccPurchaseOrderId
     */
    void updateArrival2Purchase(String cgeneralhid, String ncPuOrder, String orderTypeId, String nccPurchaseOrderId);
}