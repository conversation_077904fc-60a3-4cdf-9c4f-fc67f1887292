package com.labway.business.center.scm.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2023/10/27 13:04
 **/
@Data
public class PurchaseNewBodyRequest implements Serializable {
    @NotBlank(message = "请选择业务单元")
    private String orgId;

    @NotNull(message = "请勾选要采购的物料")
    private List<String> detailIds;
}