package com.labway.business.center.scm.controller;

import com.labway.business.center.scm.dto.material.ImportMaterialDTO;
import com.labway.business.center.scm.model.request.material.ImportApplyFormRequest;
import com.labway.business.center.scm.model.request.material.MaterialApplyFormRequest;
import com.labway.business.center.scm.dto.material.ApplyFormMaterialDTO;
import com.labway.business.center.scm.dto.material.ApplyFormUnStockOutDTO;
import com.labway.business.center.scm.request.NccMaterialSearchRequest;
import com.labway.business.center.scm.request.ScmRejectOutApplyFormRequest;
import com.labway.business.center.scm.service.MaterialApplyFormService;
import com.labway.business.center.third.ncc.dto.ncc.NccMaterialDto;
import com.labway.oms.request.web.RejectOutApplyFormRequest;
import com.swak.frame.dto.Response;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 物料申领单
 * <AUTHOR> Tianhao
 * @version 2023/08/10 15:11
 **/
@RestController
@RequestMapping("/apply")
public class MaterialApplyFormController {
    @Resource
    private MaterialApplyFormService materialApplyFormService;

    /**
     * 查询未出库完成的申领单(导入申请单按钮)
     * @param materialApplyFormRequest
     * @return
     */
    @PostMapping("/search/unstock")
    public Response<List<ApplyFormUnStockOutDTO>> searchUnStockApply(@Valid @RequestBody MaterialApplyFormRequest materialApplyFormRequest) {
        return materialApplyFormService.searchUnStockApply(materialApplyFormRequest);
    }

    /**
     * 根据申领单号查询申领单详情
     * @return
     */
    @GetMapping("/{orderCode}")
    public Response<List<ApplyFormMaterialDTO>> queryUnStockOutApplyFormDetail(@PathVariable(name = "orderCode")String orderCode) {
        return materialApplyFormService.searchDetailByApplyCode(orderCode);
    }

    /**
     * 导入申领单按钮
     * @param importApplyFormRequest
     * @return
     */
    @PostMapping("/import")
    public Response<List<ImportMaterialDTO>> importApplyForm(@Valid @RequestBody ImportApplyFormRequest importApplyFormRequest) {
        return materialApplyFormService.importApplyForm(importApplyFormRequest);
    }

    /**
     * 驳回申请单
     * @param scmRejectOutApplyFormRequest
     * @return
     */
    @PostMapping("/reject")
    public Response<String> rejectApply(@Valid @RequestBody ScmRejectOutApplyFormRequest scmRejectOutApplyFormRequest) {
        return materialApplyFormService.rejectApply(scmRejectOutApplyFormRequest);
    }

    /**
     * 根据物料编码查询物料信息
     * @param nccMaterialSearchRequest
     * @return
     */
    @PostMapping("/searchByCode")
    public Response<NccMaterialDto> getNccMaterialDtoByMaterialCode(@RequestBody @Valid NccMaterialSearchRequest nccMaterialSearchRequest) {
        return materialApplyFormService.getNccMaterialByMaterialCode(nccMaterialSearchRequest);
    }
}