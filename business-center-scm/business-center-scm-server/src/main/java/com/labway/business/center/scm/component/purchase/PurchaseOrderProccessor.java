 package com.labway.business.center.scm.component.purchase;

import com.labway.business.center.scm.model.request.purchase.OperatePurchaseOrderParams;
import com.swak.frame.dto.Response;

/**
  * 操作采购单处理
  * <AUTHOR>
  * @date 2023/08/10
  */
 public interface PurchaseOrderProccessor {
     
     
     /**
      * 处理采购订单
      * @param params
      * @return
      */
     public Response<String> operatePurchaseOrder(OperatePurchaseOrderParams params);

}
