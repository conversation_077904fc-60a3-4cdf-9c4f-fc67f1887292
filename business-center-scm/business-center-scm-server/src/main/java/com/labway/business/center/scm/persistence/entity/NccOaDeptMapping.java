package com.labway.business.center.scm.persistence.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * NCC OA部门对照表
 * @TableName tb_ncc_oa_dept_mapping
 */
@TableName(value ="tb_ncc_oa_dept_mapping")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class NccOaDeptMapping implements Serializable {

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 业务单元ID
     */
    @TableField(value = "business_id")
    private String businessId;

    /**
     * ncc部门id
     */
    @TableField(value = "ncc_dept_id")
    private String nccDeptId;

    /**
     * oa部门id
     */
    @TableField(value = "oa_dept_id")
    private String oaDeptId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}