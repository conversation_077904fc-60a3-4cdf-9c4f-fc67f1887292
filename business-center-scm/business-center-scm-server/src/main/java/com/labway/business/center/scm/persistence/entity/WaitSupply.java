package com.labway.business.center.scm.persistence.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 物料出库信息表
 * @TableName tb_wait_supply
 */
@TableName(value ="tb_wait_supply")
@Data
public class WaitSupply implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 订单编码
     */
    @TableField(value = "order_code")
    private String orderCode;

    /**
     * 父级订单编码，如果为父级则是0
     */
    @TableField(value = "parent_code")
    private String parentCode;

    /**
     * 待出库单号
     */
    @TableField(value = "wait_supply_no")
    private String waitSupplyNo;

    /**
     * web端用户id
     */
    @TableField(value = "account_id")
    private Long accountId;

    /**
     * 出库对象id
     */
    @TableField(value = "supply_customer_id")
    private String supplyCustomerId;

    /**
     * 出库对象名称
     */
    @TableField(value = "supply_customer_name")
    private String supplyCustomerName;

    /**
     * 出库仓库id
     */
    @TableField(value = "supply_warehouse_id")
    private String supplyWarehouseId;

    /**
     * 出库仓库名称
     */
    @TableField(value = "supply_warehouse_name")
    private String supplyWarehouseName;

    /**
     * 仓库所属组织
     */
    @TableField(value = "supply_warehouse_org_id")
    private String supplyWarehouseOrgId;

    /**
     * 是否审核0未审核，1已审核，-1已驳回
     */
    @TableField(value = "is_check")
    private Integer isCheck;

    /**
     * 审核人
     */
    @TableField(value = "check_user")
    private String checkUser;

    /**
     * 审核时间
     */
    @TableField(value = "check_time")
    private LocalDateTime checkTime;

    /**
     * 审核备注
     */
    @TableField(value = "check_remark")
    private String checkRemark;

    /**
     * 审批结果 0 驳回 1 通过
     */
    @TableField(value = "check_result")
    private Integer checkResult;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;

    /**
     * 是否关闭 0未关闭 1关闭
     */
    @TableField(value = "is_closed")
    private Integer isClosed;

    /**
     * 是否删除0 否 1 是
     */
    @TableField(value = "deleted")
    private Integer deleted;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}