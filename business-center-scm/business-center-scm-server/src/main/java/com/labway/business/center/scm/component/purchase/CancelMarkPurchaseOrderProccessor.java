package com.labway.business.center.scm.component.purchase;

import java.util.Objects;

import javax.annotation.Resource;

import com.labway.business.center.core.user.LoginUserInfoUtil;
import com.labway.business.center.scm.repository.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.labway.business.center.scm.constants.DirectStatus;
import com.labway.business.center.scm.constants.UserResultCode;
import com.labway.business.center.scm.model.request.purchase.OperatePurchaseOrderParams;
import com.labway.business.center.scm.persistence.entity.PurchaseOrder;
import com.swak.frame.dto.Response;

/**
 * 取消标记
 */
@Component("cancelMarkPurchaseOrderProccessor")
public class CancelMarkPurchaseOrderProccessor implements PurchaseOrderProccessor {

    @Resource
    private PurchaseRequestOrderMaterialRepository purchaseRequestOrderMaterialRepository;

    @Resource
    private PurchaseOrderRepository purchaseOrderRepository;

    @Resource
    private PurchaseOrderMaterialRepository purchaseOrderMaterialRepository;

    @Resource
    private DirectCheckRespository directCheckRespository;
    @Resource
    private ApplyCheckRespository applyCheckRespository;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Response<String> operatePurchaseOrder(OperatePurchaseOrderParams params) {

        Integer type = params.getType();
        String userId = LoginUserInfoUtil.getUserId();
        String orgId = params.getOrgId();
        String orderId = params.getOrderId();
        if (StringUtils.isBlank(orderId) || StringUtils.isBlank(userId) || StringUtils.isBlank(orgId)
            || Objects.isNull(type)||CollectionUtils.isEmpty(params.getMatList())) {
            return Response.builder(UserResultCode.PARAMS_ERROR);
        }
        PurchaseOrder purchaseOrder = purchaseOrderRepository.getPurchaseOrderByOrderId(orderId);
        if (Objects.isNull(purchaseOrder)) {
            return Response.builder(UserResultCode.PARAMS_ERROR);
        }
        boolean isAdmin = applyCheckRespository.isAdmin(userId, orgId, 3);
        if (!isAdmin) {
            return Response.builder(UserResultCode.NOT_APPROVE_PEM);
        }
      purchaseOrderMaterialRepository.updatePurchaseDirect(params.getMatIdList(), orderId, DirectStatus.NO.getStatus());
        return Response.success();
    }

}
