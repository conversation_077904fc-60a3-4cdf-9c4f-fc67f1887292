package com.labway.business.center.scm.controller;


import com.alibaba.fastjson.JSONObject;
import com.labway.business.center.scm.dto.BdRackDTO;
import com.labway.business.center.scm.dto.GetPurchaseOrderListDto;
import com.labway.business.center.scm.request.*;
import com.labway.business.center.scm.service.ArrivalDocumentService;
import com.labway.business.center.third.ncc.service.BdRackService;
import com.swak.frame.dto.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Arrays;
import java.util.List;

/**
 * 到货单表(ArrivalDocument)表控制层
 *
 * <AUTHOR>
 * @since 2023-07-05 10:04:43
 */
@Slf4j
@RestController
@RequestMapping("/ArrivalDocument")
public class ArrivalDocumentController {

    @DubboReference
    private BdRackService bdRackService;

    /**
     * 服务对象
     */
    @Resource
    private ArrivalDocumentService ArrivalDocumentService;


    /**
     * 分页查询到货单列表
     */
    @PostMapping("/queryArrivalDocumentPage")
    public Response<?> queryArrivalDocumentPage(@RequestBody @Valid QueryArrivalDocumentPageRequest queryArrivalDocumentPageRequest){
        return ArrivalDocumentService.queryArrivalDocumentPage(queryArrivalDocumentPageRequest);
    }


    /**
     * 保存到货单
     */
    @PostMapping("/saveQueryArrivalDocumentPage")
    public Response<?> saveQueryArrivalDocumentPage(@RequestBody @Valid SaveQueryArrivalDocumentPageRequest saveQueryArrivalDocumentPageRequest){
        log.info("saveQueryArrivalDocumentPage入参信息：{}",JSONObject.toJSONString(saveQueryArrivalDocumentPageRequest));
        return ArrivalDocumentService.saveQueryArrivalDocumentPage(saveQueryArrivalDocumentPageRequest);
    }


    /**
     * 审核到货单(通过)
     */
    @PostMapping("/passArrivalDocument")
    public Response<?> passArrivalDocument(@RequestBody @Valid passArrivalDocumentRequest passArrivalDocumentRequest){
        return ArrivalDocumentService.passArrivalDocument(passArrivalDocumentRequest);
    }


    /**
     * 驳回到货单
     */
    @PostMapping("/rejectArrivalDocument")
    public Response<?> rejectArrivalDocument(@RequestBody @Valid RejectArrivalDocumentRequest rejectArrivalDocumentRequest ){
        return ArrivalDocumentService.rejectArrivalDocument(rejectArrivalDocumentRequest);
    }


    /**
     * 导入采购单
     */
    @PostMapping("/getPurchaseOrderList")
    public Response<List<GetPurchaseOrderListDto>> getPurchaseOrderList(@RequestBody @Valid GetPurchaseOrderListRequest getPurchaseOrderListRequest){
        return ArrivalDocumentService.getPurchaseOrderList(getPurchaseOrderListRequest);
    }


    /**
     * 关闭/开启 到货单
     */
    @PostMapping("/closedOrOpenArrivalDocument")
    public Response<?> closedOrOpenArrivalDocument(@RequestBody @Valid ClosedOrOpenArrivalDocumentRequest closedOrOpenArrivalDocumentRequest){
        return ArrivalDocumentService.closedOrOpenArrivalDocument(closedOrOpenArrivalDocumentRequest);
    }


    /**
     * 查询物料货位
     */
    @PostMapping("/queryRackInfo")
    public Response<List<BdRackDTO>> queryRackInfo(@RequestBody @Valid QueryRackInfoRequest queryRackInfoRequest){
        return ArrivalDocumentService.searchBdRackListByStorId(queryRackInfoRequest);
    }


}

