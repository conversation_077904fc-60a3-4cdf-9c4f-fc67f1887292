package com.labway.business.center.scm.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.labway.business.center.core.enums.Constants;
import com.labway.business.center.core.enums.ResultCode;
import com.labway.business.center.core.user.LoginUserInfoUtil;
import com.labway.business.center.core.util.BeanCopyUtils;
import com.labway.business.center.core.util.NotifyUtil;
import com.labway.business.center.scm.config.SCMRabbitmqConfig;
import com.labway.business.center.scm.constants.*;
import com.labway.business.center.scm.converter.MaterialSupplyDetailFormConverter;
import com.labway.business.center.scm.converter.MaterialSupplyFormConverter;
import com.labway.business.center.scm.converter.OrderMaterialConverter;
import com.labway.business.center.scm.dto.QuerySupplyDetailListDto;
import com.labway.business.center.scm.dto.QuerySupplyPageDto;
import com.labway.business.center.scm.dto.material.*;
import com.labway.business.center.scm.model.request.material.ImportApplyFormRequest;
import com.labway.business.center.scm.mq.out.OutInfoMessageDTO;
import com.labway.business.center.scm.persistence.entity.MaterialSupply;
import com.labway.business.center.scm.persistence.entity.MaterialSupplyDetail;
import com.labway.business.center.scm.persistence.entity.WaitSupply;
import com.labway.business.center.scm.persistence.entity.oms.Order;
import com.labway.business.center.scm.persistence.entity.oms.OrderMaterial;
import com.labway.business.center.scm.persistence.entity.oms.OrderRecord;
import com.labway.business.center.scm.persistence.params.NotifySystemDTO;
import com.labway.business.center.scm.persistence.params.SearchRefundParams;
import com.labway.business.center.scm.repository.*;
import com.labway.business.center.scm.repository.oms.OrderRecordRepository;
import com.labway.business.center.scm.request.*;
import com.labway.business.center.scm.service.MaterialApplyFormService;
import com.labway.business.center.scm.service.MaterialSupplyService;
import com.labway.business.center.scm.util.MdmUtil;
import com.labway.business.center.scm.util.OutIdGeneratorService;
import com.labway.business.center.third.ncc.dto.material.TbMaterialDto;
import com.labway.business.center.third.ncc.dto.ncc.*;
import com.labway.business.center.third.ncc.dto.out.OutBodyDto;
import com.labway.business.center.third.ncc.dto.out.OutDto;
import com.labway.business.center.third.ncc.dto.out.ScmOutDto;
import com.labway.business.center.third.ncc.request.UserAuthorizationRequest;
import com.labway.business.center.third.ncc.service.NccHttpServer;
import com.swak.frame.dto.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
* <AUTHOR> Tianhao
* @description 针对表【tb_material_supply(物料出库信息表)】的数据库操作Service实现
* @createDate 2023-08-10 14:18:43
*/
@Slf4j
@DubboService
public class MaterialSupplyServiceImpl implements MaterialSupplyService{

    @Resource
    private MaterialSupplyFormRepository materialSupplyFormRepository;
    @Resource
    private MaterialSupplyFormDetailRepository materialSupplyFormDetailRepository;
    @Resource
    private OrderMaterialRepository orderMaterialRepository;
    @Resource
    private OrderRepository orderRepository;
    @Resource
    private WaitSupplyRepository waitSupplyRepository;
    @Resource
    private OrderRecordRepository orderRecordRepository;

    @Resource
    private MaterialSupplyFormConverter materialSupplyFormConverter;
    @Resource
    private MaterialSupplyDetailFormConverter materialSupplyDetailFormConverter;
    @Resource
    private OrderMaterialConverter orderMaterialConverter;

    @Resource
    private MaterialApplyFormService materialApplyFormService;
    @Resource
    private OutIdGeneratorService outIdGeneratorService;
    @Resource
    private NotifyUtil notifyUtil;
    @Resource
    private MdmUtil mdmUtil;
    @Resource
    private RabbitTemplate rabbitTemplate;
    @Resource
    private SCMRabbitmqConfig scmRabbitmqConfig;

    @Value("${labway.sso.authPath:http://localhost:9980/sso-server/login-user/authorization}")
    private String ssoAuthPath;

    @DubboReference
    private NccHttpServer nccHttpServer;

    /**
     * 查询出库单列表
     * @param querySupplyPageRequest
     * @return
     */
    @Override
    public Response querySupplyPage(QuerySupplyPageRequest querySupplyPageRequest) {

        // 查询出库单
        IPage<MaterialSupply> materialSupplies = materialSupplyFormRepository.querySupplyPage(querySupplyPageRequest);

        IPage<QuerySupplyPageDto> resultPage = new Page<>();
        BeanUtils.copyProperties(materialSupplies,resultPage);
        if (CollectionUtils.isEmpty(materialSupplies.getRecords())){
            resultPage.setRecords(Collections.emptyList());
        }else {
            List<QuerySupplyPageDto> querySupplyPageDtos = BeanCopyUtils.copyList(materialSupplies.getRecords(), QuerySupplyPageDto.class);
            resultPage.setRecords(querySupplyPageDtos);
        }

        return Response.success(resultPage);
    }


    /**
     * 查询出库单物料明细
     * @param querySupplyDetailListRequest
     * @return
     */
    @Override
    public Response<List<QuerySupplyDetailListDto>> querySupplyDetailList(QuerySupplyDetailListRequest querySupplyDetailListRequest) {

        List<MaterialSupplyDetail> materialSupplyDetails = materialSupplyFormDetailRepository.querySupplyDetailList(querySupplyDetailListRequest.getSupplyNo());
        if (CollectionUtils.isEmpty(materialSupplyDetails)){
            return Response.success(Collections.emptyList());
        }

        List<QuerySupplyDetailListDto> querySupplyDetailListDtos = BeanCopyUtils.copyList(materialSupplyDetails, QuerySupplyDetailListDto.class);

        // 订单编码
        String orderCode = materialSupplyDetails.stream().map(e -> e.getOrderCode()).filter(Objects::nonNull).findFirst().get();
        String omsCode = orderCode;
        // 去待出库单查询
        WaitSupply waitSupply = waitSupplyRepository.searchWaitSupplyByOrderCode(orderCode);
        // 不是待出库单 则去原始订单查询
        if (Objects.nonNull(waitSupply)) {
            omsCode = waitSupply.getParentCode();
        }
        // 物料编码
        List<String> materialCodes = materialSupplyDetails.stream().map(e -> e.getMaterialCode()).collect(Collectors.toList());

        // 查询填充物料的原始申请数量
        List<OrderMaterial> orderMaterials = orderMaterialRepository.queryListByOrderCodeAndMaterialCodes(omsCode,materialCodes);
        if (CollectionUtils.isNotEmpty(orderMaterials)){
            Map<String, OrderMaterial> orderMaterialMap = orderMaterials.stream().collect(Collectors.toMap(e -> e.getMaterialSn(), p -> p, (o1, o2) -> o2));
            for (QuerySupplyDetailListDto querySupplyDetailListDto : querySupplyDetailListDtos) {
                OrderMaterial orderMaterial = orderMaterialMap.get(querySupplyDetailListDto.getMaterialCode());
                if (orderMaterial!=null){
                    querySupplyDetailListDto.setApplyAssistNumber(orderMaterial.getNumber());
                }
            }
        }

        return Response.success(querySupplyDetailListDtos);
    }

    /**
     * 添加物流单号
     * @param addLogisticsNoRequest
     * @return
     */
    @Override
    public Response<?> addLogisticsNo(AddLogisticsNoRequest addLogisticsNoRequest) {
        Integer count = materialSupplyFormRepository.addLogisticsNo(addLogisticsNoRequest);
        return Response.success(count);
    }

    /**
     * 库存检查
     *
     * @param outDto 出库信息
     */
    @Override
    public Response<Boolean> checkInventory(OutDto outDto) {
        // 过滤出出库辅数量为0的
        List<OutBodyDto> zero = outDto.getOutBody().stream()
                .filter(item -> item.getNassistnum().compareTo(BigDecimal.ZERO) == 0)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(zero)) {
            String zeroMaterial = zero.stream().map(OutBodyDto::getMaterialName).collect(Collectors.joining(","));
            log.error(DateUtil.now() + ":出库单中【"+zeroMaterial+"】,出库数量为0,请检查");
            return Response.fail(ResultCode.OUT_NUMBER_IS_ZERO.getCode(),String.format(ResultCode.OUT_NUMBER_IS_ZERO.getMsg(),zeroMaterial));
        }

        List<ApplyFormMaterialDTO> applyFormDetailResultData = Lists.newArrayList();
        // 查询本地库中的数据
        List<OrderMaterial> orderMaterials = orderMaterialRepository.searchDetailByOrderCode(outDto.getOrderCode());
        if (CollectionUtils.isNotEmpty(orderMaterials)) {
            applyFormDetailResultData = orderMaterialConverter.convertOrderMaterials2ApplyFormMaterialDtoList(orderMaterials);
        }
        Map<String, ApplyFormMaterialDTO> applyFormDetailMap = applyFormDetailResultData.stream().collect(Collectors.toMap(ApplyFormMaterialDTO::getMaterialCode, Function.identity()));
        Map<String, List<OutBodyDto>> groupByMaterialCode = outDto.getOutBody().stream().collect(Collectors.groupingBy(OutBodyDto::getMaterialCode));
        // 获取物料在ncc中的库存数量
        for (OutBodyDto outBodyDto : outDto.getOutBody()) {
            if (StringUtils.isBlank(outBodyDto.getMaterialCode()) || StringUtils.isBlank(outBodyDto.getBatchCode())) {
                continue;
            }
            // 判断已出的数量是否超量  已出主辅数量
            ApplyFormMaterialDTO applyFormMaterialDTO = applyFormDetailMap.get(outBodyDto.getMaterialCode());
            BigDecimal materialCountStockOut = new BigDecimal(Objects.requireNonNullElse(applyFormMaterialDTO.getMaterialCountStockOut(),0));
            BigDecimal materialAssistCountStockOut = applyFormMaterialDTO.getMaterialAssistCountStockOut();
            // 出库主辅数量
            BigDecimal nnum = BigDecimal.ZERO;
            BigDecimal nassistnum = BigDecimal.ZERO;
            List<OutBodyDto> list = groupByMaterialCode.get(outBodyDto.getMaterialCode());
            for (OutBodyDto bodyDto : list) {
                nnum = nnum.add(bodyDto.getNnum());
                nassistnum = nassistnum.add(bodyDto.getNassistnum());
            }
            // 如果这次出库后 数量为
            BigDecimal materialCountStockOutCount = nnum.add(materialCountStockOut);
            BigDecimal materialAssistCountStockOutCount = nassistnum.add(materialAssistCountStockOut);
            if (materialCountStockOutCount.compareTo(outBodyDto.getNshouldnum()) > 0
                    || materialAssistCountStockOutCount.compareTo(outBodyDto.getMaterialAssistCountApply()) > 0) {
                return Response.fail(ResultCode.OUT_NUMBER_MAX.getCode(),String.format(ResultCode.OUT_NUMBER_MAX.getMsg(),outBodyDto.getMaterialName()));
            }
            NccInventoryDto nccInventoryDto = new NccInventoryDto(outBodyDto.getMaterialCode(), outBodyDto.getBatchCode(), outDto.getStorDocId());
            Response<List<NccInventoryResultDto>> result = nccHttpServer.getInventory(nccInventoryDto);
            // 获取是否成功
            if (!result.isSuccess()) {
                return Response.fail(result.getCode(),result.getMsg());
            }
            List<NccInventoryResultDto> data = result.getData();
            Optional<NccInventoryResultDto> first = data.stream().findFirst();
            if (first.isPresent()) {
                // 库存量
                BigDecimal nonhandastnum = first.get().getNonhandastnum();
                int flag = nonhandastnum.compareTo(outBodyDto.getNassistnum());
                // 库存量 小于 申请量
                if (flag < 0) {
                    return Response.fail(500,"物料：" + outBodyDto.getMaterialCode() + "辅数量库存仅剩：" + nonhandastnum);
                }
            }
        }
        return Response.success(true);
    }

    /**
     * 检查用户本人
     *
     * @param username
     * @param password
     * @return
     */
    @Override
    public Response<String> loginUserCheck(String username, String password, HttpServletRequest request) {
        // 通过http调用接口进行校验
        try {
            String sessionId = request.getHeader(Constants.SSO_SESSIONID);
            String cliendId = request.getHeader(Constants.SSO_CLIENTID);
            UserAuthorizationRequest userAuthorizationRequest = new UserAuthorizationRequest(username, password);
            String body = JSONUtil.toJsonStr(userAuthorizationRequest);
            HttpResponse response = HttpRequest.post(ssoAuthPath).header(Constants.SSO_SESSIONID, sessionId).header(Constants.SSO_CLIENTID, cliendId)
                    .body(body).execute();
            Response<String> result = JSONUtil.toBean(response.body(), Response.class);
            return result;
        } catch (Exception e) {
            log.info("调用SSO服务进行本人校验出现异常",e);
        }
        return Response.fail(ResultCode.INVOKE_FAIL);
    }

    /**
     * 出库
     * 涉及到 SCM OMS 库
     * @param outDto
     * @return
     */
    @Override
//    @Transactional(rollbackFor = Exception.class)
    public Response<?> materialOut(OutDto outDto) {
        String orderCode = outDto.getParentCode();
        String systemCode = "";
        Order order = orderRepository.searchOrderByOrderCode(orderCode);
        if (Objects.nonNull(order)) {
            systemCode = SystemEnum.OMS.getSystemCode();
        }
        String realName = LoginUserInfoUtil.getUserUserName();
        // 出库id
        String outId = outIdGeneratorService.generateId();
        outDto.setOutId(outId);
        log.info("开始调用ncc方出库，调用参数：{}",JSONUtil.toJsonStr(outDto));
        // 调用ncc出库
        Response<NccResponseDto> nccResult = wareHousing(outDto);
        if (!Objects.equals(nccResult.getCode(),Response.success().getCode())) {
            return nccResult;
        }
        log.info("调用NCC方出库结束，响应参数：{}",nccResult);
        notifyUtil.send("调用NCC方出库结束，响应参数：" + JSONUtil.toJsonStr(nccResult));
        // 新增 出库单
        Response<MaterialSupply> supplyResult = insertMaterialSupply(realName,outDto,nccResult.getData(),SupplyTypeEnum.OUT.getCode(),null,order.getAccountId());
        if (!supplyResult.isSuccess()) {
            return supplyResult;
        }
        // 新增出库单详情
        MaterialSupply supply = supplyResult.getData();
        Response<List<MaterialSupplyDetail>> details = insertMaterialSupplyDetail(supply.getItemId(),outDto, nccResult.getData());
        if (!details.isSuccess()) {
            return details;
        }
        // 通过出库数量 判断 出库状态
        OutMaterialStatus outMaterialStatus = OutMaterialStatus.ALL;
        // 查询申领单详情
        List<OrderMaterial> orderMaterials = orderMaterialRepository.searchDetailByOrderCode(orderCode);
        List<ApplyFormMaterialDTO> centerMaterialApplyFormDetails = orderMaterialConverter.convertOrderMaterials2ApplyFormMaterialDtoList(orderMaterials);

        // key -> 物料编码 value -> 应出数量
        Map<String, BigDecimal> map = centerMaterialApplyFormDetails.stream()
                .collect(Collectors.toMap(ApplyFormMaterialDTO::getMaterialCode, ApplyFormMaterialDTO::getMaterialAssistCountApply));
        // 已出物料的主数量
        Map<String, BigDecimal> outTotal = getOutTotal(orderCode);
        if (MapUtils.isNotEmpty(outTotal)) {
            for (String materialCode : map.keySet()) {
                BigDecimal count = new BigDecimal(0);
                for (String code : outTotal.keySet()) {
                    // 包含这个物料
                    if (code.contains(materialCode)) {
                        // 计算数量
                        BigDecimal alreadyNum = outTotal.get(code);
                        count = count.add(alreadyNum);
                    }
                }
                log.info("物料:{}，出库总数量：{}",materialCode,count);
                // 应出数量大于已出总数量 部分出库
                if (map.get(materialCode).compareTo(count) > 0) {
                    outMaterialStatus = OutMaterialStatus.SOME;
                    break;
                }
            }
        }

        // 修改数据库
        List<MaterialSupplyDetail> supplyDetails = details.getData();

        //updateApplyFormInfo(orderCode,orderMaterials, supply, supplyDetails, outMaterialStatus);
        // 通知消息
        NotifySystemDTO notifySystemDTO = NotifySystemDTO.builder()
                .supply(supply).supplyDetail(supplyDetails).systemCode(systemCode)
                .applyStatus(outMaterialStatus.getApplyCode()).order(order).build();
        notifySystem(notifySystemDTO);

        OrderRecord orderRecord = new OrderRecord();
        orderRecord.setOrderCode(orderCode).setMsg("物料出库").setOperationTime(new Date()).setOperationUser(LoginUserInfoUtil.getUserId()).setOperationUserName(realName);
        orderRecordRepository.saveRecord(orderRecord);

        return Response.success(outId);
    }

    /**
     * 打印出库单的信息
     *
     * @param printRequest
     * @return
     */
    @Override
    public Response<PrintDTO> print(PrintRequest printRequest) {
        if (StringUtils.isAnyBlank(printRequest.getSupplyNo())) {
            return Response.fail(ResultCode.PARAMS_NOT_VALIDATE);
        }
        // 头部
        PrintHeadDTO printHeadDTO = new PrintHeadDTO();
        // 头部 查询出库单
        String supplyNo = printRequest.getSupplyNo();
        MaterialSupply supply = materialSupplyFormRepository.searchSupplyBySupplyNo(supplyNo);
        if (Objects.isNull(supply)) {
            return Response.fail(ResultCode.GET_SUPPLY_ERR.getCode(),String.format(ResultCode.GET_SUPPLY_ERR.getMsg(),supplyNo));
        }

        printHeadDTO.setWarehouseName(supply.getSupplyWarehouseName());
        printHeadDTO.setRemark(supply.getSupplyNo());
        printHeadDTO.setNccSupplyNo(supply.getNccSupplyNo());
        printHeadDTO.setSupplyTime(supply.getSupplyTime());
        printHeadDTO.setDeptName(supply.getSupplyDeptName());
        printHeadDTO.setCustomerName(supply.getSupplyCustomerName());
        printHeadDTO.setPrepareTime(supply.getPrepareTime());
        // 表体
        List<PrintBodyDTO> list = new ArrayList<>();
        // 查询出库详情的物料
        List<MaterialSupplyDetail> supplyDetails = materialSupplyFormDetailRepository.searchSupplyDetailsBySupplyNo(supplyNo);
        Set<String> materialCodes = supplyDetails.stream().map(MaterialSupplyDetail::getMaterialCode).collect(Collectors.toSet());
        Response<List<TbMaterialDto>> mdmResponse = mdmUtil.getMaterialDetail(materialCodes);
        if (!mdmResponse.isSuccess()) {
            return Response.fail(mdmResponse.getCode(),mdmResponse.getMsg());
        }
        List<TbMaterialDto> data = mdmResponse.getData();
        Map<String, TbMaterialDto> materialDtoMap = data.stream().collect(Collectors.toMap(TbMaterialDto::getMaterialCode, Function.identity()));
        // 构建表体
        for (MaterialSupplyDetail supplyDetail : supplyDetails) {
            TbMaterialDto tbMaterialDto = materialDtoMap.get(supplyDetail.getMaterialCode());
            PrintBodyDTO printBodyDTO = new PrintBodyDTO();
            // 数量
            printBodyDTO.setAssistNumber(supplyDetail.getAssistNumber());
            // 存储条件
            printBodyDTO.setStorageRequirement(supplyDetail.getStorageRequirement());
            // 批次号
            printBodyDTO.setBatchNo(supplyDetail.getBatchNo());
            // 单位
            printBodyDTO.setSecondaryUnit(tbMaterialDto.getSecondaryUnit());
            // 规格
            printBodyDTO.setSpecification(tbMaterialDto.getSpecification());
            // 物料编码
            printBodyDTO.setMaterialCode(supplyDetail.getMaterialCode());
            // 物料名称
            printBodyDTO.setMaterialName(supplyDetail.getMaterialName());
            // 失效时间
            printBodyDTO.setInvalidTime(supplyDetail.getInvalidTime());
            printBodyDTO.setManufacturer(supplyDetail.getManufacturer());
            printBodyDTO.setRegistrant(supplyDetail.getRegistrant());
            printBodyDTO.setRegistrationName(supplyDetail.getRegistrationName());
            list.add(printBodyDTO);
        }
        List<PrintBodyDTO> printList = list.stream().filter(item -> Objects.nonNull(item.getAssistNumber()) && item.getAssistNumber().compareTo(BigDecimal.ZERO) >= 0).collect(Collectors.toList());
        PrintDTO printDTO = new PrintDTO();
        printDTO.setPrintHeadDTO(printHeadDTO);
        printDTO.setBodyDTO(printList);
        printDTO.setName(LoginUserInfoUtil.getUserUserName());
        return Response.success(printDTO);
    }

    /**
     * 退库
     *
     * @param outDto
     * @return
     */
    @Override
//    @Transactional(rollbackFor = Exception.class)
    public Response materialRefundOut(OutDto outDto,String relationSupplyNo) {
        String realName = LoginUserInfoUtil.getUserUserName();
        // 出库id
        String outId = outIdGeneratorService.generateId();
        outDto.setOutId(outId);
        log.info("开始调用ncc方出库，调用参数：{}",outDto);
        // 调用ncc出库
        Response<NccResponseDto> nccResult = wareHousing(outDto);
        if (!Objects.equals(nccResult.getCode(),Response.success().getCode())) {
            return nccResult;
        }
        log.info("调用NCC方出库结束，响应参数：{}",nccResult);
        notifyUtil.send("调用NCC方出库结束，响应参数：" + JSONUtil.toJsonStr(nccResult));
//        Order order = orderRepository.searchOrderByOrderCode(outDto.getOrderCode());
        // 新增 出库单
        Response<MaterialSupply> supplyResult = insertMaterialSupply(realName,outDto,nccResult.getData(), SupplyTypeEnum.REFUND.getCode(), relationSupplyNo,Long.parseLong(outDto.getOrderCode()));
        if (!supplyResult.isSuccess()) {
            return supplyResult;
        }

        // 新增出库单详情
        MaterialSupply supply = supplyResult.getData();
        Response<List<MaterialSupplyDetail>> details = insertMaterialSupplyDetail(supply.getItemId(),outDto, nccResult.getData());
        if (!details.isSuccess()) {
            return details;
        }
        return Response.success();
    }

    /**
     * 分页查询退库单
     *
     * @param refundSupplyRequest
     * @return
     */
    @Override
    public Response searchRefundSupplyPage(RefundSupplyRequest refundSupplyRequest) {
        Page<MaterialSupply> page = new Page<MaterialSupply>(refundSupplyRequest.getPage(),refundSupplyRequest.getPageSize());
        SearchRefundParams searchRefundParams = new SearchRefundParams(refundSupplyRequest.getOrgId(), refundSupplyRequest.getCustomerCode());
        page = materialSupplyFormRepository.searchRefundSupplyPage(page, searchRefundParams);
        Page<RefundMaterialPageMain> resultPage = materialSupplyFormConverter.convertMaterialSupplyPage2RefundMaterialPageMain(page);
        if (CollectionUtils.isEmpty(resultPage.getRecords())) {
            return Response.success();
        }
        // 所有的原始出库单
        Set<String> relationSupplyNo = resultPage.getRecords().stream().map(RefundMaterialPageMain::getRelationSupplyNo).collect(Collectors.toSet());
        List<MaterialSupply> materialSupplies = materialSupplyFormRepository.searchSupplyBySupplyNos(relationSupplyNo);
        Map<String, MaterialSupply> supplyMap = materialSupplies.stream().collect(Collectors.toMap(MaterialSupply::getSupplyNo, Function.identity()));
        resultPage.getRecords().forEach(item -> {
            MaterialSupply materialSupply = supplyMap.get(item.getRelationSupplyNo());
            item.setRelationSupplyTime(materialSupply.getSupplyTime());
        });
        return Response.success(resultPage);
    }

    /**
     * 退库单详情
     *
     * @param request
     * @return
     */
    @Override
    public Response<List<RefundMaterialPageItem>> searchRefundSupplyDetails(RefundSupplyDetailRequest request) {
        List<MaterialSupplyDetail> supplyDetails = materialSupplyFormDetailRepository.searchRefundSupplyDetails(request.getSupplyNo(),request.getMaterialName());
        List<RefundMaterialPageItem> refundMaterialPageItems = materialSupplyDetailFormConverter.convertSupplyDetails2RefundItems(supplyDetails);
        String relationSupplyNo = materialSupplyFormRepository.searchRelationSupplyNoBySupplyNo(request.getSupplyNo());
        // 通过原始单号查询详情
        List<MaterialSupplyDetail> materialSupplyDetails = materialSupplyFormDetailRepository.searchSupplyDetailsBySupplyNo(relationSupplyNo);
        Map<String, MaterialSupplyDetail> assistNumberMap = materialSupplyDetails.stream().collect(Collectors.toMap(item -> item.getMaterialCode() + item.getBatchNo(), Function.identity()));
        Set<String> materialCodes = refundMaterialPageItems.stream().map(RefundMaterialPageItem::getMaterialCode).collect(Collectors.toSet());
        Response<List<TbMaterialDto>> materialDetail = mdmUtil.getMaterialDetail(materialCodes);
        List<TbMaterialDto> data = materialDetail.getData();
        Map<String, TbMaterialDto> modelMap = data.stream().collect(Collectors.toMap(TbMaterialDto::getMaterialCode, Function.identity()));

        refundMaterialPageItems.forEach(item -> {
            MaterialSupplyDetail materialSupplyDetail = assistNumberMap.get(item.getMaterialCode() + item.getBatchNo());
            // 出库辅数量
            item.setOutNumber(materialSupplyDetail.getAssistNumber());
            // 已退辅数量取绝对值 本次退库辅数量
            BigDecimal returnedNumber = item.getReturnedNumber();
            item.setReturnNumber(returnedNumber != null ? returnedNumber.abs() : null);
            item.setReturnedNumber(materialSupplyDetail.getReturnNumber());
            TbMaterialDto tbMaterialDto = modelMap.get(item.getMaterialCode());
            // 规格
            item.setMaterialSpecification(tbMaterialDto.getSpecification());
        });
        return Response.success(refundMaterialPageItems);
    }

    /**
     * 自制单出库库存校验
     *
     * @param outDto
     * @return
     */
    @Override
    public Response<Boolean> makeOutCheckInventory(ScmOutDto outDto) {
        // 过滤出出库辅数量为0的
        List<OutBodyDto> zero = outDto.getOutBody().stream()
                .filter(item -> item.getNassistnum().compareTo(BigDecimal.ZERO) == 0)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(zero)) {
            String zeroMaterial = zero.stream().map(OutBodyDto::getMaterialName).collect(Collectors.joining(","));
            log.error(DateUtil.now() + ":出库单中【"+zeroMaterial+"】,出库数量为0,请检查");
            return Response.fail(ResultCode.OUT_NUMBER_IS_ZERO.getCode(),String.format(ResultCode.OUT_NUMBER_IS_ZERO.getMsg(),zeroMaterial));
        }

        // 获取物料在ncc中的库存数量
        for (OutBodyDto outBodyDto : outDto.getOutBody()) {
            if (StringUtils.isBlank(outBodyDto.getMaterialCode()) || StringUtils.isBlank(outBodyDto.getBatchCode())) {
                continue;
            }
            // 应/实发主/辅数量
            Integer nshouldnum = outBodyDto.getNshouldnum().intValue();
            Integer nnum = outBodyDto.getNnum().intValue();
            BigDecimal materialAssistCountApply = outBodyDto.getMaterialAssistCountApply();
            BigDecimal nassistnum = outBodyDto.getNassistnum();
            // 超量
            if (nnum > nshouldnum || materialAssistCountApply.compareTo(nassistnum) < 0) {
                return Response.fail(500,"出库单中【"+outBodyDto.getMaterialName()+"】,不可超量出库,请检查");
            }
            // 批号实际库存校验
            NccInventoryDto nccInventoryDto = new NccInventoryDto(outBodyDto.getMaterialCode(), outBodyDto.getBatchCode(), outDto.getStorDocId());
            Response<List<NccInventoryResultDto>> result = nccHttpServer.getInventory(nccInventoryDto);
            // 获取是否成功
            if (!result.isSuccess()) {
                return Response.fail(result.getCode(),result.getMsg());
            }
            List<NccInventoryResultDto> data = result.getData();
            if (CollectionUtils.isEmpty(data)) {
                return Response.fail(ResultCode.MATERIAL_NOT_HAS_BATCH.getCode(),String.format(ResultCode.MATERIAL_NOT_HAS_BATCH.getMsg(),outBodyDto.getMaterialName(),outDto.getStorDocName(),outBodyDto.getBatchCode()));
            }
            // 库存量
            BigDecimal nonhandastnum = data.get(0).getNonhandastnum();
            int flag = nonhandastnum.compareTo(nassistnum);
            // 库存量 小于 申请量
            if (flag < 0) {
                return Response.fail(500,"物料：" + outBodyDto.getMaterialCode() + "辅数量库存仅剩：" + nonhandastnum + "不满足出库数量");
            }
        }
        return Response.success(true);
    }

    /**
     * 自制单据出库
     *
     * @param scmOutDto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Response<?> materialMakeOut(ScmOutDto scmOutDto) {
        OutDto outDto = materialSupplyFormConverter.convertScmOutDto2OutDto(scmOutDto);
        String systemCode = SystemEnum.MAKE.getSystemCode();
        String realName = LoginUserInfoUtil.getUserUserName();
        // 出库id
        String outId = outIdGeneratorService.generateId();
        outDto.setOutId(outId);
        log.info("SCM手动制单，开始调用ncc方出库，调用参数：{}",JSONUtil.toJsonStr(outDto));
        // 调用ncc出库
        Response<NccResponseDto> nccResult = wareHousing(outDto);
        if (!Objects.equals(nccResult.getCode(),Response.success().getCode())) {
            return nccResult;
        }
        log.info("SCM手动制单，调用NCC方出库结束，响应参数：{}",JSONUtil.toJsonStr(nccResult));
        notifyUtil.send("SCM手动制单，调用NCC方出库结束，响应参数：" + JSONUtil.toJsonStr(nccResult));
        NccResponseDto nccResultData = nccResult.getData();
        // 生成出库记录
        MaterialSupply materialSupply = buildMaterialSupply(nccResultData,outDto, realName);
        List<MaterialSupplyDetail> materialSupplyDetails = buildMaterialSupplyDetail(materialSupply.getItemId(),nccResultData,outDto);
        materialSupplyFormRepository.saveMaterialSupply(materialSupply);
        materialSupplyFormDetailRepository.saveMaterialDetailsBatch(materialSupplyDetails);
        return Response.success(outId);
    }

    /**
     * 作废
     *
     * @param cancelOmsOrderDetailRequest
     * @return
     */
    @Override
    @DS("oms")
    @Transactional(rollbackFor = Exception.class)
    public Response<String> cancelOmsOrderDetail(CancelOmsOrderDetailRequest cancelOmsOrderDetailRequest) {
        orderMaterialRepository.cancelOmsOrderDetail(cancelOmsOrderDetailRequest.getItemIds());
        return Response.success();
    }

    /**
     * 增加相同行
     *
     * @param copyMaterialInfoRequest
     * @return
     */
    @Override
    public Response addRow(CopyMaterialInfoRequest copyMaterialInfoRequest) {
        String materialCode = copyMaterialInfoRequest.getMaterialCode();
        String orderCode = copyMaterialInfoRequest.getOrderCode();
        String storDocId = copyMaterialInfoRequest.getStorDocId();
        ImportApplyFormRequest importApplyFormRequest = new ImportApplyFormRequest();
        importApplyFormRequest.setApplyCode(orderCode);
        importApplyFormRequest.setStorDocId(storDocId);
        Response<List> importResponse = materialApplyFormService.importApplyForm(importApplyFormRequest);
        if (!importResponse.isSuccess()) {
            return Response.fail(importResponse.getCode(),importResponse.getMsg());
        }
        if (importResponse.getData().get(0) instanceof NccMaterialDto) {
            List<NccMaterialDto> data = importResponse.getData();
            return Response.success(data.stream().filter(item -> item.getInvcode().equals(materialCode)).findFirst().get());
        } else {
            List<ImportMaterialDTO> data = importResponse.getData();
            return Response.success(data.stream().filter(item -> item.getInvcode().equals(materialCode)).findFirst().get());
        }
    }

    /**
     * 构建出库单详情
     * @param supplyId
     * @param nccResponseDto
     * @param outDto
     * @return
     */
    private List<MaterialSupplyDetail> buildMaterialSupplyDetail(String supplyId, NccResponseDto nccResponseDto, OutDto outDto) {
        Map<String, OutBodyDto> outBodyDtoMap = outDto.getOutBody().stream().collect(Collectors.toMap(item ->item.getRowNo() + item.getMaterialCode() + item.getBatchCode(), Function.identity()));
        Map<String, String> remarkMap = outDto.getOutBody().stream()
                .collect(Collectors.toMap(
                        item ->item.getRowNo() + item.getMaterialCode() + item.getBatchCode(),
                        remark -> Optional.ofNullable(remark.getRemark()).orElse(StringUtils.EMPTY)));
        List<MaterialSupplyDetail> list = new ArrayList<>();
        for (int i = 0; i < nccResponseDto.getBodyDtoList().size(); i++) {
            NccBodyDto item = nccResponseDto.getBodyDtoList().get(i);
            OutBodyDto outBodyDto = outBodyDtoMap.get((i + 1) + item.getMaterialCode() + item.getBatchNo());

            MaterialSupplyDetail detail = new MaterialSupplyDetail();
            detail.setItemId(CommonConstant.STOCK_OUT_DETAIL_ID + IdWorker.getId());
            detail.setOrderCode("手工制单");
            detail.setSupplyId(supplyId);
            detail.setSupplyNo(outDto.getOutId());
            // 申领单详细id
            detail.setMaterialCode(outBodyDto.getMaterialCode());
            detail.setMaterialName(outBodyDto.getMaterialName());
            detail.setBatchNo(item.getBatchNo());
            detail.setInvalidTime(item.getInvalidTime());
            detail.setPrimaryNumber(item.getPrimaryNumber().intValue());
            detail.setAssistNumber(new BigDecimal(item.getAssistNumber()));
            detail.setRemark(remarkMap.get((i + 1) + item.getMaterialCode() + item.getBatchNo()));
            // 新增的字段
            detail.setReturnNumber(new BigDecimal(0));
            detail.setManufacturer(outBodyDto.getManufacturer());
            detail.setRegistrationNo(outBodyDto.getRegistrationNo());
            detail.setRegistrationName(outBodyDto.getRegistrationName());
            detail.setRegistrant(outBodyDto.getRegistrantCode());
            detail.setRegistrantCode(outBodyDto.getRegistrant());
            detail.setStorageRequirement(outBodyDto.getStorageRequirement());
            detail.setMaterialSpecification(outBodyDto.getMaterialSpecification());
            detail.setRackName(outBodyDto.getRackName());
            list.add(detail);
        }
        return list;
    }

    private MaterialSupply buildMaterialSupply(NccResponseDto responseDto,OutDto outDto,String realName) {
        MaterialSupply materialSupply = new MaterialSupply();
        String itemId = CommonConstant.STOCK_OUT_ID + IdWorker.getId();
        materialSupply.setItemId(itemId);
        materialSupply.setOrderCode("手工制单");
        materialSupply.setSupplyNo(outDto.getOutId());
        materialSupply.setSupplyCustomerId(outDto.getCustomerId());
        materialSupply.setSupplyCustomerName(outDto.getCustomerName());
        materialSupply.setSupplyPerson(realName);
        DateTime time = DateUtil.parse(responseDto.getHeadDto().getSupplyTime(), "yyyy-MM-dd HH:mm:ss");
        materialSupply.setSupplyTime(time);
        materialSupply.setStatus(responseDto.getHeadDto().getStatus());
        materialSupply.setNccSupplyNo(responseDto.getHeadDto().getNccSupplyNo());
        materialSupply.setSupplyType(outDto.getTypeName());
        materialSupply.setSupplyTypeId(outDto.getTypeId());
        materialSupply.setType(SupplyTypeEnum.OUT.getCode());
        materialSupply.setSupplyWarehouseId(outDto.getStorDocId());
        materialSupply.setSupplyWarehouseOrgId(outDto.getOrgId());
        materialSupply.setSupplyWarehouseName(outDto.getStorDocName());
        materialSupply.setSupplyDeptId(outDto.getDeptId());
        materialSupply.setSupplyDeptVid(outDto.getDeptvId());
        materialSupply.setSupplyDeptName(outDto.getDeptName());
        materialSupply.setPrepareTime(outDto.getMakeDate());
        return materialSupply;
    }

    /**
     * 投递到mq中 其他系统自行监听
     * @param notifySystemDTO
     */
    private void notifySystem(NotifySystemDTO notifySystemDTO) {
        // 出库单
        MaterialSupply supply = notifySystemDTO.getSupply();
        List<MaterialSupplyDetail> supplyDetail = notifySystemDTO.getSupplyDetail();
        Order order = notifySystemDTO.getOrder();
        Integer applyStatus = notifySystemDTO.getApplyStatus();
        String systemCode = notifySystemDTO.getSystemCode();

        OutInfoMessageDTO outInfoMessageDTO = new OutInfoMessageDTO();
        outInfoMessageDTO.setSupplyNo(supply.getSupplyNo()).setSupplyTime(supply.getSupplyTime())
                .setIsAllOut(applyStatus == 0 ? Boolean.FALSE : Boolean.TRUE)
                .setOrderCode(order.getOrderCode());
        List<OutInfoMessageDTO.OutMaterialInfo> outMaterialInfos = materialSupplyDetailFormConverter.convertMaterialSupplyDetails2OutMaterialInfo(supplyDetail);
        outInfoMessageDTO.setMaterialInfos(outMaterialInfos);
        log.info("----出库通知投递mq开始----,参数：{}", JSONUtil.toJsonStr(outInfoMessageDTO));
        String routing = scmRabbitmqConfig.getSystemRoutingBySystemCode(systemCode);
        rabbitTemplate.convertAndSend(SCMRabbitmqConfig.OUT_INFO_EXCHANGE,routing, JSONUtil.toJsonStr(outInfoMessageDTO));
        log.info("----出库通知投递完成----");
    }

    /**
     * 插入 出库单详情
     * @param supplyId 出库单id
     * @param outDto 入库信息
     * @param nccResponseDto 入库信息
     * @return 是否成功
     */
    private Response<List<MaterialSupplyDetail>> insertMaterialSupplyDetail(String supplyId, OutDto outDto, NccResponseDto nccResponseDto) {
        return materialSupplyFormDetailRepository.insertMaterialSupplyDetail(supplyId,outDto, nccResponseDto);
    }

    /**
     * dubbo调用ncc方出库
     * @param outDto 出库对象
     * @return 出库信息
     */
    private Response<NccResponseDto> wareHousing(OutDto outDto){
        try {
            return nccHttpServer.wareHousing(outDto);
        }catch (Exception e){
            log.error("调用ncc服务进行出库时发生异常",e);
            return Response.fail(ResultCode.NCC_INVOKE_FAIL);
        }
    }

    /**
     * 插入 出库单
     * @param outDto 入库信息
     * @param responseDto 入库信息
     * @return 是否成功
     */
    private Response<MaterialSupply> insertMaterialSupply(String realName, OutDto outDto, NccResponseDto responseDto, Integer type, String relationSupplyNo, Long accountId){

        return materialSupplyFormRepository.insertMaterialSupply(realName,outDto,responseDto,type,relationSupplyNo, accountId);
    }

    /**
     * 根据申领单id获取物料已出库的所有主数量
     * @param orderCode 订单编码
     * @return key 物料code value 已出数量
     */
    private Map<String, BigDecimal> getOutTotal(String orderCode) {
        // 根据订单编码查询订单下的所有出库单
        List<MaterialSupplyDetail> detailList = materialSupplyFormDetailRepository.searchSupplyDetailsByOrderCode(orderCode);
        if (CollectionUtils.isEmpty(detailList)) {
            return Collections.emptyMap();
        }
        // 所有出库单详情已出库辅数量
        return detailList.stream().collect(
                Collectors.toMap(itemA -> itemA.getItemId() + itemA.getMaterialCode(),
                        MaterialSupplyDetail::getAssistNumber));
    }

    /**
     * 更换为mq
     * 操作数据库 更改申领单状态
     * @param orderCode 订单编码
     * @param orderMaterials 订单详情
     * @param supply 出库单
     * @param supplyDetails 出库单详情
     * @param outMaterialStatus 出库状态
     */
    private void updateApplyFormInfo(String orderCode,List<OrderMaterial> orderMaterials , MaterialSupply supply, List<MaterialSupplyDetail> supplyDetails, OutMaterialStatus outMaterialStatus) {
        // key ==> 物料编码 + 批次号  value => 出库单详情
        Map<String, MaterialSupplyDetail> supplyDetailMap = supplyDetails.stream().collect(Collectors.toMap(material -> material.getMaterialCode() + material.getApplyDetailId(), Function.identity()));
        for (OrderMaterial orderMaterial : orderMaterials) {
            List<MaterialSupplyDetail> materialSupplyDetailList = supplyDetailMap.entrySet().stream()
                    .filter(entry -> entry.getKey().startsWith(orderMaterial.getMaterialSn()))
                    .map(Map.Entry::getValue).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(materialSupplyDetailList)) {
                continue;
            }
            BigDecimal assistNumber = new BigDecimal(0.0);
            for (MaterialSupplyDetail tbMaterialSupplyDetail : materialSupplyDetailList) {
                assistNumber = assistNumber.add(tbMaterialSupplyDetail.getAssistNumber());
            }
            // 更新后的辅数量 orderMaterial.getOutNumber() 不会为null 数据库限制非空
            BigDecimal materialAssistCountStockOutNum = assistNumber.add(orderMaterial.getOutNumber());
            orderMaterial.setOutNumber(materialAssistCountStockOutNum);
        }
        // TODO 记录 失败重试 记录订单编号 出库单号 定时任务扫单处理
        // 修改订单 和 订单详情
        orderRepository.updateSupplyStatus(orderCode, OrderSupplyStatusEnum.getSupplyType(outMaterialStatus));
        orderMaterialRepository.updateBatch(orderMaterials);
    }
}




