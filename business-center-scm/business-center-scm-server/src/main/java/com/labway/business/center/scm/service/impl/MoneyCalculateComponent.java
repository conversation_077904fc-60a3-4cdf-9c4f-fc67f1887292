package com.labway.business.center.scm.service.impl;

import com.labway.business.center.scm.constants.IsGiftEnum;
import com.labway.business.center.scm.dto.MoneyCalculateResponse;
import com.labway.business.center.scm.request.purchasenew.MoneyCalculateRequest;
import com.swak.frame.dto.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 2023/11/07 09:10
 **/
@Slf4j
@Component
public class MoneyCalculateComponent {

    /**
     * 金额计算
     * @param moneyCalculateRequest
     * @return
     */
    public Response calculate(MoneyCalculateRequest moneyCalculateRequest) {
        List<MoneyCalculateResponse> responseList = new ArrayList<>();
        Response<String> checkParams = checkParams(moneyCalculateRequest);
        if (!checkParams.isSuccess()) {
            return checkParams;
        }
        // 计算
        if (Objects.nonNull(moneyCalculateRequest.getOrderTax())) {
            moneyCalculateRequest.getItems().forEach(item -> item.setTax(moneyCalculateRequest.getOrderTax()));
        }
        for (MoneyCalculateRequest.Item item : moneyCalculateRequest.getItems()) {
            MoneyCalculateResponse response = new MoneyCalculateResponse();
            response.setId(item.getId());
            // 无税单价 = 含税单价 / (1 + 税率) 保留⑧位 四舍五入
            // 1 + 税率
            BigDecimal count = item.getTax().multiply(new BigDecimal("0.01")).add(new BigDecimal("1"));
            BigDecimal noTaxPrice = item.getTaxPrice().multiply(new BigDecimal("1000000000000")).divide(count,RoundingMode.HALF_UP).divide(new BigDecimal("1000000000000")).setScale(10,RoundingMode.HALF_UP);
            BigDecimal temp = noTaxPrice;
            DecimalFormat df = new DecimalFormat("0.00000000");
            response.setNoTaxPrice(df.format(noTaxPrice.setScale(10,RoundingMode.HALF_UP)));
            response.setTaxPrice(item.getTaxPrice().toString());
            if (item.getIsGift().equals(IsGiftEnum.YES.getCode())) {
                buildGiftResponse(response,item);
            }else {
                buildNotGiftResponse(response,temp,item,count);
            }

            responseList.add(response);
        }
        return Response.success(responseList);
    }

    private void buildNotGiftResponse(MoneyCalculateResponse response,BigDecimal noTaxPrice, MoneyCalculateRequest.Item item,BigDecimal count) {
        BigDecimal oneHundred = new BigDecimal(100);
        BigDecimal thousand = new BigDecimal(10000);
        DecimalFormat df8 = new DecimalFormat("0.00000000");
        DecimalFormat df2 = new DecimalFormat("0.00");
        // 折扣
        if (Objects.isNull(item.getDiscount())) {
            // 折扣 = 含税净价 / 含税单价 保留8位小数 四舍五入
            BigDecimal discount = item.getTaxNetPrice().multiply(new BigDecimal("100000000000000000")).divide(item.getTaxPrice(), RoundingMode.HALF_UP).divide(new BigDecimal("100000000000000000")).setScale(10, RoundingMode.HALF_UP);
            String discountString = df8.format(discount.multiply(oneHundred));
            response.setDiscount(discountString);
            // 含税净价 = 输入的含税净价
            response.setTaxNetPrice(df8.format(item.getTaxNetPrice()));
        } else {
            response.setDiscount(item.getDiscount().toString());
            String discountString = df8.format((item.getDiscount()));
            response.setDiscount(discountString);
            // 含税净价 = 含税单价 * 折扣
            BigDecimal taxNetPrice = item.getTaxPrice().multiply(item.getDiscount()).multiply(new BigDecimal("100000000000000000")).divide(oneHundred,RoundingMode.HALF_UP).divide(new BigDecimal("100000000000000000"));
            response.setTaxNetPrice(df8.format(taxNetPrice));
        }
        BigDecimal discount = new BigDecimal(response.getDiscount()).divide(oneHundred);
        // 无税金额 = 无税单价 * 数量 * 折扣 保留两位小数 四舍五入
        BigDecimal noTaxMoney = noTaxPrice.multiply(oneHundred).multiply(item.getAssistNumber())
                .multiply(discount).divide(oneHundred).setScale(2,RoundingMode.HALF_UP);
        response.setNoTaxMoney(df2.format(noTaxMoney));
        // 价税合计 = 无税单价 * 数量 * (1+税率) * 折扣
        BigDecimal total = noTaxPrice
                .multiply(item.getAssistNumber())
                .multiply(count)
                .multiply(discount)
                .setScale(2,RoundingMode.HALF_UP);
        response.setTotal(df2.format(total));
        // 记成本金额 = 无税金额
        response.setNoTaxMoneyBak(df2.format(noTaxMoney));
        // 税率
        response.setTax(df8.format(item.getTax()));
        // 税额 = 无税单价 * 税率 保留两位小数 四舍五入 或 税额 = 价税合计 - 无税金额
        BigDecimal taxMoney = new BigDecimal(response.getTotal()).subtract(new BigDecimal(response.getNoTaxMoney()));
        response.setTaxMoney(df2.format(taxMoney));
        // 无税净价 = 无税单价 * 折扣 保留⑧位小数 四舍五入
        BigDecimal noTaxNetPrice = noTaxPrice.multiply(discount)
                        .setScale(8,RoundingMode.HALF_UP);
        response.setNoTaxNetPrice(df8.format(noTaxNetPrice));
    }

    private void buildGiftResponse(MoneyCalculateResponse response, MoneyCalculateRequest.Item item) {
        response.setDiscount("0.00");
        response.setNoTaxMoney("0.00");
        response.setTotal("0.00");
        response.setNoTaxMoneyBak("0.00");
        response.setTax("0.00");
        response.setTaxMoney("0.00");
        response.setNoTaxNetPrice("0.00");
        response.setTaxNetPrice("0.00");
    }

    private Response<String> checkParams(MoneyCalculateRequest moneyCalculateRequest) {
        for (MoneyCalculateRequest.Item item : moneyCalculateRequest.getItems()) {
            if (Objects.nonNull(item.getTax()) && item.getTax().compareTo(new BigDecimal(100)) > 0) {
                return Response.fail(500,"税率不能超过100");
            }
            if (Objects.isNull(item.getTaxPrice())  || item.getTaxPrice().compareTo(new BigDecimal(0)) <= 0) {
                return Response.fail(500,"含税单价不能为空或不能小于等于0");
            }
            if (Objects.isNull(item.getDiscount()) && Objects.isNull(item.getTaxNetPrice())) {
                return Response.fail(500,"折扣和含税净价不能同时为空");
            }
            if (Objects.isNull(item.getAssistNumber()) || item.getAssistNumber().compareTo(new BigDecimal(0)) <= 0) {
                return Response.fail(500,"数量不能为空或为0");
            }
        }
        return Response.success();
    }
}