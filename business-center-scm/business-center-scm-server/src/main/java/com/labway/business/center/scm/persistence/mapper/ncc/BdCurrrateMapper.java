package com.labway.business.center.scm.persistence.mapper.ncc;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.labway.business.center.core.injector.ExtBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * 汇率
 *
 * <AUTHOR>
 * @version 2023/11/01 11:44
 **/
@DS("ncc")
public interface BdCurrrateMapper extends ExtBaseMapper {
    List<BigDecimal> searchCurrTypesInf(@Param("pkCurrtype") String pkCurrType);
}