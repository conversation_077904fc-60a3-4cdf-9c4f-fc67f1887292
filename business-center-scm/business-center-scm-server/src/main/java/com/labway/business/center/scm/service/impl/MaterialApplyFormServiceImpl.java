package com.labway.business.center.scm.service.impl;

import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import com.labway.business.center.core.enums.ResultCode;
import com.labway.business.center.core.util.BeanCopyUtils;
import com.labway.business.center.scm.converter.*;
import com.labway.business.center.scm.dto.material.ApplyFormMaterialDTO;
import com.labway.business.center.scm.dto.material.ApplyFormUnStockOutDTO;
import com.labway.business.center.scm.dto.material.ImportMaterialDTO;
import com.labway.business.center.scm.dto.material.MaterialSupplyDTO;
import com.labway.business.center.scm.model.request.material.ImportApplyFormRequest;
import com.labway.business.center.scm.model.request.material.MaterialApplyFormRequest;
import com.labway.business.center.scm.persistence.entity.MaterialApplyForm;
import com.labway.business.center.scm.persistence.entity.MaterialApplyFormDetail;
import com.labway.business.center.scm.persistence.entity.MaterialSupply;
import com.labway.business.center.scm.persistence.entity.MaterialSupplyDetail;
import com.labway.business.center.scm.persistence.entity.oms.Order;
import com.labway.business.center.scm.persistence.entity.oms.OrderMaterial;
import com.labway.business.center.scm.repository.*;
import com.labway.business.center.scm.request.NccMaterialSearchRequest;
import com.labway.business.center.scm.request.ScmRejectOutApplyFormRequest;
import com.labway.business.center.scm.service.MaterialApplyFormService;
import com.labway.business.center.scm.persistence.params.DataAssemblyDTO;
import com.labway.business.center.scm.util.MdmUtil;
import com.labway.business.center.third.ncc.dto.material.TbMaterialDto;
import com.labway.business.center.third.ncc.dto.ncc.NccMaterial;
import com.labway.business.center.third.ncc.dto.ncc.NccMaterialDto;
import com.labway.business.center.third.ncc.service.NccMaterialService;
import com.labway.business.center.third.ncc.vo.NccMaterialRequestVo;
import com.labway.oms.dto.manage.MaterialDubboSearchDTO;
import com.labway.oms.request.manage.MaterialDubboSearchRequest;
import com.labway.oms.request.web.RejectOutApplyFormRequest;
import com.labway.oms.service.MaterialService;
import com.labway.oms.service.TbOrderService;
import com.swak.frame.dto.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
* <AUTHOR> Tianhao
* @description 针对表【tb_material_apply_form(物料申领单信息表)】的数据库操作Service实现
* @createDate 2023-08-10 14:18:43
*/
@Slf4j
@DubboService
public class MaterialApplyFormServiceImpl implements MaterialApplyFormService {

    @Resource
    private MaterialApplyFormRepository materialApplyFormRepository;
    @Resource
    private MaterialApplyFormDetailRepository materialApplyFormDetailRepository;
    @Resource
    private MaterialSupplyFormRepository materialSupplyFormRepository;
    @Resource
    private MaterialSupplyFormDetailRepository materialSupplyFormDetailRepository;
    @Resource
    private OrderRepository orderRepository;
    @Resource
    private OrderMaterialRepository orderMaterialRepository;

    @Resource
    private MaterialApplyFormConverter materialApplyFormConverter;
    @Resource
    private MaterialApplyFormDetailConverter materialApplyFormDetailConverter;
    @Resource
    private MaterialSupplyFormConverter materialSupplyFormConverter;
    @Resource
    private OrderConverter orderConverter;
    @Resource
    private OrderMaterialConverter orderMaterialConverter;

    @Resource
    private MdmUtil mdmUtil;

    @DubboReference
    private NccMaterialService nccMaterialService;
    @DubboReference
    private TbOrderService tbOrderService;
    @DubboReference
    private MaterialService materialService;

    /**
     * 查询未出库的申请单
     *
     * @param queryParam
     * @return
     */
    @Override
    public Response<List<ApplyFormUnStockOutDTO>> searchUnStockApply(MaterialApplyFormRequest queryParam) {
        // 查询OMS未出库完成的订单
        List<Order> orders = orderRepository.searchNotOutedOrder(queryParam.getCustomerCode(),queryParam.getApplyTimeBegin(),queryParam.getApplyTimeEnd(),queryParam.getOrgCode());
        if (CollectionUtils.isEmpty(orders)) {
            return Response.success();
        }

//        List<ApplyFormUnStockOutDTO> applyForms = orderConverter.convertOrder2ApplyFormUnStockOutDTOList(orders);
        List<ApplyFormUnStockOutDTO> applyFormUnStockOutDTOS = new ArrayList<>();
        orders.forEach(e->{
            ApplyFormUnStockOutDTO temp = new ApplyFormUnStockOutDTO();
            temp.setApplyTime(e.getCreateTime()==null?null:Date.from(e.getCreateTime().atZone(ZoneId.systemDefault()).toInstant()));
            temp.setApplyUserId(String.valueOf(e.getAccountId()));
            temp.setAppleUserName(e.getCreator());
            temp.setApplyOrgCode(e.getOrgCode());
            temp.setOrderCode(e.getOrderCode());
            temp.setApplyOrgName(e.getOrgName());
            temp.setApplyCustomerPk(e.getCustomerCode());
            temp.setApplyCustomerName(e.getCustomerName());
            temp.setApplyStatus(e.getSupplyStatus());
            applyFormUnStockOutDTOS.add(temp);
        });


        return Response.success(applyFormUnStockOutDTOS);
    }

    /**
     * 根据申领单号查询申领单详情
     *
     * @param orderCode
     * @return
     */
    @Override
    public Response<List<ApplyFormMaterialDTO>> searchDetailByApplyCode(String orderCode) {
        if (StringUtils.isBlank(orderCode)) {
            return Response.fail(ResultCode.PARAMS_NOT_VALIDATE);
        }
        List<OrderMaterial> orderMaterials = orderMaterialRepository.searchDetailByOrderCode(orderCode);
        Map<String, OrderMaterial> orderMaterialMap = orderMaterials.stream().collect(Collectors.toMap(OrderMaterial::getMaterialSn, Function.identity()));
        Set<String> materialCodes = orderMaterials.stream().map(OrderMaterial::getMaterialSn).collect(Collectors.toSet());
        List<ApplyFormMaterialDTO> applyFormMaterials = orderMaterialConverter.convertOrderMaterials2ApplyFormMaterialDtoList(orderMaterials);
        Response<List<TbMaterialDto>> mdmResponse = Response.success();
        try {
            mdmResponse = mdmUtil.getMaterialDetail(materialCodes);
            if (!mdmResponse.isSuccess()) {
                return Response.fail(ResultCode.MDM_INVOKE_FAIL);
            }
        } catch (Exception e) {
            log.error("调用主数据获取【{}】",materialCodes);
            return Response.fail(ResultCode.MDM_INVOKE_FAIL);
        }
        List<TbMaterialDto> data = mdmResponse.getData();
        Map<String, TbMaterialDto> materialDtoMap = data.stream().collect(Collectors.toMap(TbMaterialDto::getMaterialCode, Function.identity()));
        for (ApplyFormMaterialDTO applyFormMaterial : applyFormMaterials) {
            TbMaterialDto tbMaterialDto = materialDtoMap.get(applyFormMaterial.getMaterialCode());
            if (Objects.isNull(tbMaterialDto)) {
                continue;
            }
            // 申请主数量
            Integer materialPrimaryNumber = mdmUtil.getMaterialPrimaryNumber(tbMaterialDto.getUnitTransRate(), applyFormMaterial.getMaterialAssistCountApply());
            applyFormMaterial.setMaterialCountApply(materialPrimaryNumber);
            // 出库主数量
            Integer materialCountStockOut = mdmUtil.getMaterialPrimaryNumber(tbMaterialDto.getUnitTransRate(), applyFormMaterial.getMaterialAssistCountStockOut());
            applyFormMaterial.setMaterialCountStockOut(materialCountStockOut);
            applyFormMaterial.setManufacturer(tbMaterialDto.getManufacturer());
            applyFormMaterial.setMaterialAssistUnit(tbMaterialDto.getSecondaryUnit());
            applyFormMaterial.setMaterialUnit(tbMaterialDto.getPrimaryUnit());
            OrderMaterial orderMaterial = orderMaterialMap.get(applyFormMaterial.getMaterialCode());
            if (orderMaterial.getOutNumber().equals(new BigDecimal("0.0000"))) {
                applyFormMaterial.setStatus("未出库");
            } else if (orderMaterial.getOutNumber().compareTo(orderMaterial.getNumber()) == 0) {
                applyFormMaterial.setStatus("已出库");
            } else if (orderMaterial.getOutNumber().compareTo(orderMaterial.getNumber()) < 0) {
                applyFormMaterial.setStatus("部分出库");
            }
        }
        return Response.success(applyFormMaterials);
    }

    /**
     * 导入申领单
     *
     * @param importApplyFormRequest
     * @return
     */
    @Override
    public Response importApplyForm(ImportApplyFormRequest importApplyFormRequest) {
        String applyCode = importApplyFormRequest.getApplyCode();

        // 通过申领单编码查询申领单详情
        List<OrderMaterial> orderMaterials = orderMaterialRepository.searchDetailByOrderCode(applyCode);
        List<ApplyFormMaterialDTO> applyFormMaterials = orderMaterialConverter.convertOrderMaterials2ApplyFormMaterialDtoList(orderMaterials);

        // 所有物料的编码
        List<String> materCodeList =
                applyFormMaterials.stream().map(ApplyFormMaterialDTO::getMaterialCode).collect(Collectors.toList());
        // 申领单明细id
        List<String> applyFormDetailItemIds = applyFormMaterials.stream()
                .map(ApplyFormMaterialDTO::getItemId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(applyFormDetailItemIds)) {
            return Response.fail(ResultCode.APPLY_FORM_NULL);
        }
        // 根据申领单详情查询出库单详情
        List<MaterialSupplyDetail> detailList = materialSupplyFormDetailRepository.searchSupplyDetailsByOrderCode(applyCode);

        // 所有物料已出库主数量
        Map<String, Integer> primaryNumber = detailList.stream().collect(
                Collectors.toMap(itemA -> itemA.getItemId() + itemA.getMaterialCode(),
                        MaterialSupplyDetail::getPrimaryNumber));
        // 所有物料已出库辅数量
        Map<String, BigDecimal> assistNumber = detailList.stream().collect(
                Collectors.toMap(itemA -> itemA.getItemId() + itemA.getMaterialCode(),
                        MaterialSupplyDetail::getAssistNumber));
        // 获取物料的详情
        Response<List<MaterialDubboSearchDTO>> materialDetails = getMaterialDetail(materCodeList);
        if (!materialDetails.isSuccess()) {
            return materialDetails;
        }
        // 物料的相关信息 转map key 物料编码 value 物料信息
        Map<String, MaterialDubboSearchDTO> materialMap = materialDetails.getData().stream()
                .collect(Collectors.toMap(MaterialDubboSearchDTO::getMaterialCode, item -> item));


        // 处理主数量为空的
        applyFormMaterials.forEach(item -> {
            MaterialDubboSearchDTO materialDubboSearchDTO = materialMap.get(item.getMaterialCode());
            Integer materialPrimaryNumber = mdmUtil.getMaterialPrimaryNumber(materialDubboSearchDTO.getUnitTransRate(), item.getMaterialAssistCountApply());
            item.setMaterialCountApply(materialPrimaryNumber);
        });

        // 根据所有的物料id去ncc查询对应的信息
        if (CollectionUtils.isEmpty(materCodeList)) {
            log.error("编号：{}，该申请单没有申请材料", applyCode);
            return Response.fail(ResultCode.APPLY_NOT_HAS_DETAIL);
        }
        // 组装请求对象
        NccMaterialRequestVo nccMaterialRequestVo = new NccMaterialRequestVo(materCodeList,importApplyFormRequest.getStorDocId());
        log.info("开始调用ncc获取编号{}的申领单物料详情", applyCode);
        Response<List<NccMaterialDto>> nccMaterial = getNccMaterial(nccMaterialRequestVo);
        if (!nccMaterial.isSuccess()) {
            return Response.success(getNotInListMaterialDetail(materCodeList,applyFormMaterials));
        }
        log.info("从ncc获取物料详情获取完成,{}", JSONUtil.toJsonStr(nccMaterial));
        // 通过申领单的编码查询所有的出库单
        List<MaterialSupply> materialSupplies = materialSupplyFormRepository.searchSupplyListByApplyFormCode(applyCode);
        List<MaterialSupplyDTO> materialSupplyDtos = materialSupplyFormConverter.convertMaterialSupplyList2DTO(materialSupplies);
        DataAssemblyDTO dataAssemblyDto = new DataAssemblyDTO(nccMaterial.getData(), applyFormMaterials, primaryNumber, assistNumber, materialMap, materialSupplyDtos, materCodeList);
        List<NccMaterialDto> list = dataAssembly(dataAssemblyDto);
        // 查询当前申领单的客商信息
        Order order = orderRepository.searchOrderByOrderCode(applyCode);
        list.forEach(item -> {
            item.setCostumerId(order.getOrderCode());
            item.setCostumerName(order.getCustomerName());
        });
        List<ImportMaterialDTO> resultList = new ArrayList<>();
        for (NccMaterialDto nccMaterialDto : list) {
            if (Objects.isNull(nccMaterialDto.getMaterialAssistCountApply()) || Objects.isNull(nccMaterialDto.getMaterialAssistCountApproval())
                    || nccMaterialDto.getMaterialAssistCountApproval().compareTo(nccMaterialDto.getMaterialAssistCountApply()) < 0) {
                ImportMaterialDTO importMaterialDTO =materialApplyFormConverter.convertNccMaterialDTO2ImportMaterialDTO(nccMaterialDto);
                resultList.add(importMaterialDTO);
            }
        }
        resultList.forEach(item -> item.setSupplyWarehouseId(importApplyFormRequest.getStorDocId()));
        return Response.success(resultList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Response<String> rejectApply(ScmRejectOutApplyFormRequest rejectOutApplyFormRequest) {
        RejectOutApplyFormRequest request = materialApplyFormConverter.convertScmRejectOut2OmsRejectOut(rejectOutApplyFormRequest);
        Response<String> response = tbOrderService.rejectOrder(request);
        if (!response.isSuccess()) {
            return response;
        }
        // 修改本地库中的数据
        materialApplyFormRepository.rejecteApply(rejectOutApplyFormRequest.getRemark(),rejectOutApplyFormRequest.getApplyCode());
        return Response.success();
    }

    /**
     * 根据物料编码查询物料信息
     *
     * @param nccMaterialSearchRequest
     * @return
     */
    @Override
    public Response<NccMaterialDto> getNccMaterialByMaterialCode(NccMaterialSearchRequest nccMaterialSearchRequest) {
        NccMaterialRequestVo nccMaterialRequestVo = new NccMaterialRequestVo();
        nccMaterialRequestVo.setNccCode(Lists.newArrayList(nccMaterialSearchRequest.getMaterialCode()));
        nccMaterialRequestVo.setStorDocId(nccMaterialSearchRequest.getStorDocId());
        Response<List<NccMaterialDto>> nccMaterialResponse = getNccMaterial(nccMaterialRequestVo);
        if (!nccMaterialResponse.isSuccess()) {
            return getNotInListMaterialDetail(nccMaterialSearchRequest.getMaterialCode());
        }
        NccMaterialDto nccMaterialDto = nccMaterialResponse.getData().get(0);
        nccMaterialDto.setNassistnum(new BigDecimal(nccMaterialDto.getNonhandastnum()));
        nccMaterialDto.setNnum(nccMaterialDto.getNonhandnum());
        return Response.success(nccMaterialDto);
    }

    private Response getNotInListMaterialDetail(String materialCode) {
        if (StringUtils.isBlank(materialCode)) {
            return Response.success();
        }
        // 获取物料的详情
        Response<List<MaterialDubboSearchDTO>> materialDetails = getMaterialDetail(Lists.newArrayList(materialCode));
        if (!materialDetails.isSuccess()) {
            return materialDetails;
        }
        List<MaterialDubboSearchDTO> data = materialDetails.getData();
        if (CollectionUtils.isEmpty(data)) {
            return Response.success();
        }
        MaterialDubboSearchDTO item = data.get(0);
        NccMaterialDto nccMaterialDto = new NccMaterialDto();
        nccMaterialDto.setInvcode(materialCode);
        nccMaterialDto.setInvname(item.getMaterialName());
        nccMaterialDto.setMaterialSpecification(item.getSpecification());
        nccMaterialDto.setMaterialCountApply(new BigDecimal(0));
        nccMaterialDto.setMaterialAssistCountApply(new BigDecimal(0));
        nccMaterialDto.setMeasname(item.getPrimaryUnit());
        nccMaterialDto.setMeasCode(item.getPrimaryUnitCode());
        nccMaterialDto.setMeasnamef(item.getSecondaryUnit());
        nccMaterialDto.setMeasCodef(item.getSecondaryUnitCode());
        nccMaterialDto.setMainmeasrate(item.getUnitTransRate());
        nccMaterialDto.setStorageRequirement(item.getStorageRequirement());
        // 已出 主辅为0  没有这个批次
        nccMaterialDto.setMaterialAssistCountApproval(BigDecimal.ZERO);
        nccMaterialDto.setMaterialCountApproval(BigDecimal.ZERO);
        nccMaterialDto.setRegistrantCode(item.getRegistrantCode());
        nccMaterialDto.setRegistrant(item.getRegistrant());
        nccMaterialDto.setRegistrationNo(item.getRegistrationNo());
        nccMaterialDto.setType(item.getMaterialTypeName());
        return Response.success(nccMaterialDto);
    }

    /**
     * 通过物料编码集合 获取物料的详细信息
     * @param materCodeList 物料编码集合
     * @return 物料的详细信息
     */
    private Response<List<MaterialDubboSearchDTO>> getMaterialDetail(List<String> materCodeList) {
        if (CollectionUtils.isEmpty(materCodeList)) {
            return Response.fail(ResultCode.MATERIAL_NOT_NULL);
        }
        log.info("开始调用OMS系统，获取编码为：{}，的物料详情",materCodeList);
        // 通过http调用 方法中已经处理异常
        MaterialDubboSearchRequest materialDubboSearchRequest = new MaterialDubboSearchRequest();
        materialDubboSearchRequest.setMaterialCodes(materCodeList);
        try {
            Response<List<MaterialDubboSearchDTO>> omsResponse = materialService.dubboSearchByMaterialCodeList(materialDubboSearchRequest);
            if (!omsResponse.isSuccess()) {
                log.error("调用OMS获取物料详情失败:{}", JSONUtil.toJsonStr(omsResponse));
            }
            return omsResponse;
        }catch (Exception e) {
            log.error("dubbo调用OMS系统查询物料详情出现异常",e);
            return Response.fail(ResultCode.OMS_DUBBO_INVOKE_ERR);
        }
    }

    /**
     * 根据物料编码获取物料详情
     * @param nccMaterialRequestVo 物料编码
     * @return 物料详情
     */
    private Response<List<NccMaterialDto>> getNccMaterial(NccMaterialRequestVo nccMaterialRequestVo) {
        // 通过dubbo调用ncc服务
        try {
            List<NccMaterial> materialDetail = nccMaterialService.getMaterialDetail(nccMaterialRequestVo);
            if (CollectionUtils.isNotEmpty(materialDetail)) {
                List<NccMaterialDto> list = new ArrayList<>();
                materialDetail.forEach(item -> {
                    NccMaterialDto nccMaterialDto = new NccMaterialDto();
                    BeanUtils.copyProperties(item, nccMaterialDto);
                    nccMaterialDto.setRegistrantNo(item.getVfree1()); // 注册人/备案人
                    nccMaterialDto.setRegistrantCode(item.getRegistrantNo()); // 注册人/备案人
                    nccMaterialDto.setRegistrationNo(item.getVfree1()); // 注册人/备案人
                    nccMaterialDto.setRegistrant(item.getRegistrant()); // 注册人 编码
                    nccMaterialDto.setRegistrationName(item.getVfree2()); // 注册证产品名称
                    nccMaterialDto.setMeasCode(item.getShortname()); // 主单位编码
                    nccMaterialDto.setMeasCodef(item.getShortnamef()); // 辅单位编码
                    nccMaterialDto.setType(item.getTypeName()); // 辅单位编码
                    list.add(nccMaterialDto);
                });
                return CollectionUtils.isNotEmpty(list) ? Response.success(list) : Response.fail(ResultCode.NCC_SELECT_MATERIAL_ERROR);
            }
        } catch (Exception e) {
            log.error("获取ncc中物料详情发生异常",e);
        }
        return Response.fail(ResultCode.NCC_SELECT_MATERIAL_ERROR);
    }

    /**
     * 获取没有在ncc中找到的物料的详情
     * @param subtract 没有找到的物料编码
     * @param applyFormDetailDtos 申领单明细
     * @return 物料详情
     */
    private List<NccMaterialDto> getNotInListMaterialDetail(List<String> subtract, List<ApplyFormMaterialDTO> applyFormDetailDtos) {
        if (CollectionUtils.isEmpty(subtract)) {
            return Collections.emptyList();
        }
        // 获取物料的详情
        Response<List<MaterialDubboSearchDTO>> materialDetails = getMaterialDetail(subtract);
        List<NccMaterialDto> list = new ArrayList<>();
        Map<String, ApplyFormMaterialDTO> materialMap = applyFormDetailDtos.stream().collect(Collectors.toMap(ApplyFormMaterialDTO::getMaterialCode, Function.identity()));
        materialDetails.getData().forEach(item -> {
            ApplyFormMaterialDTO material = materialMap.get(item.getMaterialCode());
            if (Objects.nonNull(material)) {
                NccMaterialDto nccMaterialDto = new NccMaterialDto();
                nccMaterialDto.setApplyCode(material.getApplyFormCode());
                nccMaterialDto.setItemId(material.getItemId());
                nccMaterialDto.setInvcode(material.getMaterialCode());
                nccMaterialDto.setInvname(material.getMaterialName());
                nccMaterialDto.setMaterialSpecification(item.getSpecification());
                nccMaterialDto.setMaterialCountApply(new BigDecimal(material.getMaterialCountApply()));
                nccMaterialDto.setMaterialAssistCountApply(material.getMaterialAssistCountApply());
                nccMaterialDto.setMeasnamef(item.getSecondaryUnit());
                nccMaterialDto.setMeasCodef(item.getSecondaryUnitCode());
                nccMaterialDto.setMainmeasrate(item.getUnitTransRate());
                nccMaterialDto.setVfree3(item.getManufacturer());
                // 已出 主辅为0  没有这个批次
                nccMaterialDto.setMaterialAssistCountApproval(BigDecimal.ZERO);
                nccMaterialDto.setMaterialCountApproval(BigDecimal.ZERO);
                nccMaterialDto.setRegistrantCode(item.getRegistrantCode());
                nccMaterialDto.setRegistrant(item.getRegistrant());
                nccMaterialDto.setRegistrationNo(item.getRegistrationNo());
                nccMaterialDto.setType(item.getMaterialTypeName());
                list.add(nccMaterialDto);
            }
        });
        return list;
    }

    /**
     * 出库对象数据组装
     * @param dataAssemblyDto 相关数据 {@link DataAssemblyDTO}
     * @return 出库列表对象 {@link NccMaterialDto}
     */
    private List<NccMaterialDto> dataAssembly(DataAssemblyDTO dataAssemblyDto) {
        List<NccMaterialDto> list = new ArrayList<>();
        for (NccMaterialDto item : dataAssemblyDto.getNccMaterial()) {
            for (ApplyFormMaterialDTO material : dataAssemblyDto.getApplyFormDetailDtos()) {
                NccMaterialDto nccMaterialDto = new NccMaterialDto();
                BeanUtils.copyProperties(item, nccMaterialDto);
                // 数据已有
                if (CollectionUtils.isNotEmpty(list)) {
                    List<String> ids = list.stream().map(NccMaterialDto::getInvcode).collect(Collectors.toList());
                    if (ids.contains(item.getInvcode())) {
                        break;
                    }
                }
                // 物料名称相同
                if (Objects.equals(item.getInvcode(), material.getMaterialCode())) {
                    // 申领物料id
                    nccMaterialDto.setItemId(material.getItemId());
                    // 物料属性赋值 规格
                    nccMaterialDto.setMaterialSpecification(material.getMaterialSpecification());
                    // 申请主数量
                    nccMaterialDto.setMaterialCountApply(new BigDecimal(material.getMaterialCountApply()));
                    BigDecimal materialAssistCountStockOut = material.getMaterialAssistCountStockOut();
                    Integer materialPrimaryNumber = mdmUtil.getMaterialPrimaryNumber(nccMaterialDto.getMainmeasrate(), materialAssistCountStockOut);
                    // 已出主数量
                    nccMaterialDto.setMaterialCountApproval(BigDecimal.valueOf(materialPrimaryNumber));
                    // 申请辅数量
                    nccMaterialDto.setMaterialAssistCountApply(material.getMaterialAssistCountApply());
                    // 已出辅数量
                    nccMaterialDto.setMaterialAssistCountApproval(materialAssistCountStockOut);
                    // 申领单号
                    nccMaterialDto.setApplyCode(material.getApplyFormCode());
                    // 存储方式
                    MaterialDubboSearchDTO materialDubboSearchDTO = dataAssemblyDto.getMaterialMap().get(material.getMaterialCode());
                    nccMaterialDto.setStorageRequirement(materialDubboSearchDTO.getStorageRequirement());
                    // 注册证号
                    nccMaterialDto.setRegistrationNo(item.getRegistrantNo());
                    // 注册证名称
                    nccMaterialDto.setRegistrationName(item.getRegistrationName());
                    // 注册人
                    nccMaterialDto.setRegistrant(materialDubboSearchDTO.getRegistrant());
                    // 注册人code
                    nccMaterialDto.setRegistrantCode(materialDubboSearchDTO.getRegistrantCode());
                    // 主单位code
                    nccMaterialDto.setMeasCode(materialDubboSearchDTO.getPrimaryUnitCode());
                    // 辅单位code
                    nccMaterialDto.setMeasCodef(materialDubboSearchDTO.getSecondaryUnitCode());
                    // 物料标识
                    nccMaterialDto.setMaterialFlag(materialDubboSearchDTO.getIdentification());
                    // 物料类别
                    nccMaterialDto.setType(materialDubboSearchDTO.getMaterialTypeName());
                    // 实出主数量判断 如果 库存数量小于申请数量-已出数量 则 赋值为库存数量  库存数量充足则赋值为申请数量
                    BigDecimal materialCountApplySubtract = nccMaterialDto.getMaterialCountApply();
                    if (Objects.nonNull(nccMaterialDto.getMaterialCountApproval())) {
                        materialCountApplySubtract = nccMaterialDto.getMaterialCountApply().subtract(nccMaterialDto.getMaterialCountApproval());
                    }
                    nccMaterialDto.setNnum(materialCountApplySubtract.compareTo(new BigDecimal(nccMaterialDto.getNonhandnum())) <= 0 ?
                            materialCountApplySubtract.toString() : nccMaterialDto.getNonhandnum());
                    // 实出辅数量判断 如果 库存数量小于申请数量-已出数量 则 赋值为库存数量  库存数量充足则赋值为申请数量
                    BigDecimal materialAssistCountApplySubtract = nccMaterialDto.getMaterialAssistCountApply();
                    if (Objects.nonNull(nccMaterialDto.getMaterialAssistCountApproval())) {
                        materialAssistCountApplySubtract = nccMaterialDto.getMaterialAssistCountApply().subtract(nccMaterialDto.getMaterialAssistCountApproval());
                    }
                    nccMaterialDto.setNassistnum(materialAssistCountApplySubtract.compareTo(new BigDecimal(nccMaterialDto.getNonhandastnum())) <=0 ?
                            materialAssistCountApplySubtract : new BigDecimal(nccMaterialDto.getNonhandastnum()));
                    list.add(nccMaterialDto);
                }
            }
        }
        // 过滤出没查到的数据
        List<String> hasIds = list.stream().map(NccMaterialDto::getInvcode).collect(Collectors.toList());
        List<String> subtract = (List<String>) CollectionUtils.subtract(dataAssemblyDto.getMaterCodeList(), hasIds);
        // 不存在的物料 过滤出id 去物料表中查数据
        List<NccMaterialDto> notIn = getNotInListMaterialDetail(subtract, dataAssemblyDto.getApplyFormDetailDtos());
        list.addAll(notIn);
        return list;
    }
}




