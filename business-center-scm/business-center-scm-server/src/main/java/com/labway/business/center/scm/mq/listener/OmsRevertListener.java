package com.labway.business.center.scm.mq.listener;

import cn.hutool.json.JSONUtil;
import com.labway.business.center.core.util.NotifyUtil;
import com.labway.business.center.core.util.RedisUtils;
import com.labway.business.center.scm.config.SCMRabbitmqConfig;
import com.labway.business.center.scm.mq.param.MessageDTO;
import com.labway.business.center.scm.service.OmsCancelOrderListenerService;
import com.rabbitmq.client.Channel;
import com.swak.frame.dto.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 2023/09/14 19:56
 **/
@Slf4j
@Component
public class OmsRevertListener implements ChannelAwareMessageListener {

    @Resource
    private RedisUtils redisUtils;
    @Resource
    private NotifyUtil notifyUtil;
    @Resource
    private OmsCancelOrderListenerService omsCancelOrderListenerService;
    private static final String SCM_REDIS_MQ_KEY = "SCM:MQ:KEY:";

    @Override
    @RabbitListener(queues = SCMRabbitmqConfig.OMS_REVERT_ORDER_QUEUE_NAME)
    public void onMessage(Message message, Channel channel) throws Exception {
        long deliveryTag = message.getMessageProperties().getDeliveryTag();
        String body = new String(message.getBody(), "UTF-8");
        MessageDTO messageDTO = JSONUtil.toBean(body, MessageDTO.class);
        log.info("--------------监听到OMS恢复订单：{}", body);
        if (StringUtils.isBlank(body)) {
            channel.basicAck(deliveryTag, true);
            return;
        }
        if (!redisUtils.lock(SCM_REDIS_MQ_KEY + messageDTO.getMessageId(), StringUtils.EMPTY, 1, TimeUnit.HOURS)) {
            channel.basicAck(deliveryTag, true);
            return;
        }
        String orgCode = messageDTO.getOrgCode();
        String orderCode = messageDTO.getOrderCode();
        try {
            // 取消相关订单
            Response<String> response = omsCancelOrderListenerService.revertOrder(orgCode, orderCode);
            if (response.isSuccess()) {
                log.info("订单【{}】的相关订单恢复成功", orderCode);
                notifyUtil.send("处理恢复订单消息：订单编号：" + orderCode + "成功");
                channel.basicAck(deliveryTag, true);
            } else {
                log.info("订单【{}】的相关订单恢复成功", orderCode);
                notifyUtil.send("处理恢复订单消息：订单编号：" + orderCode + "失败" + JSONUtil.toJsonStr(response));
                channel.basicReject(deliveryTag, true);
            }
        } catch (Exception e) {
            log.error("处理消息：订单编号：{}失败", orderCode, e);
            notifyUtil.send("处理恢复订单消息：订单编号：" + orderCode + "失败" + e);
            channel.basicReject(deliveryTag, false);
        }
    }
}