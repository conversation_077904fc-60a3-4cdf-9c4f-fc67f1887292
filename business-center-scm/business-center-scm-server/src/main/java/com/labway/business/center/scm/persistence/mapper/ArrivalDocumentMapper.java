package com.labway.business.center.scm.persistence.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.labway.business.center.scm.persistence.entity.ArrivalDocument;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 到货单表(ArrivalDocument)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-07-05 10:04:43
 */
@Mapper
public interface ArrivalDocumentMapper extends BaseMapper<ArrivalDocument> {

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<ArrivalDocument> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<ArrivalDocument> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<ArrivalDocument> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<ArrivalDocument> entities);

}

