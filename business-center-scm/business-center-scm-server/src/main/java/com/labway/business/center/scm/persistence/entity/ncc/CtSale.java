 package com.labway.business.center.scm.persistence.entity.ncc;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;


@TableName("ct_sale")
@Data
public class CtSale implements Serializable {
    
    
    @TableField(value = "pk_ct_sale")
    private String ctSaleId;
    
    @TableField(value = "vbillcode")
    private String vbillcode;
    
    @TableField("ctname")
    
    private String ctname;

}
