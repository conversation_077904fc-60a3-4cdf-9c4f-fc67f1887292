package com.labway.business.center.scm.config;


import com.labway.business.center.scm.constants.SystemEnum;
import org.springframework.amqp.core.*;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

/**
 * SCMRabbitmqConfig
 *
 */
@Configuration
public class SCMRabbitmqConfig {

    public Map<String,String> systemMappingKeys = Map.of(SystemEnum.OMS.getSystemCode(),OUT_INFO_OMS_ROUTING_KEY);
    public static final String OUT_INFO_EXCHANGE = "SPD.MATERIAL.OUT";
    public static final String OUT_INFO_OMS_QUEUE_NAME = "SPD.MATERIAL.OMS.QUEUE";
    public static final String OUT_INFO_OMS_ROUTING_KEY = "SPD.MATERIAL.OUT.OMS.ROUTING.KEY";

    /**
     * OMS 取消订单的队列名称
     */
    public static final String OMS_CANCEL_ORDER_QUEUE_NAME = "OMS.CANCEL.ORDER.QUEUE";

    /**
     * OMS 取消订单的队列名称
     */
    public static final String OMS_REVERT_ORDER_QUEUE_NAME = "OMS.REVERT.ORDER.QUEUE";

    @Bean
    public DirectExchange outInfoDirectExchange() {
        //Direct交换机
        return new DirectExchange(OUT_INFO_EXCHANGE, true, false);
    }

    @Bean
    public Queue outOmsInfoQueue() {
        return new Queue(OUT_INFO_OMS_QUEUE_NAME,true,false,false);
    }

    @Bean
    public Binding outInfoQueueBinding() {
        return BindingBuilder.bind(outOmsInfoQueue())
                .to(outInfoDirectExchange())
                .with(OUT_INFO_OMS_ROUTING_KEY);
    }

    public String getSystemRoutingBySystemCode(String systemCode) {
        return systemMappingKeys.get(systemCode);
    }
}
