package com.labway.business.center.scm.repository.ncc;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.labway.business.center.scm.persistence.entity.ncc.BdIncome;
import com.labway.business.center.scm.persistence.entity.ncc.BdPayment;
import com.labway.business.center.scm.persistence.mapper.ncc.BdIncomeMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2023/09/24 17:04
 **/
@DS("ncc")
@Repository
public class BdIncomeRepository {
    @Resource
    private BdIncomeMapper bdIncomeMapper;

    public List<BdIncome> getBdIncomesByOrg(String orgId) {
        if (StringUtils.isBlank(orgId)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<BdIncome> queryWrapper = new LambdaQueryWrapper<>();
        // 000101100000000004NT -> 兰卫医学
        queryWrapper.eq(BdIncome::getPkOrg,orgId)
                .or().eq(BdIncome::getPkOrg,"000101100000000004NT");
        return bdIncomeMapper.selectList(queryWrapper);
    }
}