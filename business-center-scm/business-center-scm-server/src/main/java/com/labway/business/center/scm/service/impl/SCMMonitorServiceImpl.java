package com.labway.business.center.scm.service.impl;

import com.labway.business.center.mdm.api.reagent.service.MdmMonitorService;
import com.labway.business.center.scm.service.SCMMonitorService;
import com.labway.business.center.third.ncc.service.NCCMonitorService;
import com.swak.frame.dto.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.cloud.context.config.annotation.RefreshScope;

/**
 * <AUTHOR>
 */
@Slf4j
@DubboService
@RefreshScope
public class SCMMonitorServiceImpl implements SCMMonitorService {

    @DubboReference
    private MdmMonitorService mdmMonitorService;
    @DubboReference
    private NCCMonitorService nccMonitorService;


    @Override
    public Response<?> serviceMonitor() {
        log.info("SCM系统服务心跳监测。。。");

        Response<?> mdmResponse = mdmMonitorService.getServiceHeartbeat();
        log.info("MDM系统服务心跳监测结果：{}", mdmResponse.isSuccess());

        Response<?> nccResponse = nccMonitorService.getServiceHeartbeat();
        log.info("NCC系统服务心跳监测结果：{}", nccResponse.isSuccess());

        return Response.success();
    }

    @Override
    public Response<?> getServiceHeartbeat() {
        return Response.success();
    }

}
