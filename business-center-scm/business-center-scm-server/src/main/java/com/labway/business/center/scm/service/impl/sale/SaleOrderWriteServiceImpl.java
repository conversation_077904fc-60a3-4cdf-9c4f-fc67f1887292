package com.labway.business.center.scm.service.impl.sale;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.labway.business.center.core.enums.DeleteFlagEnum;
import com.labway.business.center.core.enums.ResultCode;
import com.labway.business.center.core.user.LoginUserInfoUtil;
import com.labway.business.center.core.util.OrderNumUtil;
import com.labway.business.center.scm.constants.SaleOrderMaterialStatus;
import com.labway.business.center.scm.constants.SaleOrderStatus;
import com.labway.business.center.scm.model.ncc.sale.NccSaleOrder;
import com.labway.business.center.scm.persistence.entity.NccOaDeptMapping;
import com.labway.business.center.scm.persistence.entity.sale.SaleRequestOrder;
import com.labway.business.center.scm.repository.sale.SaleRequestOrderRespository;
import com.labway.business.center.scm.service.NccOaMappingService;
import com.labway.business.center.scm.service.ncc.NccOrderService;
import com.labway.business.center.scm.util.MdmUtil;
import com.labway.business.center.third.ncc.dto.material.TbMaterialDto;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;

import com.alibaba.fastjson.JSON;
import com.labway.business.center.scm.constants.UserResultCode;
import com.labway.business.center.scm.model.control.NccControlCheckBody;
import com.labway.business.center.scm.model.control.NccControlCheckHeader;
import com.labway.business.center.scm.model.control.NccControlCheckRequest;
import com.labway.business.center.scm.model.request.sale.SaleOrderMaterialParams;
import com.labway.business.center.scm.model.request.sale.SaleOrderParams;
import com.labway.business.center.scm.persistence.entity.sale.SaleOrder;
import com.labway.business.center.scm.persistence.entity.sale.SaleOrderMaterial;
import com.labway.business.center.scm.repository.sale.AggerateSaleOrderRespository;
import com.labway.business.center.scm.service.ncc.NccSaleOrderControlFormService;
import com.labway.business.center.scm.service.sale.SaleOrderWriteService;
import com.swak.frame.dto.Response;
import com.swak.frame.util.DateTimeUtils;

import cn.hutool.core.date.DatePattern;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 销售订单写服务
 * 
 * <AUTHOR>
 * @date 2023/08/15
 */
@DubboService
@Slf4j
public class SaleOrderWriteServiceImpl implements SaleOrderWriteService {

    @Resource
    private NccSaleOrderControlFormService nccSaleOrderControlFormService;

    @Resource
    private AggerateSaleOrderRespository aggerateSaleOrderRespository;

    @Resource
    private SaleRequestOrderRespository saleRequestOrderRespository;

    @Override
    public Response<List<String>> createSaleOrder(SaleOrderParams saleOrderParams) {

        String userId = LoginUserInfoUtil.getUserId();
        String orgId = saleOrderParams.getOrgId();
        LocalDateTime now = LocalDateTime.now();
        List<SaleOrderMaterialParams> materialParams = saleOrderParams.getMaterials();
        if (StringUtils.isBlank(userId) || StringUtils.isBlank(orgId) || CollectionUtils.isEmpty(materialParams)) {
            return Response.builder(UserResultCode.PARAMS_ERROR);
        }
        // TODO 校验该用户是否含有该销售订单权限

        NccControlCheckRequest request = new NccControlCheckRequest();
        NccControlCheckHeader header = new NccControlCheckHeader();
        header.setCcustomerid(saleOrderParams.getCustomerId());
        header.setOrderTime(DateTimeUtils.date2String(new Date(), DatePattern.NORM_DATETIME_PATTERN));
        header.setPkOrg(orgId);
        request.setCheckHeader(header);
        List<NccControlCheckBody> bodys = materialParams.stream().map(mat -> {
            NccControlCheckBody checkBody = new NccControlCheckBody();
            checkBody.setMaterialId(mat.getMaterialId());
            return checkBody;
        }).collect(Collectors.toList());

        request.setCheckBodies(bodys);

        Response<List<String>> controlResponse = nccSaleOrderControlFormService.checkNccSaleOrder(request);
        if (!controlResponse.isSuccess()) {
            log.info("销售订单管控被管控， controlResponse={}", JSON.toJSONString(controlResponse));
            return controlResponse;
        }
        SaleRequestOrder saleRequestOrder = saleRequestOrderRespository.searchSaleRequestOrderBySaleRequestId(saleOrderParams.getRequestOrderId());
        SaleOrder saleOrder = new SaleOrder();
        BeanUtils.copyProperties(saleOrderParams, saleOrder);
        String saleOrderId = OrderNumUtil.getSaleOrderId();
        saleOrder.setSaleOrderRequestId(saleOrderParams.getRequestOrderId());
        saleOrder.setSaleOrderId(saleOrderId);
        saleOrder.setBusinessId(saleOrderParams.getOrgId());
        saleOrder.setSaleOrderStatus(SaleOrderStatus.WAIT_SALE.getCode());
        saleOrder.setSaleUser(LoginUserInfoUtil.getUserId());
        saleOrder.setSaleUserName(LoginUserInfoUtil.getUserUserName());
        saleOrder.setDeleted(DeleteFlagEnum.NO_DELETE.getCode());
        saleOrder.setCreateTime(saleOrderParams.getOmsOrderCreateTime());
        saleOrder.setUpdateTime(now);
        saleOrder.setMakeTime(saleOrderParams.getMakeTime());
        saleOrder.setSourceOrderId(saleRequestOrder.getSourceOrderId());
        saleOrder.setAddress(saleRequestOrder.getAddress());
        // TODO 地址
        List<SaleOrderMaterial> saleOrderMaterials = materialParams.stream().map(t -> {
            SaleOrderMaterial material = new SaleOrderMaterial();
            BeanUtils.copyProperties(t, material);
            material.setVctcode(t.getContracts().get(0).getVbillcode());
            material.setSourceOrderId(saleOrderParams.getOmsOrderCode());
            material.setSaleOrderId(saleOrder.getSaleOrderId());
            material.setSaleRequestOrderId(saleOrder.getSaleOrderRequestId());
            material.setPlainSaleNum(t.getPlainSaleNum());
            material.setSaleNum(t.getSaleNum());
            material.setCreateTime(now);
            material.setUpdateTime(now);
            material.setDeleted(DeleteFlagEnum.NO_DELETE.getCode());
            material.setStatus(SaleOrderMaterialStatus.WAIT_WAIT.getCode());
            return material;
        }).collect(Collectors.toList());
        aggerateSaleOrderRespository.batchInsert(saleOrder, saleOrderMaterials);

        saleRequestOrderRespository.updateOrderStatus(saleOrderParams.getRequestOrderId(), SaleOrderStatus.SALED.getCode());
        return Response.success();
    }

}
