package com.labway.business.center.scm.controller;


import com.labway.business.center.scm.model.request.cancelorder.ApprovalCancelOrderPassParams;
import com.labway.business.center.scm.model.request.cancelorder.ApprovalCancelOrderRejectParams;
import com.labway.business.center.scm.model.request.cancelorder.QueryCancelOrderMaterialParams;
import com.labway.business.center.scm.model.request.cancelorder.QueryCancelOrderParams;
import com.labway.business.center.scm.model.response.cancelorder.CancelOrderMaterialDTO;
import com.labway.business.center.scm.model.response.cancelorder.CancelPurchaseOrderDTO;
import com.labway.business.center.scm.request.AddCancelPurchaseParams;
import com.labway.business.center.scm.service.CancelPurchaseOrderService;
import com.swak.frame.dto.Response;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Arrays;
import java.util.List;

/**
 * 取消采购
 */
@RestController
@RequestMapping("/cancelPurchase")
public class CancelPurchaseOrderController {

    @Resource
    private CancelPurchaseOrderService cancelPurchaseOrderService;

    /**
     * 添加取消采购单
     */
    @PostMapping("/cancelOrder")
    public Response<?> cancelPurchase(@RequestBody @Valid AddCancelPurchaseParams params) {
        return cancelPurchaseOrderService.cancelPurchase(params);
    }


    /**
     * 查询采购单
     */
    @PostMapping("/listOrder")
    public Response<List<CancelPurchaseOrderDTO>> listOrder(@RequestBody @Valid QueryCancelOrderParams params) {
        return cancelPurchaseOrderService.listOrder(params);
    }


    /**
     * 查询采购单下物料
     */
    @PostMapping("/listMaterial")
    public Response<List<CancelOrderMaterialDTO>> listMaterial(@RequestBody @Valid QueryCancelOrderMaterialParams params) {
        return cancelPurchaseOrderService.listMaterial(params);
    }


    /**
     * 审批通过
     */
    @PostMapping("/pass")
    public Response<?> approvalPass(@RequestBody @Valid ApprovalCancelOrderPassParams params) {
        return cancelPurchaseOrderService.approvalPass(params);
    }

    /**
     * 驳回
     */
    @PostMapping("/reject")
    public Response<?> approvalReject(@RequestBody @Valid ApprovalCancelOrderRejectParams params) {
        return cancelPurchaseOrderService.approvalReject(params);
    }
}
