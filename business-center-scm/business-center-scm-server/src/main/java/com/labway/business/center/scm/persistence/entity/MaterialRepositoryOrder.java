package com.labway.business.center.scm.persistence.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;

/**
 * 物料出库仓库优先级
 * @TableName tb_material_repository_order
 */
@TableName(value ="tb_material_repository_order")
@Data
public class MaterialRepositoryOrder implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 业务单元
     */
    @TableField(value = "org_id")
    private String orgId;

    /**
     * 仓库编码
     */
    @TableField(value = "warehouse_id")
    private String warehouseId;

    /**
     * 排序
     */
    @TableField(value = "sort")
    private Integer sort;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}