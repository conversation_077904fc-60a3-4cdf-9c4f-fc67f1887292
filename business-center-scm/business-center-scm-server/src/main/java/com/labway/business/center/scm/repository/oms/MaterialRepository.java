package com.labway.business.center.scm.repository.oms;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.business.center.scm.persistence.entity.oms.Material;
import com.labway.business.center.scm.persistence.mapper.oms.MaterialMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public class MaterialRepository {

    @Resource
    private MaterialMapper materialMapper;


    /**
     * 根据物料编码查询物料信息
     * @param materialCodes
     * @return
     */
    public List<Material> queryMaterialListByCodes(Collection<String> materialCodes){
        if (CollectionUtils.isEmpty(materialCodes)){
            return Collections.emptyList();
        }

       return materialMapper.selectList(Wrappers.lambdaQuery(Material.class)
               .in(Material::getMaterialCode,materialCodes).orderByDesc(Material::getCreateTime));
    }


    public List<Material> searchMaterial(String searchKey) {
        if (StringUtils.isBlank(searchKey)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<Material> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(Material::getMaterialCode,searchKey)
                .or()
                .like(Material::getMaterialName,searchKey)
                .select(Material::getMaterialCode,Material::getMaterialName,Material::getSpecification,Material::getModel,Material::getSecondaryUnit,Material::getManufacturer);
        return materialMapper.selectList(queryWrapper);
    }
}
