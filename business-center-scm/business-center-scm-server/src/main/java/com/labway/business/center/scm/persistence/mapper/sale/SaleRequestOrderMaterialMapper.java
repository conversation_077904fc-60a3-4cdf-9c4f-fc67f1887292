 package com.labway.business.center.scm.persistence.mapper.sale;

import java.util.List;
import java.util.Map;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.labway.business.center.core.injector.ExtBaseMapper;
import com.labway.business.center.scm.persistence.entity.PurchaseRequestOrderMaterial;
import com.labway.business.center.scm.persistence.entity.sale.SaleRequestOrderMaterial;


@DS("master")
public interface SaleRequestOrderMaterialMapper extends ExtBaseMapper<SaleRequestOrderMaterial> {

    /**
     * 获取我的销售列表信息
     * @param params
     * @return
     */
//    public List<SaleRequestOrderMaterial> getUserSaleRequestOrderMaterial(  Map<String, Object> params);
}
