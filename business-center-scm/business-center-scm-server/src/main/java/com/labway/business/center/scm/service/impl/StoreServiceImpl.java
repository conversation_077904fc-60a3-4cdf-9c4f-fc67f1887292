package com.labway.business.center.scm.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.labway.business.center.scm.persistence.mapper.StoreMapper;
import com.labway.business.center.scm.repository.StoreRepository;
import com.labway.business.center.scm.service.StoreService;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 仓库 (bd_stordoc / nc.vo.bd.stordoc.StordocVO)(Store)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-07-05 10:02:10
 */
@DubboService
public class StoreServiceImpl implements StoreService {

    @Resource
    private StoreRepository StoreRepository;


}

