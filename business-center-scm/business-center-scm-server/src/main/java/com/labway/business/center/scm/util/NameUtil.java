 package com.labway.business.center.scm.util;

import java.util.regex.Pattern;

public class NameUtil {
     
     public static String extractName(String name) {
         String regex = "[\\.（\\(]\\S*?[\\.）\\)]";
         Pattern pattern = Pattern.compile(regex);
          name = pattern.matcher(name).replaceAll("");
         Pattern patternNum = Pattern.compile("[\\d]");
         name= patternNum.matcher(name).replaceAll("").replaceAll(" ", "");
         return  name;
     }
     
     /**
      * 检查是否为空
      * @param o
      * @return 
      */
     public static String CheckNull(Object o) {
         if (o == null) {
             return "";
         } else {
             return o.toString();
         }
     }
     /**
      * 检查是否为空
      * @param o
      * @return 
      */
     public static String CheckNull1(Object o) {
         if (o == null) {
             return "~";
         } else {
             return o.toString();
         }
     }
     

}
