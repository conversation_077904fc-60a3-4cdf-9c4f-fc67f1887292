 package com.labway.business.center.scm.service.impl.sale;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.labway.business.center.core.enums.ResultCode;
import com.labway.business.center.core.user.LoginUserInfoUtil;
import com.labway.business.center.scm.model.response.sale.SaleReqestOrderMaterialVo;
import com.labway.business.center.scm.model.response.sale.SaleReqestOrderVo;
import com.labway.business.center.scm.persistence.entity.oms.Order;
import com.labway.business.center.scm.repository.OrderRepository;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.labway.business.center.core.config.Pager;
import com.labway.business.center.scm.constants.UserResultCode;
import com.labway.business.center.scm.converter.sale.SaleReqestOrderConverter;
import com.labway.business.center.scm.converter.sale.SaleReqestOrderMaterialConverter;
import com.labway.business.center.scm.model.request.sale.GetSaledOrderParams;
import com.labway.business.center.scm.model.response.sale.SaleReqestOrders;
import com.labway.business.center.scm.persistence.entity.sale.SaleRequestOrder;
import com.labway.business.center.scm.persistence.entity.sale.SaleRequestOrderMaterial;
import com.labway.business.center.scm.repository.sale.SaleRequestOrderMaterialRespository;
import com.labway.business.center.scm.repository.sale.SaleRequestOrderRespository;
import com.labway.business.center.scm.service.sale.SaleRequestOrderService;
import com.swak.frame.dto.Response;


@DubboService
public class SaleRequestOrderServiceImpl implements SaleRequestOrderService {
    
   
    
    @Resource
    private  SaleRequestOrderRespository saleRequestOrderRespository;
    
    @Resource
    private SaleRequestOrderMaterialRespository saleRequestOrderMaterialRespository;
    
    @Resource
    private SaleReqestOrderConverter saleReqestOrderConverter;
    
    @Resource
    private SaleReqestOrderMaterialConverter saleReqestOrderMaterialConverter;
    @Resource
    private OrderRepository orderRepository;


    @Override
    public Response<Pager<List<SaleReqestOrderVo>>> getSaleRequestOrders(GetSaledOrderParams params) {
        //TODO 先判断改人是否有销售订单权限
        String userId = LoginUserInfoUtil.getUserId();
        String orgId = params.getOrgId();
        String customId = params.getCustomerId();
        String startTime = params.getStartTime();
        String endTime = params.getEndTime();
        String orderId = params.getOrderId();
        Integer page = Objects.isNull(params.getPage()) ? 1 : params.getPage();
        Integer pageSize = Objects.isNull(params.getPageSize()) ? 1 : params.getPageSize();
        if (StringUtils.isBlank(orgId) || StringUtils.isBlank(userId)) {
            return Response.builder(UserResultCode.PARAMS_ERROR);
        }

        Map<String, Object> searchMap = Maps.newHashMap();
        searchMap.put("orgId", orgId);
        if (StringUtils.isNotBlank(customId)) {
            searchMap.put("customId", customId);
        }
        if (StringUtils.isNotBlank(orderId)) {
            searchMap.put("orderId", orderId);
        }
        if (StringUtils.isNotBlank(startTime)) {
            searchMap.put("startTime", startTime);
            searchMap.put("endTime", endTime);
        }
        Long total = saleRequestOrderRespository.getUserSaleRequestOrderCount(searchMap);
        Pager<List<SaleReqestOrderVo>> pager = new Pager<>(pageSize,total);
        pager.setPage(page);
        searchMap.put("pageSize", pageSize);
        searchMap.put("start", pager.getStart());
        
        List<SaleRequestOrder> purchaseOrders = saleRequestOrderRespository.getUserSaleRequestOrders(searchMap);
        if (CollectionUtils.isEmpty(purchaseOrders)) {
           return  Response.success();
        }
        List<String> orderIds =
            purchaseOrders.stream().map(SaleRequestOrder::getSaleRequestOrderId).collect(Collectors.toList());

        // 原始订单编码
        List<String> orderCodes = purchaseOrders.stream().map(SaleRequestOrder::getSourceOrderId).collect(Collectors.toList());
        // 查询原始订单信息
        List<Order> orders = orderRepository.queryListByOrderCodes(orderCodes);
        if (CollectionUtils.isEmpty(orders)){
            Response.fail(ResultCode.SALE_ORDER_NOT_EXIST_ERROR);
        }
        Map<String, Order> codeMap = orders.stream().collect(Collectors.toMap(e -> e.getOrderCode(), p -> p, (o1, o2) -> o2));


        List<SaleRequestOrderMaterial> purchaseOrderMaterials =
            saleRequestOrderMaterialRespository.getMaterialsByOrderIds(orderIds);

        Map<String, List<SaleRequestOrderMaterial>> materialMap =
            purchaseOrderMaterials.stream().collect(Collectors.groupingBy(SaleRequestOrderMaterial::getSaleRequestOrderId));

        List<SaleReqestOrderVo> list = Lists.newArrayList();

        for (SaleRequestOrder order : purchaseOrders) {
            String sorderId = order.getSaleRequestOrderId();
            List<SaleRequestOrderMaterial> materials = materialMap.get(sorderId);
            SaleReqestOrderVo saleReqestOrderVo = saleReqestOrderConverter.converter2Vo(order);
            saleReqestOrderVo.setOrderMaterials(materials.stream()
                .map(c -> {
                    SaleReqestOrderMaterialVo saleReqestOrderMaterialVo = saleReqestOrderMaterialConverter.converter2Vo(c);
                    saleReqestOrderMaterialVo.setMaterialNum(c.getSaleRequestNum());
                    return saleReqestOrderMaterialVo;
                }).collect(Collectors.toList()));

            // 源单创建时间
            Order tempOrder = codeMap.get(order.getSourceOrderId());
            if (tempOrder!=null){
                saleReqestOrderVo.setOrderCreateTime(tempOrder.getCreateTime());
            }

            list.add(saleReqestOrderVo);
        }
        
        pager.setItem(list);
        Response<Pager<List<SaleReqestOrderVo>>> response = Response.success();
        response.setData(pager);
        return response;
    }

   
   

}
