package com.labway.business.center.scm.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.business.center.core.enums.ResultCode;
import com.labway.business.center.scm.constants.CommonConstant;
import com.labway.business.center.scm.persistence.entity.MaterialSupplyDetail;
import com.labway.business.center.scm.persistence.mapper.MaterialSupplyDetailMapper;
import com.labway.business.center.third.ncc.dto.ncc.NccBodyDto;
import com.labway.business.center.third.ncc.dto.ncc.NccResponseDto;
import com.labway.business.center.third.ncc.dto.out.OutBodyDto;
import com.labway.business.center.third.ncc.dto.out.OutDto;
import com.swak.frame.dto.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Tianhao
 * @version 2023/08/10 16:57
 **/
@Slf4j
@Repository
public class MaterialSupplyFormDetailRepository {
    @Resource
    private MaterialSupplyDetailMapper materialSupplyDetailMapper;


    /**
     * 查询出库单物料明细
     * @param supplyNo
     * @return
     */
    public List<MaterialSupplyDetail> querySupplyDetailList(String supplyNo) {
        return materialSupplyDetailMapper.selectList(Wrappers.lambdaQuery(MaterialSupplyDetail.class)
                .eq(MaterialSupplyDetail::getSupplyNo,supplyNo)
        );
    }


    public List<MaterialSupplyDetail> searchSupplyDetailByApplyDetailIds(List<String> applyFormDetailItemIds) {
        if (CollectionUtils.isEmpty(applyFormDetailItemIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<MaterialSupplyDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(MaterialSupplyDetail::getApplyDetailId,applyFormDetailItemIds);
        return materialSupplyDetailMapper.selectList(queryWrapper);
    }

    public List<MaterialSupplyDetail> searchSupplyDetailsBySupplyIds(List<String> supplyIds) {
        if (CollectionUtils.isEmpty(supplyIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<MaterialSupplyDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(MaterialSupplyDetail::getSupplyId,supplyIds);
        return materialSupplyDetailMapper.selectList(wrapper);
    }

    /**
     * 插入出库详情
     * @param supplyId
     * @param outDto
     * @param nccResponseDto
     * @return
     */
    public Response<List<MaterialSupplyDetail>> insertMaterialSupplyDetail(String supplyId, OutDto outDto, NccResponseDto nccResponseDto) {
        if (StringUtils.isBlank(supplyId)) {
            return Response.fail(ResultCode.PARAMS_NOT_VALIDATE);
        }
        Map<String, OutBodyDto> outBodyDtoMap = outDto.getOutBody().stream().collect(Collectors.toMap(item ->item.getRowNo() + item.getMaterialCode() + item.getBatchCode(), Function.identity()));
        Map<String, String> remarkMap = outDto.getOutBody().stream()
                .collect(Collectors.toMap(
                        item ->item.getRowNo() + item.getMaterialCode() + item.getBatchCode(),
                        remark -> Optional.ofNullable(remark.getRemark()).orElse(StringUtils.EMPTY)));

        List<MaterialSupplyDetail> list = new ArrayList<>();
        for (int i = 0; i < nccResponseDto.getBodyDtoList().size(); i++) {
            NccBodyDto item = nccResponseDto.getBodyDtoList().get(i);
            OutBodyDto outBodyDto = outBodyDtoMap.get((i + 1) + item.getMaterialCode() + item.getBatchNo());

            MaterialSupplyDetail detail = new MaterialSupplyDetail();
            detail.setItemId(CommonConstant.STOCK_OUT_DETAIL_ID + IdWorker.getId());
            detail.setOrderCode(outDto.getOrderCode());
            detail.setSupplyId(supplyId);
            detail.setSupplyNo(outDto.getOutId());
            // 申领单详细id
            detail.setApplyDetailId(outBodyDto.getApplyDetailId());
            detail.setMaterialCode(item.getMaterialCode());
            detail.setMaterialName(outBodyDto.getMaterialName());
            detail.setBatchNo(item.getBatchNo());
            detail.setInvalidTime(item.getInvalidTime());
            detail.setPrimaryNumber(item.getPrimaryNumber().intValue());
            detail.setAssistNumber(new BigDecimal(item.getAssistNumber()));
            detail.setRemark(remarkMap.get((i + 1) + item.getMaterialCode() + item.getBatchNo()));
            // 新增的字段
            detail.setReturnNumber(new BigDecimal(0));
            detail.setManufacturer(outBodyDto.getManufacturer());
            detail.setRegistrationNo(outBodyDto.getRegistrationNo());
            detail.setRegistrationName(outBodyDto.getRegistrationName());
            detail.setRegistrant(outBodyDto.getRegistrantCode());
            detail.setRegistrantCode(outBodyDto.getRegistrant());
            detail.setStorageRequirement(outBodyDto.getStorageRequirement());
            detail.setMaterialSpecification(outBodyDto.getMaterialSpecification());
            detail.setRackName(outBodyDto.getRackName());
            detail.setRackCode(outBodyDto.getCscode());
            list.add(detail);
        }


        int i = materialSupplyDetailMapper.insertBatchSomeColumn(list);
        return Response.success(list);
    }

    public List<MaterialSupplyDetail> searchSupplyDetailsByApplyDetailIds(List<String> applyFormDetailsId) {
        LambdaQueryWrapper<MaterialSupplyDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(MaterialSupplyDetail::getApplyDetailId, applyFormDetailsId);
        return materialSupplyDetailMapper.selectList(queryWrapper);
    }

    /**
     * 查询出库单下 该物料的出库详情
     * @param materialCodes
     * @param itemId
     * @return
     */
    public List<MaterialSupplyDetail> searchDetailsByMaterialsAndSupplyId(List<String> materialCodes, String itemId) {
        LambdaQueryWrapper<MaterialSupplyDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(MaterialSupplyDetail::getMaterialCode,materialCodes)
                .eq(MaterialSupplyDetail::getSupplyId,itemId);

        return materialSupplyDetailMapper.selectList(wrapper);
    }

    public List<MaterialSupplyDetail> searchSupplyDetailsBySupplyNo(String supplyNo) {
        if(StringUtils.isBlank(supplyNo)){
            return Collections.emptyList();
        }
        LambdaQueryWrapper<MaterialSupplyDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MaterialSupplyDetail::getSupplyNo,supplyNo);
        return materialSupplyDetailMapper.selectList(queryWrapper);
    }

    public Integer updateSupplyDetailReturnNumberBatch(List<MaterialSupplyDetail> updateSupplyDetail) {
        if (CollectionUtils.isEmpty(updateSupplyDetail)) {
            return -1;
        }
        return materialSupplyDetailMapper.updateBatchReturnNumber(updateSupplyDetail);
    }

    public List<MaterialSupplyDetail> searchRefundSupplyDetails(String supplyNo, String materialName) {
        LambdaQueryWrapper<MaterialSupplyDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MaterialSupplyDetail::getSupplyNo,supplyNo);
        if (StringUtils.isNotBlank(materialName)) {
            queryWrapper.like(MaterialSupplyDetail::getMaterialName,materialName);
        }
        return materialSupplyDetailMapper.selectList(queryWrapper);
    }

    /**
     * 根据订单编码查询出库详情
     * @param orderCode
     * @return
     */
    public List<MaterialSupplyDetail> searchSupplyDetailsByOrderCode(String orderCode) {
        if (StringUtils.isBlank(orderCode)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<MaterialSupplyDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MaterialSupplyDetail::getOrderCode,orderCode);
        return materialSupplyDetailMapper.selectList(queryWrapper);
    }

    public Integer saveMaterialDetailsBatch(List<MaterialSupplyDetail> materialSupplyDetails) {
        if (CollectionUtils.isEmpty(materialSupplyDetails)) {
            return -1;
        }
        return materialSupplyDetailMapper.insertBatchSomeColumn(materialSupplyDetails);
    }
}