

package com.labway.business.center.scm.persistence.mapper.ncc;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.labway.business.center.scm.persistence.entity.ncc.NccSupplierPrice;

import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;


/**
 * <p>
 * <B>Description: mapper类</B>
 * </P>
 * <AUTHOR>
 * @version 1.0
 */
@DS("ncc")
public interface NccSupplierPriceMapper extends  BaseMapper<NccSupplierPrice> {
    
    
    /**
     * 查询供应商信息
     * @param
     * @return
     */
    public List<NccSupplierPrice> getSupplierPricesBySearch( Map<String, Object> params);
    
  

 
}
