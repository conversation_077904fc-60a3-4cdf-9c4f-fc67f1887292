package com.labway.business.center.scm.security;

import org.apache.commons.lang3.StringUtils;

import com.zaxxer.hikari.HikariDataSource;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class SecurityDataSource extends HikariDataSource {
    private String passwordDis;
    /**
     * 公钥
     */
    private static final String PUBKEY =
        "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDcTGnaZ2as+yaOruhcW7jnAMHuQWEfzcXXeUqHc+I+vQ1m44bQwLsx2DgPQKz46uFG5nxa1dqSUw2DgJylQQUGrIHWH5xC0Cdm3MtZM0rufTo8N941lz4WmNvAvKwb/oJ5lbDrI1qZLjYCMfc7h1EoT4TjUxVOKI5D/88hVx8sBwIDAQAB";

    @Override
    public String getPassword() {

        if (StringUtils.isNotBlank(passwordDis)) {
            return passwordDis;
        }
        String encPassword = super.getPassword();
        if (null == encPassword) {
            return null;
        }
        try {
            
            passwordDis = SecurityTools.decrypt(PUBKEY, encPassword);
            return passwordDis;
        } catch (Exception e) {
            log.error("数据库密码解密出错，{" + encPassword + "}");
            throw new RuntimeException ("数据库密码解密失败！", e);
        }
    }

}
