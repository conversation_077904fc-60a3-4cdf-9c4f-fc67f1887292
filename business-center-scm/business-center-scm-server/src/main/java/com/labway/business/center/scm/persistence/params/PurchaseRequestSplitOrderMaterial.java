package com.labway.business.center.scm.persistence.params;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * <B>Description: 拆单转请购详情</B>
 * </P>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class PurchaseRequestSplitOrderMaterial implements Serializable {

    /**
     * 序列号
     */
    private static final long serialVersionUID = 1L;

    /**
    * 请购订单表的订单ID
    */
    @ApiModelProperty(value = "请购订单表的订单ID")
    private String purchaseRequestOrderId;
    /**
    * 物料编码
    */
    @ApiModelProperty(value = "物料编码")
    private String materialCode;
    /**
    * 物料id
    */
    @ApiModelProperty(value = "物料id")
    private String materialId;
    /**
    * 商品名称
    */
    @ApiModelProperty(value = "商品名称")
    private String materialName;
    /**
    * 商品标识
    */
    @ApiModelProperty(value = "商品标识")
    private String materialFlag;
    /**
    * 生产企业/厂商
    */
    @ApiModelProperty(value = "生产企业/厂商")
    private String manufacturer;
    /**
    * 物料规格
    */
    @ApiModelProperty(value = "物料规格")
    private String specification;
    
    /**
     * 注册证号
     */
     @ApiModelProperty(value = "注册证号")
     private String registrationNo;
     /**
     * 注册证名称
     */
     @ApiModelProperty(value = "注册证名称")
     private String registrationName;
     /**
     * 注册证名称/备案人名称
     */
     @ApiModelProperty(value = "注册证名称/备案人名称")
     private String registrant;
     /**
     * 注册证名称/备案人名称code
     */
     @ApiModelProperty(value = "注册证名称/备案人名称code")
     private String registrantCode;
    /**
    * 客商编码
    */
    @ApiModelProperty(value = "客商编码")
    private String customCode;
    /**
    * 商品货品的购买数量
     * 主数量
    */
    @ApiModelProperty(value = "商品货品的购买数量")
    private Integer materialPrimaryNumber;
    /**
    * 辅数量
    */
    @ApiModelProperty(value = "商品货品的购买数量，辅数量")
    private BigDecimal materialAssistNumber;
    /**
    * 创建时间
    */
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;
    /**
    * 物料状态（0-未请购，1 -已请购）
    */
    @ApiModelProperty(value = "物料状态（0-未请购，1 -已请购）")
    private Integer status;
    /**
    * 更新时间
    */
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;
    /**
    * 逻辑删除0-未删除，1-已删除
    */
    @ApiModelProperty(value = "逻辑删除0-未删除，1-已删除")
    private Integer deleted;

    // 已采购数量
    private BigDecimal alreadyAssistNumber;

    private String orgCode;

    /**
     * 辅单位
     */
    private String secondaryUnit;

    /**
     * 存储条件
     */
    private String materialStorageRequirement;

    /**
     * 供应商id
     */
    private String supplierId;

    /**
     * 供应商名称
     */
    private String supplierName;
    /**
     * 源订单编号
     */
    @ApiModelProperty(value = "源订单编号")
    private String sourceOrderId;
   
}