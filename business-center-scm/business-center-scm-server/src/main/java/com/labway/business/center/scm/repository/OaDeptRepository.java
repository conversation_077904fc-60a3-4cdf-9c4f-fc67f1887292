package com.labway.business.center.scm.repository;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.labway.business.center.scm.constants.OaDeptStatus;
import com.labway.business.center.scm.persistence.entity.finance.OaDept;
import com.labway.business.center.scm.persistence.mapper.finance.OaDeptMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * OaDeptRepository oa部门持久层
 *
 * <AUTHOR>
 * @version 2023/03/28 11:13
 **/
@DS("finance")
@Repository
public class OaDeptRepository {
    @Resource
    private OaDeptMapper oaDeptMapper;

    /**
     * 模糊查询 oa部门
     * @param deptName 部门名称
     * @return 部门列表
     */
    public List<OaDept> selectList(String deptName) {
        LambdaQueryWrapper<OaDept> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(OaDept::getName,OaDept::getDeptId,OaDept::getParentId,OaDept::getGroupId,OaDept::getCode);
        wrapper.eq(OaDept::getStatus, OaDeptStatus.ENABLE.getStatus());
        if (StringUtils.isNotBlank(deptName)) {
            wrapper.like(OaDept::getName,deptName);
        }
        return oaDeptMapper.selectList(wrapper);
    }

    public List<OaDept> searchByDeptIds(Set<String> oaDeptIds) {
        if (CollectionUtils.isEmpty(oaDeptIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<OaDept> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(OaDept::getDeptId, oaDeptIds);
        queryWrapper.select(OaDept::getDeptId,OaDept::getName);
        return oaDeptMapper.selectList(queryWrapper);
    }
}