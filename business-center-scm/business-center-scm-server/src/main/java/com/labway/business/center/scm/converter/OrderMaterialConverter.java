package com.labway.business.center.scm.converter;

import com.labway.business.center.scm.dto.material.ApplyFormMaterialDTO;
import com.labway.business.center.scm.persistence.entity.oms.OrderMaterial;
import com.labway.business.center.scm.persistence.params.SplitOrderMaterial;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2023/09/04 13:29
 **/
@Mapper(componentModel = "spring")
public interface OrderMaterialConverter {
    List<OrderMaterial> convertSplitList2Entity(List<SplitOrderMaterial> splitOrderMaterials);

    @Mapping(target = "itemId", source = "id")
    @Mapping(target = "applyFormCode", source = "orderCode")
    @Mapping(target = "materialCode", source = "materialSn")
    @Mapping(target = "materialAssistCountApply", source = "number")
    @Mapping(target = "materialAssistCountStockOut", source = "outNumber")
    @Mapping(target = "manufacturer", source = "outNumber")
    ApplyFormMaterialDTO convertOrderMaterial2ApplyFormMaterialDto(OrderMaterial orderMaterial);
    List<ApplyFormMaterialDTO> convertOrderMaterials2ApplyFormMaterialDtoList(List<OrderMaterial> orderMaterials);
}