package com.labway.business.center.scm.persistence.params;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 查询退库订单
 *
 * <AUTHOR>
 * @version 2023/08/30 19:05
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SearchRefundParams implements Serializable {
    /**
     * 业务单元编码
     */
    private String orgId;

    /**
     * 出库对象的编码
     */
    private String customerCode;
}