 package com.labway.business.center.scm.persistence.mapper.ncc;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.labway.business.center.core.injector.ExtBaseMapper;
import com.labway.business.center.scm.persistence.entity.ncc.CtSale;

/**
 * 销售合同主体
 * <AUTHOR>
 * @date 2023/08/14
 */
@DS("ncc")
public interface CtSaleMapper extends ExtBaseMapper<CtSale> {
    
    
    public List<CtSale> getCtSaleByCustomerId(Map<String, String> params);

}
