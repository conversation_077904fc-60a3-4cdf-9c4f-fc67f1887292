
package com.labway.business.center.scm.persistence.mapper.ncc;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.labway.business.center.scm.persistence.entity.ncc.BdCustfinance;

import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;


/**
 * <p>
 * <B>Description: mapper类</B>
 * </P>
 * <AUTHOR>
 * @version 1.0
 */
@DS("ncc")
public interface BdCustfinanceMapper extends  BaseMapper<BdCustfinance> {

}
