
package com.labway.business.center.scm.persistence.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * <B>Description: 请购单物料表实体类</B>
 * </P>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
@TableName("tb_purchase_request_order_material")
@ApiModel(value="PurchaseRequestOrderMaterial对象", description="请购单物料表")
public class PurchaseRequestOrderMaterial{

    /**
     * 序列号
     */
    private static final long serialVersionUID = 1L;
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
    * 请购订单表的订单ID
    */
    @ApiModelProperty(value = "请购订单表的订单ID")
    @TableField("purchase_request_order_id")
    private String purchaseRequestOrderId;
    /**
    * 物料编码
    */
    @ApiModelProperty(value = "物料编码")
    @TableField("material_code")
    private String materialCode;
    /**
    * 物料id
    */
    @ApiModelProperty(value = "物料id")
    @TableField("material_id")
    private String materialId;
    /**
    * 商品名称
    */
    @ApiModelProperty(value = "商品名称")
    @TableField("material_name")
    private String materialName;
    /**
    * 商品标识
    */
    @ApiModelProperty(value = "商品标识")
    @TableField("material_flag")
    private String materialFlag;
    /**
    * 生产企业/厂商
    */
    @ApiModelProperty(value = "生产企业/厂商")
    @TableField("manufacturer")
    private String manufacturer;
    /**
    * 物料规格
    */
    @ApiModelProperty(value = "物料规格")
    @TableField("specification")
    private String specification;
    
    /**
     * 注册证号
     */
     @ApiModelProperty(value = "注册证号")
     @TableField("registration_no")
     private String registrationNo;
     /**
     * 注册证名称
     */
     @ApiModelProperty(value = "注册证名称")
     @TableField("registration_name")
     private String registrationName;
     /**
     * 注册证名称/备案人名称
     */
     @ApiModelProperty(value = "注册证名称/备案人名称")
     @TableField("registrant")
     private String registrant;
     /**
     * 注册证名称/备案人名称code
     */
     @ApiModelProperty(value = "注册证名称/备案人名称code")
     @TableField("registrant_code")
     private String registrantCode;
    /**
    * 客商编码
    */
    @ApiModelProperty(value = "客商编码")
    @TableField("custom_code")
    private String customCode;
    /**
    * 商品货品的购买数量
     * 主数量
    */
    @ApiModelProperty(value = "商品货品的购买数量")
    @TableField("material_primary_number")
    private Integer materialPrimaryNumber;
    /**
    * 辅数量
    */
    @ApiModelProperty(value = "商品货品的购买数量，辅数量")
    @TableField("material_assist_number")
    private BigDecimal materialAssistNumber;
    /**
    * 已采购数量
    */
    @ApiModelProperty(value = "已采购数量")
    @TableField("already_assist_number")
    private BigDecimal alreadyAssistNumber;
    /**
    * 创建时间
    */
    @ApiModelProperty(value = "创建时间")
    @TableField("create_time")
    private LocalDateTime createTime;
    /**
    * 物料状态（0-未请购，1 -已请购）
    */
    @ApiModelProperty(value = "物料状态（0-未请购，1 -已请购）")
    @TableField("status")
    private Integer status;
    /**
    * 更新时间
    */
    @ApiModelProperty(value = "更新时间")
    @TableField("update_time")
    private LocalDateTime updateTime;
    /**
    * 逻辑删除0-未删除，1-已删除
    */
    @ApiModelProperty(value = "逻辑删除0-未删除，1-已删除")
    @TableField("deleted")
    private Integer deleted;

    @ApiModelProperty(value = "业务单元编码")
    @TableField("org_code")
    private String orgCode;

    @ApiModelProperty(value = "辅单位")
    @TableField("secondary_unit")
    private String secondaryUnit;

    @ApiModelProperty(value = "存储条件")
    @TableField("material_storage_requirement")
    private String materialStorageRequirement;

    @ApiModelProperty(value = "供应商id")
    @TableField("supplier_id")
    private String supplierId;

    @ApiModelProperty(value = "供应商名称")
    @TableField("supplier_name")
    private String supplierName;

    /**
     * 源订单编号
     */
    @ApiModelProperty(value = "源订单编号")
    @TableField("source_order_id")
    private String sourceOrderId;
}