

package com.labway.business.center.scm.persistence.mapper.ncc;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.labway.business.center.scm.persistence.entity.ncc.BdCustomer;

import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * <p>
 * <B>Description: mapper类</B>
 * </P>
 * <AUTHOR>
 * @version 1.0
 */
@DS("ncc")
public interface BdCustomerMapper extends  BaseMapper<BdCustomer> {

    
    /**
     * 获取客户ID
     * @param customId
     * @return
     */
    public BdCustomer getCustomerById(@Param("cusId") String customId);

    public BdCustomer getCustomerByCode(@Param("cusCode") String customId);

    
    /**
     * 查询客户资质
     * @param customId
     * @return
     */
    public String getCustomerQualById(@Param("cusId") String customId);
    
    
    /**
     * 获取客户名称
     * @param financeOrgId
     * @return
     */
    public String getCustomerNameByFinanceOrgId(@Param("financeOrgId") String financeOrgId);
}
