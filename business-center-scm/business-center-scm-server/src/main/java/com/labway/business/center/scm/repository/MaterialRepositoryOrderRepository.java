package com.labway.business.center.scm.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.labway.business.center.scm.persistence.entity.MaterialRepositoryOrder;
import com.labway.business.center.scm.persistence.mapper.MaterialRepositoryOrderMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2023/08/31 16:35
 **/
@Repository
public class MaterialRepositoryOrderRepository {
    @Resource
    private MaterialRepositoryOrderMapper materialRepositoryOrderMapper;

    public List<MaterialRepositoryOrder> searchOrderByMaterialCodeAndOrgId(String materialCode, String orgId) {
        if (StringUtils.isAnyBlank(orgId,materialCode)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<MaterialRepositoryOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MaterialRepositoryOrder::getOrgId,orgId)
                .orderByAsc(MaterialRepositoryOrder::getSort);
        return materialRepositoryOrderMapper.selectList(queryWrapper);
    }

    public List<MaterialRepositoryOrder> searchOrderByMaterialCodesAndOrgId(String orgId) {
        if (StringUtils.isBlank(orgId)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<MaterialRepositoryOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MaterialRepositoryOrder::getOrgId,orgId)
                .orderByAsc(MaterialRepositoryOrder::getSort);
        return  materialRepositoryOrderMapper.selectList(queryWrapper);
    }
}