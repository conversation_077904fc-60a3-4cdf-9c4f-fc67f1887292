package com.labway.business.center.scm.service.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.labway.business.center.core.enums.ResultCode;
import com.labway.business.center.core.user.LoginUserInfoUtil;
import com.labway.business.center.scm.constants.PurchaseOrderStatus;
import com.labway.business.center.scm.constants.UserResultCode;
import com.labway.business.center.scm.converter.PurchaseOrderConverter;
import com.labway.business.center.scm.model.response.SupplierVo;
import com.labway.business.center.scm.model.response.sale.CustomVo;
import com.labway.business.center.scm.persistence.entity.PurchaseOrder;
import com.labway.business.center.scm.persistence.entity.PurchaseRequestOrder;
import com.labway.business.center.scm.persistence.entity.PurchaseRequestOrderMaterial;
import com.labway.business.center.scm.persistence.mapper.PurchaseRequestOrderMaterialMapper;
import com.labway.business.center.scm.repository.PurchaseOrderRepository;
import com.labway.business.center.scm.repository.PurchaseRequestOrderRepository;
import com.labway.business.center.scm.repository.UserMaterialRespository;
import com.labway.business.center.scm.service.PurchaseCustomerService;
import com.swak.frame.dto.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Tianhao
 * @version 2023/08/31 10:58
 **/
@Slf4j
@DubboService
public class PurchaseCustomerServiceImpl implements PurchaseCustomerService{
    @Resource
    private PurchaseOrderRepository purchaseOrderRepository;
    @Resource
    private PurchaseRequestOrderRepository purchaseRequestOrderRepository;
    @Resource
    private PurchaseRequestOrderMaterialMapper purchaseRequestOrderMaterialMapper;
    @Resource
    private UserMaterialRespository userMaterialRespository;
    @Resource
    private PurchaseOrderConverter purchaseOrderConverter;

    @Override
    public Response<Collection<CustomVo>> getPurchaseRequestOrderCustomer(String orgId) {
        if (StringUtils.isEmpty(orgId)) {
            return Response.fail(ResultCode.PARAMS_NOT_VALIDATE);
        }
        String userId = LoginUserInfoUtil.getUserId();
        if (StringUtils.isBlank(orgId) || StringUtils.isBlank(userId)) {
            return Response.builder(UserResultCode.PARAMS_ERROR);
        }

        List<String> list = userMaterialRespository.getUserMaterialFlags(userId, orgId);
        if (CollectionUtils.isEmpty(list)) {
            return Response.success();
        }
        Map<String, Object> paramsMap = Maps.newHashMap();
        paramsMap.put("flags", list);
        paramsMap.put("orgId", orgId);

        List<PurchaseRequestOrderMaterial> purchaseReqestOrderMaterials =
                purchaseRequestOrderMaterialMapper.getUserPurchaseRequestOrder(paramsMap);
        Set<String> purchaseRequestOrderId = purchaseReqestOrderMaterials.stream().map(PurchaseRequestOrderMaterial::getPurchaseRequestOrderId).collect(Collectors.toSet());
        List<PurchaseRequestOrder> requestOrders = purchaseRequestOrderRepository.getPurchaseRequestByPurchaseRequestOrderIds(purchaseRequestOrderId);
        if (CollectionUtils.isEmpty(requestOrders)) {
            return Response.success();
        }
        List<CustomVo> customVos = purchaseOrderConverter.convertPurchaseRequestOrderList2CustomerVos(requestOrders);

        Collection<CustomVo> resultCustomer = customVos.stream().collect(Collectors.toMap(e -> e.getCustomerId(), p -> p, (o1, o2) -> o2)).values();

        return Response.success(resultCustomer);
    }

    /**
     * 已采购订单的供应商
     *
     * @param orgId
     * @param status
     * @return
     */
    @Override
    public Response<Collection<SupplierVo>> getPurchaseOrderSupplier(String orgId) {
        if (StringUtils.isBlank(orgId)) {
            return Response.fail(ResultCode.PARAMS_NOT_VALIDATE);
        }
        List<PurchaseOrder> purchaseOrders = purchaseOrderRepository.getPurchaseOrdersByUserIdAndSuplierId(LoginUserInfoUtil.getUserId(),
                orgId, List.of(PurchaseOrderStatus.BUYING.getCode(),PurchaseOrderStatus.HAS_IN_KU.getCode(),PurchaseOrderStatus.PART_HAS_IN_KU.getCode()), null);
        if (CollectionUtils.isEmpty(purchaseOrders)) {
            return Response.success();
        }

        if (CollectionUtils.isEmpty(purchaseOrders)) {
            return Response.success();
        }
        Set<SupplierVo> supplierVos = Sets.newHashSet();
        for (PurchaseOrder purchaseOrder : purchaseOrders) {
            SupplierVo supplierVo = new SupplierVo();
            supplierVo.setSupplierId(purchaseOrder.getSupplierId());
            supplierVo.setSupplierName(purchaseOrder.getSupplierName());
            supplierVos.add(supplierVo);
        }
        return Response.success(supplierVos);
    }
}