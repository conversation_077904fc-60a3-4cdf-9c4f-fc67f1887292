

package com.labway.business.center.scm.persistence.mapper.ncc;

import java.util.List;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.labway.business.center.scm.persistence.entity.ncc.BdMaterial;



/**
 * <p>
 * <B>Description: mapper类</B>
 * </P>
 * <AUTHOR>
 * @version 1.0
 */
@DS("ncc")
public interface BdMaterialMapper extends  BaseMapper<BdMaterial> {
    
    public List<BdMaterial> getMaterialByIds(List<String> ids);

   
}
