 package com.labway.business.center.scm.service.impl.ncc;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.labway.business.center.scm.constants.UserResultCode;
import com.labway.business.center.scm.model.control.NccControlCheckBody;
import com.labway.business.center.scm.model.control.NccControlCheckHeader;
import com.labway.business.center.scm.model.control.NccControlCheckRequest;
import com.labway.business.center.scm.persistence.entity.ncc.BdCustomer;
import com.labway.business.center.scm.persistence.entity.ncc.BdMaterial;
import com.labway.business.center.scm.persistence.entity.ncc.LwCustdetail;
import com.labway.business.center.scm.persistence.entity.ncc.LwMatercontrol;
import com.labway.business.center.scm.persistence.entity.ncc.OrgOrgs;
import com.labway.business.center.scm.persistence.mapper.ncc.BdCustomerMapper;
import com.labway.business.center.scm.persistence.mapper.ncc.BdDefdocMapper;
import com.labway.business.center.scm.persistence.mapper.ncc.BdMaterialMapper;
import com.labway.business.center.scm.persistence.mapper.ncc.BdSupplierMapper;
import com.labway.business.center.scm.persistence.mapper.ncc.LwCustdetailMapper;
import com.labway.business.center.scm.persistence.mapper.ncc.LwMatercontrolMapper;
import com.labway.business.center.scm.persistence.mapper.ncc.LwSuppcontrolMapper;
import com.labway.business.center.scm.persistence.mapper.ncc.LwSuppdetailMapper;
import com.labway.business.center.scm.persistence.mapper.ncc.OrgOrgsMapper;
import com.labway.business.center.scm.persistence.mapper.ncc.PubSysinitMapper;
import com.labway.business.center.scm.persistence.params.CustControllParams;
import com.labway.business.center.scm.service.ncc.NccSaleOutBoundControlFormService;
import static com.labway.business.center.scm.util.NameUtil.*;
import com.labway.business.center.scm.util.UFDate;
import com.swak.frame.dto.Response;

@Service
public class NccSaleOutBoundControlFormServiceImpl implements NccSaleOutBoundControlFormService {

    @Resource
    private OrgOrgsMapper orgOrgsMapper;
    @Resource
    private BdCustomerMapper bdCustomerMapper;

    @Resource
    private PubSysinitMapper pubSysinitMapper;

    @Resource
    private BdMaterialMapper bdMaterialMapper;

    @Resource
    private LwMatercontrolMapper lwMatercontrolMapper;

    @Resource
    private BdDefdocMapper bdDefdocMapper;

    @Resource
    private LwCustdetailMapper lwCustdetailMapper;
    
    @Resource
    private BdSupplierMapper bdSupplierMapper;
    
    @Resource
    private LwSuppcontrolMapper lwSuppcontrolMapper;
    
    @Resource
    private LwSuppdetailMapper lwSuppdetailMapper;
    @Override
    public Response<List<String>> checkNccSaleOutBound(NccControlCheckRequest request) {
        Response<List<String>> response = Response.success();
        NccControlCheckHeader header = request.getCheckHeader();
        List<NccControlCheckBody> bodys = request.getCheckBodies();
        String orgId = header.getPkOrg();
        //获取组织对应的供应商
        String pkgg = bdCustomerMapper.getCustomerNameByFinanceOrgId(orgId);
        String isyn= pubSysinitMapper.getParamterSettingControll(orgId, "IC999");
        //判断销售是否受控
        if (!"Y".equals(isyn)) {
            return response;
        }
        OrgOrgs orgs = orgOrgsMapper.getOrgByOrgId(orgId);
        //判断业务单元是否受控
        String orgdata = orgs.getDef2();
        if (!"Y".equals(orgdata)) {
            return response;
        }
        
        //判断业务单元是否允许进行控制
        String df3 = orgs.getDef3();//是否首营受控
        
        //客户信息ccustomerid
        String ccustomerid=header.getCcustomerid();
        //
        BdCustomer customer = bdCustomerMapper.getCustomerById(ccustomerid);
        if (Objects.isNull(customer)) {
            return Response.builder(UserResultCode.CUSTOM_NOT_FOUND);
        }
        //判断客户属性销售是否受控
        String khdef4 = customer.getDef4();
        if (!"Y".equals(khdef4)) {
            return response;
        }
        List<String> matpklist =
            bodys.stream().map(mat -> mat.getMaterialId()).collect(Collectors.toList());
        //物料信息
        List<BdMaterial> materials = bdMaterialMapper.getMaterialByIds(matpklist);

        Map<String, BdMaterial> materialMap =
            materials.stream().collect(Collectors.toMap(BdMaterial::getPkMaterial, Function.identity()));
        List<LwMatercontrol> matercontrols = lwMatercontrolMapper.getMeterControllByList(orgId, matpklist);

        // 首营单信息
        Map<String, LwMatercontrol> sydmap =
            matercontrols.stream().collect(Collectors.toMap(LwMatercontrol::getPkMaterial, Function.identity()));
        // 订单日期
        String ssdate = header.getOrderTime();
        UFDate dbilldate = new UFDate(ssdate.substring(0,10));
        //不通过行号记录
//      营业执照有效期不能小于订单日期
        List<String> yyzz =Lists.newArrayList();
//      医疗执业许可证有效期至不能小于订单日期
        List<String> ylzy = Lists.newArrayList();
//      不存在物料首营单
        List<String> syd = Lists.newArrayList();
//      供货产品不在客户经营范围内
        List<String> ghcp = Lists.newArrayList();
//      供货产品不在公司供应商经营范围内
        List<String> ghcpg = Lists.newArrayList();
//      经营到期日不能小于订单日期
        List<String> jydqr = Lists.newArrayList();
//      经营到期日不能小于订单日期
        List<String> jydqr1 = Lists.newArrayList();
        a:for (int i = 0; i < bodys.size(); i++) {
            NccControlCheckBody saleOrderBVO = bodys.get(i);
            String matid = saleOrderBVO.getMaterialId();
           BdMaterial materialVO=  materialMap.get(matid);
           String rowno = materialVO.getName();
            String  def34 =  CheckNull(customer.getDef34());
            if ("".equals(def34)) {
                yyzz.add(rowno);
                continue a;
            }
            //判断营业执照日期是否大于订单日期
            UFDate zzrq=new UFDate(def34.substring(0,10));
            boolean after2 = dbilldate.after(zzrq);
            if (after2) {
                yyzz.add(rowno);
                continue a;
            }
            //判断医疗执业许可证有效期至是否存在并判断是否大于订单日期
            String khdef32 = CheckNull(customer.getDef32());
            if (!"".equals(khdef32)) {
                UFDate zyxkrq=new UFDate(khdef32.substring(0, 10));
                if (zyxkrq.before(dbilldate)) {
                    ylzy.add(rowno);
                    continue a;
                }
                if (zyxkrq.after(dbilldate)) {
//                  throw new Exception("医疗执业许可证有效期至不能小于订单日期");
//                  ylzy.add(rowno);
                    continue a;
                }
            }
            //判断物料类别是否为非医疗器械
            String gllb=CheckNull(materialVO.getDef14());
            String jyfw=CheckNull(materialVO.getDef15());
            String gllbcode =  bdDefdocMapper.getDefCodeByDefId(gllb);

            if ("05".equals(gllbcode)) {
                //是非医疗器械返回Y
                continue a;
            }
            //判断公司自定义项首营是否需要管控
            if ("Y".equals(df3)) {
                //是否存在审批通过的物料首营单
                if (sydmap==null||sydmap.size()<=0||sydmap.get(matid)==null) {
                    syd.add(rowno);
                    continue a;
                }
            }
            
            CustControllParams.CustControllParamsBuilder paramsBuilder = CustControllParams.builder();
            paramsBuilder.customerId(pkgg).gllb(gllb).jyfw(jyfw).orgId(orgId);
            // 一句物料档案属性【管理类别+经营范围】到客户管控单中寻找【管控类别+经营范围】是否存在else {
            LwCustdetail gsbs = lwCustdetailMapper.getCustdetailBySearch(paramsBuilder.build());
            if (Objects.isNull(gsbs)) {
                ghcpg.add(rowno);
                continue a;
            }
            String enddate1 = CheckNull(gsbs.getEnddate());
            if (!"".equals(enddate1)) {
                String endd1 = enddate1.substring(0, 10);
                UFDate tenp1 = new UFDate(endd1);
                if (tenp1.before(dbilldate)) {
                    jydqr1.add(rowno);
                    continue a;
                }
            }
            //一句物料档案属性【管理类别+经营范围】到客户管控单中寻找【管控类别+经营范围】是否存在else {
           paramsBuilder = CustControllParams.builder();
            paramsBuilder.customerId(ccustomerid).gllb(gllb).jyfw(jyfw).orgId(orgId);
            // 一句物料档案属性【管理类别+经营范围】到客户管控单中寻找【管控类别+经营范围】是否存在else {
            LwCustdetail gysgkd = lwCustdetailMapper.getCustdetailBySearch(paramsBuilder.build());
            //客户资质
            if (Objects.isNull(gysgkd)) {
                ghcp.add(rowno);
                continue a;
            }
            String enddate = CheckNull(gysgkd.getEnddate());
            if (!"".equals(enddate)) {
                String endd = enddate.substring(0, 10);
                UFDate tenp = new UFDate(endd);
                if (tenp.before(dbilldate)) {
                    jydqr.add(rowno);
                }
            }
        }
        
        List<String> tipList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(yyzz)) {
            tipList.add("物料号:"+yyzz.toString()+"营业执照有效期为空或小于订单日期");
        }
        if (CollectionUtils.isNotEmpty(ylzy)) {
            tipList.add("物料号:"+ylzy.toString()+"医疗执业许可证有效期至不能小于订单日期");
        }
        if (CollectionUtils.isNotEmpty(syd)) {
            tipList.add("物料号:"+syd.toString()+"不存在物料首营单");
        }
        if (CollectionUtils.isNotEmpty(ghcpg)) {
            tipList.add("物料号:"+ghcpg.toString()+"供货产品不在公司经营范围内");
        }
        if (CollectionUtils.isNotEmpty(ghcp)) {
            tipList.add("物料号:"+ghcp.toString()+"供货产品不在客户经营范围内");
        }
        if (CollectionUtils.isNotEmpty(jydqr1)) {
            tipList.add("物料号:"+jydqr1.toString()+"公司经营到期日不能小于订单日期");
        }
        if (CollectionUtils.isNotEmpty(jydqr)) {
            tipList.add("物料号:"+jydqr.toString()+"客户经营到期日不能小于订单日期");
        }
        
        if (CollectionUtils.isEmpty(tipList)) {
         return response;   
        }
        response.setData(tipList);
        response.setCode(UserResultCode.CONTROL.getCode());
        response.setMsg(UserResultCode.CONTROL.getMsg());
        return response;
    }

}
