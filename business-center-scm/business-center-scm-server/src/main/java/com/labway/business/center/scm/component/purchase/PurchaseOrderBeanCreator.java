package com.labway.business.center.scm.component.purchase;

import java.util.Map;

import javax.annotation.PostConstruct;
import org.springframework.stereotype.Component;

import com.google.common.collect.Maps;
import com.labway.business.center.scm.config.CustomApplicationContextInitializer;
import com.labway.business.center.scm.constants.OperateStatus;
import com.labway.business.center.scm.model.request.purchase.OperatePurchaseOrderParams;
import com.swak.frame.dto.Response;

import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
public class PurchaseOrderBeanCreator {

    private Map<Integer, String> reportTypeService = Maps.newHashMap();

    @PostConstruct
    public void initBeanConfig() {
        reportTypeService.put(OperateStatus.CANCAL.getStatus(), "cancelPurchaseOrderProccessor");
        reportTypeService.put(OperateStatus.PASS.getStatus(), "passPurchaseOrderProccessor");
        reportTypeService.put(OperateStatus.BACK.getStatus(), "cancelPurchaseOrderProccessor");
        reportTypeService.put(OperateStatus.mark.getStatus(), "markPurchaseOrderProccessor");
        reportTypeService.put(OperateStatus.nuMark.getStatus(), "cancelMarkPurchaseOrderProccessor");
        reportTypeService.put(OperateStatus.unBuy.getStatus(), "cancelBuyPurchaseOrderProccessor");
    }

    public Response<String> doProccessor(OperatePurchaseOrderParams request) {
        PurchaseOrderProccessor proccessor = CustomApplicationContextInitializer.getContext()
                .getBean(reportTypeService.get(request.getType()), PurchaseOrderProccessor.class);
        try {
            return proccessor.operatePurchaseOrder(request);
        } catch (Exception e) {
            log.error("处理采购订单失败", e);
            return Response.fail(500, e.getMessage());
        }

    }

}
