package com.labway.business.center.scm.persistence.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 业务单元仓库等级关联表
 * @TableName tb_business_store
 */
@TableName(value ="tb_business_store")
@Data
public class BusinessStore implements Serializable {
    /**
     * 
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 业务单元ID
     */
    @TableField(value = "business_id")
    private String businessId;

    /**
     * 仓库id
     */
    @TableField(value = "store_id")
    private String storeId;

    /**
     * 仓库等级一级仓二级仓
     */
    @TableField(value = "level")
    private Integer level;

    /**
     * 状态(1 启用，0，未启用)
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * 是否删除
     */
    @TableField(value = "deleted")
    private Integer deleted;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}