<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.business.center.scm.persistence.mapper.ncc.BdBilltypeMapper">


<select id="getBdBillTypes" resultType="com.labway.business.center.scm.persistence.entity.ncc.BdBilltype">

select pk_billtypecode as billtypecode, billtypename as billtypename, pk_billtypeid as billtypeid from bd_billtype where ( istransaction = 'Y' and pk_group = '000101100000000004NT' and nvl ( islock, 'N' ) = 'N' and ( ( parentbilltype = '21' and pk_billtypecode in ( '21-06', '21-05', '21-04', '21-03', '21-02', '21-01', '21-Cxx-04', '21-Cxx-05', '21-Cxx-02', '21-Cxx-03', '21-Cxx-01' ) ) ) ) order by pk_billtypecode
</select>

<select id="getBdSaleBillTypes" resultType="com.labway.business.center.scm.persistence.entity.ncc.BdBilltype">

    select pk_billtypecode as billtypecode, billtypename as billtypename, pk_billtypeid as billtypeid
    from bd_billtype
    where (
                  istransaction = 'Y' and pk_group = '000101100000000004NT' and nvl ( islock, 'N' ) = 'N'
                  AND
                  ( ( parentbilltype = '30' and pk_billtypecode in ( '30-03', '30-Cxx-01', '30-Cxx-02', '30-Cxx-03', '30-Cxx-04', '30-Cxx-05', '30-Cxx-06', '30-Cxx-07') ) )
              )
    order by pk_billtypecode
</select>
    <select id="searchInOut" resultType="com.labway.business.center.scm.persistence.entity.ncc.BdBilltype">
        select pk_billtypecode as billtypecode, billtypename as billtypename, pk_billtypeid as billtypeid
        from bd_billtype
        where (istransaction = 'Y' and pk_group = '000101100000000004NT' and nvl(islock, 'N') = 'N' and
               ((parentbilltype = '45' and pk_billtypecode in ('45-Cxx-03', '45-Cxx-04'))))
        order by pk_billtypecode
    </select>
    <select id="searchOut" resultType="com.labway.business.center.scm.persistence.entity.ncc.BdBilltype">
        select pk_billtypecode as billtypecode, billtypename as billtypename, pk_billtypeid as billtypeid
        from bd_billtype
        where (istransaction = 'Y' and pk_group = '000101100000000004NT' and nvl(islock, 'N') = 'N' and
               ((parentbilltype = '4D' and
                 pk_billtypecode in ('4D-01', '4D-02', '4D-03', '4D-Cxx-02', '4D-Cxx-01', '4D-Cxx-03'))))
        order by pk_billtypecode
    </select>
</mapper>