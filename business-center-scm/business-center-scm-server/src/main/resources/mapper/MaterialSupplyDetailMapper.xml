<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.business.center.scm.persistence.mapper.MaterialSupplyDetailMapper">

    <resultMap id="BaseResultMap" type="com.labway.business.center.scm.persistence.entity.MaterialSupplyDetail">
            <id property="itemId" column="item_id" jdbcType="VARCHAR"/>
            <result property="orderCode" column="order_code" jdbcType="VARCHAR"/>
            <result property="supplyId" column="supply_id" jdbcType="VARCHAR"/>
            <result property="supplyNo" column="supply_no" jdbcType="VARCHAR"/>
            <result property="applyDetailId" column="apply_detail_id" jdbcType="VARCHAR"/>
            <result property="materialCode" column="material_code" jdbcType="VARCHAR"/>
            <result property="batchNo" column="batch_no" jdbcType="VARCHAR"/>
            <result property="invalidTime" column="invalid_time" jdbcType="VARCHAR"/>
            <result property="primaryNumber" column="primary_number" jdbcType="INTEGER"/>
            <result property="assistNumber" column="assist_number" jdbcType="DECIMAL"/>
            <result property="signNumber" column="sign_number" jdbcType="DECIMAL"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="returnNumber" column="return_number" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        item_id,order_code,supply_id,
        supply_no,apply_detail_id,material_code,
        batch_no,invalid_time,primary_number,
        assist_number,sign_number,remark
    </sql>
    <update id="updateBatchReturnNumber">
        update tb_material_supply_detail
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="return_number =case" suffix="end,">
                <foreach collection="list" item="item">
                    when item_id=#{item.itemId} then #{item.returnNumber}
                </foreach>
            </trim>
        </trim>
        WHERE
        item_id IN
        <foreach collection="list" index="index" item="i" separator="," open="(" close=")">
            #{i.itemId}
        </foreach>
    </update>
</mapper>
