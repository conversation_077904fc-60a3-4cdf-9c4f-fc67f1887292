<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.labway.business.center.scm.persistence.mapper.PkSupplierMapper">
    <select id="getSupplierList" resultType="com.labway.business.center.scm.persistence.entity.ncc.PkSupplier">
        select pk_org,
               code,
               name,
               pk_supplier,
               pk_supplier_main,
               pk_supplierclass
        from bd_supplier
        where 11 = 11
          and (enablestate = 2)
          and ((pk_supplier in
                (select pk_supplier from bd_suporg where pk_org = #{orgId} and enablestate = 2)))
        order by pk_org
    </select>
</mapper>