<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.labway.business.center.scm.persistence.mapper.ncc.BdSupplierAddressMapper">
    <select id="searchSupplierAddress" resultType="com.labway.business.center.scm.dto.SupplierAddressDTO">
        select ad.PK_ADDRESS as pkAddress,ad.DETAILINFO as address
        from bd_supaddress su join bd_address ad on su.PK_ADDRESS = ad.PK_ADDRESS
                              join BD_SUPPLIER sup on su.PK_SUPPLIER = sup.PK_SUPPLIER
        where sup.PK_SUPPLIER = #{supplierId}
    </select>
</mapper>