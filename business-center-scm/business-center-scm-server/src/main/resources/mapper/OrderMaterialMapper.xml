<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.business.center.scm.persistence.mapper.oms.OrderMaterialMapper">

    <resultMap id="BaseResultMap" type="com.labway.business.center.scm.persistence.entity.oms.OrderMaterial">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="orderCode" column="order_code" jdbcType="VARCHAR"/>
            <result property="materialName" column="material_name" jdbcType="VARCHAR"/>
            <result property="materialSn" column="material_sn" jdbcType="VARCHAR"/>
            <result property="shipmentType" column="shipment_type" jdbcType="TINYINT"/>
            <result property="number" column="number" jdbcType="INTEGER"/>
            <result property="outNumber" column="out_number" jdbcType="DECIMAL"/>
            <result property="rejectNumber" column="reject_number" jdbcType="DECIMAL"/>
            <result property="materialUrl" column="material_url" jdbcType="VARCHAR"/>
            <result property="materialBrand" column="material_brand" jdbcType="VARCHAR"/>
            <result property="materialSpecification" column="material_specification" jdbcType="VARCHAR"/>
            <result property="materialModel" column="material_model" jdbcType="VARCHAR"/>
            <result property="materialStorageRequirement" column="material_storage_requirement" jdbcType="VARCHAR"/>
            <result property="materialTypeCode" column="material_type_code" jdbcType="VARCHAR"/>
            <result property="materialTypeName" column="material_type_name" jdbcType="VARCHAR"/>
            <result property="materialStatus" column="material_status" jdbcType="INTEGER"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="deleted" column="deleted" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,order_code,material_name,
        material_sn,shipment_type,number,
        out_number,reject_number,material_url,
        material_brand,material_specification,material_model,
        material_storage_requirement,material_type_code,material_type_name,
        material_status,create_time,update_time,
        deleted
    </sql>
    <update id="updateBatch">
        <foreach collection="list" item="item" separator=";">
            update tb_order_material
            set out_number = #{item.outNumber}
            where id = #{item.id}
        </foreach>
    </update>
</mapper>
