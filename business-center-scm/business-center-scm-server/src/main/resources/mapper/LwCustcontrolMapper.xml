<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.business.center.scm.persistence.mapper.LwCustcontrolMapper">

<select id="getCustgkd" resultType="java.util.List">

select nvl(bb.vdef1,'~') || nvl(bb.vdef2,'~') || nvl(bz.pk_supplier,'~') || nvl(bz.pk_org,'~') as ssign from  lw_custdetail bb left join lw_custcontrol bz on bz.pk_custcontrol = bb.pk_custcontrol where nvl(bz.dr,0)=0 and nvl(bb.dr,0)=0 and  nvl(bb.vdef1,'~') || nvl(bb.vdef2,'~') || nvl(bz.pk_customer,'~') || nvl(bz.pk_org,'~')

in 

 <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
		  
</select>

</mapper>