<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.business.center.scm.persistence.mapper.StoreMapper">

    <resultMap type="com.labway.business.center.scm.persistence.entity.Store" id="StoreMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="storeId" column="store_id" jdbcType="VARCHAR"/>
        <result property="businessId" column="business_id" jdbcType="VARCHAR"/>
        <result property="code" column="code" jdbcType="VARCHAR"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="address" column="address" jdbcType="VARCHAR"/>
        <result property="principalCode" column="principal_code" jdbcType="VARCHAR"/>
        <result property="phone" column="phone" jdbcType="VARCHAR"/>
        <result property="enableState" column="enable_state" jdbcType="INTEGER"/>
        <result property="defaultAble" column="default_able" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into business-scm.tb_store(store_id, business_id, code, name, address, principal_code, phone, enable_state, default_able, create_time, update_time)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.storeId}, #{entity.businessId}, #{entity.code}, #{entity.name}, #{entity.address}, #{entity.principalCode}, #{entity.phone}, #{entity.enableState}, #{entity.defaultAble}, #{entity.createTime}, #{entity.updateTime})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into business-scm.tb_store(store_id, business_id, code, name, address, principal_code, phone, enable_state, default_able, create_time, update_time)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.storeId}, #{entity.businessId}, #{entity.code}, #{entity.name}, #{entity.address}, #{entity.principalCode}, #{entity.phone}, #{entity.enableState}, #{entity.defaultAble}, #{entity.createTime}, #{entity.updateTime})
        </foreach>
        on duplicate key update
         store_id = values(store_id) , business_id = values(business_id) , code = values(code) , name = values(name) , address = values(address) , principal_code = values(principal_code) , phone = values(phone) , enable_state = values(enable_state) , default_able = values(default_able) , create_time = values(create_time) , update_time = values(update_time)     </insert>

</mapper>

