<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.business.center.scm.persistence.mapper.BusinessStoreMapper">

    <resultMap type="com.labway.business.center.scm.persistence.entity.BusinessStore" id="BusinessStoreMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="businessId" column="business_id" jdbcType="VARCHAR"/>
        <result property="storeId" column="store_id" jdbcType="VARCHAR"/>
        <result property="level" column="level" jdbcType="INTEGER"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="deleted" column="deleted" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into business-scm.tb_business_store(business_id, store_id, level, status, deleted, create_time, update_time)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.businessId}, #{entity.storeId}, #{entity.level}, #{entity.status}, #{entity.deleted}, #{entity.createTime}, #{entity.updateTime})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into business-scm.tb_business_store(business_id, store_id, level, status, deleted, create_time, update_time)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.businessId}, #{entity.storeId}, #{entity.level}, #{entity.status}, #{entity.deleted}, #{entity.createTime}, #{entity.updateTime})
        </foreach>
        on duplicate key update
         business_id = values(business_id) , store_id = values(store_id) , level = values(level) , status = values(status) , deleted = values(deleted) , create_time = values(create_time) , update_time = values(update_time)     </insert>

</mapper>

