server:
  port: 9994
  compression:
    enabled: true #启用数据压缩

spring:
  application:
    name: business-center-mdm-server
  profiles:
    active: dev
  main:
    allow-bean-definition-overriding: true
#  datasource:
#    type: com.alibaba.druid.pool.DruidDataSource
#    druid:
#      # 初始化大小，最小，最大
#      initial-size: 5
#      min-idle: 2
#      max-active: 50
#      # 配置获取连接等待超时的时间
#      max-wait: 60000
#      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
#      time-between-eviction-runs-millis: 60000
#      time-between-log-stats-millis:
#      # 配置一个连接在池中最小生存的时间，单位是毫秒
#      min-evictable-idle-time-millis: 30000
#      # 校验SQL
#      test-while-idle: true
#      test-on-borrow: false
#      test-on-return: false
#      validation-query: select 'x'
#      pool-prepared-statements: true
#      max-pool-prepared-statement-per-connection-size: 20
#      connection-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
#      filters: stat,wall,log4j # 注意这个值和druid原生不一致，默认启动了stat,wall

  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  security:
    user:
      name: labway
      password: Lanwei2022
  dinger:
    project-id: 业务中台-主数据
    dingers:
      dingtalk:
        tokenId: c79914437e8d0f780b513688ce3ed5ce68208178a23c7ef2899faa7b3b1dc3f2
        secret: SECd1ebdd8058a49ad47079d919477fe035ed0d51bcdd671b34f181c4c25e12a7c8
#  cloud:
#    nacos:
#      discovery:
#        watch-delay: 1000
  cloud:
    nacos:
      server-addr: ${nacos.address}
      config:
        server-addr: ${nacos.address}
        namespace: ${nacos.config-namespace}
        file-extension: yaml
        shared-configs:
          - data-id: dubbo.yaml
            group: public
            refresh: true
          - data-id: business-center-common.yaml
            group: public
            refresh: true
      discovery:
        server-addr: ${nacos.address}
        namespace: ${nacos.discovery-namespace}
        watch-delay: 1000

eureka:
  instance:
    # 显示IP
    preferIpAddress: true
    instance-id: ${spring.application.name}:${spring.cloud.client.ip-address}:${server.port}
    status-page-url: https://${spring.cloud.client.ip-address}:${server.port}

mybatis-plus:
  typeAliasesPackage: com.labway.business.center.mdm
  global-config:
    db-config:
      logic-delete-value: 1
      logic-not-delete-value: 0
  configuration:
    map-underscore-to-camel-case: true   #配置返回数据库(column下划线命名&&返回java实体是驼峰命名)，自动匹配无需as（没开启这个，SQL需要写as： select user_id as userId）
    cache-enabled: true #配置的缓存的全局开关
    lazyLoadingEnabled: true #延迟加载的全局开关。当开启时，所有关联对象都会延迟加载
    multipleResultSetsEnabled: true #是否允许单一语句返回多结果集
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl #打印sql语句,调试用
  mapper-locations: classpath:/**/mappers/**/*.xml

huawei:
  obs:
    ak: L2NO0C3UJDB7SFYQKQIY
    sk: lSc1G2f52iTndVB0cpdDEU89ZjfcxjiSErOMlRDN
    endPoint: obs.cn-east-3.myhuaweicloud.com
    bucketName: labway-obs



---
spring:
  profiles: dev

nacos:
  address: ***********:8848
  config-namespace: ${spring.profiles.active}
  discovery-namespace: ${spring.profiles.active}


---
spring:
  profiles: test

nacos:
  address: ***********:8848
  config-namespace: ${spring.profiles.active}
  discovery-namespace: ${spring.profiles.active}

---
spring:
  profiles: uat

nacos:
  address: ***********:8848
  config-namespace: ${spring.profiles.active}
  discovery-namespace: ${spring.profiles.active}

---
spring:
  profiles: prod

nacos:
  address: ***********:8848
  config-namespace: ${spring.profiles.active}
  discovery-namespace: ${spring.profiles.active}
