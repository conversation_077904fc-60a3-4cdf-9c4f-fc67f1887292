package com.labway.business.center.mdm.reagent.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.labway.business.center.mdm.api.reagent.param.reagent.QueryAllSysDictRequest;
import com.labway.business.center.mdm.api.reagent.param.reagent.SysDictPageQueryRequest;
import com.labway.business.center.mdm.api.reagent.param.reagent.SysDictQueryRequest;
import com.labway.business.center.mdm.api.user.request.QueryDictRequest;
import com.labway.business.center.mdm.reagent.model.TbSysDict;
import com.swak.frame.dto.Response;

import java.io.Serializable;
import java.util.List;
import java.util.Set;
import java.util.function.BiConsumer;
import java.util.function.Function;


/**
 * 字典表操作封装
 */
public interface SysDictService extends Serializable {

    Response save(TbSysDict tbSysDict);

    Response save(String dictName, String dictNameDesc, List<TbSysDict> tbSysDictList);

    Response updateCodeInfoById(TbSysDict tbSysDict);

    Response deleteById(String dictId);

    Response deleteById(List<String> dictIdList);

    Response<List<TbSysDict>> listById(List<String> dictIdList);

    Response<List<TbSysDict>> listEnabledByDictNames(List<String> dictNameList);

    Response<List<TbSysDict>> listByDictNames(List<String> dictNameList, QueryDictRequest request);

    Response<List<TbSysDict>> listByDictNameAndCodes(String dictName, Set<String> dictCodeList);

    Response<List<TbSysDict>> listEnabledByCondition(SysDictQueryRequest request);

    Response<IPage<TbSysDict>> pageByCondition(SysDictPageQueryRequest request);

    /**
     * 翻译字典code to value
     */
    <T> void convertCodeToValue(String dictName, List<T> dataList, Function<T, String> getCodeDo, BiConsumer<T, String> setValueDo);

    /**
     * 翻译字典value to code
     */
    <T> void convertValueToCode(String dictName, List<T> dataList, Function<T, String> getValueDo, BiConsumer<T, String> setCodeDo);

    Response<List<TbSysDict>> listByDictNameAndValues(String dictName, Set<String> dictValueList);

    /**
     * 查询全部字典，包括未启用和逻辑删除
     */
    List<TbSysDict> listAll(QueryAllSysDictRequest request);

    /**
     * 查询全部字典，包括未启用和逻辑删除
     */
    List<TbSysDict> listByDictCode(String dictName, String dictCode);
}
