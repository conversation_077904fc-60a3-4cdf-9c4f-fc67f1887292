package com.labway.business.center.mdm.util;

import com.labway.sso.core.enums.SsoUserPluginsEnum;
import com.labway.sso.core.user.SsoUser;
import com.labway.sso.starter.common.SsoUserHelper;

import java.util.List;


/**
 * 用户信息工具类
 *
 * <AUTHOR>
 * @version 2023/05/15 15:20
 **/
public class LoginUserInfoUtil {
    /**
     * 获取当前登录人信息
     *
     * @return
     */
    public static SsoUser getLoginUser() {
        return SsoUserHelper.getLoginUser();
    }

    /**
     * 获取当前用户名称
     *
     * @return
     */
    public static String getUserUserName() {
        SsoUser loginUser = getLoginUser();
        return loginUser.getUserName();
    }

    /**
     * 获取扩展信息中的值
     *
     * @return
     * @see SsoUserPluginsEnum
     */
    public static List<String> getUserHasOrgs() {
        SsoUser loginUser = SsoUserHelper.getLoginUser();
        return (List<String>) loginUser.getPlugininfo().get(SsoUserPluginsEnum.ORG.getKey());
    }

    /**
     * 获取用户的登录名
     *
     * @return
     */
    public static String getUserLoginName() {
        SsoUser loginUser = SsoUserHelper.getLoginUser();
        return loginUser.getLoginName();
    }
}
