package com.labway.business.center.mdm.reagent.controller;

import com.labway.business.center.core.enums.OperationTypeEnum;
import com.labway.business.center.core.enums.SystemModuleEmun;
import com.labway.business.center.mdm.annotation.LogAnnotation;
import com.labway.business.center.mdm.api.reagent.dto.AddProvinceItemRelationDTO;
import com.labway.business.center.mdm.api.reagent.dto.DeleteItemIdDTO;
import com.labway.business.center.mdm.reagent.service.ITbProvinceItemRelationService;
import com.swak.frame.dto.Response;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

@RestController
@RequestMapping("/tbProvinceItemRelation")
public class TbProvinceItemRelationController {

    @Resource
    private ITbProvinceItemRelationService iTbProvinceItemRelationService;


    @LogAnnotation(value = "添加省级检验项目-报告项目", OperationType = OperationTypeEnum.SAVE, SystemModule = SystemModuleEmun.MDM_SERVER)
    @PostMapping("/add")
    public Response addProvinceItemRelation(@RequestBody @Valid AddProvinceItemRelationDTO addProvinceItemRelationDTO){
        return iTbProvinceItemRelationService.addProvinceItemRelation(addProvinceItemRelationDTO);
    }

    @LogAnnotation(value = "删除省级检验项目-报告项目", OperationType = OperationTypeEnum.DELETE, SystemModule = SystemModuleEmun.MDM_SERVER)
    @PostMapping("/delete")
    public Response deleteProvinceItemRelation(@RequestBody DeleteItemIdDTO deleteItemIdDTO){
        return iTbProvinceItemRelationService.deleteProvinceItemRelation(deleteItemIdDTO);
    }


}
