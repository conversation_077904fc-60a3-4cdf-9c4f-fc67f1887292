package com.labway.business.center.mdm.reagent.service.impl;

import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.labway.business.center.mdm.api.reagent.dto.AddItemReportDto;
import com.labway.business.center.mdm.api.reagent.dto.DeleteItemReportBatchDto;
import com.labway.business.center.mdm.api.reagent.dto.ImportItemReportDto;
import com.labway.business.center.mdm.api.reagent.dto.ItemReportQueryDto;
import com.labway.business.center.mdm.api.reagent.dto.QueryItemReportPageDto;
import com.labway.business.center.mdm.api.reagent.dto.UpdateItemReportDto;
import com.labway.business.center.mdm.api.reagent.param.reagent.ItemReportVo;
import com.labway.business.center.mdm.api.reagent.param.reagent.QueryItemReportPageVo;
import com.labway.business.center.mdm.common.enums.OptUserDefaultEnum;
import com.labway.business.center.core.enums.ResultCode;
import com.labway.business.center.mdm.common.enums.StatusEnum;
import com.labway.business.center.mdm.reagent.excel.ItemReportImportDTO;
import com.labway.business.center.mdm.reagent.mapper.dal.TbItemReportDal;
import com.labway.business.center.mdm.reagent.mapper.mdm.TbItemReportMapper;
import com.labway.business.center.mdm.reagent.model.TbItemRelation;
import com.labway.business.center.mdm.reagent.model.TbItemReport;
import com.labway.business.center.mdm.reagent.service.ITbProvinceItemRelationService;
import com.labway.business.center.mdm.reagent.service.TbItemRelationService;
import com.labway.business.center.mdm.reagent.service.TbItemReportService;
import com.labway.business.center.mdm.util.ExtraCollectionUtil;
import com.labway.business.center.mdm.util.SerialIdUtil;
import com.swak.frame.dto.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 报告项目表(TbItemReport)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-04-03 09:17:26
 */
@Slf4j
@Service("tbItemReportService")
public class TbItemReportServiceImpl extends ServiceImpl<TbItemReportMapper, TbItemReport> implements TbItemReportService {

    @Autowired
    private TbItemReportDal tbItemReportDal;
    @Autowired
    private TbItemRelationService tbItemRelationService;
    @Autowired
    private SerialIdUtil serialIdUtil;

    @Autowired
    @Lazy
    private ITbProvinceItemRelationService iTbProvinceItemRelationService;


    @Override
    public List<QueryItemReportPageVo> queryItemReportAll(QueryItemReportPageDto queryDto) {
        return tbItemReportDal.queryItemReportAll(queryDto);
    }

    /**
     * 分页查询报告项目信息
     *
     * @param queryDto
     * @return
     */
    @Override
    public Response<Page<QueryItemReportPageVo>> queryItemReportPage(QueryItemReportPageDto queryDto) {
        Page<QueryItemReportPageVo> resultList = tbItemReportDal.queryItemReportPage(queryDto);
        return Response.success(resultList);
    }

    /**
     * 新增报告项目信息（编码和名称不能重复）
     *
     * @param addDto
     * @return
     */
    @Override
    public Response<Integer> addItemReport(AddItemReportDto addDto) {

        // 根据类型编码或者名称查询样本类型
        ItemReportQueryDto queryDto = new ItemReportQueryDto();
        BeanUtils.copyProperties(addDto, queryDto);
        List<TbItemReport> sampleTypes = tbItemReportDal.queryItemReportByCode(queryDto);
        if (!CollectionUtils.isEmpty(sampleTypes)) {
            log.info("报告项目编码已存在！");
            return Response.fail(ResultCode.E112003);
        }
        Date now = new Date();
        addDto.setItemId(serialIdUtil.getSerialNumber());
        addDto.setCreateTime(now);
        addDto.setUpdateTime(now);
        addDto.setOptUserCode(OptUserDefaultEnum.default_user.getOptUserCode());
        addDto.setOptUserAccount(OptUserDefaultEnum.default_user.getOptUserAccount());
        addDto.setOptUserName(OptUserDefaultEnum.default_user.getOptUserName());
        addDto.setEnabled(Boolean.TRUE);
        ;

        return Response.success(tbItemReportDal.addItemReport(addDto));
    }

    /**
     * 修改报告项目信息（编码和名称不能重复）
     *
     * @param updateDto
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Response<Integer> updateItemReport(UpdateItemReportDto updateDto) {

        // 根据类型编码或者名称查询样本类型
        ItemReportQueryDto queryDto = new ItemReportQueryDto();
        BeanUtils.copyProperties(updateDto, queryDto);
        Date now = new Date();

        String itemReportCode = updateDto.getItemReportCode();
        updateDto.setUpdateTime(now);
        updateDto.setOptUserCode(OptUserDefaultEnum.default_user.getOptUserCode());
        updateDto.setOptUserAccount(OptUserDefaultEnum.default_user.getOptUserAccount());
        updateDto.setOptUserName(OptUserDefaultEnum.default_user.getOptUserName());
        updateDto.setEnabled(Boolean.TRUE);
        updateDto.setItemReportCode(null);
        // 修改报告项目
        tbItemReportDal.updateItemReport(updateDto);
        // 修改省级报告项目
        iTbProvinceItemRelationService.updateReportNameByReportCode(itemReportCode, updateDto.getItemReportName(), "1");
        return Response.success();
    }

    /**
     * 删除报告项目信息（判断报告项目是否被检验项目引用到--被引用则无法删除）
     *
     * @param deleteDto
     * @return
     */
    @Override
    public Response<Integer> deleteItemReportBatch(DeleteItemReportBatchDto deleteDto) {

        // 查询项目关联关系表，判断报告项目是否被引用到
        List<TbItemRelation> tbItemRelations = tbItemRelationService.queryItemRelationByReportCodes(deleteDto.getReportCodes());
        if (!CollectionUtils.isEmpty(tbItemRelations)) {
            StringJoiner sj = new StringJoiner(",").add("删除失败");
            for (TbItemRelation tbItemRelation : tbItemRelations) {
                sj.add("集团检验项目编码["+ tbItemRelation.getItemTestCode() +"]正在引用集团报告项目编码["+tbItemRelation.getItemReportCode()+"]");
            }
            return Response.fail(112007, sj.toString());
        }
        return Response.success(tbItemReportDal.deleteItemReportBatch(deleteDto));
    }

    /**
     * 根据报告项目编码查询报告项目集合-批量
     *
     * @param reportCodes
     * @return
     */
    @Override
    public List<ItemReportVo> queryItemReportListByCodes(List<String> reportCodes) {
        return tbItemReportDal.queryItemReportListByCodes(reportCodes);
    }

    /**
     * 导入报告项目信息
     * @param importDto
     * @return
     */
    @Override
    public Response<Integer> importItemReport(ImportItemReportDto importDto) {

        // 根据文件地址获取文件steam
        RestTemplate restTemplate = new RestTemplate();
        ResponseEntity<byte[]> forEntity = restTemplate.getForEntity(importDto.getUrl(), byte[].class);
        InputStream fileStream = new ByteArrayInputStream(forEntity.getBody());

        // 读取第一个sheet页，解析为外部检验项目
        List<ItemReportImportDTO> itemList = EasyExcel.read(fileStream)
                .head(ItemReportImportDTO.class)
                .sheet(0)
                .doReadSync();
        if (CollectionUtils.isEmpty(itemList)){
            return Response.fail(ResultCode.E112014);
        }

        Date now = new Date();

        // 校验报告项目合法性--编码不重复,名称不重复,和数据库已有项目不重复
        Map<Integer, List<String>> errorMap = checkItemReport(itemList);
        if (errorMap.size()>0){
            String errorInfo = TbItemTestServiceImpl.parseErrorsMap(errorMap);
            return Response.fail(ResultCode.E112015.getCode(), ResultCode.E112015.getMsg() + errorInfo);
        }

        //  新增报告项目-批量
        List<TbItemReport> insertList = itemList.stream().map(e->fillItemReport(e,now)).collect(Collectors.toList());

        return Response.success(tbItemReportDal.saveItemReportBatch(insertList));
    }

    // 校验错误信息
    private Map<Integer, List<String>> checkItemReport(List<ItemReportImportDTO> itemList) {

        // 补充行号
        for (int num = 0; num < itemList.size(); num++) {
            // 所在行行号
            // 序号从0开始，且excel首行为表头，所以加2
            itemList.get(num).setRowNum(num + 2);
        }

        Map<Integer, List<String>> errorMap = new TreeMap<>();

        // 编码为空校验
        List<ItemReportImportDTO> emptyCodes = itemList.stream().filter(e -> !StringUtils.hasText(e.getItemReportCode())).collect(Collectors.toList());
        for (ItemReportImportDTO dto : emptyCodes) {
            // 错误行号
            int rowNum = dto.getRowNum();
            List<String> errors = ExtraCollectionUtil.getOrDefaultAndPut(errorMap, rowNum, () -> new ArrayList<>());
            errors.add("报告项目编码为空");
        }

        // 名称为空校验
        List<ItemReportImportDTO> emptyNames = itemList.stream().filter(e -> !StringUtils.hasText(e.getItemReportName())).collect(Collectors.toList());
        for (ItemReportImportDTO dto : emptyNames) {
            // 错误行号
            int rowNum = dto.getRowNum();
            List<String> errors = ExtraCollectionUtil.getOrDefaultAndPut(errorMap, rowNum, () -> new ArrayList<>());
            errors.add("报告项目名称为空");
        }

        // 编码重复行数校验
        Map<String, List<ItemReportImportDTO>> itemCodeGroup = itemList.stream().filter(e -> StringUtils.hasText(e.getItemReportCode())).collect(Collectors.groupingBy(ItemReportImportDTO::getItemReportCode));
        for (Map.Entry<String, List<ItemReportImportDTO>> codeEntry : itemCodeGroup.entrySet()) {
            List<ItemReportImportDTO> tempItem = codeEntry.getValue();
            if (tempItem.size()>1){
                for (ItemReportImportDTO dto : tempItem) {
                    // 错误行号
                    int rowNum = dto.getRowNum();
                    List<String> errors = ExtraCollectionUtil.getOrDefaultAndPut(errorMap, rowNum, () -> new ArrayList<>());
                    errors.add("报告项目编码在导入文件重复");
                }
            }
        }

        // 数据库项目判重校验
        List<TbItemReport> existsCodes = tbItemReportDal.queryItemCodes(itemCodeGroup.keySet());
        Map<String, List<TbItemReport>> existsCodeMap = existsCodes.stream().collect(Collectors.groupingBy(TbItemReport::getItemReportCode));
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(existsCodes)) {
            for (ItemReportImportDTO dto : itemList) {
                List<TbItemReport> repeatCode = existsCodeMap.get(dto.getItemReportCode());
                if (!CollectionUtils.isEmpty(repeatCode)) {
                    // code重复
                    int rowNum = dto.getRowNum();
                    List<String> errors = ExtraCollectionUtil.getOrDefaultAndPut(errorMap, rowNum, () -> new ArrayList<>());
                    errors.add("报告项目编码已存在");
                }
            }
        }

        return errorMap;
    }

    // 导入报告项目填充
    private TbItemReport fillItemReport(ItemReportImportDTO tbItemReport, Date now) {
        TbItemReport temp = new TbItemReport();
        BeanUtils.copyProperties(tbItemReport,temp);

        temp.setItemId(serialIdUtil.getSerialNumber());
        temp.setCreateTime(now);
        temp.setUpdateTime(now);
        temp.setOptUserCode(OptUserDefaultEnum.default_user.getOptUserCode());
        temp.setOptUserName(OptUserDefaultEnum.default_user.getOptUserName());
        temp.setOptUserAccount(OptUserDefaultEnum.default_user.getOptUserAccount());
        temp.setEnabled(Boolean.TRUE);
        temp.setStatus(StatusEnum.enable.getValue());
        return temp;
    }

    private InputStream getFileByUrl(String url) {
        RestTemplate restTemplate = new RestTemplate();
        ResponseEntity<byte[]> forEntity = restTemplate.getForEntity(url, byte[].class);
        return new ByteArrayInputStream(forEntity.getBody());
    }


}

