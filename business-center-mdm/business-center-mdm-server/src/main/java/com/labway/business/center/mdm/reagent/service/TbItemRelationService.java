package com.labway.business.center.mdm.reagent.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.labway.business.center.mdm.api.reagent.dto.DeleteItemRelationDto;
import com.labway.business.center.mdm.api.reagent.dto.SaveItemRelationDto;
import com.labway.business.center.mdm.api.reagent.dto.customer.ItemReportSimpleBatchDto;
import com.labway.business.center.mdm.api.reagent.dto.customer.ItemReportSimpleDto;
import com.labway.business.center.mdm.api.reagent.param.reagent.ItemReportSimpleBatchVo;
import com.labway.business.center.mdm.api.reagent.param.reagent.ItemReportSimpleVo;
import com.labway.business.center.mdm.reagent.excel.ItemTestRelationImportDTO;
import com.labway.business.center.mdm.reagent.model.TbItemRelation;
import com.swak.frame.dto.Response;

import java.util.List;

/**
 * 检验项目和报告项目关联关系表(TbItemRelation)表服务接口
 *
 * <AUTHOR>
 * @since 2023-04-03 15:37:49
 */
public interface TbItemRelationService extends IService<TbItemRelation> {


    /**
     * 根据报告项目编码查询项目关联关系-批量
     *
     * @param reportCodes
     * @return
     */
    List<TbItemRelation> queryItemRelationByReportCodes(List<String> reportCodes);

    /**
     * 根据检验项目编码删除项目关联关系
     *
     * @param testCodes
     * @return
     */
    int deleteItemRelationByTestCodes(List<String> testCodes);

    /**
     * 根据检验项目编码查询项目关联关系
     *
     * @param testCode
     * @return
     */
    List<TbItemRelation> queryItemRelationByTestCode(String testCode);

    /**
     * 根据检验项目编码批量删除关联的报告项目信息（检验项目编码-报告项目编码）
     *
     * @param deleteDto
     * @return
     */
    Response<Integer> deleteItemRelation(DeleteItemRelationDto deleteDto);

    /**
     * 批量保存检验项目和报告项目关联关系（不影响已有的关联关系，需要过滤掉重复的报告项目）
     *
     * @param saveDto
     * @return
     */
    Response<Integer> saveItemRelation(SaveItemRelationDto saveDto);

    /**
     * 保存检验项目与报告项目的对照关系（批量，不过滤重复数据，默认认为已进行了重复校验）
     *
     * @param relationImportDTOList
     * @return
     */
    Integer saveItemTestRelations(List<ItemTestRelationImportDTO> relationImportDTOList);

    /**
     * 根据检验项目查询关联的报告项目-业务中台查询调用
     *
     * @param queryDto
     * @return
     */
    List<ItemReportSimpleVo> queryItemReportList(ItemReportSimpleDto queryDto);

    /**
     * 根据检验项目查询关联的报告项目(批量)
     *
     * @param queryDto
     * @return
     */
    List<ItemReportSimpleBatchVo> queryItemReportListBatch(ItemReportSimpleBatchDto queryDto);

    /**
     * 根据检验项目编码和报告项目编码查询（批量）
     *
     * @param entities
     * @return
     */
    List<TbItemRelation> queryItemRelationByItemAndReportCodes(List<TbItemRelation> entities);
}

