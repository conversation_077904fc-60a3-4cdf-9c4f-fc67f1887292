package com.labway.business.center.mdm.user.mq;

import com.labway.business.center.mdm.common.constants.RabbitConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description 消费者
 * @Date 2023/3/8 17:10
 */
@Slf4j
@Component
@RabbitListener(queues = RabbitConstant.RABBITMQ_OA_TOPIC)
public class Consumer {
    @RabbitHandler
    public void process(String message) {
        log.info("--------------监听到  {}  的消息--------------", RabbitConstant.RABBITMQ_OA_TOPIC);
        log.info(message);
    }
}
