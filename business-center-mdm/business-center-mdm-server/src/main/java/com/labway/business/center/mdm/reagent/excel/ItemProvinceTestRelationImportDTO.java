package com.labway.business.center.mdm.reagent.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.labway.business.center.mdm.common.file.annotation.Excel;
import lombok.Data;

@Data
public class ItemProvinceTestRelationImportDTO {



    @Excel(value = "检验项目编码", index = 0)
    @ExcelProperty("检验项目编码")
    private String itemTestCode;

    @Excel(value = "报告项目编码", index = 1)
    @ExcelProperty("报告项目编码")
    private String itemReportCode;

    @Excel(value = "报告项目名称", index = 2)
    @ExcelProperty("报告项目名称")
    private String itemReportName;

    @Excel(value = "集团or省份", index = 3)
    @ExcelProperty("集团or省份")
    private String companyOrProvince;


    // 所在行号
    private int rowNum;

    // 检验项目id
    private String testId;

    // 报告项目id
    private String reportId;

    private String provinceCode;

    private String provinceName;

    /**
     * 是否是集团
     */
    public boolean getIsCompany() {
        return companyOrProvince.trim().equals("集团");
    }
}
