package com.labway.business.center.mdm.reagent.server;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.labway.business.center.mdm.api.reagent.dto.DeleteItemRelationDto;
import com.labway.business.center.mdm.api.reagent.dto.page.Pager;
import com.labway.business.center.mdm.api.reagent.dto.SaveItemRelationDto;
import com.labway.business.center.mdm.api.reagent.dto.TbItemRelationDto;
import com.labway.business.center.mdm.api.reagent.service.ItemRelationService;
import com.labway.business.center.mdm.common.util.BeanCopyUtils;
import com.labway.business.center.mdm.reagent.model.TbItemRelation;
import com.labway.business.center.mdm.reagent.service.TbItemRelationService;
import com.swak.frame.dto.Response;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.Serializable;
import java.util.List;

/**
 * 检验项目和报告项目关联关系表(TbItemRelation)表控制层
 *
 * <AUTHOR>
 * @since 2023-04-03 15:37:49
 */
@DubboService
public class ItemRelationServiceImpl implements ItemRelationService {
    /**
     * 服务对象
     */
    @Resource
    private TbItemRelationService tbItemRelationService;

    /**
     * 分页查询所有数据
     *
     * @param pager           分页对象
     * @param tbItemRelationDto 查询实体
     * @return 所有数据
     */
    public Response<Pager<TbItemRelationDto>> selectAll(Pager<TbItemRelationDto> pager, TbItemRelationDto tbItemRelationDto) {

        Page<TbItemRelation> page = new Page<>();
        BeanUtils.copyProperties(pager, page);
        TbItemRelation tbItemRelation = new TbItemRelation();
        BeanUtils.copyProperties(tbItemRelationDto, tbItemRelation);
        Page<TbItemRelation> pageResult = tbItemRelationService.page(page, new QueryWrapper<>(tbItemRelation));

        Pager<TbItemRelationDto> pagerResult = new Pager<>();
        BeanUtils.copyProperties(pageResult, pagerResult);
        List<TbItemRelation> records = pageResult.getRecords();
        if (CollectionUtils.isNotEmpty(records)) {
            pagerResult.setRecords(BeanCopyUtils.copyList(records, TbItemRelationDto.class));
        }

        return Response.success(pagerResult);
    }

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    public Response<TbItemRelationDto> selectOne(Serializable id) {
        TbItemRelation byId = this.tbItemRelationService.getById(id);
        TbItemRelationDto tbItemRelationDto = new TbItemRelationDto();
        BeanUtils.copyProperties(byId, tbItemRelationDto);
        return Response.success(tbItemRelationDto);
    }

    /**
     * 新增数据
     *
     * @param tbItemRelationDto 实体对象
     * @return 新增结果
     */
    public Response<Boolean> insert(TbItemRelationDto tbItemRelationDto) {
        TbItemRelation tbItemRelation = new TbItemRelation();
        BeanUtils.copyProperties(tbItemRelationDto, tbItemRelation);
        return Response.success(this.tbItemRelationService.save(tbItemRelation));
    }

    /**
     * 修改数据
     *
     * @param tbItemRelationDto 实体对象
     * @return 修改结果
     */
    public Response<Boolean> update(TbItemRelationDto tbItemRelationDto) {
        TbItemRelation tbItemRelation = new TbItemRelation();
        BeanUtils.copyProperties(tbItemRelationDto, tbItemRelation);
        return Response.success(this.tbItemRelationService.updateById(tbItemRelation));
    }

    /**
     * 删除数据
     *
     * @param idList 主键结合
     * @return 删除结果
     */
    public Response<Boolean> delete(List<Long> idList) {
        return Response.success(this.tbItemRelationService.removeByIds(idList));
    }


    //==================================================================================================================

    /**
     * 根据检验项目编码批量删除关联的报告项目信息（检验项目编码-报告项目编码）
     */
    public Response<Integer> deleteItemRelation(@Valid DeleteItemRelationDto deleteDto) {
        return this.tbItemRelationService.deleteItemRelation(deleteDto);
    }

    /**
     * 批量保存检验项目和报告项目关联关系（不影响已有的关联关系，需要过滤掉重复的报告项目）
     */
    public Response<Integer> saveItemRelation(@Valid SaveItemRelationDto saveDto) {
        return this.tbItemRelationService.saveItemRelation(saveDto);
    }


}

