package com.labway.business.center.mdm.user.controller;


import com.labway.business.center.core.enums.OperationTypeEnum;
import com.labway.business.center.core.enums.SystemModuleEmun;
import com.labway.business.center.core.user.RequiresRoles;
import com.labway.business.center.mdm.annotation.LogAnnotation;
import com.labway.business.center.mdm.api.reagent.dto.page.Pager;
import com.labway.business.center.mdm.api.user.param.SelectUserVo;
import com.labway.business.center.mdm.api.user.param.UserVo;
import com.labway.business.center.mdm.api.user.request.UserFailureListRequest;
import com.labway.business.center.mdm.api.user.request.UserListRequest;
import com.labway.business.center.mdm.api.user.service.UserServerBaseService;
import com.swak.frame.dto.Response;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 用户信息 控制层
 *
 * <AUTHOR>
 * @since 2023-03-06
 */
@RequiresRoles
@RestController
@RequestMapping("/user")
public class TbUserController {

    @Resource
    private UserServerBaseService userServerBaseService;

    /**
     * 全量同步OA系统数据
     *
     * @return 成功
     */
    @LogAnnotation(value = "全量同步OA系统数据", OperationType = OperationTypeEnum.SYNCHRONIZATION, SystemModule = SystemModuleEmun.MDM_SERVER)
    @GetMapping("/sync-all")
    public Response<Boolean> syncAllUser() {
        return userServerBaseService.syncAllUser();
    }

    /**
     * 增量同步OA系统数据
     *
     * @return 成功
     */
    @LogAnnotation(value = "增量同步OA系统数据", OperationType = OperationTypeEnum.SYNCHRONIZATION, SystemModule = SystemModuleEmun.MDM_SERVER)
    @GetMapping("/sync-timing")
    public Response<Boolean> syncOaUser() {
        return userServerBaseService.syncOaUser();
    }

    /**
     * 分页查询用户信息
     *
     * @return 用户信息
     */
//    @LogAnnotation(value = "分页查询用户信息", OperationType = OperationTypeEnum.QUERY, SystemModule = SystemModuleEmun.MDM_SERVER)
    @PostMapping("/user-list")
    public Response<List<UserVo>> getUserList(@RequestBody UserListRequest userListRequest) {
        return userServerBaseService.getUserList(userListRequest);
    }

    /**
     * 分页查询用户信息
     *
     * @return 用户信息
     */
//    @LogAnnotation(value = "分页查询用户信息", OperationType = OperationTypeEnum.QUERY, SystemModule = SystemModuleEmun.MDM_SERVER)
    @PostMapping("/user-page")
    public Response<Pager<UserVo>> getUserPage(@RequestBody SelectUserVo selectUserVo) {
        return userServerBaseService.getUserPage(selectUserVo);
    }

    /**
     * 查询失效用户
     * @return
     */
//    @LogAnnotation(value = "查询失效用户", OperationType = OperationTypeEnum.QUERY, SystemModule = SystemModuleEmun.MDM_SERVER)
    @PostMapping("/user-failure-list")
    public Response<List<UserVo>> getUserFailureList(@RequestBody UserFailureListRequest userFailureListRequest) {
        return userServerBaseService.getUserFailureList(userFailureListRequest);
    }
}
