package com.labway.business.center.mdm.reagent.mapper.dal;

import com.labway.business.center.mdm.reagent.mapper.NccOrgsMapper;
import com.labway.business.center.mdm.reagent.model.ncc.NccOrgs;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

@Repository
public class NccOrgsDal {

    @Resource
    private NccOrgsMapper nccOrgsMapper;

    public List<NccOrgs> getAllNccOrgs(){
        return  nccOrgsMapper.getAllNccOrgs();

    }
}
