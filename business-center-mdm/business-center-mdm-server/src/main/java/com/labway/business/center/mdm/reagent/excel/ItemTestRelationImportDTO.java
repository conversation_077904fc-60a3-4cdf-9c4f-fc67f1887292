package com.labway.business.center.mdm.reagent.excel;


import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 检验项目与报告项目对照关系Excel导入DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ItemTestRelationImportDTO implements Serializable {


    //检验项目编码
    @ExcelProperty("检验项目编码")
    private String itemTestCode;

    //报告项目编码
    @ExcelProperty("报告项目编码")
    private String itemReportCode;

    //报告项目名称
    @ExcelProperty("报告项目名称")
    private String itemReportName;

    // 所在行号
    private int rowNum;

    // 检验项目id
    private String testId;

    // 报告项目id
    private String reportId;

}

