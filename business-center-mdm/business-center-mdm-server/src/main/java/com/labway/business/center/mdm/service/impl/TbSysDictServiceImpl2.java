package com.labway.business.center.mdm.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.labway.business.center.core.enums.DeleteFlagEnum;
import com.labway.business.center.mdm.api.reagent.dto.SysDictDto;
import com.labway.business.center.mdm.api.request.QuerySysDictRequest;
import com.labway.business.center.mdm.api.service.TbSysDictService;
import com.labway.business.center.mdm.reagent.mapper.mdm.TbSysDictMapper;
import com.labway.business.center.mdm.reagent.model.TbSysDict;
import com.swak.frame.dto.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.cloud.context.config.annotation.RefreshScope;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 字典表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-17
 */
@Slf4j
@RefreshScope
@DubboService
public class TbSysDictServiceImpl2 extends ServiceImpl<TbSysDictMapper, TbSysDict> implements TbSysDictService {

    @Resource
    private TbSysDictMapper tbSysDictMapper;


    /**
     * 查询数据字典
     * @param request
     * @return
     */
    @Override
    public Response<List<SysDictDto>> querySysDict(QuerySysDictRequest request) {

        List<TbSysDict> tbSysDicts = tbSysDictMapper.selectList(Wrappers.lambdaQuery(TbSysDict.class)
                .in(StringUtils.isNotBlank(request.getDictName()),TbSysDict::getDictName, request.getDictName())
                .eq(request.getIsValid() != null, TbSysDict::getIsValid, request.getIsValid())
                .eq(TbSysDict::getDeleteFlag, DeleteFlagEnum.NO_DELETE.getCode()));
        if (CollectionUtils.isEmpty(tbSysDicts)) {
            return Response.success(Collections.emptyList());
        }

        return Response.success(JSONObject.parseArray(JSONObject.toJSONString(tbSysDicts), SysDictDto.class));
    }

}
