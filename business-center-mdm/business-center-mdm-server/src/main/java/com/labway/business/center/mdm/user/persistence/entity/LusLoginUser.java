package com.labway.business.center.mdm.user.persistence.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 统一登录系统登录名表
 * @TableName tb_lus_login_user
 */
@TableName(value ="tb_lus_login_user")
@Data
public class LusLoginUser implements Serializable {
    /**
     * 
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户的姓名
     */
    @TableField(value = "user_name")
    private String userName;

    /**
     * OA登录名称
     */
    @TableField(value = "login_name")
    private String loginName;

    /**
     * 登录密码
     */
    @TableField(value = "password")
    private String password;

    /**
     * 人员编号
     */
    @TableField(value = "code")
    private String code;

    /**
     * 部门名称
     */
    @TableField(value = "department_name")
    private String departmentName;

    /**
     * OA人员ID
     */
    @TableField(value = "user_id")
    private String userId;

    /**
     * 性别：1：男,2：女，-1：未定义
     */
    @TableField(value = "sex")
    private Integer sex;

    /**
     * 人员状态0-正常,1-已离职
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * 是否启用：0：是,1：否
     */
    @TableField(value = "enabled")
    private Integer enabled;

    /**
     * 是否删除：0：是,1：否
     */
    @TableField(value = "is_delete")
    private Integer isDelete;

    /**
     * 同步时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}