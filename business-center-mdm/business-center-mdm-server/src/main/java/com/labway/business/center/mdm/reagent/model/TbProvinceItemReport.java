package com.labway.business.center.mdm.reagent.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.labway.business.center.mdm.page.BasePage;
import com.labway.business.center.mdm.api.reagent.dto.AddProvinceItemReportDTO;
import com.labway.business.center.mdm.util.LoginUserInfoUtil;
import com.labway.sso.core.user.SsoUser;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;
import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.NoArgsConstructor;

/**
 * @description tb_province_item_report
 * <AUTHOR>
 * @date 2023-10-26
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("tb_province_item_report")
public class TbProvinceItemReport extends BasePage implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty("省级报告项目id ")
    private String reportId;

    /**
     * 集团报告项目主键id
     */
    @ApiModelProperty("集团报告项目主键id")
    private String companyItemReportId;

    /**
     * 省份code
     */
    @ApiModelProperty("省份code")
    private String provinceCode;

    /**
     * 省份名称
     */
    @ApiModelProperty("省份名称")
    private String provinceName;

    /**
     * 省级报告项目编码;唯一不重复
     */
    @ApiModelProperty("省级报告项目编码;唯一不重复")
    private String itemReportCode;

    /**
     * 省级报告项目名称;唯一不重复
     */
    @ApiModelProperty("省级报告项目名称;唯一不重复")
    private String itemReportName;

    /**
     * 报告项目启用状态 0未启用1启用
     */
    @ApiModelProperty("报告项目启用状态 0未启用1启用")
    private Integer status;

    /**
     * 是否有效（逻辑删）
     */
    @ApiModelProperty("是否有效（逻辑删）")
    private Boolean enabled;

    /**
     * 操作人编码
     */
    @ApiModelProperty("操作人编码")
    private String optUserCode;

    /**
     * 操作人账号
     */
    @ApiModelProperty("操作人账号")
    private String optUserAccount;

    /**
     * 操作人姓名
     */
    @ApiModelProperty("操作人姓名")
    private String optUserName;


    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    public static TbProvinceItemReport dto2po(AddProvinceItemReportDTO.ProvinceItemReportDTO dto) {
        SsoUser loginUser = LoginUserInfoUtil.getLoginUser();
        return TbProvinceItemReport.builder()
                .reportId(dto.getReportId())
                .companyItemReportId(dto.getCompanyItemReportId())
                .provinceCode(dto.getProvinceCode())
                .provinceName(dto.getProvinceName())
                .itemReportCode(dto.getItemReportCode())
                .itemReportName(dto.getItemReportName())
                .status(dto.getStatus())
                .optUserCode(loginUser.getUserId())
                .optUserName(loginUser.getUserName())
                .optUserAccount(loginUser.getLoginName())
                .build();
    }
}