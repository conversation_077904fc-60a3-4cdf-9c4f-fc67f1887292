package com.labway.business.center.mdm.util;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomUtils;
import org.springframework.stereotype.Component;

/**
 * id生成
 *
 * <AUTHOR>
 * @since 2023/2/28 16:04
 */
@Slf4j
@Component
public class SerialIdUtil {

    private static final Snowflake SNOWFLAKE =
            IdUtil.getSnowflake(RandomUtils.nextInt(0, 30), RandomUtils.nextInt(0, 30));

    /**
     * 获取 序列号
     *
     * @return str
     */
    public String getSerialNumber() {
        return SNOWFLAKE.nextIdStr();
    }

}
