package com.labway.business.center.mdm.reagent.mapper.dal;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.labway.business.center.mdm.api.reagent.dto.NccReagentTypeDto;
import com.labway.business.center.mdm.api.reagent.dto.ReagentTypeDto;
import com.labway.business.center.mdm.api.reagent.dto.SelectReagentTypeListDto;
import com.labway.business.center.mdm.common.constants.CommonConstant;
import com.labway.business.center.mdm.common.util.JacksonUtil;
import com.labway.business.center.mdm.reagent.mapper.mdm.TbReagentMaterialTypeMapper;
import com.labway.business.center.mdm.reagent.model.TbReagentMaterialType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 试剂耗材 类型 Dal
 *
 * <AUTHOR>
 * @since 2023/3/1 17:07
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ReagentTypeDal {

    private final TbReagentMaterialTypeMapper tbReagentMaterialTypeMapper;

    private final NccReagentMaterialDal nccReagentMaterialDal;

    /**
     * 主数据获取试剂耗材类型数据接口
     *
     * @param condition 参数
     * @return Page
     */
    public Page<ReagentTypeDto> selectReagentTypeList(SelectReagentTypeListDto condition) {
        Page<ReagentTypeDto> page = new Page<>(condition.getCurrent(), condition.getSize());
        return tbReagentMaterialTypeMapper.selectReagentTypeDtoByPage(page, condition);
    }

    /**
     * 获取 ncc 试剂耗材类型相关信息
     *
     * @return list
     */
    public List<NccReagentTypeDto> selectNccReagentTypeDtoList() {
        return nccReagentMaterialDal.selectNccReagentTypeDto();
    }

    /**
     * 获取 主数据 试剂耗材类型相关信息
     *
     * @return list
     */
    public List<ReagentTypeDto> selectReagentTypeDtoList() {
        return tbReagentMaterialTypeMapper.selectReagentTypeDto();
    }

    /**
     * 保存 试剂耗材类型
     */
    public void saveReagentTypeDtoList(List<ReagentTypeDto> reagentTypeDtos) {
        if (CollectionUtils.isEmpty(reagentTypeDtos)) {
            return;
        }
        List<List<ReagentTypeDto>> tempList = ListUtils.partition(reagentTypeDtos, CommonConstant.INT_ONE_THOUSAND);
        for (List<ReagentTypeDto> list : tempList) {
            tbReagentMaterialTypeMapper.saveReagentTypeDtoList(list);
        }
    }

    /**
     * 更新 试剂耗材类型
     */
    public void updateReagentTypeDtoList(List<ReagentTypeDto> reagentTypeDtos) {
        if (CollectionUtils.isEmpty(reagentTypeDtos)) {
            return;
        }
        reagentTypeDtos.forEach(item -> {
            TbReagentMaterialType updateItem = JacksonUtil.convertValue(item, TbReagentMaterialType.class);
            LambdaUpdateWrapper<TbReagentMaterialType> updateWrapper = Wrappers.lambdaUpdate();
            updateWrapper.eq(TbReagentMaterialType::getItemId, item.getItemId());
            tbReagentMaterialTypeMapper.update(updateItem, updateWrapper);
        });

    }

    /**
     * 失效 试剂耗材信息 根据 ncc 唯一码
     */
    public void invalidReagentByFromNo(List<String> fromNoList) {
        if (CollectionUtils.isEmpty(fromNoList)) {
            return;
        }
        List<List<String>> tempList = ListUtils.partition(fromNoList, CommonConstant.INT_ONE_THOUSAND);
        for (List<String> list : tempList) {
            tbReagentMaterialTypeMapper.invalidReagentTypeByFromNo(list);
        }
    }

}
