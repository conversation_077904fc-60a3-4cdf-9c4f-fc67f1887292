package com.labway.business.center.mdm.reagent.server;

import com.labway.business.center.mdm.api.reagent.service.MdmMonitorService;
import com.swak.frame.dto.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.cloud.context.config.annotation.RefreshScope;


/**
 * <AUTHOR>
 */
@Slf4j
@RefreshScope
@DubboService
public class MdmMonitorServiceImpl implements MdmMonitorService {


    @Override
    public Response<?> serviceMonitor() {
        return Response.success();
    }

    @Override
    public Response<?> getServiceHeartbeat() {
        return Response.success();
    }

}
