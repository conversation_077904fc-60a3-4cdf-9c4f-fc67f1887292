package com.labway.business.center.mdm.user.server;

import com.labway.business.center.mdm.api.user.service.OaServerBaseService;
import com.labway.business.center.mdm.user.service.TbDepartmentService;
import com.labway.business.center.mdm.user.service.TbUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;

@Slf4j
@DubboService
public class OaServerBaseServiceImpl implements OaServerBaseService {

    @Resource
    private TbDepartmentService departmentService;

    @Resource
    private TbUserService userService;

    @Override
    public void syncAllDeptInfo() {
        log.info("主数据 从 OA 全量同步所有的部门数据");
        departmentService.syncOaAllDept();
    }

    @Override
    public void syncDeptInfo() {
        log.info("主数据 从 OA 增量同步部门数据");
        departmentService.syncOaDept();
    }

    @Override
    public void syncAllUserInfo() {
        log.info("主数据 从 OA 全量同步所有的人员数据");
        userService.syncOaAllUsers();
    }

    @Override
    public void syncUserInfo() {
        log.info("主数据 从 OA 增量同步人员数据");
        userService.syncOaUsers();
    }
}
