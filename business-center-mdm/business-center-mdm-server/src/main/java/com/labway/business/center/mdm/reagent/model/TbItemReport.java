package com.labway.business.center.mdm.reagent.model;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.labway.business.center.mdm.common.file.annotation.Excel;

import java.util.Date;

/**
 * 报告项目表(TbItemReport)表实体类
 *
 * <AUTHOR>
 * @since 2023-04-03 09:17:26
 */
@SuppressWarnings("serial")
public class TbItemReport extends Model<TbItemReport> {
    //主键标识
    @TableId(type = IdType.INPUT)
    private String itemId;
    //报告项目编码;唯一不重复
    @Excel(value = "报告项目编码", index = 0)
    private String itemReportCode;
    //报告项目名称;唯一不重复
    @Excel(value = "报告项目名称", index = 1)
    private String itemReportName;
    //报告项目启用状态 0未启用1启用
    private Integer status;
    //是否有效（逻辑删）
    @TableLogic(value = "1", delval = "0")
    private Boolean enabled;
    //操作人编码
    private String optUserCode;
    //操作人账号
    private String optUserAccount;
    //操作人姓名
    private String optUserName;
    //创建时间
    private Date createTime;
    //修改时间
    private Date updateTime;


    public String getItemId() {
        return itemId;
    }

    public void setItemId(String itemId) {
        this.itemId = itemId;
    }

    public String getItemReportCode() {
        return itemReportCode;
    }

    public void setItemReportCode(String itemReportCode) {
        this.itemReportCode = itemReportCode;
    }

    public String getItemReportName() {
        return itemReportName;
    }

    public void setItemReportName(String itemReportName) {
        this.itemReportName = itemReportName;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public String getOptUserCode() {
        return optUserCode;
    }

    public void setOptUserCode(String optUserCode) {
        this.optUserCode = optUserCode;
    }

    public String getOptUserAccount() {
        return optUserAccount;
    }

    public void setOptUserAccount(String optUserAccount) {
        this.optUserAccount = optUserAccount;
    }

    public String getOptUserName() {
        return optUserName;
    }

    public void setOptUserName(String optUserName) {
        this.optUserName = optUserName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }


}

