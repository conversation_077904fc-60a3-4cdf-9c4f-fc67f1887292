package com.labway.business.center.mdm.chain.itemTest.check;

import com.google.j2objc.annotations.ReflectionSupport;
import com.labway.business.center.mdm.chain.itemTest.ItemTestChain;
import com.labway.business.center.mdm.reagent.excel.ItemProvinceTestImportDTO;
import com.labway.business.center.mdm.reagent.excel.ItemTestImportDTO;
import com.labway.business.center.mdm.reagent.model.TbSysDict;
import com.labway.business.center.mdm.reagent.service.SysDictService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.labway.business.center.mdm.reagent.service.impl.SysDictServiceImpl.TEST_METHOD_DICT_NAME;

@Order(8)
@Component
public class CheckTestMethod implements ItemTestChain {

    @Resource
    private SysDictService sysDictService;

    @Override
    public void check(List<ItemProvinceTestImportDTO> itemList, Map<Integer, List<String>> errorsMap) {
        List<TbSysDict> tbSysDicts = sysDictService.listEnabledByDictNames(Arrays.asList(TEST_METHOD_DICT_NAME)).getData();
        Set<String> testMethods = tbSysDicts.stream().map(TbSysDict::getDictValue).collect(Collectors.toSet());
        Map<String, String> nameCodeMap = tbSysDicts.stream().collect(Collectors.toMap(TbSysDict::getDictValue, TbSysDict::getDictCode));

        for (ItemProvinceTestImportDTO item : itemList) {

            String testMethod = item.getTestMethod();
            int rowNum = item.getRowNum();
            if (StringUtils.isNotBlank(testMethod) && !testMethods.contains(testMethod)) {
                List<String> errorList = getOrDefault(errorsMap, rowNum);
                errorList.add("检验方法不存在");
                errorsMap.put(rowNum, errorList);
                continue;
            }
            item.setTestMethodCode(nameCodeMap.get(item.getTestMethod()));
        }
    }
}
