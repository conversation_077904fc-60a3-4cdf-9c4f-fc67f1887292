package com.labway.business.center.mdm.reagent.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.Page;
import com.labway.business.center.core.enums.ResultCode;
import com.labway.business.center.mdm.api.reagent.param.reagent.SampleTypeInfoVo;
import com.labway.business.center.mdm.config.RabbitmqConfig;
import com.labway.business.center.mdm.page.PageUtils;
import com.labway.business.center.mdm.api.reagent.dto.AddProvinceItemTestDTO;
import com.labway.business.center.mdm.api.reagent.dto.DeleteProvinceItemTestDTO;
import com.labway.business.center.mdm.api.reagent.dto.UpdateProvinceItemTestDTO;
import com.labway.business.center.mdm.api.reagent.dto.page.Pager;
import com.labway.business.center.mdm.api.reagent.param.reagent.ItemReportVo;
import com.labway.business.center.mdm.api.reagent.vo.ItemTestVO;
import com.labway.business.center.mdm.chain.itemTest.ItemTestChainStart;
import com.labway.business.center.mdm.chain.relation.ItemTestRelationStart;
import com.labway.business.center.mdm.common.enums.StatusEnum;
import com.labway.business.center.mdm.common.file.export.ExportUtil;
import com.labway.business.center.mdm.common.util.StringUtils;
import com.labway.business.center.mdm.reagent.excel.ItemProvinceTestImportDTO;
import com.labway.business.center.mdm.reagent.excel.ItemProvinceTestRelationImportDTO;
import com.labway.business.center.mdm.reagent.mapper.mdm.TbProvinceItemTestMapper;
import com.labway.business.center.mdm.reagent.model.*;
import com.labway.business.center.mdm.reagent.service.*;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.swak.frame.dto.Response;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

import static com.labway.business.center.mdm.reagent.service.impl.SysDictServiceImpl.TEST_METHOD_DICT_NAME;
import static com.labway.business.center.mdm.reagent.service.impl.SysDictServiceImpl.PROVINCES_NAME;

@Service
public class TbProvinceItemTestServiceImpl extends ServiceImpl<TbProvinceItemTestMapper, TbProvinceItemTest>
        implements ITbProvinceItemTestService {

    @Resource
    private SysDictService sysDictService;

    @Resource
    private ITbProvinceItemReportService iTbProvinceItemReportService;
    @Resource
    private ITbProvinceItemRelationService iTbProvinceItemRelationService;

    @Resource
    @Lazy
    private ItemTestChainStart itemTestChainStart;

    @Resource
    private ItemTestRelationStart itemTestRelationStart;

    @Resource
    private TbItemTestService tbItemTestService;
    @Resource
    private TbItemReportService tbItemReportService;

    @Resource
    private TbSampleTypeService sampleTypeService;

    @Resource
    private RabbitTemplate rabbitTemplate;

    @Override
    public Response selectPage(TbProvinceItemTest tbProvinceItemTest) {
        Page page = PageUtils.initPageNoAndPageSize(tbProvinceItemTest);
        List<ItemTestVO> itemTestVOS = selectList(tbProvinceItemTest, new ArrayList<>());
        Pager<ItemTestVO> pagerResult = new Pager<>();
        pagerResult.setRecords(itemTestVOS);
        pagerResult.setTotal(page.getTotal());
        return Response.success(pagerResult);
    }

    @Override
    public List<ItemTestVO> selectList(TbProvinceItemTest tbProvinceItemTest, List<String> itemTestCodeList) {
        List<ItemTestVO> itemTestVOS = baseMapper.selectProvinceItemTest(tbProvinceItemTest, itemTestCodeList);
        // 字典转换
        sysDictService.convertCodeToValue(TEST_METHOD_DICT_NAME, itemTestVOS,
                ItemTestVO::getTestMethod,
                (x, value) -> {
                    if (org.apache.commons.lang3.StringUtils.isBlank(value)) {
                        x.setTestMethodCode(null);
                        x.setTestMethod(null);
                        return;
                    }
                    x.setTestMethodCode(x.getTestMethod());
                    x.setTestMethod(value);
                });
        return itemTestVOS;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Response addProvinceItemTest(AddProvinceItemTestDTO addProvinceItemTestDTO) {

        Response response = checkAddTestItem(addProvinceItemTestDTO);
        if(!response.isSuccess()){
            return response;
        }
        List<SampleTypeInfoVo> sampleTypeInfoVos = sampleTypeService.querySampleTypeInfo();
        Map<String, String> codeNameMap = sampleTypeInfoVos.stream().collect(Collectors.toMap(SampleTypeInfoVo::getSampleTypeCode, SampleTypeInfoVo::getSampleTypeName));

        List<TbSysDict> data = sysDictService.listEnabledByDictNames(List.of(TEST_METHOD_DICT_NAME)).getData();
        Map<String, String> testCodeNameMap = data.stream().collect(Collectors.toMap(TbSysDict::getDictCode, TbSysDict::getDictValue));

        for (AddProvinceItemTestDTO.ProvinceItemTestDTO dto : addProvinceItemTestDTO.getProvinceItemTestList()) {
            String sampleName = codeNameMap.get(dto.getSampleTypeCode());
            if(!StringUtils.hasLength(sampleName)){
                return Response.fail(451, dto.getSampleTypeCode() +" 样本类型不存在");
            }
            if(StringUtils.hasLength(dto.getTestMethod())) {
                String testName = testCodeNameMap.get(dto.getTestMethod());
                if (!StringUtils.hasLength(testName)) {
                    return Response.fail(452, dto.getTestMethod() + " 检验方法不存在");
                }
            }
            dto.setSampleTypeName(sampleName);
        }
        // 3. 数据类型转换
        List<TbProvinceItemTest> tbProvinceItemTestList = addProvinceItemTestDTO.getProvinceItemTestList().
                stream().map(TbProvinceItemTest::dto2po).collect(Collectors.toList());
        // 4. 批量添加
        this.saveBatch(tbProvinceItemTestList);
        return Response.success();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Response updateProvinceItemTest(UpdateProvinceItemTestDTO dto) {
        this.update(Wrappers.lambdaUpdate(TbProvinceItemTest.class)
                .eq(TbProvinceItemTest::getTestId, dto.getTestId())
                .set(StringUtils.hasLength(dto.getItemTestName()), TbProvinceItemTest::getItemTestName, dto.getItemTestName())
                .set(TbProvinceItemTest::getEnglishName, dto.getEnglishName())
                .set(TbProvinceItemTest::getTestMethod, dto.getTestMethod())
                .set(TbProvinceItemTest::getSampleTypeCode, dto.getSampleTypeCode())
                .set(TbProvinceItemTest::getSampleTypeName, dto.getSampleTypeName())
                .set(TbProvinceItemTest::getCompanyItemTestId, dto.getCompanyItemTestId())

                .set(dto.getStatus() != null, TbProvinceItemTest::getStatus, dto.getStatus())
                .set(TbProvinceItemTest::getSaveDescription, dto.getSaveDescription())
        );
        return Response.success();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Response deleteProvinceItemTest(DeleteProvinceItemTestDTO dto) {
        if (CollectionUtils.isNotEmpty(dto.getTestIds())) {
            this.update(Wrappers.lambdaUpdate(TbProvinceItemTest.class)
                    .set(TbProvinceItemTest::getEnabled, Boolean.FALSE)
                    .in(TbProvinceItemTest::getTestId, dto.getTestIds())
            );
            iTbProvinceItemRelationService.deleteByItemTestId(dto.getTestIds());
            dto.setMessageId(IdUtil.fastUUID());
            rabbitTemplate.convertAndSend(RabbitmqConfig.LABWAY_MDM_ROVINCES_ITEM_RELATION, "", JSONUtil.toJsonStr(dto));
        }
        return Response.success();
    }

    @Override
    public List<TbProvinceItemTest> selectProvinceItemTestByTestId(List<String> itemIdList) {
        if(CollectionUtils.isEmpty(itemIdList)){
            return Collections.emptyList();
        }
        return this.list(Wrappers.lambdaQuery(TbProvinceItemTest.class)
                .in(TbProvinceItemTest::getCompanyItemTestId, itemIdList)
                .eq(TbProvinceItemTest::getEnabled, Boolean.TRUE)
        );
    }

    @Override
    public List<TbProvinceItemTest> selectProvinceItemTestByProvinceTestId(List<String> itemIdList) {
        if(CollectionUtils.isEmpty(itemIdList)){
            return Collections.emptyList();
        }
        return this.list(Wrappers.lambdaQuery(TbProvinceItemTest.class)
                .in(TbProvinceItemTest::getTestId, itemIdList)
                .eq(TbProvinceItemTest::getEnabled, Boolean.TRUE)
        );
    }

    @Override
    public void exportProvinceItemTest(TbProvinceItemTest tbProvinceItemTest, HttpServletResponse response) {
        List<ItemTestVO> itemTestVOS = this.selectList(tbProvinceItemTest, new ArrayList());
        List<ItemProvinceTestRelationImportDTO> itemRelationVOS = new ArrayList<>();

        List<String> testIds = itemTestVOS.stream().map(ItemTestVO::getTestId).collect(Collectors.toList());
        List<TbProvinceItemRelation> tbProvinceItemRelationList = this.iTbProvinceItemRelationService.selectRelationByTestIds(testIds, null);
        Map<String, List<TbProvinceItemRelation>> collect = tbProvinceItemRelationList.stream().collect(Collectors.groupingBy(TbProvinceItemRelation::getIsCompany));
        List<TbProvinceItemRelation> companyItemRelationList = collect.get("1");
        List<TbProvinceItemRelation> provinceItemRelationList = collect.get("0");
        if(CollectionUtils.isNotEmpty(companyItemRelationList)){
            for (TbProvinceItemRelation relation : companyItemRelationList) {
                ItemProvinceTestRelationImportDTO dto = new ItemProvinceTestRelationImportDTO();
                dto.setItemReportCode(relation.getItemReportCode());
                dto.setItemReportName(relation.getItemReportName());
                dto.setItemTestCode(relation.getItemTestCode());
                dto.setCompanyOrProvince("集团");
                itemRelationVOS.add(dto);
            }
        }
        if(CollectionUtils.isNotEmpty(provinceItemRelationList)){
            // 查询省级报告项目
            for (TbProvinceItemRelation relation : provinceItemRelationList) {
                ItemProvinceTestRelationImportDTO dto = new ItemProvinceTestRelationImportDTO();
                dto.setItemReportCode(relation.getItemReportCode());
                dto.setItemReportName(relation.getItemReportName());
                dto.setItemTestCode(relation.getItemTestCode());
                dto.setCompanyOrProvince(itemTestVOS.get(0).getProvinceName());
                itemRelationVOS.add(dto);
            }
        }
        itemRelationVOS.sort(Comparator.comparing(ItemProvinceTestRelationImportDTO::getItemTestCode));
        ExportUtil.createBigFile("省级检验项目信息").setTotal(itemTestVOS, ItemTestVO.class).reSheetName("检验项目")
                .nextSheet("映射关系").setTotal(itemRelationVOS, ItemProvinceTestRelationImportDTO.class)
                .exportFile(response);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Response importItemTestAndRelation(MultipartFile file) throws IOException {
        // 文件流不能为空
        if (file.isEmpty()) {
            return Response.fail(ResultCode.E112011);
        }
        // 文件后缀名必须是xls或xlsx
        String fileName = file.getOriginalFilename();
        if (fileName.indexOf(".xls") == -1 && fileName.indexOf(".xlsx") == -1) {
            return Response.fail(ResultCode.E112012);
        }

        // 读取第一个sheet页，解析为检验项目
        List<ItemProvinceTestImportDTO> itemList = EasyExcel.read(file.getInputStream())
                .head(ItemProvinceTestImportDTO.class)
                .sheet(0)
                .doReadSync();

        // 读取第二个sheet页，解析为项目映射关系
        List<ItemProvinceTestRelationImportDTO> relationList = EasyExcel.read(file.getInputStream())
                .head(ItemProvinceTestRelationImportDTO.class)
                .sheet(1)
                .doReadSync();

        Map<String, List<ItemProvinceTestImportDTO>> sheet1 = itemList.stream()
                .filter(e-> org.apache.commons.lang3.StringUtils.isNotBlank(e.getProvinceName()))
                .collect(Collectors.groupingBy(ItemProvinceTestImportDTO::getProvinceName));
        if(CollectionUtils.isEmpty(sheet1) || sheet1.size() != 1){
            throw new IllegalStateException("Sheet1 中不能为空且只能有一个省份");
        }
        Map<String, List<ItemProvinceTestRelationImportDTO>> sheet2 = relationList.stream()
                .filter(e-> org.apache.commons.lang3.StringUtils.isNotBlank(e.getCompanyOrProvince()))
                .collect(Collectors.groupingBy(ItemProvinceTestRelationImportDTO::getCompanyOrProvince));

        if(CollectionUtils.isNotEmpty(sheet2)){
            sheet2.remove("集团");
            if(CollectionUtils.isNotEmpty(sheet2)) {
                if(sheet2.size() != 1) {
                    throw new IllegalStateException("Sheet2 中不能为空且只能有一个省份");
                }
                if(!sheet1.keySet().stream().findFirst().equals(sheet2.keySet().stream().findFirst())){
                    throw new IllegalStateException("Sheet2 的省份和 Sheet1 不匹配");
                }
            }
        }

        // 检查检验项目导入数据
        Map<Integer, List<String>> itemErrorsMap = Collections.emptyMap();
        if (!org.springframework.util.CollectionUtils.isEmpty(itemList)) {
            itemErrorsMap = itemTestChainStart.check(itemList);
        }
        if(CollectionUtils.isNotEmpty(itemErrorsMap)){
            String errorMsg = org.springframework.util.CollectionUtils.isEmpty(itemErrorsMap) ? "" : ("Sheet1中，" + TbItemTestServiceImpl.parseErrorsMap(itemErrorsMap));
            throw new RuntimeException(errorMsg);
        }

        if(CollectionUtils.isNotEmpty(itemList)){
            testDataComplete(itemList);
            saveProvinceItemTestList(itemList);
        }



        // 检查对照关系导入数据
        Map<Integer, List<String>> relationErrorsMap = Collections.emptyMap();
        if (!org.springframework.util.CollectionUtils.isEmpty(relationList)) {
            relationErrorsMap = itemTestRelationStart.check(relationList);
        }
        if (CollectionUtils.isNotEmpty(relationErrorsMap)) {
            // 返回错误信息
            String errorMsg = org.springframework.util.CollectionUtils.isEmpty(relationErrorsMap) ? "" : ("Sheet页2，" + TbItemTestServiceImpl.parseErrorsMap(relationErrorsMap));
            throw new RuntimeException(errorMsg);
//            return Response.fail(ResultCode.E111002.getCode(), errorMsg);
        }

        if(CollectionUtils.isNotEmpty(relationList)){
            relationDataComplete(relationList, sheet1.keySet().stream().findFirst().get());
            saveProvinceItemRelationList(relationList);
        }
        return Response.success();
    }

    private void saveProvinceItemRelationList(List<ItemProvinceTestRelationImportDTO> relationList) {
        if (org.springframework.util.CollectionUtils.isEmpty(relationList)) {
            return;
        }
        List<TbProvinceItemRelation> list = relationList.stream().map(relation -> TbProvinceItemRelation.builder()
                        .itemRelationId(null)
                        .itemTestId(relation.getTestId())
                        .itemTestCode(relation.getItemTestCode())
                        .itemReportId(relation.getReportId())
                        .itemReportCode(relation.getItemReportCode())
                        .itemReportName(relation.getItemReportName())
                        .isCompany(relation.getIsCompany() ? "1" : "0")
                        .build())
                .collect(Collectors.toList());
        iTbProvinceItemRelationService.saveBatch(list);
    }

    private void saveProvinceItemTestList(List<ItemProvinceTestImportDTO> itemList) {
        List<TbProvinceItemTest> tbProvinceItemTestList = itemList.stream()
                .map(item -> TbProvinceItemTest.builder()
                        .testId(null)
                        .companyItemTestId(item.getItemId())
                        .provinceCode(item.getProvinceCode())
                        .provinceName(item.getProvinceName())
                        .itemTestCode(item.getItemTestCode())
                        .itemTestName(item.getItemTestName())
                        .englishName(item.getEnglishName())
                        .sampleTypeCode(item.getSampleTypeCode())
                        .sampleTypeName(item.getSampleTypeName())
                        .saveDescription(item.getSaveDescription())
                        .testMethod(item.getTestMethodCode())
                        .build())
                .collect(Collectors.toList());
        this.saveBatch(tbProvinceItemTestList);
    }

    /**
     * 完善导入的数据
     *
     * @param itemList
     */
    private void testDataComplete(List<ItemProvinceTestImportDTO> itemList) {
        //根据集团检验code批量获取数据
        List<String> companyTestCodeList = itemList.stream().distinct().map(ItemProvinceTestImportDTO::getCompanyItemTestCode).collect(Collectors.toList());
        List<TbItemTest> tbItemTests = tbItemTestService.selectByItemCodes(companyTestCodeList);
        // 集团检验项目code 和 集团检验项目id
        Map<String, String> codeIdMap = tbItemTests.stream().collect(Collectors.toMap(TbItemTest::getItemTestCode, TbItemTest::getItemId));

//        // 省份列表
//        QueryDictRequest queryDictRequest = new QueryDictRequest();
//        queryDictRequest.setEnabled(1);
//        List<TbSysDict> provinceList = sysDictService.listByDictNames(Arrays.asList(PROVINCES_NAME), queryDictRequest).getData();
//        Map<String, String> provinceMap = provinceList.stream().collect(Collectors.toMap(TbSysDict::getDictValue, TbSysDict::getDictCode));

        //样本类型
        List<TbSampleType> sampleTypeList = sampleTypeService.list(Wrappers.lambdaQuery(TbSampleType.class)
                .eq(TbSampleType::getEnabled, StatusEnum.enable.getValue()));
        Map<String, String> sampleTypeMap = sampleTypeList.stream().collect(Collectors.toMap(TbSampleType::getSampleTypeName, TbSampleType::getSampleTypeCode));

        // 查询字典表的检验方法名称
//        List<TbSysDict> tbSysDicts = sysDictService.listEnabledByDictNames(Arrays.asList(TEST_METHOD_DICT_NAME)).getData();
//        Map<String, String> testMethodMap = tbSysDicts.stream().collect(Collectors.toMap(TbSysDict::getDictValue, TbSysDict::getDictCode));


        for (ItemProvinceTestImportDTO item : itemList) {
            item.setItemId(codeIdMap.get(item.getCompanyItemTestCode()));
//            item.setProvinceCode(provinceMap.get(item.getProvinceName()));
            item.setSampleTypeCode(sampleTypeMap.get(item.getSampleTypeName()));
//            item.setTestMethodCode(testMethodMap.get(item.getTestMethodCode()));
        }
    }

    private void relationDataComplete(List<ItemProvinceTestRelationImportDTO> relationList, String provinces) {
        // 检验报告id 和 code
        List<String> testCode = relationList.stream().map(ItemProvinceTestRelationImportDTO::getItemTestCode).collect(Collectors.toList());
        List<TbProvinceItemTest> tbProvinceItemTests = this.selectProvinceNameList(provinces, testCode);
        Map<String, String> testMap = tbProvinceItemTests.stream().collect(Collectors.toMap(TbProvinceItemTest::getItemTestCode, TbProvinceItemTest::getTestId));

        // 报告项目id code name
        Map<String, List<ItemProvinceTestRelationImportDTO>> provinceMap = relationList.stream().collect(Collectors.groupingBy(ItemProvinceTestRelationImportDTO::getCompanyOrProvince));
        List<ItemProvinceTestRelationImportDTO> companyItemTestList = provinceMap.get("集团");
        // 集团map
        Map<String, String> companyItemReportMap = Collections.emptyMap();
        if (CollectionUtils.isNotEmpty(companyItemTestList)) {
            List<String> companyItemReportCodeList = companyItemTestList.stream().map(ItemProvinceTestRelationImportDTO::getItemReportCode).collect(Collectors.toList());
            List<ItemReportVo> itemReportVos = tbItemReportService.queryItemReportListByCodes(companyItemReportCodeList);
            companyItemReportMap = itemReportVos.stream().collect(Collectors.toMap(ItemReportVo::getItemReportCode, ItemReportVo::getItemId));
        }
        if(CollectionUtils.isNotEmpty(companyItemTestList)) {
            for (ItemProvinceTestRelationImportDTO relation : companyItemTestList) {
                relation.setTestId(testMap.get(relation.getItemTestCode()));
                relation.setReportId(companyItemReportMap.get(relation.getItemReportCode()));
            }
        }
    }

    /**
     * 查询省级项目编号重复的
     *
     * @param provinceCode
     * @param itemTestCodeList
     * @return
     */
    public List<TbProvinceItemTest> selectProvinceCodeList(String provinceCode, List<String> itemTestCodeList) {
        return this.list(Wrappers.lambdaQuery(TbProvinceItemTest.class)
                .select(TbProvinceItemTest::getItemTestCode, TbProvinceItemTest::getTestId)
                .eq(StringUtils.hasLength(provinceCode), TbProvinceItemTest::getProvinceCode, provinceCode)
                .in(TbProvinceItemTest::getItemTestCode, itemTestCodeList)
                .eq(TbProvinceItemTest::getEnabled, 1)
        );
    }

    public List<TbProvinceItemTest> selectProvinceNameList(String provinceName, List<String> itemTestCodeList) {
        return this.list(Wrappers.lambdaQuery(TbProvinceItemTest.class)
                .select(TbProvinceItemTest::getItemTestCode, TbProvinceItemTest::getTestId)
                .eq(StringUtils.hasLength(provinceName), TbProvinceItemTest::getProvinceName, provinceName)
                .in(TbProvinceItemTest::getItemTestCode, itemTestCodeList)
                .eq(TbProvinceItemTest::getEnabled, 1)
        );
    }

    private Response checkAddTestItem(AddProvinceItemTestDTO addProvinceItemTestDTO){
        String provinceCode = addProvinceItemTestDTO.getProvinceCode();
        List<AddProvinceItemTestDTO.ProvinceItemTestDTO> provinceItemTestList = addProvinceItemTestDTO.getProvinceItemTestList();

        if (CollectionUtils.isEmpty(provinceItemTestList)) {
            return Response.fail(447, "没有新增检验项目！");
        }
        // 判断 省级编码是否存在
        // 1. 查询参数有没有重复编码
        Set<String> itemTestCodeSet = new HashSet<>();
        Set<String> duplicateItemReportCodes = provinceItemTestList.stream()
                .map(AddProvinceItemTestDTO.ProvinceItemTestDTO::getItemTestCode)
                .filter(n -> !itemTestCodeSet.add(n))
                .collect(Collectors.toSet());
        if (!duplicateItemReportCodes.isEmpty()) {
            return Response.fail(446, "新增检验项目编码重复: " + String.join(",", duplicateItemReportCodes));
        }
        // 2. 查询省级所有已存在的code
        List<TbProvinceItemTest> itemTestCodeList = selectProvinceCodeList(addProvinceItemTestDTO.getProvinceCode(),
                provinceItemTestList.stream()
                        .map(AddProvinceItemTestDTO.ProvinceItemTestDTO::getItemTestCode).collect(Collectors.toList())
        );
        if (CollectionUtils.isNotEmpty(itemTestCodeList)) {
            String getItemTestCodeStr = itemTestCodeList.stream().map(TbProvinceItemTest::getItemTestCode).collect(Collectors.joining(","));
            return Response.fail(445, getItemTestCodeStr + " 检验项目编码已存在！");
        }
        List<TbSysDict> tbSysDictList = sysDictService.listByDictCode(PROVINCES_NAME, provinceCode);
        if(tbSysDictList.size() != 1){
            return Response.fail(450," 省级编码库中不存在或重复！");
        }
        TbSysDict tbSysDict = tbSysDictList.get(0);
        provinceItemTestList.forEach(dto ->{
            dto.setProvinceCode(tbSysDict.getDictCode());
            dto.setProvinceName(tbSysDict.getDictValue());
        });
        return Response.success();
    }
}
