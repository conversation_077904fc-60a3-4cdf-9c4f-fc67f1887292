package com.labway.business.center.mdm.chain.relation.check;

import com.labway.business.center.mdm.chain.relation.ItemTestRelationChain;
import com.labway.business.center.mdm.reagent.excel.ItemProvinceTestImportDTO;
import com.labway.business.center.mdm.reagent.excel.ItemProvinceTestRelationImportDTO;
import com.labway.business.center.mdm.reagent.excel.ItemTestRelationImportDTO;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Order(3)
@Component
public class CheckTestAndReportCode implements ItemTestRelationChain {

    @Override
    public void checkRelation(List<ItemProvinceTestRelationImportDTO> relationList,  Map<Integer, List<String>> errorsMap) {
        Map<String, List<ItemProvinceTestRelationImportDTO>> relationMap = relationList.stream()
                .collect(Collectors.groupingBy(r -> r.getItemTestCode() + r.getItemReportCode()));
        Set<Map.Entry<String, List<ItemProvinceTestRelationImportDTO>>> entries = relationMap.entrySet();
        for (Map.Entry<String, List<ItemProvinceTestRelationImportDTO>> entry : entries) {
            if(entry.getValue().size() > 1){
                String errorMsg = "对照关系在导入文件中重复";
                for (ItemProvinceTestRelationImportDTO dto : entry.getValue()) {
                    int rowNum = dto.getRowNum();
                    List<String> errors = errorsMap.getOrDefault(rowNum, new ArrayList<>());
                    errors.add(errorMsg);
                    errorsMap.put(rowNum, errors);
                }
            }
        }
    }
}
