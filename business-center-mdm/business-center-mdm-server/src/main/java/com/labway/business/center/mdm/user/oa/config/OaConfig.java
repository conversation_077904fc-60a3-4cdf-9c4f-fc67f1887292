package com.labway.business.center.mdm.user.oa.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @Description OA相关配置
 * @Date 2023/3/7 11:48
 */
@Configuration
public class OaConfig {

    /**
     * 获取OA系统token所需的用户名
     */
    @Value("${oa.username}")
    public String username;

    /**
     * 获取OA系统token所需的密码
     */
    @Value("${oa.password}")
    public String password;

    /**
     * OA系统地址
     */
    @Value("${oa.url}")
    public String url;

}
