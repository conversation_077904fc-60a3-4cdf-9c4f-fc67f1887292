package com.labway.business.center.mdm.reagent.mapper.dal;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.labway.business.center.mdm.api.customer.dto.QueryListForOmsSyncDto;
import com.labway.business.center.mdm.api.customer.request.QueryListForOmsSyncRequest;
import com.labway.business.center.mdm.api.reagent.dto.customer.CustomerInfoInsertDto;
import com.labway.business.center.mdm.api.reagent.dto.customer.CustomerInfoQueryDto;
import com.labway.business.center.mdm.api.reagent.dto.customer.CustomerInfoUpdateDto;
import com.labway.business.center.mdm.api.reagent.dto.customer.NccCustomerInfoQueryDto;
import com.labway.business.center.mdm.api.reagent.dto.customer.QueryCustomerPageDto;
import com.labway.business.center.mdm.api.reagent.dto.customer.SyncCustomerInfoDto;
import com.labway.business.center.mdm.api.reagent.dto.customer.SyncCustomerInfoQueryDto;
import com.labway.business.center.mdm.api.reagent.param.customer.QueryCustomerClassificationVo;
import com.labway.business.center.mdm.api.reagent.param.customer.QueryCustomerPageVo;
import com.labway.business.center.mdm.common.util.BeanCopyUtils;
import com.labway.business.center.mdm.reagent.mapper.mdm.TbCustomerInfoMapper;
import com.labway.business.center.mdm.reagent.model.TbCustomerInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2023/03/22 16:02
 * @description
 **/
@Slf4j
@Component
public class CustomerInfoDal {

    @Autowired
    private TbCustomerInfoMapper tbCustomerInfoMapper;
    @Autowired
    private NccCustomerInfoDal nccCustomerInfoDal;


    /**
     * 查询Ncc系统客商信息
     *
     * @param syncCustomerInfoQueryDto
     * @return
     */
    public List<NccCustomerInfoQueryDto> queryNccCustomerInfo(SyncCustomerInfoQueryDto syncCustomerInfoQueryDto) {
        return nccCustomerInfoDal.queryNccCustomerInfo(syncCustomerInfoQueryDto);
    }

    /**
     * 查询NCC所有的客商编码 -- 用于主数据判断客商信息删除
     */
    public List<String> queryAllNccCustomerCodes(){
        return nccCustomerInfoDal.queryAllNccCustomerCodes();
    }


    /**
     * 批量新增客商信息
     *
     * @param insertDtos
     * @return
     */
    public int insertCustomerBatch(List<CustomerInfoInsertDto> insertDtos) {
        if (CollectionUtils.isEmpty(insertDtos)) {
            return 0;
        }
        List<TbCustomerInfo> tbCustomerInfos = BeanCopyUtils.copyList(insertDtos, TbCustomerInfo.class);
        return tbCustomerInfoMapper.insertBatch(tbCustomerInfos);
    }

    /**
     * 批量更新客商信息
     *
     * @param updateDtos
     * @return
     */
    public int updateCustomerBatch(List<CustomerInfoUpdateDto> updateDtos) {
        if (CollectionUtils.isEmpty(updateDtos)) {
            return 0;
        }

        List<TbCustomerInfo> tbCustomerInfos = BeanCopyUtils.copyList(updateDtos, TbCustomerInfo.class);
        return tbCustomerInfoMapper.updateBatch(tbCustomerInfos);
    }

    /**
     * 查询主数据所有的客商信息 --查所有，包括逻辑删除的客商信息
     *
     * @return
     */
    public List<SyncCustomerInfoDto> queryAllSyncCustomerInfo() {
        return tbCustomerInfoMapper.queryAllSyncCustomerInfo();
    }

    /**
     * 查询所有启用的客商信息-客商编码和客商名称
     *
     * @param queryDto
     * @return
     */
    public List<TbCustomerInfo> querySimpleCustomerInfo(CustomerInfoQueryDto queryDto) {
        return tbCustomerInfoMapper.selectList(Wrappers.lambdaQuery(TbCustomerInfo.class)
                .select(TbCustomerInfo::getCustomerCode, TbCustomerInfo::getCustomerName)
                .like(StringUtils.hasText(queryDto.getCustomerName()), TbCustomerInfo::getCustomerName, queryDto.getCustomerName())
                .eq(TbCustomerInfo::getCustomerStatus, 1)
                .eq(TbCustomerInfo::getEnabled, 1).orderByDesc(TbCustomerInfo::getCreateTime));
    }

    public Page<QueryCustomerPageVo> queryCustomerPage(QueryCustomerPageDto queryDto) {

        Page<TbCustomerInfo> tbSampleTypePage = tbCustomerInfoMapper.selectPage(new Page<>(queryDto.getCurrent(), queryDto.getSize()),
                Wrappers.lambdaQuery(TbCustomerInfo.class)
                        .eq(StringUtils.hasText(queryDto.getCustclass()), TbCustomerInfo::getCustclass, queryDto.getCustclass())
                        .like(StringUtils.hasText(queryDto.getCustomerName()), TbCustomerInfo::getCustomerName, queryDto.getCustomerName())
                        .eq(TbCustomerInfo::getEnabled, Boolean.TRUE)
                        .orderByDesc(TbCustomerInfo::getCreateTime));
        Page<QueryCustomerPageVo> resultPage = new Page<>();
        BeanUtils.copyProperties(tbSampleTypePage,resultPage);
        if (CollectionUtils.isEmpty(tbSampleTypePage.getRecords())){
            return resultPage;
        }

        List<QueryCustomerPageVo> querySampleTypePageVos = BeanCopyUtils.copyList(tbSampleTypePage.getRecords(), QueryCustomerPageVo.class);
        resultPage.setRecords(querySampleTypePageVos);

        return resultPage;
    }


    /**
     * 根据基本分类名称查询
     * @return
     */
    public List<QueryCustomerClassificationVo> queryCustomerClassification() {

        List<TbCustomerInfo> tbCustomerInfos = tbCustomerInfoMapper.selectList(Wrappers.lambdaQuery(TbCustomerInfo.class)
                .eq(TbCustomerInfo::getEnabled, 1)
                .groupBy(TbCustomerInfo::getCustclass));
        if (CollectionUtils.isEmpty(tbCustomerInfos)){
            return Collections.emptyList();
        }

        return tbCustomerInfos.stream().map(e->new QueryCustomerClassificationVo(e.getCustclass())).collect(Collectors.toList());
    }


    /**
     * 根据客商编码删除客商信息
     * @param deleteCodes
     * @return
     */
    public int deleteByCodes(List<String> deleteCodes) {
        return tbCustomerInfoMapper.delete(Wrappers.lambdaQuery(TbCustomerInfo.class).in(TbCustomerInfo::getCustomerCode,deleteCodes));
    }

    /**
     * 根据跟新时间查询全量客商信息
     * @param queryListForOmsSyncRequest
     * @return
     */
    public List<QueryListForOmsSyncDto> queryListForOmsSync(QueryListForOmsSyncRequest queryListForOmsSyncRequest) {
        return tbCustomerInfoMapper.queryListForOmsSync(queryListForOmsSyncRequest);
    }


}