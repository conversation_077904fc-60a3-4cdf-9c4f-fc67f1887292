package com.labway.business.center.mdm.reagent.server;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.labway.business.center.mdm.api.reagent.dto.AddSampleTypeDto;
import com.labway.business.center.mdm.api.reagent.dto.DeleteSampleTypeBatchDto;
import com.labway.business.center.mdm.api.reagent.dto.page.Pager;
import com.labway.business.center.mdm.api.reagent.dto.QuerySampleTypeAllDto;
import com.labway.business.center.mdm.api.reagent.dto.QuerySampleTypePageDto;
import com.labway.business.center.mdm.api.reagent.dto.TbSampleTypeDto;
import com.labway.business.center.mdm.api.reagent.dto.UpdateSampleTypeDto;
import com.labway.business.center.mdm.api.reagent.param.reagent.QuerySampleTypePageVo;
import com.labway.business.center.mdm.api.reagent.service.SampleTypeService;
import com.labway.business.center.mdm.common.util.BeanCopyUtils;
import com.labway.business.center.mdm.reagent.model.TbSampleType;
import com.labway.business.center.mdm.reagent.service.TbSampleTypeService;
import com.swak.frame.dto.Response;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.Serializable;
import java.util.List;

/**
 * 样本类型表(TbSampleType)表控制层
 *
 * <AUTHOR>
 * @since 2023-03-31 09:21:23
 */
@DubboService
public class SampleTypeServiceImpl implements SampleTypeService {
    /**
     * 服务对象
     */
    @Resource
    private TbSampleTypeService tbSampleTypeService;

    /**
     * 分页查询所有数据
     *
     * @param pager         分页对象
     * @param tbSampleTypeDto 查询实体
     * @return 所有数据
     */
    public Response<Pager<TbSampleTypeDto>> selectAll(Pager<TbSampleTypeDto> pager, TbSampleTypeDto tbSampleTypeDto) {

        Page<TbSampleType> page = new Page<>();
        BeanUtils.copyProperties(pager, page);
        TbSampleType tbSampleType = new TbSampleType();
        BeanUtils.copyProperties(tbSampleTypeDto, tbSampleType);
        Page<TbSampleType> pageResult = tbSampleTypeService.page(page, new QueryWrapper<>(tbSampleType));

        Pager<TbSampleTypeDto> pagerResult = new Pager<>();
        BeanUtils.copyProperties(pageResult, pagerResult);
        List<TbSampleType> records = pageResult.getRecords();
        if (CollectionUtils.isNotEmpty(records)) {
            pagerResult.setRecords(BeanCopyUtils.copyList(records, TbSampleTypeDto.class));
        }

        return Response.success(pagerResult);
    }

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    public Response<TbSampleTypeDto> selectOne(Serializable id) {
        TbSampleType byId = this.tbSampleTypeService.getById(id);
        TbSampleTypeDto tbSampleTypeDto = new TbSampleTypeDto();
        BeanUtils.copyProperties(byId, tbSampleTypeDto);
        return Response.success(tbSampleTypeDto);
    }

    /**
     * 新增数据
     *
     * @param tbSampleTypeDto 实体对象
     * @return 新增结果
     */
    public Response<Boolean> insert(TbSampleTypeDto tbSampleTypeDto) {
        TbSampleType tbSampleType = new TbSampleType();
        BeanUtils.copyProperties(tbSampleTypeDto, tbSampleType);
        return Response.success(this.tbSampleTypeService.save(tbSampleType));
    }

    /**
     * 修改数据
     *
     * @param tbSampleTypeDto 实体对象
     * @return 修改结果
     */
    public Response<Boolean> update(TbSampleTypeDto tbSampleTypeDto) {
        TbSampleType tbSampleType = new TbSampleType();
        BeanUtils.copyProperties(tbSampleTypeDto, tbSampleType);
        return Response.success(this.tbSampleTypeService.updateById(tbSampleType));
    }

    /**
     * 删除数据
     *
     * @param idList 主键结合
     * @return 删除结果
     */
    public Response<Boolean> delete(List<Long> idList) {
        return Response.success(this.tbSampleTypeService.removeByIds(idList));
    }


    //==================================================================================================================


    /**
     * 分页查询所有样本类型信息
     */
    public Response<Pager<QuerySampleTypePageVo>> querySampleTypePage(QuerySampleTypePageDto queryDto) {

        Response<IPage<QuerySampleTypePageVo>> response = tbSampleTypeService.querySampleTypePage(queryDto);
        IPage<QuerySampleTypePageVo> pageResult = response.getData();
        Pager<QuerySampleTypePageVo> pagerResult = new Pager<>();
        BeanUtils.copyProperties(pageResult, pagerResult);
        pagerResult.setRecords(pageResult.getRecords());

        return Response.success(pagerResult);
    }

    /**
     * 新增样本类型信息（编码和名称不能重复）
     */
    public Response<Integer> addSampleType(@Valid AddSampleTypeDto addDto) {
        return tbSampleTypeService.addSampleType(addDto);
    }

    /**
     * 修改样本类型信息（编码不能修改，名称不能重复）
     */
    public Response<Integer> updateSampleType(@Valid UpdateSampleTypeDto updateDto) {
        return tbSampleTypeService.updateSampleType(updateDto);
    }

    /**
     * 删除样本类型信息-批量
     */
    public Response<Integer> deleteSampleTypeBatch(@Valid DeleteSampleTypeBatchDto deleteDto) {
        return tbSampleTypeService.deleteSampleTypeBatch(deleteDto);
    }

    @Override
    public Response<List<QuerySampleTypePageVo>> querySampleTypeAll(QuerySampleTypeAllDto queryDto) {
        return tbSampleTypeService.querySampleTypeAll(queryDto);
    }


}

