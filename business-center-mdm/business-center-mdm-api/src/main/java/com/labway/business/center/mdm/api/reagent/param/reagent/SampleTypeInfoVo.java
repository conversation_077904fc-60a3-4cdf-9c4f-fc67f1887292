package com.labway.business.center.mdm.api.reagent.param.reagent;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 2023/04/21 09:09
 * @description
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SampleTypeInfoVo implements Serializable {

    private static final long serialVersionUID = -5647892497733713559L;
    //样本类型编码
    @NotBlank(message = "样本类型编码不能为空！")
    private String sampleTypeCode;
    //样本类型名称
    @NotBlank(message = "样本类型名称不能为空！")
    private String sampleTypeName;
    //是否允许分血 0否1是
    @NotNull(message = "是否允许分血不能为空！")
    private Integer isSplit;
    //样本类型启用状态 0未启用 1启用
    private Integer status;

}