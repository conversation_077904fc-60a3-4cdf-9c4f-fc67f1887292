package com.labway.business.center.mdm.api.reagent.service;

import com.labway.business.center.mdm.api.reagent.dto.SysDictDto;
import com.labway.business.center.mdm.api.reagent.param.reagent.QueryAllSysDictRequest;
import com.labway.business.center.mdm.api.user.request.QueryDictRequest;
import com.swak.frame.dto.Response;

import java.util.List;

/**
 * 数据字典
 */
public interface SysDictDubboService {

    /**
     * 查询数据字典（包括未启用和逻辑删除）
     */
    List<SysDictDto> queryAllSysDict(QueryAllSysDictRequest request);

    /**
     * 查询检验方法
     */
    Response<List<SysDictDto>> listAllTestMethod(QueryDictRequest request);

    /**
     * 查询异常原因
     */
    Response<List<SysDictDto>> listAllEReason(QueryDictRequest request);


    Response<List<SysDictDto>> provinceList(List<String> userHasOrgs);

}
