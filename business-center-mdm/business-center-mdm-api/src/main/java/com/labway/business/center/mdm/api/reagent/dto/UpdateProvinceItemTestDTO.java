package com.labway.business.center.mdm.api.reagent.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class UpdateProvinceItemTestDTO {

    @NotBlank(message = "检验项目id不能为空！")
    private String testId;

    /**
     * 集团检验项目主键id
     */
    @NotBlank(message = "集团检验项目不能为空！")
    private String companyItemTestId;

    /**
     * 检验项目编码;唯一不重复
     */
    @NotBlank(message = "省份Code不能为空！")
    private String provinceCode;

    /**
     * 检验项目编码;唯一不重复
     */
    @NotBlank(message = "检验项目编码不能为空！")
    private String itemTestCode;

    /**
     * 检验项目名称;唯一不重复
     */
    @NotBlank(message = "检验项目名称不能为空！")
    private String itemTestName;

    /**
     * 检验项目英文名称
     */
    private String englishName;

    /**
     * 样本类型编码
     */
    @NotBlank(message = "样本类型不能为空！")
    private String sampleTypeCode;

    /**
     * 样本类型名称
     */
    @NotBlank(message = "样本类型不能为空！")
    private String sampleTypeName;

    /**
     * 样本存放说明
     */
    private String saveDescription;

    /**
     * 检验方法
     */
    private String testMethod;

    /**
     * 检验项目启用状态 0未启用 1启用
     */
    @NotNull(message = "是否启用不能为空！")
    private Integer status;
}
