package com.labway.business.center.mdm.api.customer.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class QueryListForOmsSyncRequest implements Serializable {

    // 更新时间-开始时间
    private Date updateTimeBegin;

    // 更新时间-结束时间
    private Date updateTimeEnd;

}
