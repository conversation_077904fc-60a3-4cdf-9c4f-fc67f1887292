package com.labway.business.center.mdm.api.reagent.param.reagent;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;


/**
 * <p>
 * 字典表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-17
 */
@Data
public class DictDeleteRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotEmpty
    @NotNull
    private List<String> dictIdList;

}
