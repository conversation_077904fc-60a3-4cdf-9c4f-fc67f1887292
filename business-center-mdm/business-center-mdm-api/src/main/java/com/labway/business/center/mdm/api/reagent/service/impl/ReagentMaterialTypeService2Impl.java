package com.labway.business.center.mdm.api.reagent.service.impl;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.labway.business.center.mdm.api.reagent.param.reagent.ReagentTypeVo;
import com.labway.business.center.mdm.api.reagent.param.reagent.SelectReagentTypeListVi;
import com.labway.business.center.mdm.api.reagent.service.ReagentMaterialTypeService;
import com.swak.frame.dto.Response;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;

import java.util.List;

/**
 * http 调用
 *
 * <AUTHOR>
 * @since 2023/10/9 20:23
 */
class ReagentMaterialTypeService2Impl implements ReagentMaterialTypeService {
    @Value("${business.center.mdm-api:}")
    private String businessHttpUrl;

    @Override
    public Response<List<ReagentTypeVo>> selectReagentTypeList(SelectReagentTypeListVi condition) {
        if (StringUtils.isBlank(businessHttpUrl)) {
            throw new IllegalStateException("请设置请求地址");
        }
        HttpRequest request = HttpUtil.createPost(businessHttpUrl + "/lims/selectReagentTypeList").body(JSON.toJSONString(condition));
        try (HttpResponse execute = request.execute()) {
            return JSONObject.parseObject(execute.body(), new TypeReference<Response<List<ReagentTypeVo>>>() {
            });
        } catch (Exception e) {
            throw new IllegalStateException("调用业务中台方法报错", e);
        }
    }
}
