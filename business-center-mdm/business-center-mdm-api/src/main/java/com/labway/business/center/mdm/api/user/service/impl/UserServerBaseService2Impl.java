package com.labway.business.center.mdm.api.user.service.impl;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.labway.business.center.mdm.api.reagent.dto.page.Pager;
import com.labway.business.center.mdm.api.user.param.SelectUserVo;
import com.labway.business.center.mdm.api.user.param.UserLoginVo;
import com.labway.business.center.mdm.api.user.param.UserVo;
import com.labway.business.center.mdm.api.user.request.UserFailureListRequest;
import com.labway.business.center.mdm.api.user.request.UserListRequest;
import com.labway.business.center.mdm.api.user.service.UserServerBaseService;
import com.swak.frame.dto.Response;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;

import java.util.Date;
import java.util.List;

/**
 * http 调用
 *
 * <AUTHOR>
 * @since 2023/10/9 20:12
 */
class UserServerBaseService2Impl implements UserServerBaseService {

    @Value("${business.center.mdm-api:}")
    private String businessHttpUrl;

    @Override
    public Response<Boolean> syncAllUser() {
        throw new IllegalStateException("此方法不支持http");
    }

    @Override
    public Response<Boolean> syncOaUser() {
        throw new IllegalStateException("此方法不支持http");
    }

    @Override
    public Response<List<UserVo>> getUserList(UserListRequest userListRequest) {
        if (StringUtils.isBlank(businessHttpUrl)) {
            throw new IllegalStateException("请设置请求地址");
        }
        HttpRequest request = HttpUtil.createPost(businessHttpUrl + "/lims/getUserList").body(JSON.toJSONString(userListRequest));
        try (HttpResponse execute = request.execute()) {
            return JSONObject.parseObject(execute.body(), new TypeReference<Response<List<UserVo>>>() {
            });
        } catch (Exception e) {
            throw new IllegalStateException("调用业务中台方法报错", e);
        }
    }

    @Override
    public Response<Pager<UserVo>> getUserPage(SelectUserVo selectUserVo) {
        throw new IllegalStateException("此方法不支持http");
    }

    @Override
    public Response<List<UserVo>> getUserFailureList(UserFailureListRequest userFailureListRequest) {
        if (StringUtils.isBlank(businessHttpUrl)) {
            throw new IllegalStateException("请设置请求地址");
        }
        HttpRequest request = HttpUtil.createPost(businessHttpUrl + "/lims/getUserFailureList").body(JSON.toJSONString(userFailureListRequest));
        try (HttpResponse execute = request.execute()) {
            return JSONObject.parseObject(execute.body(), new TypeReference<Response<List<UserVo>>>() {
            });
        } catch (Exception e) {
            throw new IllegalStateException("调用业务中台方法报错", e);
        }
    }

    @Override
    public Response<List<UserLoginVo>> getEnableUserByDept(List<String> deptIdList, String username) {
        throw new IllegalStateException("此方法不支持http");
    }

    @Override
    public Response<List<UserLoginVo>> getByLoginName(List<String> loginNameList) {
        throw new IllegalStateException("此方法不支持http");
    }

    @Override
    public Response<List<UserLoginVo>> getByUpdateTime(List<String> deptIdList, Date updateStart, Date updateEnd) {
        throw new IllegalStateException("此方法不支持http");
    }

    @Override
    public Response<Boolean> checkUserPwd(String loginName, String password) {
        throw new IllegalStateException("此方法不支持http");
    }
}
