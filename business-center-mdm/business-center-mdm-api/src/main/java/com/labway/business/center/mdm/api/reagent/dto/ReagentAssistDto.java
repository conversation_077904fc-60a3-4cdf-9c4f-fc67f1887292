package com.labway.business.center.mdm.api.reagent.dto;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 试剂耗材辅计量单位信息 Dto
 *
 * <AUTHOR>
 * @since 2023/2/28 16:11
 */
@Getter
@Setter
public class ReagentAssistDto {

    /**
     * 关联试剂耗材id
     */
    private String tbReagentMaterialId;
    /**
     * 信息hash
     */
    private String infoHash;
    /**
     * ncc唯一码
     */
    private String fromNo;
    /**
     * ncc关联试剂码
     */
    private String fromMaterialNo;
    /**
     * 计量单位名称
     */
    private String unitName;
    /**
     * 计量单位名称code
     */
    private String unitNameCode;
    /**
     * 主单位/辅单位
     */
    private String unitTransRate;
    /**
     * 固定换算
     */
    private Boolean fixedFlag;
    /**
     * 结存
     */
    private Boolean balanceFlag;
    /**
     * 采购默认单位
     */
    private Boolean purchaseFlag;
    /**
     * 生产默认单位
     */
    private Boolean productionFlag;
    /**
     * 库存默认单位
     */
    private Boolean inventoryFlag;
    /**
     * 销售默认单位
     */
    private Boolean sellFlag;
    /**
     * 零售默认单位
     */
    private Boolean retailFlag;
    /**
     * 件数管理
     */
    private Boolean pieceManagement;
    /**
     * 备注
     */
    private String remark;

    /**
     * 雪花生成唯一id
     */
    private String itemId;

    /**
     * 是否有效
     */
    private Boolean enabled;
    /**
     * 操作人编码
     */
    private String optUserCode;
    /**
     * 操作人账号
     */
    private String optUserAccount;
    /**
     * 操作人姓名
     */
    private String optUserName;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 修改时间
     */
    private Date updateTime;
}
