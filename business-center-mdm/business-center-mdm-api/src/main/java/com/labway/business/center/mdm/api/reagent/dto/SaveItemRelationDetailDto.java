package com.labway.business.center.mdm.api.reagent.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 2023/04/04 15:22
 * @description
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SaveItemRelationDetailDto implements Serializable {

    @NotBlank(message = "报告项目id不能为空！")
    private String reportId;

    @NotBlank(message = "报告项目编码不能为空！")
    private String reportCode;

}