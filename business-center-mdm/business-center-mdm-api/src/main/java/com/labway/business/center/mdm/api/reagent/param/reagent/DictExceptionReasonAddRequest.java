package com.labway.business.center.mdm.api.reagent.param.reagent;

import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;


/**
 * <p>
 * 字典表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-17
 */
@Data
public class DictExceptionReasonAddRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotBlank
    @Length(max = 20)
    private String dictCode;

    @NotBlank
    @Length(max = 100)
    private String dictValue;

    @Length(max = 200)
    private String remark;

    @Range(max = 1, min = 0)
    private Integer isValid;

}
