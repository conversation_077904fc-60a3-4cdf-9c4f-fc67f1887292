package com.labway.business.center.mdm.api.reagent.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 2023/04/03 16:53
 * @description
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ItemTestQueryDto implements Serializable {

    //主键标识
    private String itemId;
    //检验项目编码;唯一不重复
    private String itemTestCode;
    //检验项目名称;唯一不重复
    private String itemTestName;
    //检验项目英文名称
    private String englishName;
    //样本类型编码
    private String sampleTypeCode;
    //样本类型名称
    private String sampleTypeName;
    //样本存放说明
    private String saveDescription;
    // 检验方法
    private String testMethod;
    //检验项目启用状态 0未启用 1启用
    private Integer status;
    //是否有效（逻辑删）
    private Boolean enabled;
    //操作人编码
    private String optUserCode;
    //操作人账号
    private String optUserAccount;
    //操作人姓名
    private String optUserName;
    //创建时间
    private Date createTime;
    //修改时间
    private Date updateTime;


}