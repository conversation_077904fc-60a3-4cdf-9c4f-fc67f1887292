package com.labway.business.center.mdm.api.reagent.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 2023/03/31 16:12
 * @description
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UpdateSampleTypeDto implements Serializable {

    //主键
    @NotBlank(message = "检验类型id不能为空！")
    private String itemId;
    //样本类型编码
    @NotBlank(message = "样本类型编码不能为空！")
    private String sampleTypeCode;
    //样本类型名称
    @NotBlank(message = "样本类型名称不能为空！")
    private String sampleTypeName;
    //是否允许分血 0否1是
    @NotNull(message = "是否允许分血不能为空！")
    private Integer isSplit;
    //样本类型启用状态 0未启用 1启用
    @NotNull(message = "启用状态不能为空！")
    private Integer status;
    //是否有效（逻辑删）
    private Boolean enabled;
    //操作人编码
    private String optUserCode;
    //操作人账号
    private String optUserAccount;
    //操作人姓名
    private String optUserName;
    //创建时间
    private Date createTime;
    //修改时间
    private Date updateTime;


}