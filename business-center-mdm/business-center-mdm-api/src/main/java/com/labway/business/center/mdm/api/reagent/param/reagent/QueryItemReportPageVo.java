package com.labway.business.center.mdm.api.reagent.param.reagent;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 2023/04/03 09:36
 * @description
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class QueryItemReportPageVo implements Serializable {

    private static final long serialVersionUID = 7357863907124485260L;
    //主键标识
    private String itemId;
    //报告项目编码;唯一不重复
    private String itemReportCode;
    //报告项目名称;唯一不重复
    private String itemReportName;
    //报告项目启用状态 0未启用1启用
    private Integer status;
    //是否有效（逻辑删）
    private Boolean enabled;
    //操作人编码
    private String optUserCode;
    //操作人账号
    private String optUserAccount;
    //操作人姓名
    private String optUserName;
    //创建时间
    private Date createTime;
    //修改时间
    private Date updateTime;


}