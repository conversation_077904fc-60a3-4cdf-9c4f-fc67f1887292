package com.labway.business.center.mdm.api.reagent.dto;


import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class UpdateProvinceItemReportDTO {

    @NotBlank(message = "报告项目id不能为空！")
    private String reportId;

    /**
     * 集团报告项目主键id
     */
    @NotBlank(message = "集团报告项目不能为空！")
    private String companyItemReportId;

    /**
     * 省份code
     */
    @NotBlank(message = "省份Code不能为空！")
    private String provinceCode;

    /**
     * 省级报告项目编码;唯一不重复
     */
    @NotBlank(message = "报告项目Code不能为空！")
    private String itemReportCode;

    /**
     * 省级报告项目名称;唯一不重复
     */
    @NotBlank(message = "报告项目名称不能为空！")
    private String itemReportName;

    /**
     * 报告项目启用状态 0未启用1启用
     */
    @NotNull(message = "报告项目状态不能为空！")
    private Integer status;
}
