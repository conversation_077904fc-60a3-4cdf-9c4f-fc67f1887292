package com.labway.business.center.mdm.api.reagent.param.reagent;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.*;


@Data
public class QueryAllSysDictVo implements Serializable {

    private static final long serialVersionUID = -4168131241175883680L;

    /**
     * 字典名称
     */
    private String dictName;

    /**
     * 字典编码
     */
    private List<String> dictCodeList;

    /**
     * 字典值
     */
    private List<String> dictValueList;

    /**
     * 更新时间（起始）
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTimeStart;

    /**
     * 更新时间（截至）
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTimeEnd;

    /**
     * 创建时间（起始）
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTimeStart;

    /**
     * 创建时间（截至）
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTimeEnd;
}
