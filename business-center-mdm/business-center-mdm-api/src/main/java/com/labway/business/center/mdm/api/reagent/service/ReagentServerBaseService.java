package com.labway.business.center.mdm.api.reagent.service;


import com.labway.business.center.mdm.api.reagent.dto.SyncReagentFromNccDto;
import com.labway.business.center.mdm.api.reagent.dto.customer.CustomerInfoQueryDto;
import com.labway.business.center.mdm.api.reagent.dto.customer.ItemReportSimpleBatchDto;
import com.labway.business.center.mdm.api.reagent.dto.customer.ItemReportSimpleDto;
import com.labway.business.center.mdm.api.reagent.dto.customer.SimpleItemTestQueryDto;
import com.labway.business.center.mdm.api.reagent.dto.customer.SyncCustomerInfoQueryDto;
import com.labway.business.center.mdm.api.reagent.param.customer.CustomerInfoQueryVo;
import com.labway.business.center.mdm.api.reagent.param.reagent.ItemReportSimpleBatchVo;
import com.labway.business.center.mdm.api.reagent.param.reagent.ItemReportSimpleVo;
import com.labway.business.center.mdm.api.reagent.param.reagent.SampleTypeInfoVo;
import com.labway.business.center.mdm.api.reagent.param.reagent.SimpleItemTestQueryVo;

import javax.validation.Valid;
import java.util.List;

/**
 * 主数据-对接ncc试剂耗材服务 feign
 *
 */
public interface ReagentServerBaseService {

    /**
     * 主数据 从 ncc 获取 所有 试剂耗材 数据
     */
    void syncAllReagentMaterialFromNcc();

    /**
     * 主数据 从 ncc 获取 增量 试剂耗材 数据
     */
    void syncIncrementalReagentMaterialFromNcc(SyncReagentFromNccDto condition);

    /**
     * 主数据 从 ncc 获取 所有 试剂耗材类型 数据
     */
    void syncAllReagentMaterialTypeFromNcc();

    /**
     * 全量同步客商信息
     *
     * @return 同步数量
     */
    Integer fullSyncCustomerInfo();

    /**
     * 增量同步客商信息
     *
     * @param infoDto
     * @return 同步数量
     */
    Integer incrementSyncCustomerInfo(SyncCustomerInfoQueryDto infoDto);

    /**
     * 查询所有启用的客商信息--业务中台查询调用
     *
     * @param queryDto
     * @return
     */
    List<CustomerInfoQueryVo> querySimpleCustomerInfo(@Valid CustomerInfoQueryDto queryDto);

    /**
     * 查询所有样本类型数据-业务中台查询调用
     *
     * @return
     */
    List<SampleTypeInfoVo> querySampleTypeInfo();

    /**
     * 根据检验项目查询关联的报告项目-业务中台查询调用
     *
     * @return
     */
    List<ItemReportSimpleVo> queryItemReportList(@Valid ItemReportSimpleDto queryDto);

    /**
     * 根据检验项目查询关联的报告项目(批量)-业务中台查询调用
     *
     * @return
     */
    List<ItemReportSimpleBatchVo> queryItemReportListBatch(@Valid ItemReportSimpleBatchDto queryDto);

    /**
     * 查询主数据所有的检验项目信息-业务中台查询调用
     *
     * @return
     */
    List<SimpleItemTestQueryVo> queryItemTestList(@Valid SimpleItemTestQueryDto queryDto);

}
