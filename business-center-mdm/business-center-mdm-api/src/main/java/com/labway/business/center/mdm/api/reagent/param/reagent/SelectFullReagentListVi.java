package com.labway.business.center.mdm.api.reagent.param.reagent;


import com.labway.business.center.core.enums.ResultCode;
import com.labway.business.center.mdm.api.reagent.dto.page.Pager;
import com.labway.business.center.mdm.common.constants.CommonConstant;
import com.labway.business.center.mdm.common.exception.ResultException;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;

import java.text.ParseException;

/**
 * 主数据获取试剂耗材数据接口 Vi
 *
 * <AUTHOR>
 * @since 2023/3/2 10:13
 */
@Getter
@Setter
public class SelectFullReagentListVi extends Pager {
    private static final long serialVersionUID = 6039076618526157769L;

    /**
     * 同时根据 修改时间、创建时间一起查看
     */
    private Boolean creationAndModified = Boolean.FALSE;

    /**
     * 试剂类型编码
     */
    private String reagentTypeCode;
    /**
     * 试剂名称 模糊
     */
    private String reagentNameLike;

    /**
     * 主数据创建时间
     */
    private String createTimeStart;

    /**
     * 主数据创建时间
     */
    private String createTimeEnd;

    /**
     * 主数据更新时间
     */
    private String updateTimeStart;

    /**
     * 主数据更新时间
     */
    private String updateTimeEnd;

    /**
     * 检查参数有效性
     */
    public void validityCheck() {
        checkSizeThousand();
        try {
            if (!StringUtils.isBlank(createTimeStart)) {
                createTimeStart = DateFormatUtils.format(
                        DateUtils.parseDate(createTimeStart, CommonConstant.DATE_PATTERN), CommonConstant.DATE_PATTERN);
            }
            if (!StringUtils.isBlank(createTimeEnd)) {
                createTimeEnd = DateFormatUtils.format(
                        DateUtils.parseDate(createTimeEnd, CommonConstant.DATE_PATTERN), CommonConstant.DATE_PATTERN);
            }
            if (!StringUtils.isBlank(updateTimeStart)) {
                updateTimeStart = DateFormatUtils.format(
                        DateUtils.parseDate(updateTimeStart, CommonConstant.DATE_PATTERN), CommonConstant.DATE_PATTERN);
            }
            if (!StringUtils.isBlank(updateTimeEnd)) {
                updateTimeEnd = DateFormatUtils.format(
                        DateUtils.parseDate(updateTimeEnd, CommonConstant.DATE_PATTERN), CommonConstant.DATE_PATTERN);
            }
        } catch (ParseException e) {
            throw new ResultException(ResultCode.E111001);
        }
    }
}
