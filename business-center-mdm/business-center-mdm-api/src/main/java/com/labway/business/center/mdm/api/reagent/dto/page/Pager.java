package com.labway.business.center.mdm.api.reagent.dto.page;

import com.labway.business.center.mdm.common.constants.CommonConstant;
import com.labway.business.center.mdm.common.exception.ResultException;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

public class Pager<T> implements Serializable {
    private static final long serialVersionUID = 3181748306813934106L;

    protected List<T> records;
    protected Long total;
    protected Long size;
    protected Long current;
    protected List<Objects> orders;
    protected Boolean optimizeCountSql;
    protected Boolean searchCount;
    protected Boolean optimizeJoinOfCountSql;
    protected Long maxLimit;
    protected String countId;


    /**
     * 检查页码数量
     */
    protected void checkSizeThousand() {
        if (Objects.isNull(current) || Objects.isNull(size)) {
            throw new ResultException("分页参数不合法");
        }
        if (this.size > CommonConstant.INT_ONE_THOUSAND) {
            throw new ResultException("每页数量最大1000");
        }
    }

    public Pager() {
        this.records = Collections.emptyList();
        this.total = 0L;
        this.size = 10L;
        this.current = 1L;
        this.orders = new ArrayList();
        this.optimizeCountSql = true;
        this.searchCount = true;
        this.optimizeJoinOfCountSql = true;
    }

    public Pager(long current, long size) {
        this(current, size, 0L);
    }

    public Pager(long current, long size, long total) {
        this(current, size, total, true);
    }

    public Pager(long current, long size, boolean searchCount) {
        this(current, size, 0L, searchCount);
    }

    public Pager(long current, long size, long total, boolean searchCount) {
        this.records = Collections.emptyList();
        this.total = 0L;
        this.size = 10L;
        this.current = 1L;
        this.orders = new ArrayList();
        this.optimizeCountSql = true;
        this.searchCount = true;
        this.optimizeJoinOfCountSql = true;
        if (current > 1L) {
            this.current = current;
        }

        this.size = size;
        this.total = total;
        this.searchCount = searchCount;
    }

    public boolean hasPrevious() {
        return this.current > 1L;
    }

    public boolean hasNext() {
        return this.current < this.getPages();
    }

    public List<T> getRecords() {
        return this.records;
    }

    public Pager<T> setRecords(List<T> records) {
        this.records = records;
        return this;
    }

    public long getTotal() {
        return this.total;
    }

    public Pager<T> setTotal(long total) {
        this.total = total;
        return this;
    }

    public long getSize() {
        return this.size;
    }

    public Pager<T> setSize(long size) {
        this.size = size;
        return this;
    }

    public long getCurrent() {
        return this.current;
    }

    public Pager<T> setCurrent(long current) {
        this.current = current;
        return this;
    }

    public String countId() {
        return this.countId;
    }

    public Long maxLimit() {
        return this.maxLimit;
    }

    public boolean optimizeCountSql() {
        return this.optimizeCountSql;
    }

    public static <T> Pager<T> of(long current, long size, long total, boolean searchCount) {
        return new Pager(current, size, total, searchCount);
    }

    public boolean optimizeJoinOfCountSql() {
        return this.optimizeJoinOfCountSql;
    }

    public Pager<T> setSearchCount(boolean searchCount) {
        this.searchCount = searchCount;
        return this;
    }

    public Pager<T> setOptimizeCountSql(boolean optimizeCountSql) {
        this.optimizeCountSql = optimizeCountSql;
        return this;
    }

    public Pager<T> setPages(long pages) {
        return this;
    }

    public long getPages() {
        if (this.getSize() == 0L) {
            return 0L;
        } else {
            long pages = this.getTotal() / this.getSize();
            if (this.getTotal() % this.getSize() != 0L) {
                ++pages;
            }

            return pages;
        }
    }

    public static <T> Pager<T> of(long current, long size) {
        return of(current, size, 0L);
    }

    public static <T> Pager<T> of(long current, long size, long total) {
        return of(current, size, total, true);
    }

    public static <T> Pager<T> of(long current, long size, boolean searchCount) {
        return of(current, size, 0L, searchCount);
    }

    public boolean searchCount() {
        return this.total < 0L ? false : this.searchCount;
    }

    /**
     * @deprecated
     */
    @Deprecated
    public String getCountId() {
        return this.countId;
    }

    /**
     * @deprecated
     */
    @Deprecated
    public Long getMaxLimit() {
        return this.maxLimit;
    }

    /**
     * @deprecated
     */
    @Deprecated
    public List<Objects> getOrders() {
        return this.orders;
    }

    /**
     * @deprecated
     */
    @Deprecated
    public boolean isOptimizeCountSql() {
        return this.optimizeCountSql;
    }

    /**
     * @deprecated
     */
    @Deprecated
    public boolean isSearchCount() {
        return this.searchCount;
    }

    public void setOrders(final List<Objects> orders) {
        this.orders = orders;
    }

    public void setOptimizeJoinOfCountSql(final boolean optimizeJoinOfCountSql) {
        this.optimizeJoinOfCountSql = optimizeJoinOfCountSql;
    }

    public void setMaxLimit(final Long maxLimit) {
        this.maxLimit = maxLimit;
    }

    public void setCountId(final String countId) {
        this.countId = countId;
    }
}
