package com.labway.business.center.mdm.api.reagent.service.impl;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.labway.business.center.mdm.api.reagent.dto.SysDictDto;
import com.labway.business.center.mdm.api.reagent.param.reagent.QueryAllSysDictRequest;
import com.labway.business.center.mdm.api.reagent.service.SysDictDubboService;
import com.labway.business.center.mdm.api.user.request.QueryDictRequest;
import com.swak.frame.dto.Response;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;

import java.util.List;

/**
 * http 调用
 *
 * <AUTHOR>
 * @since 2023/10/9 20:17
 */
class SysDictDubboService2Impl implements SysDictDubboService {


    @Value("${business.center.mdm-api:}")
    private String businessHttpUrl;

    @Override
    public List<SysDictDto> queryAllSysDict(QueryAllSysDictRequest request) {
        throw new IllegalStateException("此方法不支持http");
    }

    @Override
    public Response<List<SysDictDto>> listAllTestMethod(QueryDictRequest dictRequest) {
        if (StringUtils.isBlank(businessHttpUrl)) {
            throw new IllegalStateException("请设置请求地址");
        }
        HttpRequest request = HttpUtil.createPost(businessHttpUrl + "/lims/listAllTestMethod").body(JSON.toJSONString(dictRequest));
        try (HttpResponse execute = request.execute()) {
            return JSONObject.parseObject(execute.body(), new TypeReference<Response<List<SysDictDto>>>() {
            });
        } catch (Exception e) {
            throw new IllegalStateException("调用业务中台方法报错", e);
        }
    }

    @Override
    public Response<List<SysDictDto>> listAllEReason(QueryDictRequest dictRequest) {
        if (StringUtils.isBlank(businessHttpUrl)) {
            throw new IllegalStateException("请设置请求地址");
        }
        HttpRequest request = HttpUtil.createPost(businessHttpUrl + "/lims/listAllEReason").body(JSON.toJSONString(dictRequest));
        try (HttpResponse execute = request.execute()) {
            return JSONObject.parseObject(execute.body(), new TypeReference<Response<List<SysDictDto>>>() {
            });
        } catch (Exception e) {
            throw new IllegalStateException("调用业务中台方法报错", e);
        }
    }

    @Override
    public Response<List<SysDictDto>> provinceList(List<String> userHasOrgs) {
        if (StringUtils.isBlank(businessHttpUrl)) {
            throw new IllegalStateException("请设置请求地址");
        }
        HttpRequest request = HttpUtil.createGet(businessHttpUrl + "/province/list");
        request.body(JSONUtil.toJsonStr(userHasOrgs));
        try (HttpResponse execute = request.execute()) {
            return JSONObject.parseObject(execute.body(), new TypeReference<>() {
            });
        } catch (Exception e) {
            throw new IllegalStateException("调用业务中台方法报错", e);
        }
    }
}
