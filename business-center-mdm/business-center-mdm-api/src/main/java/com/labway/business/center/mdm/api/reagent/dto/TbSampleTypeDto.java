package com.labway.business.center.mdm.api.reagent.dto;


import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 样本类型表(TbSampleType)DTO
 */
@Data
public class TbSampleTypeDto implements Serializable {


    private static final long serialVersionUID = -3822663406680659844L;
    //主键
    private String itemId;
    //样本类型编码
    private String sampleTypeCode;
    //样本类型名称
    private String sampleTypeName;
    //是否允许分血 0否1是
    private Integer isSplit;
    //样本类型启用状态 0未启用 1启用
    private Integer status;
    //是否有效（逻辑删）
    private Boolean enabled;
    //操作人编码
    private String optUserCode;
    //操作人账号
    private String optUserAccount;
    //操作人姓名
    private String optUserName;
    //创建时间
    private Date createTime;
    //修改时间
    private Date updateTime;


}

