package com.labway.business.center.mdm.api.reagent.service;

import com.labway.business.center.mdm.api.reagent.param.reagent.ReagentTypeVo;
import com.labway.business.center.mdm.api.reagent.param.reagent.SelectReagentTypeListVi;
import com.swak.frame.dto.Response;

import java.util.List;

/**
 * 试剂耗材类型 API
 */
public interface ReagentMaterialTypeService {

    /**
     * 主数据获取试剂耗材类型数据接口
     *
     * @param condition 参数 {@link SelectReagentTypeListVi}
     * @return list {@link ReagentTypeVo}
     */
    Response<List<ReagentTypeVo>> selectReagentTypeList(SelectReagentTypeListVi condition);
}
