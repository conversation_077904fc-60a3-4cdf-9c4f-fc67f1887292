package com.labway.business.center.mdm.common.file.annotation;



import org.springframework.core.annotation.AliasFor;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @DateTime 2022/8/18 10:58
 * @ClassName Excel
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.FIELD})
public @interface Excel {

    @AliasFor("name")
    String value() default "";

    @AliasFor("value")
    String name() default "";

    int index() default 0;

    /**
     * 分隔符,导入导出都能用
     * split = {"0_女","1_男"}
     *
     * @return
     */
    String[] split() default {};

    /**
     * 分组
     * 字符串格式, 可以多个
     *
     * @return
     */
    String[] groups() default {};
}

