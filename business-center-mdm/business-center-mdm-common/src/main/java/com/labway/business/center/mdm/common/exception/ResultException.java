package com.labway.business.center.mdm.common.exception;

import com.labway.business.center.core.enums.ResultCode;
import com.swak.frame.enums.IResultCode;
import lombok.Getter;
import lombok.Setter;

/**
 * 异常抛出
 *
 * <AUTHOR>
 * @since 2023/2/22 13:24
 */
@Getter
@Setter
public class ResultException extends RuntimeException {

    /**
     * 错误码
     */
    private final Integer code;

    /**
     * 构造方法
     *
     * @param code    错误码
     * @param message 错误信息
     */
    public ResultException(Integer code, String message) {
        super(message);
        this.code = code;
    }

    /**
     * 构造方法
     *
     * @param message 异常描述
     */
    public ResultException(String message) {
        super(message);
        this.code = ResultCode.E111500.getCode();
    }

    /**
     * 构造方法
     *
     * @param resultCode 错误异常
     */
    public ResultException(IResultCode resultCode) {
        super(resultCode.getMsg());
        this.code = resultCode.getCode();
    }

}
