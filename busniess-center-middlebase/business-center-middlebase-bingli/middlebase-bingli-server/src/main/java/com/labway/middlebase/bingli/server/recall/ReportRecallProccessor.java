 package com.labway.middlebase.bingli.server.recall;

import com.labway.middlebase.bingli.api.request.RecallApplyMainRequest;
import com.swak.frame.dto.Response;

/**
  * 报告处理器
  * <AUTHOR>
  * @date 2023/07/18
  */
 public interface ReportRecallProccessor {
     
     /**
      * 处理报告
      * @param request
      * @return
      */
     public Response<String> proccessReport(RecallApplyMainRequest request);

}
