package com.labway.middlebase.bingli.server.persistence.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 
 * @TableName EIF_LIS_APPLY_MAIN
 */
@TableName(value ="EIF_LIS_APPLY_MAIN")
@Data
public class ApplyMain implements Serializable {
    /**
     * 条码编码
     */
    private String barcode;

    /**
     * 条码的机构编码
     */
    private String hspOrgCode;

    /**
     * 体检单位ID
     */
    private Integer peOrgId;

    /**
     * 容器类型
     */
    private String tubeType;

    /**
     * 条码类型(0打印1按管型预制2按套餐预制)3医院预制4接收手工单(标本接收时系统无此条码记录)5体检名单拿回来后批量打印6加项
     */
    private String barType;

    /**
     * 来源类型(病人类型,字典码)
     */
    private String sourceType;

    /**
     * 是否加急(0否1是)
     */
    private String isUrgent;

    /**
     * 标本类型
     */
    private String sampleType;

    /**
     * 样本状态
     */
    private String sampleStatus;

    /**
     * 申请科室
     */
    private String appDept;

    /**
     * 申请病区
     */
    private String inpatientArea;

    /**
     * 病人编号
     */
    private String patientNo;

    /**
     * 病人姓名
     */
    private String patientName;

    /**
     * 性别
     */
    private String sex;

    /**
     * 年龄
     */
    private String age;

    /**
     * 生日
     */
    private Date birthday;

    /**
     * 床号
     */
    private String bed;

    /**
     * 临床诊断
     */
    private String diag;

    /**
     * 病人卡号
     */
    private String patientCard;

    /**
     * 医保号
     */
    private String medNumber;

    /**
     * 身份证号
     */
    private String idNumber;

    /**
     * 地址
     */
    private String address;

    /**
     * 电话
     */
    private String tel;

    /**
     * 备注
     */
    private String memo;

    /**
     * 是否禁用(0正常 1禁用)默认0
     */
    private Integer isForbidden;

    /**
     * 发送状态(0创建1上传成功2转发实验室)
     */
    private Integer sendStatus;

    /**
     * 申请人
     */
    private String appUserName;

    /**
     * 申请时间
     */
    private Date appDate;

    /**
     * 条码打印人
     */
    private String barprtUserName;

    /**
     * 条码打印时间
     */
    private Date barprtDate;

    /**
     * 采样人
     */
    private String extractUserName;

    /**
     * 采样时间
     */
    private Date extractDate;

    /**
     * 送检人编号
     */
    private String submitUserCode;

    /**
     * 送检人
     */
    private String submitUserName;

    /**
     * 送检接收时间
     */
    private Date submitDate;

    /**
     * 当期条码环节（字典表）
     */
    private String currentLink;

    /**
     * 检验项目数量
     */
    private Integer testitemSum;

    /**
     * 合并条码分组编码(暂时体检用)
     */
    private String combBarCode;

    /**
     * 是否已经接收0没有1接收默认0
     */
    private Integer isReceive;

    /**
     * 是否发生异常0没有1有默认0
     */
    private Integer isOccurexception;

    /**
     * 套餐说明
     */
    private String groupMemo;

    /**
     * 报告编号
     */
    private String reportNo;

    /**
     * 标本接收的日期
     */
    private Date receiveDate;

    /**
     * 接收人编号
     */
    private String receiveUserCode;

    /**
     * 接收人
     */
    private String receiveUserName;

    /**
     * 样本数量
     */
    private Integer sampleNum;

    /**
     * 主条码号
     */
    private String mainBarcode;

    /**
     * 发送时间
     */
    private Date sendDate;

    /**
     * 创建时间
     */
    private Date operDate;

    /**
     * HIS申请单号
     */
    private String hisReqno;

    /**
     * 费用
     */
    private BigDecimal charge;

    /**
     * 条码的机构编码
     */
    private String lwHspOrgCode;

    /**
     * 兰卫条码编码
     */
    private String lwBarcode;

    /**
     * 主条码
     */
    private String lwMainBarcode;

    /**
     * 操作时间
     */
    private Date lwOperDate;

    /**
     * 汇总单编码
     */
    private String summaryHandoverCode;

    /**
     * 原始送检机构编码
     */
    private String origoutOrgcode;

    /**
     * 原始送检机构名称
     */
    private String origoutOrgname;

    /**
     * 就诊流水号
     */
    private String jzlsh;

    /**
     * 卡类型
     */
    private String klx;

    /**
     * 上传PDF标志，1 是，0 否
     */
    private Integer uploadPdf;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

}