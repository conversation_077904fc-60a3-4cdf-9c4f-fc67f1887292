package com.labway.middlebase.bingli.server.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.labway.middlebase.bingli.server.persistence.model.PisResultFrozen;

/**
 * 冰冻报告表
 * <AUTHOR>
 * @date 2023/07/17
 */
public interface ResultFrozenService extends IService<PisResultFrozen> {
    
    /**
     * 获取冰冻报告信心
     * @param barcode
     * @param fBlh
     * @param fbgxh
     * @return
     */
    public PisResultFrozen getFrozenByBarcode(String barcode,String fBlh,Integer fbgxh);
    
    /**
     * 删除冰冻报告
     * @param barcode
     * @param fBlh
     * @param fbgxh
     * @return
     */
    public void deleteFrozenByBarcode(String barcode,String fBlh,Integer fbgxh);
    
    /**
     * 根据结果ID 获取冰冻信息
     * @param resultId
     * @return
     */
    public PisResultFrozen getFrozenByResultId(String resultId);
    
    
    
}
