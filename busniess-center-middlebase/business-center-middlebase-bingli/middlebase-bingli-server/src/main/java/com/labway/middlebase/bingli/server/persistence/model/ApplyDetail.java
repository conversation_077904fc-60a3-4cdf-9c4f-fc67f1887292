package com.labway.middlebase.bingli.server.persistence.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 申请单明细表
 * @TableName EIF_LIS_APPLY_DETAIL
 */
@TableName(value ="EIF_LIS_APPLY_DETAIL")
@Data
public class ApplyDetail implements Serializable {
    /**
     * 条码编码
     */
    private String barcode;

    /**
     * 外部检验项目编码
     */
    private String outTestitemCode;

    /**
     * 外部检验项目名称
     */
    private String outTestitemName;

    /**
     * 兰卫检验项目编码
     */
    private String testitemCode;

    /**
     * 条码的机构编码
     */
    private String hspOrgCode;

    /**
     * 收费标志(0否1是)
     */
    private String isFee;

    /**
     * 是否禁用(0正常 1禁用)默认0
     */
    private Integer isForbidden;

    /**
     * 收费次数
     */
    private Integer feeNum;

    /**
     * 属性1
     */
    private String property1;

    /**
     * 属性2
     */
    private String property2;

    /**
     * 属性3
     */
    private String property3;

    /**
     * 属性4
     */
    private String property4;

    /**
     * 属性5
     */
    private String property5;

    /**
     * 收费类型(0:普通 1：公卫)
     */
    private Integer feeType;

    /**
     * 显示顺序
     */
    private Integer showNo;

    /**
     * 兰卫检验项目名称
     */
    private String testitemName;

    /**
     * 条码的机构编码
     */
    private String lwHspOrgCode;

    /**
     * 兰卫条码编码
     */
    private String lwBarcode;

    /**
     * 主条码
     */
    private String lwMainBarcode;

    /**
     * 操作时间
     */
    private Date lwOperDate;

    /**
     * 容器类型
     */
    private String lwTubeType;

    /**
     * 标本类型
     */
    private String lwSampleType;

    /**
     * 合并条码分组编码(暂时体检用)
     */
    private String lwCombBarCode;

    /**
     * 套餐ID
     */
    private String labPackageId;

    /**
     * 套餐名称
     */
    private String labPackageName;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

}