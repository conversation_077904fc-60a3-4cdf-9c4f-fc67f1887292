 package com.labway.middlebase.bingli.server.recall;

import java.util.Objects;

import javax.annotation.Resource;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.labway.middlebase.bingli.api.request.RecallApplyMainRequest;
import com.labway.middlebase.bingli.server.enums.ReportTypeEnum;
import com.labway.middlebase.bingli.server.persistence.mapper.PisResultFrozenBakMapper;
import com.labway.middlebase.bingli.server.persistence.model.PisResultFrozen;
import com.labway.middlebase.bingli.server.persistence.model.PisResultFrozenBak;
import com.labway.middlebase.bingli.server.service.ResultFrozenService;
import com.labway.middlebase.bingli.server.service.ResultMainService;
import com.swak.frame.dto.Response;
import com.swak.frame.util.GetterUtil;

/**
 * 冰冻检验报告处理器
 * <AUTHOR>
 * @date 2023/07/18
 */
@Component("recallFrostReportProccessor")
public class RecallFrostReportProccessor implements ReportRecallProccessor {
    
    @Autowired
    private ResultFrozenService resultFrozenService;
    
    @Resource
    private ResultMainService resultMainService;
    
    @Resource
    private PisResultFrozenBakMapper pisResultFrozenBakMapper;
    
    @Autowired
    private RecallReportPdfProccessor recallReportPdfProccessor;
    


    @Transactional(rollbackFor = Exception.class)
    @Override
    public Response<String> proccessReport(RecallApplyMainRequest request) {
        PisResultFrozen dbFrozen = resultFrozenService.getFrozenByBarcode(request.getBarcode(), request.getFBlh(), GetterUtil.getInteger(request.getFBgxh()));
        if (Objects.nonNull(dbFrozen)) {
           PisResultFrozenBak resultFrozenBak = new PisResultFrozenBak();
            BeanUtils.copyProperties(dbFrozen, resultFrozenBak);
            pisResultFrozenBakMapper.insert(resultFrozenBak);
            resultFrozenService.deleteFrozenByBarcode(request.getBarcode(), request.getFBlh(), GetterUtil.getInteger(request.getFBgxh()));
            recallReportPdfProccessor.updateReportFileStatus(dbFrozen.getResultId(), ReportTypeEnum.BD.getName(), request.getFBgxh());
        }
       
        return Response.success();
    }

}
