 package com.labway.middlebase.bingli.server.config;

 import org.springframework.context.ApplicationContextInitializer;
 import org.springframework.context.ConfigurableApplicationContext;
 import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;

 @Component
 @Slf4j
 public class CustomApplicationContextInitializer implements ApplicationContextInitializer<ConfigurableApplicationContext> {
     private static ConfigurableApplicationContext context = null;
     @Override
     public void initialize(ConfigurableApplicationContext applicationContext) {
         context = applicationContext;
     }
    public static ConfigurableApplicationContext getContext() {
        return context;
    }
     
     
   
     
 }