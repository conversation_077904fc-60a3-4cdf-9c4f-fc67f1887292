 package com.labway.middlebase.bingli.server.recall;

import java.util.Objects;

import javax.annotation.Resource;

import com.labway.middlebase.bingli.server.push.PushReportPdfProccessor;
import org.springframework.stereotype.Component;

import com.labway.middlebase.bingli.api.request.PushApplyMainRequest;
import com.labway.middlebase.bingli.server.config.Constants;
import com.labway.middlebase.bingli.server.persistence.model.LisReportFile;
import com.labway.middlebase.bingli.server.service.ReportFileService;
import com.labway.middlebase.bingli.server.vo.ReportPdfParams;

import cn.hutool.core.codec.Base64;

/**
  * 报告单处理
  * <AUTHOR>
  * @date 2023/07/24
  */
 @Component
 public class RecallReportPdfProccessor {

    @Resource
    private ReportFileService reportFileService;

    /**
     * @see PushReportPdfProccessor#saveOrUpdateReportPdf
     * @deprecated 参考其他保存方法，这个不是最新的
     */
    @Deprecated
    public void saveOrUpdateReportPdf(ReportPdfParams params) {
        String resultId = params.getResultId();
        String type = params.getReportKind();
        String index = params.getRequest().getFSqxh();
        PushApplyMainRequest request = params.getRequest();
        LisReportFile reportFile = reportFileService.getFilesByResultIdAndTypeAndIndex(resultId, type, index);
        if (Objects.nonNull(reportFile)) {
            reportFile.setFileType(request.getFileType());
            byte[] bytes = Base64.decode(request.getFileBlob());
            reportFile.setFileBlob(bytes);
            reportFileService.updateReportFile(reportFile);
        } else {
            reportFile = new LisReportFile();
            reportFile.setOutKey(resultId);
            reportFile.setFileType(request.getFileType());
            byte[] bytes = Base64.decode(request.getFileBlob());
            reportFile.setFileBlob(bytes);
            reportFile.setIsEnable(1);
            reportFile.setOperType(1);
            reportFile.setReportType(Constants.REPORT_TYPE);
            reportFile.setOrgId(request.getHspOrgCode());
            reportFile.setZdy2(request.getFBlh());
            reportFile.setZdy3(type);
            reportFile.setZdy5(index);
            reportFileService.save(reportFile);
        }

    }

    public void updateReportFileStatus(String resultId, String type, String index) {
        LisReportFile reportFile = reportFileService.getFilesByResultIdAndTypeAndIndex(resultId, type, index);
        if (Objects.nonNull(reportFile)) {
            reportFileService.updateReportFileStatus(reportFile);
        }
    }

}
