 package com.labway.middlebase.bingli.server.recall;


import java.util.Objects;

import javax.annotation.Resource;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.labway.middlebase.bingli.api.request.RecallApplyMainRequest;
import com.labway.middlebase.bingli.server.converter.ResultMainConverter;
import com.labway.middlebase.bingli.server.enums.ReportTypeEnum;
import com.labway.middlebase.bingli.server.persistence.mapper.PisResultMainBakMapper;
import com.labway.middlebase.bingli.server.persistence.model.PisResultMain;
import com.labway.middlebase.bingli.server.persistence.model.PisResultMainBak;
import com.labway.middlebase.bingli.server.service.ResultMainService;
import com.swak.frame.dto.Response;

 /**
 * 普通检验报告处理器
 * <AUTHOR>
 * @date 2023/07/18
 */
@Component("recallNormalReportProccessor")
public class RecallNormalReportProccessor implements ReportRecallProccessor {
    
    @Resource
    private ResultMainService resultMainService;
    
    @Autowired
    private ResultMainConverter resultMainConverter;
    
    @Autowired
    private RecallReportPdfProccessor recallReportPdfProccessor;
    
    @Resource
    private PisResultMainBakMapper pisResultMainBakMapper;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Response<String> proccessReport(RecallApplyMainRequest request) {
        PisResultMain main = resultMainService.selectByBarcode(request.getFBlh(), request.getBarcode());
        if (Objects.nonNull(main)) {
            PisResultMainBak mainBak = new PisResultMainBak();
            BeanUtils.copyProperties(main, mainBak);
            pisResultMainBakMapper.insert(mainBak);
            resultMainService.deleteResultMainByBlhAndBarcode(request.getFBlh(), request.getBarcode());
            recallReportPdfProccessor.updateReportFileStatus(main.getResultId(), ReportTypeEnum.CG.getName(), request.getFBgxh());
         }

        return Response.success();
    }
    
  

}
