package com.labway.middlebase.bingli.server.recall;

import java.util.Objects;

import javax.annotation.Resource;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.labway.middlebase.bingli.api.request.RecallApplyMainRequest;
import com.labway.middlebase.bingli.server.converter.ResultAddtionConverter;
import com.labway.middlebase.bingli.server.enums.ReportTypeEnum;
import com.labway.middlebase.bingli.server.persistence.mapper.PisResultAddtionBakMapper;
import com.labway.middlebase.bingli.server.persistence.model.PisResultAddtion;
import com.labway.middlebase.bingli.server.persistence.model.PisResultAddtionBak;
import com.labway.middlebase.bingli.server.service.ResultAddtionService;
import com.labway.middlebase.bingli.server.service.ResultMainService;
import com.swak.frame.dto.Response;
import com.swak.frame.util.GetterUtil;

/**
 * 补充检验报告处理器
 * 
 * <AUTHOR>
 * @date 2023/07/18
 */
@Component("recallSupplyReportProccessor")
public class RecallSupplyReportProccessor implements ReportRecallProccessor {

    @Resource
    private ResultAddtionConverter resultAddtionConverter;

    @Resource
    private ResultAddtionService resultAddtionService;
    
    @Resource
    private PisResultAddtionBakMapper pisResultAddtionBakMapper;

    @Resource
    private ResultMainService resultMainService;
    
    @Autowired
    private RecallReportPdfProccessor recallReportPdfProccessor;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Response<String> proccessReport(RecallApplyMainRequest request) {
        PisResultAddtion dbResultAddtion = resultAddtionService.getResultAddtionByCode(request.getBarcode(), GetterUtil.getInteger(request.getFBgxh()), request.getFBlh());
        if (Objects.nonNull(dbResultAddtion)) {
            String resultId =dbResultAddtion.getResultId();
            PisResultAddtionBak pisResultAddtionBak = new PisResultAddtionBak();
            BeanUtils.copyProperties(dbResultAddtion, pisResultAddtionBak);
            pisResultAddtionBakMapper.insert(pisResultAddtionBak);
            resultAddtionService.deleteResultAddtionByCode(request.getBarcode(), GetterUtil.getInteger(request.getFBgxh()), request.getFBlh());
            recallReportPdfProccessor.updateReportFileStatus(resultId, ReportTypeEnum.BC.getName(), request.getFBgxh());
        }
      
        return Response.success();
    }

}
