 package com.labway.middlebase.bingli.server.service.impl;

import javax.annotation.Resource;

import cn.hutool.json.JSONUtil;
import com.labway.middlebase.bingli.server.utils.BingliRedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.dubbo.config.annotation.DubboService;
import com.labway.middlebase.bingli.api.request.PushApplyMainRequest;
import com.labway.middlebase.bingli.api.request.RecallApplyMainRequest;
import com.labway.middlebase.bingli.api.service.PathologyWriteRpcService;
import com.labway.middlebase.bingli.server.push.PushBeanCreator;
import com.labway.middlebase.bingli.server.recall.RecallBeanCreator;
import com.swak.frame.dto.Response;

import java.util.concurrent.TimeUnit;

import static com.labway.middlebase.bingli.api.enums.ResultCode.E726001;

 /**
 * 
 * <AUTHOR>
 * @date 2023/07/17
 */
@DubboService
@Slf4j
public class PathologyWriteRpcServiceImpl implements PathologyWriteRpcService {
    
    @Resource
    private PushBeanCreator pushBeanCreator;
    @Resource
    private RecallBeanCreator recallBeanCreator;
    @Resource
    private BingliRedisUtils redisUtils;


    @Override
    public Response<String> pushPathologyApplyMainResult(PushApplyMainRequest pushRequest) {

        try {
            //  幂等
            String uniqueKey = DigestUtils.md5Hex(JSONUtil.toJsonStr(pushRequest));
            if (!redisUtils.setIfAbsent(uniqueKey, 10, TimeUnit.SECONDS)) {
                log.warn("病理结果数据推送中间库幂等校验不通过");
                return Response.fail(E726001.getCode(), E726001.getMsg());
            }
        } catch (Exception e) {
            log.error("幂等处理异常", e);
        }

        return pushBeanCreator.doProccessor(pushRequest);
    }

    @Override
    public Response<String> recallPathologyApplyMainResult(RecallApplyMainRequest recallRequest) {



        return recallBeanCreator.doProccessor(recallRequest);
    }

}
