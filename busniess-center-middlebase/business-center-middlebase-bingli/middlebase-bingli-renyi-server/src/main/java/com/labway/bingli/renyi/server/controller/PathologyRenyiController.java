package com.labway.bingli.renyi.server.controller;

import com.labway.middlebase.bingli.api.request.RecallApplyMainRequest;
import com.labway.middlebase.bingli.api.request.PushApplyMainRequest;
import com.labway.middlebase.bingli.base.service.PathologyMiddleBaseService;
import com.swak.frame.dto.Response;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;


/**
 * 调用webservice接口推送到人医的数据库
 */
@RestController
@RequestMapping("/pathology")
public class PathologyRenyiController {

    @Resource(name = "pathologyWriteRpcServiceRenyiImpl")
    private PathologyMiddleBaseService pathologyMiddleBaseService;



    /**
     * 病理结果推送
     * @return
     */
    @PostMapping("/write/push")
    public Response<String> pushPathologyApplyMainResult(@RequestBody PushApplyMainRequest pushRequest) {
        return pathologyMiddleBaseService.pushPathologyApplyMainResult(pushRequest);
    }


    /**
     * 病理结果召回
     */
    @PostMapping("/write/recall")
    public Response<String> recallPathologyApplyMainResult(@RequestBody RecallApplyMainRequest recallRequest) {
        return pathologyMiddleBaseService.recallPathologyApplyMainResult(recallRequest);
    }

}
