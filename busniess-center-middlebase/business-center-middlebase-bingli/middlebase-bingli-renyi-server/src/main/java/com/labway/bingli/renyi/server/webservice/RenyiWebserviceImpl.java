package com.labway.bingli.renyi.server.webservice;

import lombok.extern.slf4j.Slf4j;
import org.apache.axis.client.Call;
import org.apache.axis.client.Service;
import org.apache.axis.encoding.XMLType;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.xml.namespace.QName;
import javax.xml.rpc.ParameterMode;
import javax.xml.rpc.ServiceException;
import java.rmi.RemoteException;

@Slf4j
@Component
public class RenyiWebserviceImpl implements RenyiWebservice {

    @Value("${renyi.push-result.url}")
    private String url;

    @Override
    public String SavePacsInfo(String requestStr) throws ServiceException, RemoteException {
        return doWsRequest("SavePacsInfo", "jsonParma", requestStr);
    }

    /**
     * 发起webservice请求
     */
    protected String doWsRequest(String methodName, String paramName, String requestParam) throws ServiceException, RemoteException {

        log.info("[Renyi His Webservice doWsRequest] methodName = [{}], requestParam = \n{}", methodName, requestParam);

        //获取webservice接口地址
        //获取域名地址，server定义的，一般默认是http://tempuri.org/，如果不对，找对方索要
        String soapaction = "http://tempuri.org/";
        Service service = new Service();

        Call call = (Call) service.createCall();
        call.setTargetEndpointAddress(url);
        //设置要调用的方法
        call.setOperationName(new QName(soapaction, methodName));
        // 设置要传递的参数,字符串用XMLType.XSD_STRING，日期用XMLType.XSD_DATE(根据对方要求)
        call.addParameter(new QName(soapaction, paramName), XMLType.XSD_STRING, ParameterMode.IN);
        //设置要返回的数据类型（标准的类型）
        call.setReturnType(XMLType.XSD_STRING);
        call.setUseSOAPAction(true);
        call.setSOAPActionURI(soapaction + methodName);
        //调用方法并传递参数
        String response = (String) call.invoke(new Object[]{requestParam});

        log.info("[Renyi His Webservice doWsRequest] methodName = [{}], response = \n{}", methodName, response);
        return response;
    }
}
