package com.labway.middlebase.bingli.api.dto;

import lombok.Data;

import java.io.Serializable;

@Data
public class PisResultFrozenDto implements Serializable {
    /**
     * f_id
     */
    private String fId;

    /**
     * 结果主键
     */
    private String resultId;

    /**
     * 病理号
     */
    private String fBlh;

    /**
     * 条码号
     */
    private String barcode;

    /**
     * 冰冻报告序号
     */
    private Integer fBdBgxh;

    /**
     * 冰冻报告医生
     */
    private String fBdBgys;

    /**
     * 冰冻审核医生
     */
    private String fBdShys;

    /**
     * 冰冻报告日期
     */
    private String fBdBgrq;

    /**
     * 冰冻报告状态
     */
    private String fBdBgzt;

    /**
     * 冰冻诊断
     */
    private String fBdzd;

    /**
     * 冰冻制片人
     */
    private String fBdZpr;

    /**
     * 冰冻报告收到时间
     */
    private String fBdSdrq;

    /**
     * 冰冻取材医生
     */
    private String fBdQcys;

    /**
     *  冰冻报告迟发原因
     */
    private String fBdCfyy;

    /**
     * 冰冻制片数
     */
    private String fBdZps;

    /**
     * 冰冻备注
     */
    private String fBdBz;

    /**
     * 冰冻复诊医生
     */
    private String fBdFzys;

    /**
     * 冰冻打印状态
     */
    private String fBdDyzt;

    /**
     * 冰冻签收本打印状态
     */
    private String fBdQsbDyzt;

    /**
     * 冰冻报告位置
     */
    private String fBdBgwz;

    /**
     * 冰冻报告位置确认时间
     */
    private String fBdBgwzQrsj;

    /**
     * 冰冻报告位置操作员
     */
    private String fBdBgwzQrczy;

    /**
     * 冰冻制片时间
     */
    private String fZpsj;

    /**
     * 冰冻离体时间
     */
    private String fLtsj;

    /**
     * 制片开始时间
     */
    private String fZpkssj;

    /**
     * 冰冻报告发布时间
     */
    private String fBdFbsj;

    /**
     * 冰冻报告发布医生
     */
    private String fBdFbys;
}
