package com.labway.middlebase.bingli.base.utils;


import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;


@Component
public class BingliRedisUtils {

    @Resource
    RedisTemplate<String, Object> redisTemplate;

    public boolean setIfAbsent(String key,long time,TimeUnit unit) {
        try {
            return Boolean.TRUE.equals(redisTemplate.opsForValue().setIfAbsent(key, StringUtils.EMPTY, time, unit));
        } catch (Exception e) {
            return false;
        }
    }
}
