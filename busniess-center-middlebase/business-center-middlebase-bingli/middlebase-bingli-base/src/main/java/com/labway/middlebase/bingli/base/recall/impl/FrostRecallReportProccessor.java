 package com.labway.middlebase.bingli.base.recall.impl;

 import com.labway.middlebase.bingli.api.enums.BingliReportTypeEnum;
 import com.labway.middlebase.bingli.api.request.RecallApplyMainRequest;
 import com.labway.middlebase.bingli.base.persistence.entity.PisResultFrozen;
 import com.labway.middlebase.bingli.base.persistence.entity.PisResultFrozenBak;
 import com.labway.middlebase.bingli.base.persistence.mapper.PisResultFrozenBakMapper;
 import com.labway.middlebase.bingli.base.recall.RecallReportProccessor;
 import com.labway.middlebase.bingli.base.service.ResultFrozenService;
 import com.labway.middlebase.bingli.base.service.ResultMainService;
 import com.swak.frame.dto.Response;
 import com.swak.frame.util.GetterUtil;
 import org.springframework.beans.BeanUtils;
 import org.springframework.beans.factory.annotation.Autowired;
 import org.springframework.stereotype.Component;
 import org.springframework.transaction.annotation.Transactional;

 import javax.annotation.Resource;
 import java.util.Objects;

/**
 * 冰冻检验报告处理器
 * <AUTHOR>
 * @date 2023/07/18
 */
@Component("frostRecallReportProccessor")
public class FrostRecallReportProccessor implements RecallReportProccessor {
    
    @Autowired
    private ResultFrozenService resultFrozenService;
    
    @Resource
    private ResultMainService resultMainService;
    
    @Resource
    private PisResultFrozenBakMapper pisResultFrozenBakMapper;
    
    @Autowired
    private PdfRecallReportProccessor pdfRecallReportProccessor;
    


    @Transactional(rollbackFor = Exception.class)
    @Override
    public Response<String> proccessReport(RecallApplyMainRequest request) {
        PisResultFrozen dbFrozen = resultFrozenService.getFrozenByBarcode(request.getBarcode(), request.getFBlh(), GetterUtil.getInteger(request.getFBgxh()));
        if (Objects.nonNull(dbFrozen)) {
           PisResultFrozenBak resultFrozenBak = new PisResultFrozenBak();
            BeanUtils.copyProperties(dbFrozen, resultFrozenBak);
            pisResultFrozenBakMapper.insert(resultFrozenBak);
            resultFrozenService.deleteFrozenByBarcode(request.getBarcode(), request.getFBlh(), GetterUtil.getInteger(request.getFBgxh()));
            pdfRecallReportProccessor.updateReportFileStatus(dbFrozen.getResultId(), BingliReportTypeEnum.FROZEN.getName(), request.getFBgxh());
        }
       
        return Response.success();
    }

}
