 package com.labway.middlebase.bingli.base.recall.impl;


 import com.labway.middlebase.bingli.api.enums.BingliReportTypeEnum;
 import com.labway.middlebase.bingli.api.request.RecallApplyMainRequest;
 import com.labway.middlebase.bingli.base.converter.ResultMainConverter;
 import com.labway.middlebase.bingli.base.persistence.entity.PisResultMain;
 import com.labway.middlebase.bingli.base.persistence.entity.PisResultMainBak;
 import com.labway.middlebase.bingli.base.persistence.mapper.PisResultMainBakMapper;
 import com.labway.middlebase.bingli.base.recall.RecallReportProccessor;
 import com.labway.middlebase.bingli.base.service.ResultMainService;
 import com.swak.frame.dto.Response;
 import org.springframework.beans.BeanUtils;
 import org.springframework.beans.factory.annotation.Autowired;
 import org.springframework.stereotype.Component;
 import org.springframework.transaction.annotation.Transactional;

 import javax.annotation.Resource;
 import java.util.Objects;

 /**
 * 普通检验报告处理器
 * <AUTHOR>
 * @date 2023/07/18
 */
@Component("normalRecallReportProccessor")
public class NormalRecallReportProccessor implements RecallReportProccessor {
    
    @Resource
    private ResultMainService resultMainService;
    
    @Autowired
    private ResultMainConverter resultMainConverter;
    
    @Autowired
    private PdfRecallReportProccessor pdfRecallReportProccessor;
    
    @Resource
    private PisResultMainBakMapper pisResultMainBakMapper;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Response<String> proccessReport(RecallApplyMainRequest request) {
        PisResultMain main = resultMainService.selectByBarcode(request.getFBlh(), request.getBarcode());
        if (Objects.nonNull(main)) {
            PisResultMainBak mainBak = new PisResultMainBak();
            BeanUtils.copyProperties(main, mainBak);
            pisResultMainBakMapper.insert(mainBak);
            resultMainService.deleteResultMainByBlhAndBarcode(request.getFBlh(), request.getBarcode());
            pdfRecallReportProccessor.updateReportFileStatus(main.getResultId(), BingliReportTypeEnum.NORMAL.getName(), request.getFBgxh());
         }

        return Response.success();
    }
    
  

}
