package com.labway.middlebase.bingli.base.push.impl;

import cn.hutool.core.codec.Base64;
import com.labway.middlebase.bingli.api.enums.ReportTypeEnum;
import com.labway.middlebase.bingli.api.request.PushApplyMainRequest;
import com.labway.middlebase.bingli.base.config.PathologyBusinessConfig;
import com.labway.middlebase.bingli.base.persistence.entity.LisReportFile;
import com.labway.middlebase.bingli.base.persistence.params.ReportPdfParams;
import com.labway.middlebase.bingli.base.service.ReportFileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Locale;
import java.util.Objects;
import java.util.UUID;

/**
 * 报告单处理
 *
 * <AUTHOR>
 * @date 2023/07/24
 */
@Component
public class PdfPushReportProccessor {

    @Resource
    private ReportFileService reportFileService;
    @Resource
    private PathologyBusinessConfig pathologyBusinessConfig;

    public void saveOrUpdateReportPdf(ReportPdfParams params) {
        String resultId = params.getResultId();
        String type = params.getReportKind();
        String index = String.valueOf(params.getRequest().getFBgxh());
        PushApplyMainRequest request = params.getRequest();
        LisReportFile reportFile = reportFileService.getFilesByResultIdAndTypeAndIndex(resultId, type, index);
        if (Objects.nonNull(reportFile)) {
            reportFile.setId(null);
            fileProperty(resultId, type, index, request, reportFile);
            reportFileService.updateReportFile(reportFile);
        } else {
            reportFile = new LisReportFile();
            reportFile.setId(UUID.randomUUID().toString());
            fileProperty(resultId, type, index, request, reportFile);
            reportFileService.save(reportFile);
        }
    }

    /**
     * 补充其它字段
     */
    private void fileProperty(String resultId, String type, String index, PushApplyMainRequest request, LisReportFile reportFile) {
        reportFile.setFileName(UUID.randomUUID().toString() + "." + request.getFileType());
        reportFile.setOutKey(resultId);
        reportFile.setBarcode(request.getBarcode());
        reportFile.setOutBarcode(request.getBarcode());
        reportFile.setFileType(request.getFileType() != null ? request.getFileType().toUpperCase(Locale.ROOT) : request.getFileType());
        byte[] bytes = Base64.decode(request.getFileBlob());
        reportFile.setFileBlob(bytes);
        reportFile.setIsEnable(1);
        reportFile.setOperType(1);
        reportFile.setReportType(ReportTypeEnum.PIS.getCode());
        reportFile.setOrgId(pathologyBusinessConfig.getOtherOrgCode(request.getHspOrgCode()));
        reportFile.setZdy2(request.getFBlh());
        reportFile.setZdy3(type);
        reportFile.setZdy5(index);
    }

}
