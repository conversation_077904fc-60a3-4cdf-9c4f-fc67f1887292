package com.labway.middlebase.bingli.base.push.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.middlebase.bingli.api.enums.BingliReportTypeEnum;
import com.labway.middlebase.bingli.api.request.PushApplyMainRequest;
import com.labway.middlebase.bingli.base.converter.ResultFrozenConverter;
import com.labway.middlebase.bingli.base.persistence.entity.PisResultFrozen;
import com.labway.middlebase.bingli.base.persistence.entity.PisResultMain;
import com.labway.middlebase.bingli.base.persistence.params.ReportPdfParams;
import com.labway.middlebase.bingli.base.push.PushReportProccessor;
import com.labway.middlebase.bingli.base.service.ResultFrozenService;
import com.labway.middlebase.bingli.base.service.ResultMainService;
import com.labway.middlebase.bingli.base.utils.BingLiUtils;
import com.swak.frame.dto.Response;
import com.swak.frame.util.UUIDHexGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 冰冻检验报告处理器
 *
 * <AUTHOR>
 * @date 2023/07/18
 */
@Component("frostPushReportProccessor")
public class FrostPushReportProccessor implements PushReportProccessor {

    @Autowired
    private ResultFrozenService resultFrozenService;

    @Resource
    private ResultFrozenConverter resultFrozenConverter;

    @Autowired
    private PdfPushReportProccessor pdfPushReportProccessor;

    @Resource
    private ResultMainService resultMainService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Response<String> proccessReport(PushApplyMainRequest request) {

        String resultId = BingLiUtils.makeResultId(request.getFBlh(), request.getBarcode());

        PisResultFrozen resultFrozen = resultFrozenConverter.convertToResultFrozen(request);
        PisResultFrozen dbFrozen = resultFrozenService.getFrozenByBarcode(request.getBarcode(), request.getFBlh(), request.getFBgxh());
        if (Objects.nonNull(dbFrozen)) {
            resultFrozen.setResultId(resultId);
            resultFrozen.setFId(dbFrozen.getFId());
            resultFrozenService.update(dbFrozen, Wrappers.lambdaUpdate(PisResultFrozen.class)
                    .eq(PisResultFrozen::getFId, dbFrozen.getFId()));
        } else {
            resultFrozen.setResultId(resultId);
            resultFrozen.setFId(UUIDHexGenerator.generator());
            resultFrozenService.save(resultFrozen);
        }

        // 更新主表upload_flag
        resultMainService.update(Wrappers.lambdaUpdate(PisResultMain.class)
                .set(PisResultMain::getUploadFlag, 1)
                .eq(PisResultMain::getResultId, resultId));

        ReportPdfParams.ReportPdfParamsBuilder builder = ReportPdfParams.builder()
                .reportKind(BingliReportTypeEnum.FROZEN.getName())
                .resultId(resultFrozen.getResultId())
                .request(request);
        pdfPushReportProccessor.saveOrUpdateReportPdf(builder.build());

        return Response.success();
    }

}
