server:
  port: 9701
  compression:
    enabled: true #启用数据压缩

spring:
  application:
    name: middlebase-bingli-shenhe

  profiles:
    active: dev

  cloud:
    nacos:
      server-addr: ${nacos.address}
      config:
        server-addr: ${nacos.address}
        namespace: ${nacos.config-namespace}
        file-extension: yaml
        shared-configs:
          - data-id: dubbo.yaml
            group: public
            refresh: true
          - data-id: business-center-common.yaml
            group: public
            refresh: true
      discovery:
        server-addr: ${nacos.address}
        namespace: ${nacos.discovery-namespace}

---
spring:
  profiles: dev

nacos:
  address: ***********:8848
  config-namespace: ${spring.profiles.active}
  discovery-namespace: ${spring.profiles.active}


---
spring:
  profiles: test

nacos:
  address: ***********:8848
  config-namespace: ${spring.profiles.active}
  discovery-namespace: ${spring.profiles.active}

---
spring:
  profiles: uat

nacos:
  address: ***********:8848
  config-namespace: ${spring.profiles.active}
  discovery-namespace: ${spring.profiles.active}

---
spring:
  profiles: prod

nacos:
  address: ***********:8848
  config-namespace: ${spring.profiles.active}
  discovery-namespace: ${spring.profiles.active}
