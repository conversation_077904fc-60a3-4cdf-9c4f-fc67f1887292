package com.labway.busniess.center.dongguan.middlebase.jianyan.utils;

import lombok.extern.slf4j.Slf4j;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Marshaller;
import javax.xml.bind.Unmarshaller;
import java.io.StringReader;
import java.io.StringWriter;

/**
 * <AUTHOR>
 */
@Slf4j
public class XmlUtil {


//    /**
//     * 格式化XML字符串
//     */
//    public static String formatXml(String str) throws Exception {
//        Document document;
//        document = DocumentHelper.parseText(str);
//        // 格式化输出格式
//        OutputFormat format = OutputFormat.createPrettyPrint();
//        // 设置xml的输出编码
//        format.setEncoding(ConstantDefault.UTF_8);
//        StringWriter writer = new StringWriter();
//        // 格式化输出流
//        XMLWriter xmlWriter = new XMLWriter(writer, format);
//        // 将document写入到输出流
//        xmlWriter.write(document);
//        xmlWriter.close();
//        return writer.toString();
//    }

    /**
     * 将返回数据转换为XML格式(格式化、不省略头信息)
     */
    public static String responseToXml(Object obj) {
        try {
            JAXBContext context = JAXBContext.newInstance(obj.getClass());

            Marshaller marshaller = context.createMarshaller();
            // //编码格式
            marshaller.setProperty(Marshaller.JAXB_ENCODING, "UTF-8");
            // 是否格式化生成的xml串
            marshaller.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT, true);
            // 是否省略xm头声明信息
            marshaller.setProperty(Marshaller.JAXB_FRAGMENT, false);
            StringWriter writer = new StringWriter();
            marshaller.marshal(obj, writer);
            return writer.toString();
        } catch (Exception ex) {
            throw new RuntimeException(ex);
        }
    }

    /**
     * 将返回数据转换为XML格式(不格式化、省略头信息)
     */
    public static String responseToNoHeadXml(Object obj) {
        try {
            JAXBContext context = JAXBContext.newInstance(obj.getClass());

            Marshaller marshaller = context.createMarshaller();
            //编码格式
            marshaller.setProperty(Marshaller.JAXB_ENCODING, "UTF-8");
            // 是否格式化生成的xml串
            marshaller.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT, false);
            // 是否省略xm头声明信息
            marshaller.setProperty(Marshaller.JAXB_FRAGMENT, true);
            StringWriter writer = new StringWriter();
            marshaller.marshal(obj, writer);
            return writer.toString();
        } catch (Exception ex) {
            throw new RuntimeException(ex);
        }
    }


    /**
     * xml 转 对象
     */
    public static <T> T xml2Bean(String xmlStr, Class<T> clazz) {
        T obj = null;
        try {
            JAXBContext jaxbContext = JAXBContext.newInstance(clazz);
            Unmarshaller unmarshaller = jaxbContext.createUnmarshaller();
            obj = (T) unmarshaller.unmarshal(new StringReader(xmlStr));
        } catch (JAXBException ex) {
            log.error("xml 转 对象异常", ex);
        }
        return obj;
    }



}
