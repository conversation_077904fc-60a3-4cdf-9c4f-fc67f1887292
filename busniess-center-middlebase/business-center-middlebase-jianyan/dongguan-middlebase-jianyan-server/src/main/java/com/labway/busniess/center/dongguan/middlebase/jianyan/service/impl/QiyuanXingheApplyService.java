package com.labway.busniess.center.dongguan.middlebase.jianyan.service.impl;

import com.labway.busniess.center.dongguan.middlebase.jianyan.service.AbstractXingheApplyService;
import com.labway.busniess.center.middlebase.jianyan.reqeust.QueryApplyRequest;
import com.labway.busniess.center.middlebase.jianyan.utils.XmlUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.w3c.dom.Document;
import org.w3c.dom.Element;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <pre>
 * QiyuanXingheApplyService
 * 杏和 - 七院接口
 * </pre>
 *
 * <AUTHOR>
 * @since 2024/7/1 11:29
 */
@Slf4j
@Service
@RefreshScope
public class QiyuanXingheApplyService extends AbstractXingheApplyService {

    @Value("${hsp.qiyuan.hsp-org-codes:}")
    private List<String> hspOrgCodes;

    @Value("${hsp.qiyuan.ws-url:}")
    private String wsUrl;

    @Value("${hsp.qiyuan.ticket:04e0ef69f2645c8f41aca8d4da7767e3}")
    private String ticket;

    @Override
    public Set<String> getHspOrgCodes() {
        return new HashSet<>(hspOrgCodes);
    }

    public static void main(String[] args) throws Exception {
        String methodName = "SelectAreaReqInfo";
        String data = "<DATA><ITEM HOSPITAL_ID=\"44A003\" HOSPITAL_SERVICE=\"42A010\" REQUISITION_ID=\"100093916000\"></ITEM></DATA>";

        System.out.println("data = " + data);
        SelectAreaReqInfo selectAreaReqInfo = XmlUtils.xml2Bean(data, SelectAreaReqInfo.class);
        System.out.println(selectAreaReqInfo);

        // 记录时间：2024-07-01 14:21:26,287
        // 描述：入参:HeaderXml: [<Root><MethodCode>SelectAreaReqInfo</MethodCode><Ticket>04e0ef69f2645c8f41aca8d4da7767e3</Ticket></Root>]
        //   BodyXml [ <DATA><ITEM HOSPITAL_ID='44S001' HOSPITAL_SERVICE='44S002' REQUISITION_ID='024000029500' ></ITEM></DATA> ]
        SelectAreaReqInfo selectAreaReqInfo1 = new SelectAreaReqInfo();
        selectAreaReqInfo1.setItem(new SelectAreaReqItem("44S001", "44S002", "024000029500"));
        data = XmlUtils.responseToNoHeadXml(selectAreaReqInfo1);
        System.out.println("data = " + data);

        QueryApplyRequest request = new QueryApplyRequest();
        request.setBarcode("024000029500");
        request.setHspOrgCode("44S001");
        request.setSignOrgCode("44S002");
        new QiyuanXingheApplyService().get(request);

    }

    @Override
    public String getUrl() {
        return this.wsUrl;
    }

    @Override
    public String getTicket() {
        return this.ticket;
    }

    @Override
    public String extractResponseResult(Document document, String methodName) {
        return ((Element) document.getDocumentElement().getElementsByTagName("CallAreaInterface" + "Response")
                .item(0)).getElementsByTagName("CallAreaInterface" + "Result").item(0).getTextContent();
    }

}
