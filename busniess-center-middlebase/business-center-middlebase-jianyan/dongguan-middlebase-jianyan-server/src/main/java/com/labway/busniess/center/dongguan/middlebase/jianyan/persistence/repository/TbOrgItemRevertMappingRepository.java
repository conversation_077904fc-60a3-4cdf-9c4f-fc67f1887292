package com.labway.busniess.center.dongguan.middlebase.jianyan.persistence.repository;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.busniess.center.dongguan.middlebase.jianyan.persistence.entity.TbOrgItemRevertMapping;
import com.labway.busniess.center.dongguan.middlebase.jianyan.persistence.mapper.TbOrgItemRevertMappingMapper;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public class TbOrgItemRevertMappingRepository {

    @Resource
    private TbOrgItemRevertMappingMapper tbOrgItemRevertMappingMapper;


    /**
     *
     * @param customerCode 送检客商
     * @param orgCode 检验机构
     * @param type 项目类型 1报告项目2细菌2药物
     * @return
     */
    public List<TbOrgItemRevertMapping> queryMappingByType(String customerCode, String orgCode, int type) {

        return tbOrgItemRevertMappingMapper.selectList(Wrappers.lambdaQuery(TbOrgItemRevertMapping.class)
                .eq(TbOrgItemRevertMapping::getCustomerCode,customerCode)
                .eq(TbOrgItemRevertMapping::getOrgCode,orgCode)
                .eq(TbOrgItemRevertMapping::getMappingType,type)
                .eq(TbOrgItemRevertMapping::getDeleteFlag,0));
    }

}
