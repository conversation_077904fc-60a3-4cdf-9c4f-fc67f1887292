package com.labway.busniess.center.dongguan.middlebase.jianyan.service.impl;

import com.alibaba.fastjson.JSON;
import com.labway.busniess.center.dongguan.middlebase.jianyan.service.MiddleBaseService;
import com.zaxxer.hikari.HikariDataSource;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.LinkedHashSet;
import java.util.Set;

/**
 * 社区LIS
 */
@Slf4j
@Service
class ShequApplyService extends MiddleBaseService {
    @Resource
    private ShequConfig shequConfig;

    @Override
    public Set<String> getHspOrgCodes() {
        return shequConfig.getOrgs();
    }


    @Override
    protected HikariDataSource getHikariDataSource() {

        log.info("社区配置 {}", JSON.toJSONString(shequConfig));

        final HikariDataSource dataSource = new HikariDataSource();
        dataSource.setDriverClassName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
        dataSource.setJdbcUrl(String.format("************************************************",
                shequConfig.getHost(), shequConfig.getPort(), shequConfig.getDatabase()));
        dataSource.setConnectionTestQuery("select 1");
        dataSource.setUsername(shequConfig.getUsername());
        dataSource.setPassword(shequConfig.getPassword());

        dataSource.setMinimumIdle(0);
        dataSource.setMaximumPoolSize(10);

        return dataSource;
    }

    @Getter
    @Setter
    @Configuration
    @ConfigurationProperties(prefix = "hsp.shequ")
    static class ShequConfig {
        private Set<String> orgs = new LinkedHashSet<>();

        private String host;

        private int port;

        private String username;

        private String password;

        private String database;
    }
}
