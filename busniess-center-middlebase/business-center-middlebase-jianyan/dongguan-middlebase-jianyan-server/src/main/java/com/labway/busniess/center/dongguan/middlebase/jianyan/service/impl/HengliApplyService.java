package com.labway.busniess.center.dongguan.middlebase.jianyan.service.impl;

import com.labway.busniess.center.dongguan.middlebase.jianyan.service.MiddleBaseService;
import com.zaxxer.hikari.HikariDataSource;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * 横沥医院
 */
@Service
class HengliApplyService extends MiddleBaseService {


    @Value("${hsp.hengli.org-code}")
    private List<String> orgCodes;
    @Override
    public Set<String> getHspOrgCodes() {
        return new HashSet<>(orgCodes);
    }

    @Override
    protected HikariDataSource getHikariDataSource() {
        final String host = environment.getProperty("hsp.hengli.host");
        final int port = Objects.requireNonNull(environment.getProperty("hsp.hengli.port", int.class),
                "hsp.hengli.port");
        final String username = environment.getProperty("hsp.hengli.username");
        final String password = environment.getProperty("hsp.hengli.password");
        final String database = environment.getProperty("hsp.hengli.database");

        final HikariDataSource dataSource = new HikariDataSource();
        dataSource.setDriverClassName("oracle.jdbc.OracleDriver");
        dataSource.setJdbcUrl(String.format("**************************",
                host, port, database));
//        dataSource.setConnectionTestQuery("select 1");
        dataSource.setUsername(username);
        dataSource.setPassword(password);

        dataSource.setMinimumIdle(0);
        dataSource.setMaximumPoolSize(10);

        return dataSource;
    }
}
