//package com.labway.busniess.center.dongguan.middlebase.jianyan.service.impl;
//
//import cn.hutool.core.date.StopWatch;
//import cn.hutool.core.util.XmlUtil;
//import cn.hutool.http.HttpRequest;
//import cn.hutool.http.HttpResponse;
//import cn.hutool.http.HttpStatus;
//import cn.hutool.http.HttpUtil;
//import com.labway.business.center.compare.dto.OrgApplySampleSendDetailDTO;
//import com.labway.business.center.compare.dto.OutApplyInfoDTO;
//import com.labway.business.center.compare.dto.middlebase.OrgApplySampleMainDTO;
//import com.labway.business.center.compare.service.TbOrgApplyFormService;
//import com.labway.busniess.center.dongguan.middlebase.jianyan.service.ApplyService;
//import com.labway.busniess.center.middlebase.jianyan.reqeust.LisResultRequest;
//import com.labway.busniess.center.middlebase.jianyan.reqeust.LisTestMainRequest;
//import com.labway.busniess.center.middlebase.jianyan.reqeust.QueryApplyRequest;
//import com.labway.busniess.center.middlebase.jianyan.reqeust.SignApplyInfoRequest;
//import com.labway.busniess.center.middlebase.jianyan.reqeust.SignCancelApplyInfoRequest;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections4.CollectionUtils;
//import org.apache.commons.lang3.math.NumberUtils;
//import org.apache.commons.lang3.time.DateFormatUtils;
//import org.apache.commons.lang3.time.DateUtils;
//import org.apache.dubbo.config.annotation.DubboReference;
//import org.springframework.beans.factory.annotation.Value;
//import org.w3c.dom.Document;
//import org.w3c.dom.Element;
//
//import java.net.SocketTimeoutException;
//import java.util.Date;
//import java.util.LinkedList;
//import java.util.List;
//import java.util.Objects;
//import java.util.Set;
//import java.util.stream.Collectors;
//
///**
// * 人民医院
// */
//@Slf4j
//class RenminApplyService implements ApplyService {
//
//    @Value("hsp.changan.url")
//    private String url;
//
//    @DubboReference
//    private TbOrgApplyFormService tbOrgApplyFormService;
//
//    @Override
//    public OutApplyInfoDTO get(QueryApplyRequest request) throws Exception {
//
//        final OutApplyInfoDTO apply = new OutApplyInfoDTO();
//
//        // 获取条码人员信息
//        {
//            final String text = doWsRequest("GetLisRequest", String.format("<hospSampleID>%s</hospSampleID>",
//                    request.getBarcode()));
//            final Element root = XmlUtil.getRootElement(XmlUtil.parseXml(text));
//            final Element item = XmlUtil.getElement(root, "Item");
//            if (Objects.isNull(item)) {
//                throw new IllegalStateException(String.format("条码 [%s] 不存在", request.getBarcode()));
//            }
//
//            apply.setBarcode(XmlUtil.getElement(item, "lis_Barcode").getTextContent());
//            apply.setPatientCard(XmlUtil.getElement(item, "pat_id").getTextContent());
//            apply.setPatientVisitCard(XmlUtil.getElement(item, "pat_id").getTextContent());
//            apply.setPatientName(XmlUtil.getElement(item, "pat_name").getTextContent());
//            apply.setPatientBed(XmlUtil.getElement(item, "pat_bedNo").getTextContent());
//            apply.setSamplingDate(DateUtils.parseDate(XmlUtil.getElement(item, "blood_time").getTextContent(),
//                    "yyyy-MM-dd HH:mm:ss"));
//            final String sex = XmlUtil.getElement(item, "pat_sex").getTextContent();
//            apply.setPatientSex(0);
//            if (Objects.equals(sex, "1")) {
//                apply.setPatientSex(1);
//            } else if (Objects.equals(sex, "0")) {
//                apply.setPatientSex(2);
//            }
//            apply.setPatientBirthday(DateUtils.parseDate(XmlUtil.getElement(item, "pat_birthday").getTextContent(),
//                    "yyyy-MM-dd HH:mm:ss"));
//            apply.setPatientAge(NumberUtils.toInt(XmlUtil.getElement(item, "pat_age").getTextContent()));
//            apply.setPatientSubageUnit(XmlUtil.getElement(item, "pat_ageunit").getTextContent());
//            apply.setPatientMobile(XmlUtil.getElement(item, "pat_tel").getTextContent());
//            apply.setDept(XmlUtil.getElement(item, "dept_name").getTextContent());
//            apply.setSendDoctor(XmlUtil.getElement(item, "doctor_name").getTextContent());
//            apply.setClinicalDiagnosis(XmlUtil.getElement(item, "clinical_diag").getTextContent());
//        }
//
//        apply.setItems(new LinkedList<>());
//
//        // 获取条码检验项目
//        {
//            final String text = doWsRequest("GetLisItems", String.format("<hospSampleID>%s</hospSampleID>",
//                    request.getBarcode()));
//            final Element root = XmlUtil.getRootElement(XmlUtil.parseXml(text));
//            final List<Element> items = XmlUtil.getElements(root, "Item");
//            if (CollectionUtils.isEmpty(items)) {
//                throw new IllegalStateException(String.format("条码 [%s] 检验项目为空", request.getBarcode()));
//            }
//
//            for (Element item : items) {
//                final OutApplyInfoDTO.OutApplyItem testItem = new OutApplyInfoDTO.OutApplyItem();
//                testItem.setOutTestItemCode(XmlUtil.getElement(item, "ItemCode").getTextContent());
//                testItem.setOutTestItemName(XmlUtil.getElement(item, "ItemName").getTextContent());
//                apply.getItems().add(testItem);
//            }
//        }
//
//        return apply;
//    }
//
//    /**
//     * 发起webservice请求
//     */
//    protected String doWsRequest(String methodName, String data) {
//
//        log.info("[Shanghai His Webservice doWsRequest] apiUrl [{}] methodName = [{}], request xml = \n{}", url, methodName, data);
//
//
//        final Document doc = XmlUtil.createXml("soap:Envelope");
//        final Element root = doc.getDocumentElement();
//        root.setAttribute("xmlns:xsi", "http://www.w3.org/2001/XMLSchema-instance");
//        root.setAttribute("xmlns:xsd", "http://www.w3.org/2001/XMLSchema");
//        root.setAttribute("xmlns:soap", "http://schemas.xmlsoap.org/soap/envelope/");
//
//        final Element body = XmlUtil.appendChild(root, "soap:Body");
//        final Element staticDataQuery = XmlUtil.appendChild(body, methodName);
//        staticDataQuery.setAttribute("xmlns", "http://tempuri.org/");
//        final Element inputData = XmlUtil.appendChild(staticDataQuery, "inputData");
//        inputData.appendChild(doc.createCDATASection(data));
//
//        final HttpRequest request = HttpUtil.createPost(url);
//        request.timeout(15000);
//        request.contentType("text/xml; charset=utf-8");
//        request.header("SOAPAction", "http://tempuri.org/" + methodName);
//
//        final String requestBody = XmlUtil.toStr(doc);
//
//        request.body(requestBody);
//
//        final StopWatch watch = new StopWatch();
//        watch.start();
//
//
//        try (HttpResponse response = request.execute()) {
//
//            final String responseBody = response.body();
//
//            watch.stop();
//
//            log.info("发起请求 URL [{}] 耗时 [{}ms] Body [{}] Status [{}] Response [{}]", url, watch.getTotalTimeMillis()
//                    , requestBody, response.getStatus(), responseBody);
//
//            final Document document = XmlUtil.parseXml(responseBody);
//
//            if (response.getStatus() != HttpStatus.HTTP_OK) {
//                throw new IllegalStateException(((Element) document.getDocumentElement().getElementsByTagName("soap:Fault")
//                        .item(0)).getElementsByTagName("faultstring").item(0).getTextContent());
//            }
//
//            return ((Element) document.getDocumentElement().getElementsByTagName(methodName + "Response")
//                    .item(0)).getElementsByTagName(methodName + "Result").item(0).getTextContent();
//
//        } catch (Exception e) {
//            log.error("发起请求 URL [{}] 耗时 [{}ms] Body [{}] 失败", url, watch.getTotalTimeMillis()
//                    , requestBody, e);
//
//            if (e.getCause() instanceof SocketTimeoutException) {
//                throw new IllegalStateException("调用HIS接口超时", e);
//            }
//
//            throw e;
//        }
//
//
//    }
//
//    @Override
//    public OrgApplySampleMainDTO sign(SignApplyInfoRequest request) throws Exception {
//        final String text = doWsRequest("AffirmRequest", String.format("<hospSampleID>%s</hospSampleID>",
//                request.getBarcode()));
//        return null;
//    }
//
//    @Override
//    public void cancelSign(SignCancelApplyInfoRequest request) throws Exception {
//    }
//
//    @Override
//    public Set<String> getHspOrgCodes() {
//        return Set.of();
//    }
//
//
//    @Override
//    public void notifyResult(LisTestMainRequest request) {
//
//        final OrgApplySampleSendDetailDTO apply = tbOrgApplyFormService.getOrgApplySampleSendDetail(request.getHspOrgCode(),
//                request.getOutBarcode()).getData();
//        if (Objects.isNull(apply)) {
//            throw new IllegalStateException(String.format("查询条码 [%s] 申请单信息失败",
//                    request.getOutBarcode()));
//        }
//
//        final Document doc = XmlUtil.createXml("Report_Result");
//        final Element reportInfo = doc.createElement("Report_Info");
//        XmlUtil.appendChild(reportInfo, "ext_lab_code").setTextContent("Labway");
//        XmlUtil.appendChild(reportInfo, "lis_Barcode").setTextContent(request.getOutBarcode());
//        XmlUtil.appendChild(reportInfo, "ext_Barcode").setTextContent(request.getBarcode());
//        XmlUtil.appendChild(reportInfo, "ext_checkItem").setTextContent(request.getItemType());
//        XmlUtil.appendChild(reportInfo, "pat_name").setTextContent(apply.getPatientInfo().getPatientName());
//        XmlUtil.appendChild(reportInfo, "pat_age").setTextContent(String.valueOf(apply.getPatientInfo().getPatientAge()));
//        XmlUtil.appendChild(reportInfo, "ext_receive_time").setTextContent(DateFormatUtils.format(request.getReceiveDate(),
//                "yyyy-MM-dd HH:mm:ss"));
//        XmlUtil.appendChild(reportInfo, "ext_check_time").setTextContent(DateFormatUtils.format(request.getTestTime(),
//                "yyyy-MM-dd HH:mm:ss"));
//        XmlUtil.appendChild(reportInfo, "ext_first_audit_time").setTextContent(DateFormatUtils.format(request.getOneCheckDate(),
//                "yyyy-MM-dd HH:mm:ss"));
//        XmlUtil.appendChild(reportInfo, "ext_second_audit_time").setTextContent(DateFormatUtils.format(request.getTwoCheckDate(),
//                "yyyy-MM-dd HH:mm:ss"));
//        XmlUtil.appendChild(reportInfo, "ext_upload_time").setTextContent(DateFormatUtils.format(new Date(),
//                "yyyy-MM-dd HH:mm:ss"));
//        XmlUtil.appendChild(reportInfo, "ext_checker").setTextContent(request.getTestUserName());
//        XmlUtil.appendChild(reportInfo, "ext_first_audit").setTextContent(request.getOneCheckUserName());
//        XmlUtil.appendChild(reportInfo, "ext_second_audit").setTextContent(request.getTwoCheckUserName());
//        XmlUtil.appendChild(reportInfo, "ext_lab_name").setTextContent(request.getTestUserName());
//        XmlUtil.appendChild(reportInfo, "ext_checker").setTextContent(request.getTestUserName());
//        XmlUtil.appendChild(reportInfo, "ext_lab_name").setTextContent("兰卫医学检验实验室");
//        XmlUtil.appendChild(reportInfo, "ext_checker").setTextContent(request.getTestUserName());
//
//        // 常规检验的结果
//        final List<LisResultRequest> results = request.getTestResults().stream()
//                .filter(e -> e instanceof LisResultRequest)
//                .map(e -> (LisResultRequest) e).collect(Collectors.toList());
//
//        for (int i = 0; i < results.size(); i++) {
//            final LisResultRequest result = results.get(i);
//            final Element resultInfo = doc.createElement("result_info");
//            XmlUtil.appendChild(resultInfo, "result_seq").setTextContent(String.valueOf(i + 1));
//            XmlUtil.appendChild(resultInfo, "ext_combine_name").setTextContent(result.getTestitemName());
//            XmlUtil.appendChild(resultInfo, "ext_combine_code").setTextContent(result.getTestitemCode());
//            XmlUtil.appendChild(resultInfo, "ext_item_name").setTextContent(result.getItemName());
//            XmlUtil.appendChild(resultInfo, "ext_item_code").setTextContent(result.getItemCode());
//            XmlUtil.appendChild(resultInfo, "result").appendChild(doc.createCDATASection(result.getResult()));
//            XmlUtil.appendChild(resultInfo, "result_unit").setTextContent(result.getUnit());
//            XmlUtil.appendChild(resultInfo, "result_reference").appendChild(doc.createCDATASection(result.getRange()));
//            XmlUtil.appendChild(resultInfo, "result_date").setTextContent(DateFormatUtils.format(result.getOperateDate(),
//                    "yyyy-MM-dd HH:mm:ss"));
//            XmlUtil.appendChild(resultInfo, "lis_combine_name").setTextContent(result.getOutTestitemName());
//            XmlUtil.appendChild(resultInfo, "lis_combine_code").setTextContent(result.getOutTestitemCode());
//        }
//
//        doWsRequest("UploadLisRepData", XmlUtil.toStr(doc));
//
//
//    }
//}
