package com.labway.busniess.center.dongguan.middlebase.jianyan.service.impl;

import com.labway.busniess.center.dongguan.middlebase.jianyan.service.MiddleBaseService;
import com.zaxxer.hikari.HikariDataSource;
import lombok.Getter;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * 莞城医院
 */
@Service
class GuanchengApplyService extends MiddleBaseService {

    @Value("${hsp.guancheng.org-code}")
    private List<String> hspOrgCodes;

    @Override
    public Set<String> getHspOrgCodes() {
        return new HashSet<>(hspOrgCodes);
    }
    @Override
    protected HikariDataSource getHikariDataSource() {
        final String host = environment.getProperty("hsp.guancheng.host");
        final int port = Objects.requireNonNull(environment.getProperty("hsp.guancheng.port", int.class),
                "hsp.guancheng.port");
        final String username = environment.getProperty("hsp.guancheng.username");
        final String password = environment.getProperty("hsp.guancheng.password");
        final String database = environment.getProperty("hsp.guancheng.database");
        final HikariDataSource dataSource = new HikariDataSource();
        dataSource.setDriverClassName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
        dataSource.setJdbcUrl(String.format("************************************************",
                host, port, database));
        dataSource.setConnectionTestQuery("select 1");
        dataSource.setUsername(username);
        dataSource.setPassword(password);
        dataSource.setMinimumIdle(0);
        dataSource.setMaximumPoolSize(10);
        return dataSource;
    }
}
