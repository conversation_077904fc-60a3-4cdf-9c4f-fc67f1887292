package com.labway.busniess.center.dongguan.middlebase.jianyan.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.busniess.center.dongguan.middlebase.jianyan.service.MiddleBaseService;
import com.labway.busniess.center.dongguan.middlebase.jianyan.service.XiaoZhenSuoApplyService;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSession;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/test")
public class TestController {

    @Resource
    private XiaoZhenSuoApplyService xiaoZhenSuoApplyService;

    @PostMapping("/testQuerySample")
    public String testQuerySample(@RequestParam String barcode){
        try (SqlSession session = xiaoZhenSuoApplyService.getSqlSessionFactory().openSession()) {
            final MiddleBaseService.EifLisApplyMainMapper applyMainMapper = session.getMapper(MiddleBaseService.EifLisApplyMainMapper.class);
            final MiddleBaseService.EifLisApplyDetailMapper detailMapper = session.getMapper(MiddleBaseService.EifLisApplyDetailMapper.class);
            final MiddleBaseService.EifLisApplyMain applyMain = applyMainMapper.selectOne(Wrappers.lambdaQuery(MiddleBaseService.EifLisApplyMain.class).eq(MiddleBaseService.EifLisApplyMain::getBarcode,barcode));
            return JSONObject.toJSONString(applyMain);
        }

    }


}
