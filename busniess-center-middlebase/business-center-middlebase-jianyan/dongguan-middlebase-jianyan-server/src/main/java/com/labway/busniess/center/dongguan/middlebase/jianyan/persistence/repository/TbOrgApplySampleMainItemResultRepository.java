package com.labway.busniess.center.dongguan.middlebase.jianyan.persistence.repository;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.busniess.center.dongguan.middlebase.jianyan.persistence.entity.TbOrgApplySampleMainItemResult;
import com.labway.busniess.center.dongguan.middlebase.jianyan.persistence.mapper.TbOrgApplySampleMainItemResultMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public class TbOrgApplySampleMainItemResultRepository {

    @Resource
    private TbOrgApplySampleMainItemResultMapper tbOrgApplySampleMainItemResultMapper;


    /**
     * 批量保存样本结果信息
     * @param resultList
     * @return
     */
    public Integer saveSampleResults(List<TbOrgApplySampleMainItemResult> resultList) {
        if (CollectionUtils.isEmpty(resultList)){
            return 0;
        }

        return tbOrgApplySampleMainItemResultMapper.insertBatch(resultList);
    }


    /**
     * 根据mainId删除旧的样本报告结果
     * @param mainId
     * @return
     */
    public Integer deleteByMainId(String mainId) {
        if (StringUtils.isBlank(mainId)){
            return 0;
        }

        return tbOrgApplySampleMainItemResultMapper.delete(Wrappers.lambdaQuery(TbOrgApplySampleMainItemResult.class)
                .eq(TbOrgApplySampleMainItemResult::getMainId,mainId));
    }

    /**
     * 根据mainId查询样本结果信息
     * @param mainIds
     * @return
     */
    public List<TbOrgApplySampleMainItemResult> querySampleResultInfo(List<String> mainIds) {
        if (CollectionUtils.isEmpty(mainIds)){
            return Collections.emptyList();
        }

        return tbOrgApplySampleMainItemResultMapper.selectList(Wrappers.lambdaQuery(TbOrgApplySampleMainItemResult.class)
                .in(TbOrgApplySampleMainItemResult::getMainId,mainIds)
                .eq(TbOrgApplySampleMainItemResult::getDeleteFlag,0));
    }



}
