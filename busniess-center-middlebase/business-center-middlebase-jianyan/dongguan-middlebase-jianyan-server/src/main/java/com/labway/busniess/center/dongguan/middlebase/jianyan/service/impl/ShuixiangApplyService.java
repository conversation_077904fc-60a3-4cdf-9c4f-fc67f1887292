package com.labway.busniess.center.dongguan.middlebase.jianyan.service.impl;

import com.labway.busniess.center.dongguan.middlebase.jianyan.service.MiddleBaseService;
import com.zaxxer.hikari.HikariDataSource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * 水乡医院
 */
@Service
class ShuixiangApplyService extends MiddleBaseService {

    @Value("${hsp.shuixiang.org-code}")
    private List<String> hspOrgCodes;

    @Override
    public Set<String> getHspOrgCodes() {
        return new HashSet<>(hspOrgCodes);
    }


    @Override
    protected HikariDataSource getHikariDataSource() {

        final String host = environment.getProperty("hsp.shuixiang.host");
        final int port = Objects.requireNonNull(environment.getProperty("hsp.shuixiang.port", int.class),
                "hsp.shuixiang.port");
        final String username = environment.getProperty("hsp.shuixiang.username");
        final String password = environment.getProperty("hsp.shuixiang.password");
        final String database = environment.getProperty("hsp.shuixiang.database");

        final HikariDataSource dataSource = new HikariDataSource();
        dataSource.setDriverClassName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
        dataSource.setJdbcUrl(String.format("************************************************",
                host, port, database));
        dataSource.setConnectionTestQuery("select 1");
        dataSource.setUsername(username);
        dataSource.setPassword(password);

        dataSource.setMinimumIdle(0);
        dataSource.setMaximumPoolSize(10);

        return dataSource;
    }
}
