package com.labway.busniess.center.dongguan.middlebase.jianyan.service;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.google.common.collect.Lists;
import com.labway.business.center.compare.dto.OutApplyInfoDTO;
import com.labway.business.center.compare.dto.middlebase.OrgApplySampleMainDTO;
import com.labway.business.center.compare.dto.middlebase.OrgApplySampleMainItemDTO;
import com.labway.business.center.core.enums.ApplySampleStatus;
import com.labway.business.center.core.enums.YesOrNoEnum;
import com.labway.business.center.core.util.PdfUtils;
import com.labway.busniess.center.dongguan.middlebase.jianyan.config.BusinessConfig;
import com.labway.busniess.center.dongguan.middlebase.jianyan.enums.AgeUnitEnum;
import com.labway.busniess.center.dongguan.middlebase.jianyan.persistence.entity.HuMenLisTestMain;
import com.labway.busniess.center.middlebase.jianyan.reqeust.LisResultRequest;
import com.labway.busniess.center.middlebase.jianyan.reqeust.LisTestMainRequest;
import com.labway.busniess.center.middlebase.jianyan.reqeust.QueryApplyRequest;
import com.labway.busniess.center.middlebase.jianyan.reqeust.SignApplyInfoRequest;
import com.labway.busniess.center.middlebase.jianyan.reqeust.SignCancelApplyInfoRequest;
import com.zaxxer.hikari.HikariDataSource;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.core.env.Environment;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 中间库的统一父类
 */
@Slf4j
@Getter
public abstract class HuMenMiddleBaseService implements ApplyService {

    @Resource
    protected BusinessConfig businessConfig;

    @Resource
    protected Environment environment;

    @Resource
    protected MybatisPlusProperties mybatisPlusProperties;

    @Override
    public OutApplyInfoDTO get(QueryApplyRequest request) throws Exception {
        return null;
    }

    @Override
    public OrgApplySampleMainDTO sign(SignApplyInfoRequest request) throws Exception {
        return null;
    }

    @Override
    public void cancelSign(SignCancelApplyInfoRequest request) throws Exception {

    }

    @Override
    public void notifyResult(LisTestMainRequest request) {

    }

    public OutApplyInfoDTO get(QueryApplyRequest request, SqlSessionFactory sqlSessionFactory) throws Exception {
        log.info("获取条码信息 {}", JSON.toJSONString(request));
        try (SqlSession session = sqlSessionFactory.openSession()) {
            EifLisApplyMainMapper applyMapper = session.getMapper(EifLisApplyMainMapper.class);
            EifLisApplyDetailMapper detailMapper = session.getMapper(EifLisApplyDetailMapper.class);
            // 获取申请单
            List<EifLisApplyMain> list = applyMapper.selectList(new LambdaQueryWrapper<EifLisApplyMain>()
                    .eq(EifLisApplyMain::getBarcode, request.getBarcode())
                    .eq(EifLisApplyMain::getHspOrgCode, getOtherCodeByLimsCode(request.getHspOrgCode())));
            if (CollectionUtils.isEmpty(list)) {
                throw new IllegalArgumentException(String.format("条码信息 [%s] 不存在", request.getBarcode()));
            }
            // 获取到检验项目
            final List<EifLisApplyDetail> details = detailMapper.selectList(new LambdaQueryWrapper<EifLisApplyDetail>()
                    .eq(EifLisApplyDetail::getBarcode, request.getBarcode())
                    .eq(EifLisApplyDetail::getHspOrgCode, getOtherCodeByLimsCode(request.getHspOrgCode())));
            if (CollectionUtils.isEmpty(details)) {
                throw new IllegalArgumentException(String.format("条码 [%s] 检验项目为空", request.getBarcode()));
            }
            final EifLisApplyMain apply = list.iterator().next();
            final OutApplyInfoDTO outApply = new OutApplyInfoDTO();
            outApply.setBarcode(apply.getBarcode());
            outApply.setHspOrgCode(transferLwHspOrgCode(apply.getHspOrgCode()));
            outApply.setHspOrgName(businessConfig.getOrgNameByCode(apply.getHspOrgCode()));
            outApply.setApplyType("其他");
            outApply.setPatientVisitCard(apply.getPatientNo());
            outApply.setUrgent(NumberUtils.toInt(apply.getIsUrgent()));
            outApply.setSampleType(apply.sampleType);
            outApply.setSampleProperty(apply.getSampleStatus());
            outApply.setDept(apply.getAppDept());
            outApply.setInpatientArea(apply.getInpatientArea());
            outApply.setPatientName(apply.getPatientName());
            outApply.setPatientSex(Objects.equals(apply.getSex(), "1") ? 1 : 2);
            transferAge(apply.getAge(), outApply);
            outApply.setPatientBirthday(apply.getBirthday());
            outApply.setPatientBed(apply.getBed());
            outApply.setClinicalDiagnosis(apply.getDiag());
            outApply.setPatientCard(apply.getPatientCard());
            outApply.setPatientCardType("");
            outApply.setPatientAddress(apply.getAddress());
            outApply.setPatientMobile(apply.getTel());
            outApply.setSendDoctor(apply.getAppUserName());
            outApply.setApplyDate(apply.getAppDate());
            outApply.setSamplingDate(apply.getBarprtDate());
            outApply.setRemark(apply.getMemo());
            outApply.setSampleNum(1);
            outApply.setOrigoutOrgcode(apply.getOrigoutOrgcode());
            outApply.setOrigoutOrgname(apply.getOrigoutOrgname());
            outApply.setItems(new LinkedList<>());
            for (EifLisApplyDetail detail : details) {
                final OutApplyInfoDTO.OutApplyItem item = new OutApplyInfoDTO.OutApplyItem();
                item.setOutTestItemCode(detail.getOutTestitemCode());
                item.setOutTestItemName(detail.getOutTestitemName());
                item.setItems(Lists.newArrayList());
                outApply.getItems().add(item);
            }
            return outApply;
        }
    }

    /**
     * 年龄处理
     *
     * @param age
     * @param outApply
     */
    protected void transferAge(String age, OutApplyInfoDTO outApply) {
        if (StringUtils.isBlank(age)) {
            outApply.setPatientAge(0);
            outApply.setPatientSubage(0);
            outApply.setPatientSubageUnit("");
        } else if (age.contains(AgeUnitEnum.YEAR.getUnit()) && age.contains(AgeUnitEnum.MONTH.getUnit())) {
            outApply.setPatientAge(Integer.valueOf(age.substring(0, age.indexOf(AgeUnitEnum.YEAR.getUnit()))));
            outApply.setPatientSubage(Integer.valueOf(age.substring(age.indexOf(AgeUnitEnum.YEAR.getUnit()) + 1, age.indexOf(AgeUnitEnum.MONTH.getUnit()))));
            outApply.setPatientSubageUnit(AgeUnitEnum.MONTH.getUnit());
        } else if (age.contains(AgeUnitEnum.YEAR.getUnit())) {
            outApply.setPatientAge(Integer.valueOf(age.replace(AgeUnitEnum.YEAR.getUnit(), "")));
            outApply.setPatientSubage(0);
            outApply.setPatientSubageUnit("");
        } else if (age.contains(AgeUnitEnum.MONTH.getUnit())) {
            outApply.setPatientAge(0);
            outApply.setPatientSubage(Integer.valueOf(age.replace(AgeUnitEnum.MONTH.getUnit(), "")));
            outApply.setPatientSubageUnit(AgeUnitEnum.MONTH.getUnit());
        }
    }

    /**
     * 根据兰卫机构编码获取三方机构编码
     * @param hspOrgCode
     * @return
     */
    protected String transferLwHspOrgCode(String hspOrgCode) {
        Optional<String> first = businessConfig.getOrgCodeMap().entrySet().stream()
                .filter(entry -> entry.getValue().equals(hspOrgCode))
                .map(Map.Entry::getKey).findFirst();
        return first.orElseThrow();
    }

    public OrgApplySampleMainDTO sign(SignApplyInfoRequest request, SqlSessionFactory sqlSessionFactory) throws Exception {
        try (SqlSession session = sqlSessionFactory.openSession()) {
            final EifLisApplyMainMapper applyMapper = session.getMapper(EifLisApplyMainMapper.class);
            final List<EifLisApplyMain> list = applyMapper.selectList(new LambdaQueryWrapper<EifLisApplyMain>()
                    .eq(EifLisApplyMain::getBarcode, request.getBarcode())
                    .eq(EifLisApplyMain::getHspOrgCode, getOtherCodeByLimsCode(request.getHspOrgCode())));
            if (CollectionUtils.isEmpty(list)) {
                throw new IllegalArgumentException(String.format("条码信息 [%s] 不存在", request.getBarcode()));
            }
            final EifLisApplyMain apply = list.iterator().next();
            if (Objects.equals(apply.getIsReceive(), 1)) {
                throw new IllegalArgumentException(String.format("条码 [%s] 已经签收", request.getBarcode()));
            }
            // 修改为已签收
            applyMapper.update(null, new LambdaUpdateWrapper<EifLisApplyMain>()
                    .set(EifLisApplyMain::getIsReceive, 1)
                    .set(EifLisApplyMain::getReceiveDate, new Date())
                    .eq(EifLisApplyMain::getBarcode, request.getBarcode())
                    .eq(EifLisApplyMain::getHspOrgCode, getOtherCodeByLimsCode(request.getHspOrgCode())));
            final QueryApplyRequest queryApplyRequest = new QueryApplyRequest();
            queryApplyRequest.setBarcode(request.getBarcode());
            queryApplyRequest.setHspOrgCode(request.getHspOrgCode());
            OutApplyInfoDTO outApplyInfoDTO = get(queryApplyRequest, sqlSessionFactory);
            return transferOutApplyInfoDTO(outApplyInfoDTO);
        }
    }

    private OrgApplySampleMainDTO transferOutApplyInfoDTO(OutApplyInfoDTO outApplyInfoDTO) {
        OrgApplySampleMainDTO.OrgApplySampleMainDTOBuilder builder = OrgApplySampleMainDTO.builder();
        builder.barcode(outApplyInfoDTO.getBarcode())
                .hspOrgCode(outApplyInfoDTO.getHspOrgCode())
                .hspOrgName(outApplyInfoDTO.getHspOrgName())
                .orgCode("")
                .orgName("")
                .applyType(outApplyInfoDTO.getApplyType())
                .patientVisitCard(outApplyInfoDTO.getPatientVisitCard())
                .urgent(outApplyInfoDTO.getUrgent())
                .sampleType(outApplyInfoDTO.getSampleType())
//                    .sampleProperty()
                .dept(outApplyInfoDTO.getDept())
                .inpatientArea(outApplyInfoDTO.getInpatientArea())
                .patientName(outApplyInfoDTO.getPatientName())
                .patientSex(outApplyInfoDTO.getPatientSex())
                .patientAge(outApplyInfoDTO.getPatientAge())
                .patientSubage(outApplyInfoDTO.getPatientSubage())
                .patientSubageUnit(outApplyInfoDTO.getPatientSubageUnit())
                .patientBirthday(outApplyInfoDTO.getPatientBirthday())
                .patientBed(outApplyInfoDTO.getPatientBed())
                .clinicalDiagnosis(outApplyInfoDTO.getClinicalDiagnosis())
                .patientCard(outApplyInfoDTO.getPatientCard())
                .patientCardType(outApplyInfoDTO.getPatientCardType())
                .patientAddress(outApplyInfoDTO.getPatientAddress())
                .patientMobile(outApplyInfoDTO.getPatientMobile())
                .sendDoctor(outApplyInfoDTO.getSendDoctor())
                .applyDate(null==outApplyInfoDTO.getApplyDate()?new Date():outApplyInfoDTO.getApplyDate())
                .samplingDate(outApplyInfoDTO.getSamplingDate())
                .remark(outApplyInfoDTO.getRemark())
                .status(ApplySampleStatus.UN_SIGN.getValue())
        ;
        OrgApplySampleMainDTO orgApplySampleMainDTO = builder.build();
        //送检项目
        List<OrgApplySampleMainItemDTO> sampleMainItems = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(outApplyInfoDTO.getItems())){
            for (OutApplyInfoDTO.OutApplyItem outApplyItem : outApplyInfoDTO.getItems()) {
                OrgApplySampleMainItemDTO item = new OrgApplySampleMainItemDTO();
                item.setBarcode(outApplyInfoDTO.getBarcode());
                item.setHspOrgCode(outApplyInfoDTO.getHspOrgCode());
                item.setHspOrgName(outApplyInfoDTO.getHspOrgName());
                item.setOutTestItemCode(outApplyItem.getOutTestItemCode());
                item.setOutTestItemName(outApplyItem.getOutTestItemName());
                sampleMainItems.add(item);
            }
        }
        orgApplySampleMainDTO.setSampleMainItems(sampleMainItems);
        return orgApplySampleMainDTO;
    }
    public void cancelSign(SignCancelApplyInfoRequest request, SqlSessionFactory sqlSessionFactory) throws Exception {
        try (SqlSession session = sqlSessionFactory.openSession()) {
            log.info("取消签收条码:{}",JSON.toJSONString(request.getBarcodes()));
            EifLisApplyMainMapper applyMapper = session.getMapper(EifLisApplyMainMapper.class);
            applyMapper.update(null, new LambdaUpdateWrapper<EifLisApplyMain>()
                    .set(EifLisApplyMain::getIsReceive, 0)
                    .set(EifLisApplyMain::getReceiveDate, null)
                    .in(EifLisApplyMain::getBarcode, request.getBarcodes())
                    .eq(EifLisApplyMain::getHspOrgCode, businessConfig.getOtherCodeByLimsCode(request.getSendOrgCode())));
        }
    }

    public void notifyResult(LisTestMainRequest request, SqlSessionFactory sqlSessionFactory) {
        try (SqlSession session = sqlSessionFactory.openSession()) {
            final LisResultMapper resultMapper = session.getMapper(LisResultMapper.class);
            final LisReportFileMapper reportFileMapper = session.getMapper(LisReportFileMapper.class);
            final LisTestMainMapper mainMapper = session.getMapper(LisTestMainMapper.class);
            // 常规检验的结果
            final List<LisResultRequest> results = detailTestResult(request);
            // 删除之前的
            final List<HuMenLisTestMain> oldMains = mainMapper.selectList(new LambdaQueryWrapper<HuMenLisTestMain>()
                    .eq(HuMenLisTestMain::getBarcode, request.getOutBarcode())
                    .eq(HuMenLisTestMain::getHspOrgCode, getOtherCodeByLimsCode(request.getHspOrgCode())));
            if (CollectionUtils.isNotEmpty(oldMains)) {
                final Set<String> testMainIds = oldMains.stream().map(HuMenLisTestMain::getTestMainId).collect(Collectors.toSet());
                mainMapper.delete(new LambdaQueryWrapper<HuMenLisTestMain>().in(HuMenLisTestMain::getTestMainId, testMainIds));
                reportFileMapper.delete(new LambdaQueryWrapper<LisReportFile>().eq(LisReportFile::getBarcode, request.getOutBarcode())
                        .eq(LisReportFile::getOrgId, getOtherCodeByLimsCode(request.getHspOrgCode())));
                resultMapper.delete(new LambdaQueryWrapper<LisResult>().in(LisResult::getTestMainId, testMainIds));
            }
            // test main
            final HuMenLisTestMain main = new HuMenLisTestMain();
            BeanUtils.copyProperties(request, main);
            main.setHspOrgCode(getOtherCodeByLimsCode(main.getHspOrgCode()));
            main.setTestMainId(IdUtil.simpleUUID());
            main.setBarcode(request.getOutBarcode());
            main.setOutBarcode(request.getBarcode());
            //结果出完1，没出完0
            main.setIsAll(request.getIsAll());
            mainMapper.insert(main);
            // report
            final LisReportFile file = new LisReportFile();
            file.setId(IdUtil.simpleUUID());
            file.setFileType("PDF");
            file.setOrgId(getOtherCodeByLimsCode(request.getHspOrgCode()));
            file.setReportType("1");
            file.setIsEnable(1);
            file.setPageKind(PdfUtils.getPdfInfo(request.getReportUrls().iterator().next()).getPageKind().getType());
            file.setOutKey(request.getOutBarcode()+"_"+request.getBarcode());
            file.setBarcode(request.getOutBarcode());
            file.setOutBarcode(request.getBarcode());
            file.setFileName(getFileNameByUrl(request.getReportUrls().iterator().next()));
            file.setFileBlob(pdfToByte(request.getReportUrls().iterator().next()));
            reportFileMapper.insert(file);
            // result
            if(YesOrNoEnum.YES.getCode().equals(request.getManualUploadPdf())){
                for (LisResultRequest result : results) {
                    HuMenMiddleBaseService.LisResult lr = new HuMenMiddleBaseService.LisResult();
                    BeanUtils.copyProperties(result, lr);
                    lr.setTestMainId(main.getTestMainId());
                    lr.setResult("报告详见PDF");
                    lr.setBarcode(request.getOutBarcode());
                    resultMapper.insert(lr);
                }
            }else{
                for (LisResultRequest result : results) {
                    HuMenMiddleBaseService.LisResult lr = new HuMenMiddleBaseService.LisResult();
                    BeanUtils.copyProperties(result, lr);
                    lr.setTestMainId(main.getTestMainId());
                    lr.setBarcode(request.getOutBarcode());
                    lr.setResult("");
                    resultMapper.insert(lr);
                }

            }
        } catch (Exception e) {
            log.error("虎口同步失败：", e);
        }
    }

    protected List<LisResultRequest> detailTestResult(LisTestMainRequest request) {
        List<LisResultRequest> results = new ArrayList<>();
        if (CollectionUtils.isEmpty(request.getTestResults())) {
            return results;
        }
        //通用格式转换
        JSONArray jsonArray = JSONArray.parseArray(JSON.toJSONString(request.getTestResults()));
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            LisResultRequest result = new LisResultRequest();
            result.setBarcode(jsonObject.getString("barcode"));
            result.setItemName(jsonObject.getString("itemName"));
            result.setItemCode(jsonObject.getString("itemCode"));
            result.setOperateDate(jsonObject.getDate("operateDate"));
            result.setMachineCode(jsonObject.getString("machineCode"));
            result.setUnit(jsonObject.getString("unit"));
            result.setFlag(jsonObject.getString("flag"));
            result.setSampleNo(jsonObject.getString("sampleNo"));
            result.setTestitemCode(jsonObject.getString("testitemCode"));
            result.setTestitemName(jsonObject.getString("testitemName"));
            result.setResult(jsonObject.getString("result"));
            result.setUnit(jsonObject.getString("unit"));
            result.setTestDate(jsonObject.getString("testDate"));
            result.setLabPackageId(jsonObject.getString("labPackageId"));
            result.setLabPackageName(jsonObject.getString("labPackageName"));
            result.setResultStatus(jsonObject.getInteger("resultStatus"));
            result.setIsPrint(jsonObject.getInteger("isPrint"));
            result.setOutTestitemCode(jsonObject.getString("outTestitemCode"));
            result.setOutTestitemName(jsonObject.getString("outTestitemName"));
            result.setUpValue(jsonObject.getString("upValue"));
            result.setDownValue(jsonObject.getString("downValue"));
            result.setMachineValue(jsonObject.getString("machineValue"));
            result.setRange(jsonObject.getString("range"));
            result.setResultEn(jsonObject.getString("resultEn"));
            result.setSpecialFormatResult(jsonObject.getString("specialFormatResult"));
            result.setMachinegroupCode(jsonObject.getString("machinegroupCode"));
            results.add(result);
        }
        return results;

    }

    protected byte[] pdfToByte(String link) throws IOException {
        URL url = new URL(link);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        return connection.getInputStream().readAllBytes();
    }

    /**
     * 获取文件名称
     *
     * @param url
     * @return
     */
    private String getFileNameByUrl(String url) {
        String name = "";
        if (StringUtils.isNotBlank(url)) {
            String[] split = url.split("/");
            name = split[split.length - 1] + ".pdf";
        }
        return name;
    }

    /**
     * 不同机构获取数据源方式不同
     */
    protected HikariDataSource getHikariDataSource() {
        final String host = environment.getProperty("hsp.middlebase.host");
        final int port = Objects.requireNonNull(environment.getProperty("hsp.middlebase.port", int.class),
                "hsp.middlebase.port");
        final String username = environment.getProperty("hsp.middlebase.username");
        final String password = environment.getProperty("hsp.middlebase.password");
        final String database = environment.getProperty("hsp.middlebase.database");
        final HikariDataSource dataSource = new HikariDataSource();
        dataSource.setDriverClassName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
        dataSource.setJdbcUrl(String.format("************************************************",
                host, port, database));
        dataSource.setConnectionTestQuery("select 1");
        dataSource.setUsername(username);
        dataSource.setPassword(password);
        dataSource.setMinimumIdle(0);
        dataSource.setMaximumPoolSize(10);
        return dataSource;
    }


    String getOtherCodeByLimsCode(String hspCode) {
        return businessConfig.getOtherCodeByLimsCode(hspCode);
    }

    @Getter
    @Setter
    @TableName("EIF_LIS_APPLY_DETAIL")
    public static class EifLisApplyDetail {
        private String barcode;

        private String outTestitemCode;

        private String outTestitemName;

        private String testitemCode;

        private String hspOrgCode;

        private String isFee;

        private Integer isForbidden;

        private Integer feeNum;

        private String property1;

        private String property2;

        private String property3;

        private String property4;

        private String property5;

        private Integer feeType;

        private Integer showNo;

        private String testitemName;

        private String lwHspOrgCode;

        private String lwBarcode;

        private String lwMainBarcode;

        private Date lwOperDate;

        private String lwTubeType;

        private String lwSampleType;

        private String lwCombBarCode;

        private String labPackageId;

        private String labPackageName;

    }


    @Getter
    @Setter
    @TableName("EIF_LIS_APPLY_MAIN")
    public static class EifLisApplyMain {
        private String barcode;

        private String hspOrgCode;

        private Integer peOrgId;

        private String tubeType;

        private String barType;

        private String sourceType;

        private String isUrgent;

        private String sampleType;

        private String sampleStatus;

        private String appDept;

        private String inpatientArea;

        private String patientNo;

        private String patientName;

        private String sex;

        private String age;

        private Date birthday;

        private String bed;

        private String diag;

        private String patientCard;

        private String medNumber;

        private String idNumber;

        private String address;

        private String tel;

        private String memo;

        private Integer isForbidden;

        private Integer sendStatus;

        private String appUserName;

        private Date appDate;

        private String barprtUserName;

        private Date barprtDate;

        private String extractUserName;

        private Date extractDate;

        private String submitUserCode;

        private String submitUserName;

        private Date submitDate;

        private String currentLink;

        private Integer testitemSum;

        private String combBarCode;

        private Integer isReceive;

        private Integer isOccurexception;

        private String groupMemo;

        private String reportNo;

        private Date receiveDate;

        private String receiveUserCode;

        private String receiveUserName;

        private Integer sampleNum;

        private String mainBarcode;

        private Date sendDate;

        private Date operDate;

        private String hisReqno;

        private BigDecimal charge;

        private String lwHspOrgCode;

        private String lwBarcode;

        private String lwMainBarcode;

        private Date lwOperDate;

        private String summaryHandoverCode;

        private String origoutOrgcode;

        private String origoutOrgname;

        private String jzlsh;

        private String klx;

    }

    @Getter
    @Setter
    @TableName("LIS_REPORT_FILE")
    public static class LisReportFile {
        private String id;

//        private Object fileBlob;

        private String fileName;

        private byte[] fileBlob;

        private String fileType;

        private String remark;

        private String orgId;

        private String reportType;

        private Integer isEnable;

        private Integer operType;

        private String outKey;

        private String zdy1;

        private String zdy2;

        private String zdy3;

        private String zdy4;

        private String zdy5;

        private String barcode;

        private String outBarcode;

        private String pageKind;

//        private String testMainId;

    }


    @Getter
    @Setter
    @TableName("LIS_RESULT")
    public static class LisResult {
        private String barcode;

        private String itemCode;

        private String itemName;

        private String machineCode;

        private String sampleNo;

        private String testDate;

        private Date operateDate;

        private String result;

        private String unit;

        private String flag;

        private String upValue;

        private String downValue;

        private String machineValue;

        private String range;

        private String rangeMemo;

        private String testitemCode;

        private String testitemName;

        private String odvalue;

        private String cutoffvalue;

        private String scovalue;

        private String rangeEn;

        private String rangeCnen;

        private Integer isAdd;

        private Integer isChangevalue;

        private String resultEn;

        private Integer reviewFlag;

        private String machineDate;

        private String machinegroupCode;

        private String machineresultFlag;

        private String machineValueLast;

        private Integer resultStatus;

        private String testMainId;

        private Integer isPrint;

        private Integer showNo;

        private String itemEnglish;

        private String itemEnglishSp;

        private String testitemEnglish;

        private String historyResultLast;

        private String specialFormatResult;

        private Integer testId;

        private Integer resultId;

        private String outTestitemCode;

        private String outTestitemName;

        private String labPackageId;

        private String labPackageName;

    }


    public interface EifLisApplyMainMapper extends BaseMapper<EifLisApplyMain> {

    }

    public interface EifLisApplyDetailMapper extends BaseMapper<EifLisApplyDetail> {

    }

    public interface LisReportFileMapper extends BaseMapper<LisReportFile> {

    }

    public interface LisResultMapper extends BaseMapper<LisResult> {

    }

    public interface LisTestMainMapper extends BaseMapper<HuMenLisTestMain> {

    }


}
