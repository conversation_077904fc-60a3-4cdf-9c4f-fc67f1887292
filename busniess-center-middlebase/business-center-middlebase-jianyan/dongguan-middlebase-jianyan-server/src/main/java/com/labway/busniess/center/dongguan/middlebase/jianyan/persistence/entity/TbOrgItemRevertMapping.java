package com.labway.busniess.center.dongguan.middlebase.jianyan.persistence.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * (TbOrgItemRevertMapping)表实体类
 *
 * <AUTHOR>
 * @since 2023-10-24 15:24:09
 */
@Data
@NoArgsConstructor
@SuppressWarnings("serial")
public class TbOrgItemRevertMapping extends Model<TbOrgItemRevertMapping> {
    //回传对照关系表id 主键
    @TableId(type = IdType.INPUT)
    private String mappingId;
    //送检机构编码
    private String customerCode;
    //送检客商名称
    private String customerName;
    //送检客商项目编码
    private String customerItemCode;
    //送检客商项目名称
    private String customerItemName;
    //检验机构编码
    private String orgCode;
    //检验机构名称
    private String orgName;
    //检验机构项目编码
    private String orgItemCode;
    //检验机构项目名称
    private String orgItemName;
    //回传对照类型 1报告项目2细菌2药物
    private Integer mappingType;
    //创建时间
    private Date createTime;
    //创建人id
    private String createBy;
    //更新时间
    private Date updateTime;
    //更新人id
    private String updateBy;
    //删除标识 0未删除1删除
    @TableLogic(value = "0", delval = "1")
    private Integer deleteFlag;


}

