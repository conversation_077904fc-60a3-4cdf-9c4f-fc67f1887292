package com.labway.busniess.center.dongguan.middlebase.jianyan.config;

import com.labway.business.center.core.enums.Region;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@Component
@RefreshScope
@ConfigurationProperties(prefix = "business")
public class BusinessConfig {

    /**
     * 送检机构code->名称
     */
    private Map<String, String> orgCodeNameMap;

    /**
     * 送检机构code->外部code
     */
    private Map<String,String> orgCodeMap;

    /**
     * 根据机构code获取机构名称
     * @param orgCode 机构code
     * @return 机构名称
     */
    public String getOrgNameByCode(String orgCode) {
        return orgCodeNameMap.getOrDefault(orgCode, orgCode);
    }

    /**
     * 根据机构code获取外部code
     * @param orgCode 机构code
     * @return 外部code
     */
    public String getOtherCodeByLimsCode(String orgCode) {
        return orgCodeMap.getOrDefault(orgCode, orgCode);
    }

    /**
     * 根据中间库机构获取对应的实验室机构，也可以反过来查
     */
    public String getMappingCode(String hspOrgCode) {
        for (Map.Entry<String, String> entry : orgCodeMap.entrySet()) {
            if (entry.getKey().equals(hspOrgCode)) {
                return entry.getValue();
            }
            if (entry.getValue().equals(hspOrgCode)) {
                return entry.getKey();
            }
        }

        return hspOrgCode;
    }


}
