spring:
  datasource:
    driver-class-name: org.h2.Driver
    # http://www.h2database.com/javadoc/org/h2/engine/DbSettings.html
    url: jdbc:h2:mem:mem-db;TRACE_LEVEL_FILE=0;TRACE_LEVEL_SYSTEM_OUT=0;DB_CLOSE_ON_EXIT=FALSE
    username: sa
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      auto-commit: true
      # 最小空闲连接数
      minimum-idle: 1
      # 最大连接数量
      maximum-pool-size: 10
      # 等待连接池分配连接的最大时长（毫秒）
      connection-timeout: 60000
      # 空闲连接超时时长
      idle-timeout: 60000
      connection-test-query: SELECT 1

huawei:
  obs:
    ak: L2NO0C3UJDB7SFYQKQIY
    sk: lSc1G2f52iTndVB0cpdDEU89ZjfcxjiSErOMlRDN
    endPoint: obs.cn-east-3.myhuaweicloud.com
    bucketName: labway-obs