server:
  port: 9881
spring:
  profiles:
    active: dev
  application:
    name: danyang-middlebase-jianyan
  cloud:
    nacos:
      server-addr: ${nacos.address}
      config:
        server-addr: ${nacos.address}
        namespace: ${nacos.config-namespace}
        file-extension: yaml
        shared-configs:
          - data-id: dubbo.yaml
            group: public
            refresh: true
          - data-id: business-center-common.yaml
            group: public
            refresh: true
      discovery:
        server-addr: ${nacos.address}
        namespace: ${nacos.discovery-namespace}

mybatis-plus:
  mapper-locations: classpath*:mapper/**/*.xml
  global-config:
    db-config:
      id-type: auto
      table-underline: false
  configuration:
    map-underscore-to-camel-case: true   #配置返回数据库(column下划线命名&&返回java实体是驼峰命名)，自动匹配无需as（没开启这个，SQL需要写as： select user_id as userId）
    cache-enabled: true #配置的缓存的全局开关
    lazyLoadingEnabled: true #延迟加载的全局开关。当开启时，所有关联对象都会延迟加载
    multipleResultSetsEnabled: true #是否允许单一语句返回多结果集
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl #打印sql语句,调试用

huawei:
  obs:
    ak: L2NO0C3UJDB7SFYQKQIY
    sk: lSc1G2f52iTndVB0cpdDEU89ZjfcxjiSErOMlRDN
    endPoint: obs.cn-east-3.myhuaweicloud.com
    bucketName: labway-obs

---
spring:
  profiles: dev
nacos:
  address: ***********:8848
  config-namespace: ${spring.profiles.active}
  discovery-namespace: ${spring.profiles.active}

---
spring:
  profiles: test
nacos:
  address: ***********:8848
  config-namespace: ${spring.profiles.active}
  discovery-namespace: ${spring.profiles.active}

---
spring:
  profiles: prod
nacos:
  address: ***********:8848
  config-namespace: ${spring.profiles.active}
  discovery-namespace: ${spring.profiles.active}