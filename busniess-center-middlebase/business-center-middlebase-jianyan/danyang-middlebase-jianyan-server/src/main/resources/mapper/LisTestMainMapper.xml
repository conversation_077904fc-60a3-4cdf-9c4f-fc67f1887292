<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.busniess.center.danyang.middlebase.jianyan.persistence.mapper.LisTestMainMapper">

    <resultMap id="BaseResultMap" type="com.labway.busniess.center.danyang.middlebase.jianyan.persistence.entity.TestMain">
            <result property="barcode" column="BARCODE" jdbcType="VARCHAR"/>
            <result property="hspOrgCode" column="HSP_ORG_CODE" jdbcType="VARCHAR"/>
            <result property="groupCode" column="GROUP_CODE" jdbcType="VARCHAR"/>
            <result property="machineCode" column="MACHINE_CODE" jdbcType="VARCHAR"/>
            <result property="sampleNo" column="SAMPLE_NO" jdbcType="VARCHAR"/>
            <result property="transType" column="TRANS_TYPE" jdbcType="VARCHAR"/>
            <result property="testUserCode" column="TEST_USER_CODE" jdbcType="VARCHAR"/>
            <result property="testUserName" column="TEST_USER_NAME" jdbcType="VARCHAR"/>
            <result property="testDate" column="TEST_DATE" jdbcType="VARCHAR"/>
            <result property="oneCheckUserCode" column="ONE_CHECK_USER_CODE" jdbcType="VARCHAR"/>
            <result property="oneCheckUserName" column="ONE_CHECK_USER_NAME" jdbcType="VARCHAR"/>
            <result property="oneCheckDate" column="ONE_CHECK_DATE" jdbcType="TIMESTAMP"/>
            <result property="isOneCheck" column="IS_ONE_CHECK" jdbcType="INTEGER"/>
            <result property="twoCheckUserCode" column="TWO_CHECK_USER_CODE" jdbcType="VARCHAR"/>
            <result property="twoCheckUserName" column="TWO_CHECK_USER_NAME" jdbcType="VARCHAR"/>
            <result property="twoCheckDate" column="TWO_CHECK_DATE" jdbcType="TIMESTAMP"/>
            <result property="isTwoCheck" column="IS_TWO_CHECK" jdbcType="INTEGER"/>
            <result property="checkUserCode" column="CHECK_USER_CODE" jdbcType="VARCHAR"/>
            <result property="checkUserName" column="CHECK_USER_NAME" jdbcType="VARCHAR"/>
            <result property="checkDate" column="CHECK_DATE" jdbcType="TIMESTAMP"/>
            <result property="isCheck" column="IS_CHECK" jdbcType="INTEGER"/>
            <result property="printUserCode" column="PRINT_USER_CODE" jdbcType="VARCHAR"/>
            <result property="printUserName" column="PRINT_USER_NAME" jdbcType="VARCHAR"/>
            <result property="lastPrintDate" column="LAST_PRINT_DATE" jdbcType="TIMESTAMP"/>
            <result property="isPrint" column="IS_PRINT" jdbcType="VARCHAR"/>
            <result property="testDescribe" column="TEST_DESCRIBE" jdbcType="VARCHAR"/>
            <result property="recUserCode" column="REC_USER_CODE" jdbcType="VARCHAR"/>
            <result property="recUserName" column="REC_USER_NAME" jdbcType="VARCHAR"/>
            <result property="recDate" column="REC_DATE" jdbcType="TIMESTAMP"/>
            <result property="sendStatus" column="SEND_STATUS" jdbcType="VARCHAR"/>
            <result property="sendDate" column="SEND_DATE" jdbcType="TIMESTAMP"/>
            <result property="templateCode" column="TEMPLATE_CODE" jdbcType="VARCHAR"/>
            <result property="specialMicrogroupCode" column="SPECIAL_MICROGROUP_CODE" jdbcType="VARCHAR"/>
            <result property="specialMicrogroupName" column="SPECIAL_MICROGROUP_NAME" jdbcType="VARCHAR"/>
            <result property="reportNo" column="REPORT_NO" jdbcType="VARCHAR"/>
            <result property="isMerge" column="IS_MERGE" jdbcType="INTEGER"/>
            <result property="mergeSampleNo" column="MERGE_SAMPLE_NO" jdbcType="VARCHAR"/>
            <result property="groupName" column="GROUP_NAME" jdbcType="VARCHAR"/>
            <result property="isusercenterlabtitle" column="ISUSERCENTERLABTITLE" jdbcType="INTEGER"/>
            <result property="customreporttitle" column="CUSTOMREPORTTITLE" jdbcType="VARCHAR"/>
            <result property="onetwocolumn" column="ONETWOCOLUMN" jdbcType="INTEGER"/>
            <result property="centerlaborgcode" column="CENTERLABORGCODE" jdbcType="VARCHAR"/>
            <result property="testitemSum" column="TESTITEM_SUM" jdbcType="INTEGER"/>
            <result property="testTime" column="TEST_TIME" jdbcType="TIMESTAMP"/>
            <result property="isSend" column="IS_SEND" jdbcType="VARCHAR"/>
            <result property="memo" column="MEMO" jdbcType="VARCHAR"/>
            <result property="oneCheckUserCode2" column="ONE_CHECK_USER_CODE2" jdbcType="VARCHAR"/>
            <result property="oneCheckUserName2" column="ONE_CHECK_USER_NAME2" jdbcType="VARCHAR"/>
            <result property="oneCheckDate2" column="ONE_CHECK_DATE2" jdbcType="TIMESTAMP"/>
            <result property="isOneCheck2" column="IS_ONE_CHECK2" jdbcType="VARCHAR"/>
            <result property="twoCheckUserCode2" column="TWO_CHECK_USER_CODE2" jdbcType="VARCHAR"/>
            <result property="twoCheckUserName2" column="TWO_CHECK_USER_NAME2" jdbcType="VARCHAR"/>
            <result property="twoCheckDate2" column="TWO_CHECK_DATE2" jdbcType="TIMESTAMP"/>
            <result property="isTwoCheck2" column="IS_TWO_CHECK2" jdbcType="VARCHAR"/>
            <result property="createDate" column="CREATE_DATE" jdbcType="TIMESTAMP"/>
            <result property="isExport" column="IS_EXPORT" jdbcType="VARCHAR"/>
            <result property="memo1" column="MEMO1" jdbcType="VARCHAR"/>
            <result property="twiceItemNum" column="TWICE_ITEM_NUM" jdbcType="INTEGER"/>
            <result property="isAutoprint" column="IS_AUTOPRINT" jdbcType="VARCHAR"/>
            <result property="autoprintUserCode" column="AUTOPRINT_USER_CODE" jdbcType="VARCHAR"/>
            <result property="autoprintUserName" column="AUTOPRINT_USER_NAME" jdbcType="VARCHAR"/>
            <result property="lastAutoprintDate" column="LAST_AUTOPRINT_DATE" jdbcType="TIMESTAMP"/>
            <result property="source" column="SOURCE" jdbcType="INTEGER"/>
            <result property="testMainId" column="TEST_MAIN_ID" jdbcType="VARCHAR"/>
            <result property="realTestUserCode" column="REAL_TEST_USER_CODE" jdbcType="VARCHAR"/>
            <result property="realOneCheckUserCode" column="REAL_ONE_CHECK_USER_CODE" jdbcType="VARCHAR"/>
            <result property="realTwoCheckUserCode" column="REAL_TWO_CHECK_USER_CODE" jdbcType="VARCHAR"/>
            <result property="realCheckUserCode" column="REAL_CHECK_USER_CODE" jdbcType="VARCHAR"/>
            <result property="isExistcrisis" column="IS_EXISTCRISIS" jdbcType="VARCHAR"/>
            <result property="mailBatchNo" column="MAIL_BATCH_NO" jdbcType="VARCHAR"/>
            <result property="reviewFlag" column="REVIEW_FLAG" jdbcType="VARCHAR"/>
            <result property="testId" column="TEST_ID" jdbcType="INTEGER"/>
            <result property="testRegId" column="TEST_REG_ID" jdbcType="INTEGER"/>
            <result property="outBarcode" column="OUT_BARCODE" jdbcType="VARCHAR"/>
            <result property="receiveDate" column="RECEIVE_DATE" jdbcType="TIMESTAMP"/>
            <result property="operFlag" column="Oper_Flag" jdbcType="VARCHAR"/>
            <result property="lisExecCount" column="LIS_EXEC_COUNT" jdbcType="INTEGER"/>
            <result property="testMainOuterId" column="TEST_MAIN_OUTER_ID" jdbcType="VARCHAR"/>
            <result property="labPackageId" column="LAB_PACKAGE_ID" jdbcType="VARCHAR"/>
            <result property="labPackageName" column="LAB_PACKAGE_NAME" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        [BARCODE],[HSP_ORG_CODE],[GROUP_CODE],
        [MACHINE_CODE],[SAMPLE_NO],[TRANS_TYPE],
        [TEST_USER_CODE],[TEST_USER_NAME],[TEST_DATE],
        [ONE_CHECK_USER_CODE],[ONE_CHECK_USER_NAME],[ONE_CHECK_DATE],
        [IS_ONE_CHECK],[TWO_CHECK_USER_CODE],[TWO_CHECK_USER_NAME],
        [TWO_CHECK_DATE],[IS_TWO_CHECK],[CHECK_USER_CODE],
        [CHECK_USER_NAME],[CHECK_DATE],[IS_CHECK],
        [PRINT_USER_CODE],[PRINT_USER_NAME],[LAST_PRINT_DATE],
        [IS_PRINT],[TEST_DESCRIBE],[REC_USER_CODE],
        [REC_USER_NAME],[REC_DATE],[SEND_STATUS],
        [SEND_DATE],[TEMPLATE_CODE],[SPECIAL_MICROGROUP_CODE],
        [SPECIAL_MICROGROUP_NAME],[REPORT_NO],[IS_MERGE],
        [MERGE_SAMPLE_NO],[GROUP_NAME],[ISUSERCENTERLABTITLE],
        [CUSTOMREPORTTITLE],[ONETWOCOLUMN],[CENTERLABORGCODE],
        [TESTITEM_SUM],[TEST_TIME],[IS_SEND],
        [MEMO],[ONE_CHECK_USER_CODE2],[ONE_CHECK_USER_NAME2],
        [ONE_CHECK_DATE2],[IS_ONE_CHECK2],[TWO_CHECK_USER_CODE2],
        [TWO_CHECK_USER_NAME2],[TWO_CHECK_DATE2],[IS_TWO_CHECK2],
        [CREATE_DATE],[IS_EXPORT],[MEMO1],
        [TWICE_ITEM_NUM],[IS_AUTOPRINT],[AUTOPRINT_USER_CODE],
        [AUTOPRINT_USER_NAME],[LAST_AUTOPRINT_DATE],[SOURCE],
        [TEST_MAIN_ID],[REAL_TEST_USER_CODE],[REAL_ONE_CHECK_USER_CODE],
        [REAL_TWO_CHECK_USER_CODE],[REAL_CHECK_USER_CODE],[IS_EXISTCRISIS],
        [MAIL_BATCH_NO],[REVIEW_FLAG],[TEST_ID],
        [TEST_REG_ID],[OUT_BARCODE],[RECEIVE_DATE],
        [Oper_Flag],[LIS_EXEC_COUNT],[TEST_MAIN_OUTER_ID],
        [LAB_PACKAGE_ID],[LAB_PACKAGE_NAME]
    </sql>

    <insert id="backup">
        INSERT INTO LIS_TEST_MAIN_BACKUP(<include refid="Base_Column_List" />)
        SELECT <include refid="Base_Column_List" /> FROM LIS_TEST_MAIN
        WHERE TEST_MAIN_ID IN
        <foreach collection="testMainIds" item="testMainId" separator="," open="(" close=")">
            #{testMainId}
        </foreach>
    </insert>

</mapper>
