<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.busniess.center.danyang.middlebase.jianyan.persistence.mapper.ApplyMainMapper">

    <resultMap id="BaseResultMap" type="com.labway.busniess.center.danyang.middlebase.jianyan.persistence.entity.ApplyMain">
            <result property="barcode" column="BARCODE" jdbcType="VARCHAR"/>
            <result property="hspOrgCode" column="HSP_ORG_CODE" jdbcType="VARCHAR"/>
            <result property="peOrgId" column="PE_ORG_ID" jdbcType="INTEGER"/>
            <result property="tubeType" column="TUBE_TYPE" jdbcType="VARCHAR"/>
            <result property="barType" column="BAR_TYPE" jdbcType="VARCHAR"/>
            <result property="sourceType" column="SOURCE_TYPE" jdbcType="VARCHAR"/>
            <result property="isUrgent" column="IS_URGENT" jdbcType="VARCHAR"/>
            <result property="sampleType" column="SAMPLE_TYPE" jdbcType="VARCHAR"/>
            <result property="sampleStatus" column="SAMPLE_STATUS" jdbcType="VARCHAR"/>
            <result property="appDept" column="APP_DEPT" jdbcType="VARCHAR"/>
            <result property="inpatientArea" column="INPATIENT_AREA" jdbcType="VARCHAR"/>
            <result property="patientNo" column="PATIENT_NO" jdbcType="VARCHAR"/>
            <result property="patientName" column="PATIENT_NAME" jdbcType="VARCHAR"/>
            <result property="sex" column="SEX" jdbcType="VARCHAR"/>
            <result property="age" column="AGE" jdbcType="VARCHAR"/>
            <result property="birthday" column="BIRTHDAY" jdbcType="TIMESTAMP"/>
            <result property="bed" column="BED" jdbcType="VARCHAR"/>
            <result property="diag" column="DIAG" jdbcType="VARCHAR"/>
            <result property="patientCard" column="PATIENT_CARD" jdbcType="VARCHAR"/>
            <result property="medNumber" column="MED_NUMBER" jdbcType="VARCHAR"/>
            <result property="idNumber" column="ID_NUMBER" jdbcType="VARCHAR"/>
            <result property="address" column="ADDRESS" jdbcType="VARCHAR"/>
            <result property="tel" column="TEL" jdbcType="VARCHAR"/>
            <result property="memo" column="MEMO" jdbcType="VARCHAR"/>
            <result property="isForbidden" column="IS_FORBIDDEN" jdbcType="INTEGER"/>
            <result property="sendStatus" column="SEND_STATUS" jdbcType="INTEGER"/>
            <result property="appUserName" column="APP_USER_NAME" jdbcType="VARCHAR"/>
            <result property="appDate" column="APP_DATE" jdbcType="TIMESTAMP"/>
            <result property="barprtUserName" column="BARPRT_USER_NAME" jdbcType="VARCHAR"/>
            <result property="barprtDate" column="BARPRT_DATE" jdbcType="TIMESTAMP"/>
            <result property="extractUserName" column="EXTRACT_USER_NAME" jdbcType="VARCHAR"/>
            <result property="extractDate" column="EXTRACT_DATE" jdbcType="TIMESTAMP"/>
            <result property="submitUserCode" column="SUBMIT_USER_CODE" jdbcType="VARCHAR"/>
            <result property="submitUserName" column="SUBMIT_USER_NAME" jdbcType="VARCHAR"/>
            <result property="submitDate" column="SUBMIT_DATE" jdbcType="TIMESTAMP"/>
            <result property="currentLink" column="CURRENT_LINK" jdbcType="VARCHAR"/>
            <result property="testitemSum" column="TESTITEM_SUM" jdbcType="INTEGER"/>
            <result property="combBarCode" column="COMB_BAR_CODE" jdbcType="VARCHAR"/>
            <result property="isReceive" column="IS_RECEIVE" jdbcType="INTEGER"/>
            <result property="isOccurexception" column="IS_OCCUREXCEPTION" jdbcType="INTEGER"/>
            <result property="groupMemo" column="GROUP_MEMO" jdbcType="VARCHAR"/>
            <result property="reportNo" column="REPORT_NO" jdbcType="VARCHAR"/>
            <result property="receiveDate" column="RECEIVE_DATE" jdbcType="TIMESTAMP"/>
            <result property="receiveUserCode" column="RECEIVE_USER_CODE" jdbcType="VARCHAR"/>
            <result property="receiveUserName" column="RECEIVE_USER_NAME" jdbcType="VARCHAR"/>
            <result property="sampleNum" column="SAMPLE_NUM" jdbcType="INTEGER"/>
            <result property="mainBarcode" column="MAIN_BARCODE" jdbcType="VARCHAR"/>
            <result property="sendDate" column="SEND_DATE" jdbcType="TIMESTAMP"/>
            <result property="operDate" column="OPER_DATE" jdbcType="TIMESTAMP"/>
            <result property="hisReqno" column="HIS_REQNO" jdbcType="VARCHAR"/>
            <result property="charge" column="CHARGE" jdbcType="DECIMAL"/>
            <result property="lwHspOrgCode" column="LW_HSP_ORG_CODE" jdbcType="VARCHAR"/>
            <result property="lwBarcode" column="LW_BARCODE" jdbcType="VARCHAR"/>
            <result property="lwMainBarcode" column="LW_MAIN_BARCODE" jdbcType="VARCHAR"/>
            <result property="lwOperDate" column="LW_OPER_DATE" jdbcType="TIMESTAMP"/>
            <result property="summaryHandoverCode" column="SUMMARY_HANDOVER_CODE" jdbcType="VARCHAR"/>
            <result property="origoutOrgcode" column="ORIGOUT_ORGCODE" jdbcType="VARCHAR"/>
            <result property="origoutOrgname" column="ORIGOUT_ORGNAME" jdbcType="VARCHAR"/>
            <result property="jzlsh" column="JZLSH" jdbcType="VARCHAR"/>
            <result property="klx" column="KLX" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        BARCODE,HSP_ORG_CODE,PE_ORG_ID,
        TUBE_TYPE,BAR_TYPE,SOURCE_TYPE,
        IS_URGENT,SAMPLE_TYPE,SAMPLE_STATUS,
        APP_DEPT,INPATIENT_AREA,PATIENT_NO,
        PATIENT_NAME,SEX,AGE,
        BIRTHDAY,BED,DIAG,
        PATIENT_CARD,MED_NUMBER,ID_NUMBER,
        ADDRESS,TEL,MEMO,
        IS_FORBIDDEN,SEND_STATUS,APP_USER_NAME,
        APP_DATE,BARPRT_USER_NAME,BARPRT_DATE,
        EXTRACT_USER_NAME,EXTRACT_DATE,SUBMIT_USER_CODE,
        SUBMIT_USER_NAME,SUBMIT_DATE,CURRENT_LINK,
        TESTITEM_SUM,COMB_BAR_CODE,IS_RECEIVE,
        IS_OCCUREXCEPTION,GROUP_MEMO,REPORT_NO,
        RECEIVE_DATE,RECEIVE_USER_CODE,RECEIVE_USER_NAME,
        SAMPLE_NUM,MAIN_BARCODE,SEND_DATE,
        OPER_DATE,HIS_REQNO,CHARGE,
        LW_HSP_ORG_CODE,LW_BARCODE,LW_MAIN_BARCODE,
        LW_OPER_DATE,SUMMARY_HANDOVER_CODE,ORIGOUT_ORGCODE,
        ORIGOUT_ORGNAME,JZLSH,KLX
    </sql>
</mapper>
