package com.labway.busniess.center.danyang.middlebase.jianyan.persistence.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 
 * @TableName LIS_HI_RESULT
 */
@TableName(value ="LIS_HI_RESULT")
@Data
public class LisHiResult implements Serializable {
    /**
     * 报告单ID
     */
    private String testMainId;

    /**
     * 条码编码
     */
    private String barcode;

    /**
     * 仪器项目编码
     */
    private String itemCode;

    /**
     * 仪器项目名称
     */
    private String itemName;

    /**
     * 仪器编码
     */
    private String machineCode;

    /**
     * 样本单号
     */
    private String sampleNo;

    /**
     * 检验日期
     */
    private String testDate;

    /**
     * 报告项目名称
     */
    private String reportName;

    /**
     * 仪器结果
     */
    private String result;

    /**
     * 结果判定
     */
    private String resultJudge;

    /**
     * 单位
     */
    private String unit;

    /**
     * 标记
     */
    private String flag;

    /**
     * 上限
     */
    private String upValue;

    /**
     * 下限
     */
    private String downValue;

    /**
     * 机器结果
     */
    private String machineValue;

    /**
     * 参考范围
     */
    private String range;

    /**
     * 检验项目编码
     */
    private String testitemCode;

    /**
     * 检验项目名称
     */
    private String testitemName;

    /**
     * 仪器项目英文名称
     */
    private String itemEnglish;

    /**
     * 仪器项目英文缩写
     */
    private String itemEnglishSp;

    /**
     * 显示顺序
     */
    private Integer showNo;

    /**
     * 院感项目结果数量
     */
    private Integer testitemNum;

    /**
     * 外部检测项目编码
     */
    private String outTestitemCode;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

}