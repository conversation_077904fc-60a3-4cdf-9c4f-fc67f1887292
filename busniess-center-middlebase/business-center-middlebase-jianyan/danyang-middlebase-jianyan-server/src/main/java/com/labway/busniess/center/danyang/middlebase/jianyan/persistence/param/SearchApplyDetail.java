package com.labway.busniess.center.danyang.middlebase.jianyan.persistence.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <p>
 * ApplyDetailQuery
 * 申请单明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2023/7/13 14:47
 */
@Data
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class SearchApplyDetail {

    /**
     * 条码号
     */
    private List<String> barcodes;

    /**
     * 机构编码
     */
    private String hspOrgCode;

}
