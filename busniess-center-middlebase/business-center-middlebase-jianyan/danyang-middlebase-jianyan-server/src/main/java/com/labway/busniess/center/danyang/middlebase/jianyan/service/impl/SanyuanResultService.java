package com.labway.busniess.center.danyang.middlebase.jianyan.service.impl;

import com.labway.busniess.center.danyang.middlebase.jianyan.service.AbstractSanyuanService;
import com.labway.busniess.center.danyang.middlebase.jianyan.service.ResultService;
import com.labway.busniess.center.middlebase.jianyan.reqeust.CallbackResultRequest;
import com.labway.busniess.center.middlebase.jianyan.reqeust.LisTestMainRequest;
import com.swak.frame.dto.Response;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <pre>
 * SanyuanResultService
 * 丹阳 三院
 * </pre>
 *
 * <AUTHOR>
 * @since 2025/4/7 15:40
 */
@Service
public class SanyuanResultService extends AbstractSanyuanService implements ResultService {

    @Override
    public Set<String> getHspOrgCodes() {
        return new HashSet<>(sanyuanConfig.getHspOrgCodes());
    }

    @Override
    public Response<?> receiveLimsResult(List<LisTestMainRequest> requests) {
        for (LisTestMainRequest request : requests) {
            super.notifyResult(request);
        }
        return Response.success();
    }

    @Override
    public Response<?> doReceiveLimsResult(String lwHspOrgCode, List<LisTestMainRequest> requests) {
        return Response.success();
    }

    @Override
    public Response<?> callbackResult(CallbackResultRequest request) {
        return Response.success();
    }

}
