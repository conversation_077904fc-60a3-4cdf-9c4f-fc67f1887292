package com.labway.busniess.center.danyang.middlebase.jianyan.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.cloud.commons.lang.StringUtils;
import com.alibaba.fastjson.JSON;
import com.labway.business.center.compare.dto.OutApplyInfoDTO;
import com.labway.business.center.compare.service.la.LaDubboService;
import com.labway.busniess.center.danyang.middlebase.jianyan.service.ApplyService;
import com.labway.busniess.center.middlebase.jianyan.reqeust.QueryApplyRequest;
import com.labway.busniess.center.middlebase.jianyan.reqeust.QueryOutApplyRequest;
import com.labway.busniess.center.middlebase.jianyan.reqeust.SignApplyInfoRequest;
import com.labway.busniess.center.middlebase.jianyan.reqeust.SignCancelApplyInfoRequest;
import com.swak.frame.dto.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * <p>
 * TestApplyController
 * 申请单
 * </p>
 *
 * <AUTHOR>
 * @since 2023/7/13 9:35
 */
@Slf4j
@RestController
@RequestMapping("/jianyan/middle/apply")
public class TestApplyController implements InitializingBean {

    @DubboReference
    private LaDubboService laDubboService;

    private final Collection<ApplyService> applyServices = new ArrayList<>();

    @PostMapping("get")
    public Response<OutApplyInfoDTO> get(@RequestBody @Validated QueryApplyRequest request) {
        if (StringUtils.isNotBlank(request.getNoType())) {
            log.info("丹阳妇幼两癌，样本信息查询，参数：{}", JSON.toJSONString(request));
            com.labway.business.center.compare.request.QueryApplyRequest bean = BeanUtil.toBean(request, com.labway.business.center.compare.request.QueryApplyRequest.class);
            return laDubboService.laQuerySampleInfo1(bean);
        }
        return getApplyService(request.getHspOrgCode()).get(request);
    }

    @PostMapping("selectOutApplyInfo")
    public Response<List<OutApplyInfoDTO>> selectOutApplyInfo(@RequestBody @Validated QueryOutApplyRequest request) {
        return getApplyService(request.getHspOrgCode()).selectOutApplyInfo(request);
    }

    @PostMapping("sign")
    public Response<?> sign(@RequestBody @Validated SignApplyInfoRequest request) {
        if (StringUtils.isNotBlank(request.getNoType())) {
            log.info("丹阳妇幼两癌，样本信息签收，参数：{}", JSON.toJSONString(request));
            com.labway.business.center.compare.request.SignApplyInfoRequest bean = BeanUtil.toBean(request, com.labway.business.center.compare.request.SignApplyInfoRequest.class);
            return laDubboService.laSignSampleInfo1(bean);
        }
        return getApplyService(request.getHspOrgCode()).sign(request);
    }

    @PostMapping("cancelSign")
    public Response<?> cancelSign(@RequestBody @Validated SignCancelApplyInfoRequest request) {
        if (StringUtils.isNotBlank(request.getNoType())) {
            log.info("丹阳妇幼两癌，样本信息取消签收，参数：{}", JSON.toJSONString(request));
            return Response.success();
        }
        return getApplyService(request.getSendOrgCode()).cancelSign(request);
    }

    @PostMapping("/serviceMonitor")
    public Response<?> serviceMonitor() {
        return getApplyService("DEFAULT").serviceMonitor();
    }

    private ApplyService getApplyService(String hspOrgCode) {
        return applyServices.stream().filter(e -> e.getHspOrgCodes().contains(hspOrgCode)).findAny()
                .orElse(applyServices.stream().filter(e -> e.getHspOrgCodes().contains("DEFAULT")).findFirst().orElseThrow(() -> {
                    log.error("未找到对应机构 [{}] 的ApplyService服务", hspOrgCode);
                    return new RuntimeException("未找到对应的服务");
                }));
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        applyServices.addAll(SpringUtil.getBeansOfType(ApplyService.class).values());
    }
}
