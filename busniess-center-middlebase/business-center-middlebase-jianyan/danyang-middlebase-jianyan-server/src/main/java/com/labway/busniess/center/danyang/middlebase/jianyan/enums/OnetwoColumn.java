package com.labway.busniess.center.danyang.middlebase.jianyan.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * OnetwoColumn
 * 单双列模式(1为单列，2为双列，默认为单列，值为空也是单列，目前先暂时只支持单列模式)
 *
 * <AUTHOR>
 * @since 2023/7/21 16:36
 */
@Getter
@AllArgsConstructor
public enum OnetwoColumn {

    ONE(1, "单列"),
    TWO(2, "双列"),

    ;

    private final Integer code;
    private final String desc;

}
