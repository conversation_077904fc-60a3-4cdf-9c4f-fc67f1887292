package com.labway.busniess.center.danyang.middlebase.jianyan.persistence.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 * @TableName LIS_TEST_MAIN_FAIL
 */
@TableName(value ="LIS_TEST_MAIN_FAIL")
@Data
public class TestMainFail implements Serializable {
    /**
     * 
     */
    private String testMainId;

    /**
     * 
     */
    private String testMainOuterId;

    /**
     * 
     */
    private String barcode;

    /**
     * 
     */
    private String outerBarcode;

    /**
     * 
     */
    private String hspOrgCode;

    /**
     * 
     */
    private String errorMsg;

    /**
     * 
     */
    private Date operateDate;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

}