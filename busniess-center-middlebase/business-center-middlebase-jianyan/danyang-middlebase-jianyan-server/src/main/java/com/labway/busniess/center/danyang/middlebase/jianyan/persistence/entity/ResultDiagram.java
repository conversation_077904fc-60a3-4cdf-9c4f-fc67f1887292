package com.labway.busniess.center.danyang.middlebase.jianyan.persistence.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 
 * @TableName LIS_RESULT_DIAGRAM
 */
@TableName(value ="LIS_RESULT_DIAGRAM")
@Data
public class ResultDiagram implements Serializable {
    /**
     * 条码编码
     */
    private String barcode;

    /**
     * 仪器编码
     */
    private String machineCode;

    /**
     * 样本单号
     */
    private String sampleNo;

    /**
     * 检验日期,做主键之一
     */
    private String testDate;

    /**
     * 图型结果
     */
    private Object gram;

    /**
     * 图像是否打印(0否1是)
     */
    private Integer isPrintimage;

    /**
     * 图像编码
     */
    private String gramNo;

    /**
     * 检验ID
     */
    private Integer testId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

}