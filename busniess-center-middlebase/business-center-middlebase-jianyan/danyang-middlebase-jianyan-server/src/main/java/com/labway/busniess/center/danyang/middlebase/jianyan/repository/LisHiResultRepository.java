package com.labway.busniess.center.danyang.middlebase.jianyan.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.busniess.center.danyang.middlebase.jianyan.persistence.entity.LisHiResult;
import com.labway.busniess.center.danyang.middlebase.jianyan.persistence.mapper.LisHiResultMapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * LisHiResultRepository 院感结果
 *
 * <AUTHOR>
 * @since 2023/7/18 20:35
 */
@Component
public class LisHiResultRepository {

    @Resource
    private LisHiResultMapper lisHiResultMapper;

    public int deleteHiResultByTestMainIds(List<String> testMainIds) {
        lisHiResultMapper.backup(testMainIds);
        LambdaQueryWrapper<LisHiResult> wrapper =
                Wrappers.lambdaQuery(LisHiResult.class).in(LisHiResult::getTestMainId, testMainIds);
        return lisHiResultMapper.delete(wrapper);
    }

    public int batchSave(List<LisHiResult> results) {
        int ret = 0;
        for (LisHiResult result : results) {
            ret += lisHiResultMapper.insert(result);
        }
        return ret;
    }

}
