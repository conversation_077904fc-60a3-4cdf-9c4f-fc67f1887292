package com.labway.busniess.center.danyang.middlebase.jianyan.service;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.StopWatch;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.XmlUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.labway.business.center.compare.dto.OutApplyInfoDTO;
import com.labway.business.center.compare.dto.middlebase.OrgApplySampleMainDTO;
import com.labway.business.center.compare.dto.middlebase.OrgApplySampleMainItemDTO;
import com.labway.business.center.core.enums.*;
import com.labway.business.center.core.util.NotifyUtil;
import com.labway.business.center.core.util.ObsUtil;
import com.labway.business.center.core.util.PdfUtils;
import com.labway.business.center.core.util.RegexUtils;
import com.labway.busniess.center.danyang.middlebase.jianyan.config.BusinessConfig;
import com.labway.busniess.center.danyang.middlebase.jianyan.utils.AgeUtil;
import com.labway.busniess.center.middlebase.jianyan.reqeust.LisResultRequest;
import com.labway.busniess.center.middlebase.jianyan.reqeust.LisTestMainRequest;
import com.swak.frame.util.StringPool;
import com.swak.frame.util.UUIDHexGenerator;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import org.w3c.dom.DOMException;
import org.w3c.dom.Document;
import org.w3c.dom.Element;

import javax.annotation.Resource;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.SocketTimeoutException;
import java.net.URL;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 医院和东莞人民医院 公共父类模块
 */
@RefreshScope
@Slf4j
public abstract class AbstractSanyuanService extends CachedApplyService {

    @Resource
    protected BusinessConfig businessConfig;
    @Resource
    protected SanyuanConfig sanyuanConfig;
    @Resource
    private ObsUtil obsUtil;
    @Resource
    private NotifyUtil notifyUtil;

    protected static final String GET_APPLY = "MES0192"; // 检验获取条码信息
    protected static final String NOTIFY_RESULT = "MES0191"; // 检验报告回传

    protected static final String GET_APPLY_REQ = "<![CDATA[" +
            "<Request><Header><SourceSystem>JinYu</SourceSystem><MessageID/></Header><Body><SpecimenID>%s</SpecimenID><HISOrdID/></Body></Request>]]>";
    protected static final String NOTIFY_RESULT_REQ = "<![CDATA[%s]]>";

    protected static final Function<String, String> funGetApply = (response) -> {
        try {
            final Document document = XmlUtil.parseXml(response);
            final String textContent = document.getElementsByTagName("SOAP-ENV:Envelope").item(0).getTextContent();
            log.info("丹阳三院接口 获取申请单 textContent [{}]", StrUtil.removeAllLineBreaks(textContent));
            return JSONUtil.xmlToJson(textContent).toString();
        } catch (DOMException e) {
            log.error("丹阳三院接口 获取申请单 解析Xml结果异常 {} ", StrUtil.removeAllLineBreaks(response), e);
            throw e;
        }
    };
    protected static final Function<String, String> funNotifyResult = (response) -> {
        try {
            final Document document = XmlUtil.parseXml(response);
            final String textContent = document.getElementsByTagName("SOAP-ENV:Envelope").item(0).getTextContent();
            log.info("丹阳三院接口 结果回传结果 textContent [{}]", StrUtil.removeAllLineBreaks(textContent));
            return JSONUtil.xmlToJson(textContent).toString();
        } catch (DOMException e) {
            log.error("丹阳三院接口 结果回传结果 解析Xml结果异常 {} ", StrUtil.removeAllLineBreaks(response), e);
            return response;
        }
    };

    @Override
    protected String getCacheKey(String barcode) {
        return String.format(CACHE_KEY, "SANYUAN", barcode);
    }

    /**
     * webservice访问地址
     *
     * @return
     */
    public String getUrl() {
        return sanyuanConfig.getUrl();
    }

    /**
     * 送检机构的名称
     *
     * @return
     */
    public String getHspOrgName(String hspOrgCode) {
        return businessConfig.getOrgNameByCode(hspOrgCode);
    }

    protected OutApplyInfoDTO convertOutApplyInfoDTO(JSONObject dataRow, String hspOrgCode) throws ParseException {
        String hspOrgName = getHspOrgName(hspOrgCode);
        final OutApplyInfoDTO apply = new OutApplyInfoDTO();
        // 获取条码人员信息
        apply.setHspOrgCode(hspOrgCode);
        apply.setHspOrgName(hspOrgName);
        // apply.setBarcode(dataRow.getStr("VisitNumber"));
        apply.setPatientCard(dataRow.getStr("IDNumber"));
        // 体检 门诊 住院 AdmissionType
        apply.setApplyType(dataRow.getStr("AdmissionType"));
        // 住院号:MedicalRecordNo  门诊号：AdmNo
        // apply.setPatientVisitCard(dataRow.getStr("MedicalRecordNo"));
        // 就诊类型 I是住院，O是门诊，E是急诊，H是体检
        final String admissionTypeCode = dataRow.getStr("AdmissionTypeCode", AdmissionType.MZ);
        switch (admissionTypeCode) {
            case AdmissionType.MZ_CODE: // 门诊
                apply.setApplyType(AdmissionType.MZ);
                // 门诊号：AdmNo
                apply.setPatientVisitCard(dataRow.getStr("AdmNo"));
                break;
            case AdmissionType.ZY_CODE: // 住院
                apply.setApplyType(AdmissionType.ZY);
                // 住院号:MedicalRecordNo
                apply.setPatientVisitCard(dataRow.getStr("MedicalRecordNo"));
                break;
            case AdmissionType.JZ_CODE: // 急诊
                apply.setApplyType(AdmissionType.JZ);
                // 住院号:AdmNo
                apply.setPatientVisitCard(dataRow.getStr("AdmNo"));
                break;
            case AdmissionType.TJ_CODE: // 体检
                apply.setApplyType(AdmissionType.TJ);
                // 住院号:AdmNo
                apply.setPatientVisitCard(dataRow.getStr("AdmNo"));
                break;
            default: // 默认给 门诊
                apply.setApplyType(AdmissionType.MZ);
                // 门诊号：AdmNo
                apply.setPatientVisitCard(dataRow.getStr("AdmNo"));
                break;
        }
        apply.setPatientName(dataRow.getStr("SurName"));
        apply.setPatientBed(dataRow.getStr("BedNo"));
        apply.setUrgent(0);
        // 采样时间
        apply.setSamplingDate(DateUtil.date());
        if (StringUtils.isNotBlank(dataRow.getStr("CollectDate"))) {
            String collectDate = dataRow.getStr("CollectDate") + " " + dataRow.getStr("CollectTime", "00:00:00");
            apply.setSamplingDate(parseDateTime(collectDate));
        }
        final String sex = dataRow.getStr("SpeciesDesc");
        apply.setPatientSex(SexEnum.getEnumByValue(sex).getIntCode());
        if (StringUtils.isNotBlank(dataRow.getStr("BirthDate"))) {
            final String birthDate = dataRow.getStr("BirthDate") + " " + dataRow.getStr("BirthTime", "00:00:00");
            apply.setPatientBirthday(parseDateTime(birthDate));
        }

        // 年龄 TODO x岁x月x天x小时
        final Triple<Integer, Integer, String> ageTriple = AgeUtil.parse(dataRow.getStr("AgeDesc"));
        apply.setPatientAge(ageTriple.getLeft());
        apply.setPatientSubage(ageTriple.getMiddle());
        apply.setPatientSubageUnit(ageTriple.getRight());

        apply.setPatientMobile(dataRow.getStr("MobileNo", dataRow.getStr("PhoneNo")));
        apply.setDept(dataRow.getStr("LocationDesc", dataRow.getStr("LocationCode")));
        apply.setSendDoctor(dataRow.getStr("DoctorDesc"));
        // 临床诊断
        apply.setClinicalDiagnosis(dataRow.getStr("Symptom"));
        // apply.setSampleProperty(dataRow.getStr("samp_name"));
        apply.setSampleProperty("正常"); // 样本性状
        apply.setApplyDate(Objects.requireNonNullElse(apply.getSamplingDate(), new Date()));
        apply.setItems(new LinkedList<>());
        // 获取条码检验项目
        Object items = dataRow.getByPath("OrderList.OderItem");
        if (items instanceof List) {
            JSONArray jsonArray = JSONUtil.parseArray(items);
            List<OutApplyInfoDTO.OutApplyItem> itemList = jsonArray.stream().parallel().map(this::setLisItem).collect(Collectors.toList());
            apply.getItems().addAll(itemList);
        } else {
            apply.getItems().add(setLisItem(items));
        }

        apply.setInpatientArea(dataRow.getStr("WardDesc"));

        return apply;
    }

    protected OutApplyInfoDTO.OutApplyItem setLisItem(Object items) {
        JSONObject lisItems = JSONUtil.parseObj(items);
        OutApplyInfoDTO.OutApplyItem outApplyItem = new OutApplyInfoDTO.OutApplyItem();
        outApplyItem.setHisOrderID(lisItems.getStr("HISOrderID"));
        outApplyItem.setOutTestItemCode(lisItems.getStr("TestSetCode"));
        outApplyItem.setOutTestItemName(lisItems.getStr("TestSetName"));
        return outApplyItem;
    }

    protected JSONObject queryApply(String barcode) {
        final JSONObject cache = getCache(barcode);
        if (Objects.nonNull(cache)) {
            return cache;
        }

        final String text = doWsRequest(GET_APPLY, String.format(GET_APPLY_REQ, barcode), funGetApply);
        if (StringUtils.isBlank(text)) {
            throw new IllegalStateException(String.format("条码 [%s] 未查询到申请单信息", barcode));
        }

        cn.hutool.json.JSONObject resJson = JSONUtil.parseObj(text);
        if (!Objects.equals("0", resJson.getByPath("Response.Header.ResultCode", String.class))) {
            log.error("三院 医院条码号 [{}]，查询申请单失败 [{}]", barcode, text);
            notifyUtil.notifyByRegion(Region.danyang.name(), StrUtil.format("三院 医院条码号 [{}]，查询申请单失败 [{}]", barcode, text));
        }

        JSONObject jsonObject = JSONUtil.parseObj(text);
        JSONObject data = jsonObject.getJSONObject("Response");
        JSONObject dataRow = data.getJSONObject("Body");
        if (Objects.isNull(dataRow)) {
            throw new IllegalStateException(String.format("条码 [%s] 不存在", barcode));
        }
        addCache(barcode, dataRow.toString());
        return dataRow;
    }

    /**
     * 发起查询条形码webservice请求
     */
    protected String doWsRequest(String methodName, String data, Function<String, String> responseFun) {
        final String url = getUrl();
        log.info("[Webservice doWsRequest] apiUrl [{}] methodName = [{}], request xml = {}", url, methodName, removeNewLine(data));
        Document doc = XmlUtil.createXml("soapenv:Envelope");
        final Element root = doc.getDocumentElement();
        root.setAttribute("xmlns:soapenv", "http://schemas.xmlsoap.org/soap/envelope/");
        root.setAttribute("xmlns:dhcc", "http://www.dhcc.com.cn");
        final Element header = XmlUtil.appendChild(root, "soapenv:Header");
        final Element body = XmlUtil.appendChild(root, "soapenv:Body");

        final Element method = XmlUtil.appendChild(body, "dhcc:HIPMessageServer");

        // 请求类型
        final Element actionType = XmlUtil.appendChild(method, "dhcc:input1");
        actionType.appendChild(doc.createTextNode(methodName));

        // 参数
        final Element param = XmlUtil.appendChild(method, "dhcc:input2");
        param.appendChild(doc.createTextNode(data));

        String requestBody = XmlUtil.toStr(doc, CharsetUtil.UTF_8, false, true);
        requestBody = requestBody.replace("&lt;", "<").replace("&gt;", ">");

        final HttpRequest request = HttpUtil.createPost(url);
        request.timeout(15000);
        request.contentType("text/xml; charset=utf-8");
        request.header("SOAPAction", "http://www.dhcc.com.cn/DHC.Published.PUB0003.BS.PUB0003.HIPMessageServer");
        request.body(requestBody);
        log.info("请求三方接口入参 [{}]", removeNewLine(requestBody));

        final StopWatch watch = new StopWatch();
        watch.start();
        try (HttpResponse response = request.execute()) {
            final String responseBody = response.body();

            log.info("请求三方接口出参 [{}]", removeNewLine(com.labway.busniess.center.danyang.middlebase.jianyan.utils.XmlUtil.unescapeXml(responseBody)));

            watch.stop();
            log.info("发起请求 URL [{}] 耗时 [{}ms] Status [{}]", url, watch.getTotalTimeMillis(), response.getStatus());
            return responseFun.apply(responseBody);
        } catch (Exception e) {
            log.error("发起请求 URL [{}] 耗时 [{}ms] Body [{}] 失败", url, watch.getTotalTimeMillis(), removeNewLine(requestBody), e);
            if (e.getCause() instanceof SocketTimeoutException) {
                throw new IllegalStateException("调用HIS接口超时 ", e);
            }
            throw e;
        }
    }

    protected OrgApplySampleMainDTO transferOutApplyInfoDTO(OutApplyInfoDTO outApplyInfoDTO) {
        OrgApplySampleMainDTO.OrgApplySampleMainDTOBuilder builder = OrgApplySampleMainDTO.builder();
        builder.barcode(outApplyInfoDTO.getBarcode())
                .hspOrgCode(outApplyInfoDTO.getHspOrgCode())
                .hspOrgName(outApplyInfoDTO.getHspOrgName())
                .orgCode("")
                .orgName("")
                .applyType(outApplyInfoDTO.getApplyType())
                .patientVisitCard(outApplyInfoDTO.getPatientVisitCard())
                .urgent(outApplyInfoDTO.getUrgent())
                .sampleType(outApplyInfoDTO.getSampleType())
                // .sampleProperty()
                .dept(outApplyInfoDTO.getDept())
                .inpatientArea(outApplyInfoDTO.getInpatientArea())
                .patientName(outApplyInfoDTO.getPatientName())
                .patientSex(outApplyInfoDTO.getPatientSex())
                .patientAge(outApplyInfoDTO.getPatientAge())
                .patientSubage(outApplyInfoDTO.getPatientSubage())
                .patientSubageUnit(outApplyInfoDTO.getPatientSubageUnit())
                .patientBirthday(outApplyInfoDTO.getPatientBirthday())
                .patientBed(outApplyInfoDTO.getPatientBed())
                .clinicalDiagnosis(outApplyInfoDTO.getClinicalDiagnosis())
                .patientCard(outApplyInfoDTO.getPatientCard())
                .patientCardType(outApplyInfoDTO.getPatientCardType())
                .patientAddress(outApplyInfoDTO.getPatientAddress())
                .patientMobile(outApplyInfoDTO.getPatientMobile())
                .sendDoctor(outApplyInfoDTO.getSendDoctor())
                .applyDate(Objects.requireNonNullElse(outApplyInfoDTO.getApplyDate(), new Date()))
                .samplingDate(outApplyInfoDTO.getSamplingDate())
                .remark(outApplyInfoDTO.getRemark())
                .status(ApplySampleStatus.UN_SIGN.getValue())
                .sampleSource(SampleSource.MIDDLE.getValue());
        ;
        OrgApplySampleMainDTO orgApplySampleMainDTO = builder.build();
        //送检项目
        List<OrgApplySampleMainItemDTO> sampleMainItems = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(outApplyInfoDTO.getItems())) {
            for (OutApplyInfoDTO.OutApplyItem outApplyItem : outApplyInfoDTO.getItems()) {
                OrgApplySampleMainItemDTO item = new OrgApplySampleMainItemDTO();
                item.setBarcode(outApplyInfoDTO.getBarcode());
                item.setHspOrgCode(outApplyInfoDTO.getHspOrgCode());
                item.setHspOrgName(outApplyInfoDTO.getHspOrgName());
                item.setOutTestItemCode(outApplyItem.getOutTestItemCode());
                item.setOutTestItemName(outApplyItem.getOutTestItemName());
                sampleMainItems.add(item);
            }
        }
        orgApplySampleMainDTO.setSampleMainItems(sampleMainItems);
        return orgApplySampleMainDTO;
    }

    /**
     * 回传结果
     */
    public void notifyResult(LisTestMainRequest request) {
        final String barcode = request.getBarcode();
        final String limsBarcode = request.getOutBarcode();
        try {
            final JSONObject applyInfo = queryApply(barcode);
            final Object hisItems = applyInfo.getByPath("OrderList.OderItem");
            final List<OutApplyInfoDTO.OutApplyItem> hisItemList = new ArrayList<>();
            if (hisItems instanceof List) {
                JSONArray jsonArray = JSONUtil.parseArray(hisItems);
                List<OutApplyInfoDTO.OutApplyItem> itemList = jsonArray.stream().parallel().map(this::setLisItem).collect(Collectors.toList());
                hisItemList.addAll(itemList);
            } else {
                hisItemList.add(setLisItem(hisItems));
            }
            final Map<String, String> hisOrderIdMap = hisItemList.stream().collect(Collectors.toMap(
                    OutApplyInfoDTO.OutApplyItem::getOutTestItemCode, OutApplyInfoDTO.OutApplyItem::getHisOrderID, (v1, v2) -> v1));

            StringBuilder sb = new StringBuilder();
            sb.append("<Request>");
            sb.append("<ReportMsg>");
            // sb.append("<OrdID>").append(hisItemList.get(0).getHisOrderID()).append("</OrdID>"); // his医嘱ID ，申请单ID 移到了<ResultMsgs>中
            sb.append("<PDFFile>").append(PdfUtils.mergeAndConvertBase64(request.getReportUrls())).append("</PDFFile>"); // PDF地址
            sb.append("<RecUserCode>").append(request.getRecUserName()).append("</RecUserCode>"); // 接收人工号
            sb.append("<RecUser>").append(request.getRecUserName()).append("</RecUser>"); // 接收人
            sb.append("<RecDate>").append(DateUtil.format(request.getReceiveDate(), DatePattern.NORM_DATE_PATTERN)).append("</RecDate>"); // 接收日期
            sb.append("<RecTime>").append(DateUtil.format(request.getReceiveDate(), DatePattern.NORM_TIME_PATTERN)).append("</RecTime>"); // 接收时间
            sb.append("<EntryUserCode>").append(request.getOneCheckUserName()).append("</EntryUserCode>"); // 初审人工号
            sb.append("<EntryUser>").append(request.getOneCheckUserName()).append("</EntryUser>"); // 初审人
            sb.append("<EntryDate>").append(DateUtil.format(request.getOneCheckDate(), DatePattern.NORM_DATE_PATTERN)).append("</EntryDate>"); // 初审日期
            sb.append("<EntryTime>").append(DateUtil.format(request.getOneCheckDate(), DatePattern.NORM_TIME_PATTERN)).append("</EntryTime>"); // 初审时间
            sb.append("<AuthUserCode>").append(request.getTwoCheckUserName()).append("</AuthUserCode>"); // 审核人工号
            sb.append("<AuthUser>").append(request.getTwoCheckUserName()).append("</AuthUser>"); // 审核人
            sb.append("<AuthDate>").append(DateUtil.format(request.getTwoCheckDate(), DatePattern.NORM_DATE_PATTERN)).append("</AuthDate>"); // 审核日期
            sb.append("<AuthTime>").append(DateUtil.format(request.getTwoCheckDate(), DatePattern.NORM_TIME_PATTERN)).append("</AuthTime>"); // 审核时间
            sb.append("<Notes>").append("</Notes>"); // 申请备注
            sb.append("<ImageFile>").append("</ImageFile>"); // 报告图形地址
            sb.append("<MainWarnDesc>").append("</MainWarnDesc>"); // 报告评价
            sb.append("<LabNo>").append(barcode).append("</LabNo>"); // 条码号
            sb.append("<SourceSystem>").append("LW_LIMS").append("</SourceSystem>");
            // 审核状态C取消审核/A审核
            sb.append("<ReportStatus>").append("A").append("</ReportStatus>");
            // 工作小组代码，仪器代码（用于区分常规 LW/病理检验 CXX）
            sb.append("<WorkGroupMachineDR>").append(WorkGroupMachineDR.ROUTINE).append("</WorkGroupMachineDR>");

            // ------------------------------------------------样本结果
            // 常规检验的结果
            List<Object> testResults = request.getTestResults();
            List<LisResultRequest> lisResultRequests = JSON.parseArray(JSON.toJSONString(testResults), LisResultRequest.class);
            sb.append("<ResultMsgs>");
            for (LisResultRequest result : lisResultRequests) {
                sb.append("<ResultMsg>");
                sb.append("<LabNo>").append(barcode).append("</LabNo>");
                sb.append("<WorkGroupMachineDR>").append(WorkGroupMachineDR.ROUTINE).append("</WorkGroupMachineDR>"); // 工作小组代码，仪器代码
                // sb.append("<>").append(result.getOutTestitemName()).append("</>");
                sb.append("<TestSet>").append(result.getOutTestitemCode()).append("</TestSet>");
                sb.append("<OrdID>").append(hisOrderIdMap.get(result.getOutTestitemCode())).append("</OrdID>"); // his医嘱ID ，申请单ID
                sb.append("<TestCode>").append(escapeXml(result.getItemCode())).append("</TestCode>");
                sb.append("<TestName>").append(escapeXml(result.getItemName())).append("</TestName>");
                sb.append("<TestEngName></TestEngName>");
                if (StringUtils.isBlank(result.getResult())) {
                    sb.append("<Result>").append("报告详见PDF").append("</Result>");
                } else {
                    sb.append("<Result>").append(escapeXml(result.getResult())).append("</Result>");
                }
                sb.append("<Units>").append(escapeXml(result.getUnit())).append("</Units>");
                sb.append("<Notes>").append("</Notes>");
                sb.append("<ResultFlag>").append(escapeXml(transterFlag(result.getFlag(), result.getResultStatus()))).append("</ResultFlag>");
                sb.append("<Ranges>").append(escapeXml(result.getRange())).append("</Ranges>");
                sb.append("<Sequence>").append(result.getShowNo()).append("</Sequence>");
                sb.append("<MICFlag>").append("N").append("</MICFlag>");
                sb.append("<MICNAME>").append("</MICNAME>");
                sb.append("<WarnDesc>").append("</WarnDesc>");
                sb.append("</ResultMsg>");
            }
            sb.append("</ResultMsgs>");
            // ------------------------------------------------样本结果

            sb.append("</ReportMsg>");

            sb.append("</Request>");

            final String reportResult = doWsRequest(NOTIFY_RESULT, String.format(NOTIFY_RESULT_REQ, sb.toString()), funNotifyResult);
            JSONObject resJson = JSONUtil.parseObj(reportResult);
            if (Objects.equals("0", resJson.getByPath("Response.Header.ResultCode", String.class))) {
                // 回传成功，删除缓存
                deleteCache(barcode);
            } else {
                log.error("机构编码 [{}]，实验室条码号 [{}]，医院条码号 [{}]，回传结果失败", request.getHspOrgCode(), limsBarcode, barcode);
                notifyUtil.notifyByRegion(Region.danyang.name(), StrUtil.format("机构编码 [{}]，实验室条码号 [{}]，医院条码号 [{}]，回传结果失败 [{}]", request.getHspOrgCode(), limsBarcode, barcode, reportResult));
            }
        } catch (Exception e) {
            log.error("机构编码 [{}]，实验室条码号 [{}]，医院条码号 [{}]，回传结果异常 ", request.getHspOrgCode(), limsBarcode, barcode, e);
            notifyUtil.notifyByRegion(Region.danyang.name(), StrUtil.format("机构编码 [{}]，实验室条码号 [{}]，医院条码号 [{}]，回传结果异常 {}", request.getHspOrgCode(), limsBarcode, barcode, e));
        }
    }

    /**
     * 转义xml字符
     */
    public static String escapeXml(String input) {
        if (input == null) {
            return "";
        }
        return input.replace("&", "&amp;")
                .replace("<", "&lt;")
                .replace(">", "&gt;")
                .replace("\"", "&quot;")
                .replace("'", "&apos;");
    }

    /**
     * 文字正常N 异常A
     * 数字 H L N PH PL S(文本危急值)
     * @param flag 高、低、正常
     * @param resultStatus 结果状态(0:正常 1：异常  2：危急值)
     */
    protected String transterFlag(String flag, Integer resultStatus) {
        if (StringUtils.isBlank(flag)) {
            return "";
        }
        if (Objects.equals(2, resultStatus)) {
            return "S";
        }
        if (TestJudgeEnum.UP.getValue().equals(flag) || TestJudgeEnum.UP.getFlag().equals(flag)) {
            return "H";
        } else if (TestJudgeEnum.DOWN.getValue().equals(flag) || TestJudgeEnum.DOWN.getFlag().equals(flag)) {
            return "L";
        } else if (TestJudgeEnum.DEFAULT.getValue().equals(flag) || TestJudgeEnum.DEFAULT.getFlag().equals(flag)) {
            return "";
        }

        return "";
    }

    protected String transSex(String patientSex) {
        if (StringUtils.isBlank(patientSex)) {
            return "不详";
        } else if ("1".equals(patientSex)) {
            return "男";
        } else if ("0".equals(patientSex)) {
            return "女";
        }
        return "不详";
    }

    private String getFileNameByUrl(String url) {
        String name = "";
        if (StringUtils.isNotBlank(url)) {
            String[] split = url.split("/");
            name = split[split.length - 1] + ".pdf";
        }
        return name;
    }

    protected String pdfToBase64(String link) {
        try {
            URL url = new URL(link);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            try (InputStream inputStream = connection.getInputStream()) {
                byte[] bytes = inputStream.readAllBytes();
                return Base64.getEncoder().encodeToString(bytes);
            }
        } catch (Exception e) {
            log.error("pdf转Base64异常 URL [{}] ", link, e);
            throw new RuntimeException(e);
        }
    }

    public String removeNewLine(String str) {
        if (StringUtils.isBlank(str)) {
            return com.swak.frame.util.StringPool.EMPTY;
        }

        // 把base64 的 PDF 转换成 obs Url
        if (str.contains("</PDFFile>")) {
            str = replaceBase64(str, "PDFFile");
        }

        return str.replaceAll(com.swak.frame.util.StringPool.NEW_LINE, com.swak.frame.util.StringPool.EMPTY)
                .replace(com.swak.frame.util.StringPool.RETURN, com.swak.frame.util.StringPool.EMPTY)
                .replace(com.swak.frame.util.StringPool.RETURN_NEW_LINE, StringPool.EMPTY);
    }

    private String replaceBase64(String str, String tag) {
        try {
            final String picContent = RegexUtils.xmlExtract(str, tag);
            String replacement = obsUtil.uploadText(picContent, UUIDHexGenerator.generator() + ".txt", 30);
            return RegexUtils.xmlReplace(str, tag, replacement);
        } catch (Exception e) {
            return str;
        }
    }

    public static Date parseDateTime(String strDate) {
        if (DatePattern.NORM_DATETIME_PATTERN.length() == strDate.length()) {
            return DateUtil.parse(strDate, DatePattern.NORM_DATETIME_PATTERN);
        }
        if ("yyyyMMdd HH:mm:ss".length() == strDate.length()) {
            return DateUtil.parse(strDate, "yyyyMMdd HH:mm:ss");
        }
        try {
            return DateUtil.parseDateTime(strDate);
        } catch (Exception e) {
            log.error("日期时间解析异常 参数 [{}] ", strDate, e);
            return null;
        }
    }

    @Data
    @Component
    @RefreshScope
    @ConfigurationProperties("hsp.sanyuan")
    public static class SanyuanConfig {
        private List<String> hspOrgCodes = List.of("SANYUAN");
        /**
         * 旧的：http://221.230.51.5:8888/imedicallis/service/asmx/wbsDHCLISServiceHandler.asmx?wsdl
         */
        private String url = "http://221.230.51.5:8888/csp/hsb/DHC.Published.PUB0003.BS.PUB0003.CLS";
    }

    /**
     * 就诊类型 I是住院，O是门诊，E是急诊，H是体检
     */
    public static class AdmissionType {
        public static final String MZ_CODE = "O";
        public static final String MZ = "门诊";
        public static final String ZY_CODE = "I";
        public static final String ZY = "住院";
        public static final String JZ_CODE = "E";
        public static final String JZ = "急诊";
        public static final String TJ_CODE = "H";
        public static final String TJ = "住院";
    }

    /**
     * LW 检验外送LW
     * CXX 病理外送
     */
    public static class WorkGroupMachineDR {
        /**
         * 常规检验
         */
        public static final String ROUTINE = "LW";
        /**
         * 病理
         */
        public static final String PATHOLOGY = "CXX";
    }

}
