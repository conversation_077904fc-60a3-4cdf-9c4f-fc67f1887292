package com.labway.busniess.center.danyang.middlebase.jianyan.service.impl;

import cn.hutool.json.JSONObject;
import com.labway.business.center.compare.dto.OutApplyInfoDTO;
import com.labway.business.center.compare.dto.TbPathologyApplyMainDTO;
import com.labway.business.center.compare.dto.middlebase.OrgApplySampleMainDTO;
import com.labway.business.center.compare.request.SendApplyFormInfoRequest;
import com.labway.busniess.center.danyang.middlebase.jianyan.service.AbstractSanyuanService;
import com.labway.busniess.center.danyang.middlebase.jianyan.service.ApplyService;
import com.labway.busniess.center.middlebase.jianyan.reqeust.QueryApplyRequest;
import com.labway.busniess.center.middlebase.jianyan.reqeust.QueryOutApplyRequest;
import com.labway.busniess.center.middlebase.jianyan.reqeust.SignApplyInfoRequest;
import com.labway.busniess.center.middlebase.jianyan.reqeust.SignCancelApplyInfoRequest;
import com.swak.frame.dto.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <pre>
 * SanyuanApplyService
 * 丹阳 三院
 * </pre>
 *
 * <AUTHOR>
 * @since 2025/4/7 14:27
 */
@Slf4j
@Service
public class SanyuanApplyService extends AbstractSanyuanService implements ApplyService {

    @Override
    public Set<String> getHspOrgCodes() {
        return new HashSet<>(sanyuanConfig.getHspOrgCodes());
    }

    @Override
    public Response<OutApplyInfoDTO> get(QueryApplyRequest request) {
        try {
            final JSONObject dataRow = queryApply(request.getBarcode());
            OutApplyInfoDTO outApplyInfoDTO = convertOutApplyInfoDTO(dataRow, request.getHspOrgCode());
            outApplyInfoDTO.setBarcode(request.getBarcode());
            return Response.success(outApplyInfoDTO);
        } catch (ParseException e) {
            log.error("三院扫码异常 条码号 [{}] ", request.getBarcode(), e);
            return Response.fail(-1, "获取信息失败");
        }
    }

    @Override
    public Response<TbPathologyApplyMainDTO> getForPathology(QueryApplyRequest request) {
        return Response.success();
    }

    @Override
    public Response<List<OutApplyInfoDTO>> selectOutApplyInfo(QueryOutApplyRequest request) {
        return Response.success();
    }

    @Override
    public Response<OrgApplySampleMainDTO> sign(SignApplyInfoRequest request) {
        try {
            final OutApplyInfoDTO outApplyInfoDTO = convertOutApplyInfoDTO(queryApply(request.getBarcode()), request.getHspOrgCode());
            outApplyInfoDTO.setBarcode(request.getBarcode());
            final OrgApplySampleMainDTO orgApplySampleMainDTO = transferOutApplyInfoDTO(outApplyInfoDTO);
            return Response.success(orgApplySampleMainDTO);
        } catch (Exception e) {
            log.error("三院签收异常 条码号 [{}]", request.getBarcode(), e);
            return Response.fail(-1, "签收失败");
        }
    }

    @Override
    public Response<?> cancelSign(SignCancelApplyInfoRequest request) {
        return Response.success();
    }

    @Override
    public Response<?> pushSampleInfo(SendApplyFormInfoRequest request) {
        return Response.success();
    }

    @Override
    public Response<?> serviceMonitor() {
        return Response.success();
    }

}
