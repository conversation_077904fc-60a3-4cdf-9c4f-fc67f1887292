package com.labway.busniess.center.danyang.middlebase.jianyan.pathology;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;



@NoArgsConstructor
@AllArgsConstructor
@Data
public class PushApplyMainRequest implements Serializable {

    // 申请机构id
    private String hspOrgCode;
    // 条码号（实验室）
    private String barcode;
    // 条码号（医院）
    private String outBarcode;

    // 报告类型 报告类型：1常规 2冰冻 3补充
    private Integer reportType;
    //报告唯一编号
    private String resultUniqueCode;
    // 检验机构名称
    private String testOrgName;
    // 检验机构id
    private String testOrgId;
    // 病理科
    private String fBlk;
    // 病理号
    private String fBlh;
    // 病人编号
    private String fBrbh;
    // 申请序号
    private String fSqxh;
    // 医嘱id
    private String fYzid;
    // 医嘱项目
    private String fYzxm;
    // 姓名
    private String fXm;
    // 性别
    private String fXb;
    // 年龄
    private String fNl;
    // 岁数
    private Integer fAge;
    // 名族
    private String fMz;
    //身份证号
    private String fSfzh;
    //联系信息
    private String fLxxx;
    //病人类别
    private String fBrlb;
    //费别
    private String fFb;
    //住院号
    private String fZyh;
    //门诊号
    private String fMzh;
    //病区
    private String fBq;
    //送检科室
    private String fSjks;
    //床号
    private String fCh;
    //送检单位
    private String fSjdw;
    //送检医生
    private String fSjys;
    //接收员
    private String fJsy;
    //标本类型
    private String fBblx;
    //标本名称
    private String fBbmc;
    //临床诊断
    private String fLczd;
    // 临床资料
    private String fLczl;
    //取材医生
    private String fQcys;
    //取材日期
    private String fQcrq;
    //记录员
    private String fJly;
    // 肉眼所见
    private String fRysj;
    //镜像所见
    private String fJxsj;
    //病理诊断
    private String fBlzd;
    //报告医生
    private String fBgys;
    //审核医生
    private String fShys;
    //报告日期
    private String fBgrq;
    //审核时间
    private String fSpare5;
    //原病理号
    private String fYblh;
    //末次月经
    private String fMcyj;
    //是否绝经
    private String fSfjj;
    //报告序号
    private Integer fBgxh;
    //报告收到时间
    private String fSdrq;
    // 报告迟发原因
    private String fCfyy;
    //文件内容
    private String fileBlob;
    //文件类型（pdf,xml）
    private String fileType;
    // his申请单号
    private String hisReqno;
    // 病理图片
    private List<PathologyImgRequest> txList;
    // 病理图片url
    private String imgUrls;


    public static class PathologyImgRequest implements Serializable{

        // 图像Base64
        private String txBlob;

        // 图像后缀名 (.bmp)
        private String txKzm;


        public PathologyImgRequest() {
        }

        public void setTxBlob(String txBlob) {
            this.txBlob = txBlob;
        }

        public String getTxBlob() {
            return txBlob;
        }

        public String getTxKzm() {
            return txKzm;
        }

        public void setTxKzm(String txKzm) {
            this.txKzm = txKzm;
        }

    }

}
