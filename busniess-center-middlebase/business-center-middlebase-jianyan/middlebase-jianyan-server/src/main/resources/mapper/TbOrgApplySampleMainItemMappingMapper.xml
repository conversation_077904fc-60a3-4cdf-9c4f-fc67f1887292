<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.busniess.center.middlebase.jianyan.persistence.mapper.TbOrgApplySampleMainItemMappingMapper">

    <resultMap type="com.labway.busniess.center.middlebase.jianyan.persistence.entity.TbOrgApplySampleMainItemMapping" id="TbOrgApplySampleMainItemMappingMap">
        <result property="mappingId" column="mapping_id" jdbcType="VARCHAR"/>
        <result property="mainId" column="main_id" jdbcType="VARCHAR"/>
        <result property="mainItemId" column="main_item_id" jdbcType="VARCHAR"/>
        <result property="outTestItemCode" column="out_test_item_code" jdbcType="VARCHAR"/>
        <result property="outTestItemName" column="out_test_item_name" jdbcType="VARCHAR"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="deleteFlag" column="delete_flag" jdbcType="INTEGER"/>
        <result property="testItemCode" column="test_item_code" jdbcType="VARCHAR"/>
        <result property="testItemName" column="test_item_name" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="mappingId" useGeneratedKeys="true">
        insert into tb_org_apply_sample_main_item_mapping(mapping_id, main_id, main_item_id, out_test_item_code, out_test_item_name, create_by, create_time, update_by, update_time, delete_flag,test_item_code,test_item_name)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.mappingId}, #{entity.mainId}, #{entity.mainItemId}, #{entity.outTestItemCode}, #{entity.outTestItemName}, #{entity.createBy}, #{entity.createTime}, #{entity.updateBy}, #{entity.updateTime}, #{entity.deleteFlag}, #{entity.testItemCode}, #{entity.testItemName})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="mappingId" useGeneratedKeys="true">
        insert into business-account.tb_org_apply_sample_main_item_mapping(main_id, item_id, out_test_item_code, out_test_item_name, create_by, create_time, update_by, update_time, delete_flag)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.mainId}, #{entity.itemId}, #{entity.outTestItemCode}, #{entity.outTestItemName}, #{entity.createBy}, #{entity.createTime}, #{entity.updateBy}, #{entity.updateTime}, #{entity.deleteFlag})
        </foreach>
        on duplicate key update
         main_id = values(main_id) , item_id = values(item_id) , out_test_item_code = values(out_test_item_code) , out_test_item_name = values(out_test_item_name) , create_by = values(create_by) , create_time = values(create_time) , update_by = values(update_by) , update_time = values(update_time) , delete_flag = values(delete_flag)     </insert>

</mapper>

