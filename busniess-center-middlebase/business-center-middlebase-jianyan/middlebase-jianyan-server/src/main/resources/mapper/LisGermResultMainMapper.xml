<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.busniess.center.middlebase.jianyan.persistence.mapper.LisGermResultMainMapper">

    <resultMap id="BaseResultMap" type="com.labway.busniess.center.middlebase.jianyan.persistence.entity.LisGermResultMain">
            <result property="barcode" column="BARCODE" jdbcType="VARCHAR"/>
            <result property="machineCode" column="MACHINE_CODE" jdbcType="VARCHAR"/>
            <result property="sampleNo" column="SAMPLE_NO" jdbcType="VARCHAR"/>
            <result property="testDate" column="TEST_DATE" jdbcType="VARCHAR"/>
            <result property="testitemCode" column="TESTITEM_CODE" jdbcType="VARCHAR"/>
            <result property="testitemName" column="TESTITEM_NAME" jdbcType="VARCHAR"/>
            <result property="trainResult" column="TRAIN_RESULT" jdbcType="VARCHAR"/>
            <result property="resultType" column="RESULT_TYPE" jdbcType="VARCHAR"/>
            <result property="m1" column="M1" jdbcType="VARCHAR"/>
            <result property="m2" column="M2" jdbcType="VARCHAR"/>
            <result property="whonetAge" column="WHONET_AGE" jdbcType="VARCHAR"/>
            <result property="whonetHsp" column="WHONET_HSP" jdbcType="VARCHAR"/>
            <result property="whonetWardstype" column="WHONET_WARDSTYPE" jdbcType="VARCHAR"/>
            <result property="whonetDept" column="WHONET_DEPT" jdbcType="VARCHAR"/>
            <result property="whonetSamplecause" column="WHONET_SAMPLECAUSE" jdbcType="VARCHAR"/>
            <result property="whonetBloodtype" column="WHONET_BLOODTYPE" jdbcType="VARCHAR"/>
            <result property="whonetWards" column="WHONET_WARDS" jdbcType="VARCHAR"/>
            <result property="whonetSecommnet" column="WHONET_SECOMMNET" jdbcType="VARCHAR"/>
            <result property="germResultMainId" column="GERM_RESULT_MAIN_ID" jdbcType="VARCHAR"/>
            <result property="germCode" column="GERM_CODE" jdbcType="VARCHAR"/>
            <result property="germName" column="GERM_NAME" jdbcType="VARCHAR"/>
            <result property="operType" column="OPER_TYPE" jdbcType="INTEGER"/>
            <result property="rule" column="RULE" jdbcType="VARCHAR"/>
            <result property="trainResultCode" column="TRAIN_RESULT_CODE" jdbcType="VARCHAR"/>
            <result property="germTypeCode" column="GERM_TYPE_CODE" jdbcType="VARCHAR"/>
            <result property="showNumber" column="SHOW_NUMBER" jdbcType="INTEGER"/>
            <result property="germCount" column="GERM_COUNT" jdbcType="VARCHAR"/>
            <result property="germMemoCode" column="GERM_MEMO_CODE" jdbcType="VARCHAR"/>
            <result property="expertcomments" column="EXPERTCOMMENTS" jdbcType="VARCHAR"/>
            <result property="testMainId" column="TEST_MAIN_ID" jdbcType="VARCHAR"/>
            <result property="germEname" column="GERM_ENAME" jdbcType="VARCHAR"/>
            <result property="germEnameSx" column="GERM_ENAME_SX" jdbcType="VARCHAR"/>
            <result property="trainResultno" column="TRAIN_RESULTNO" jdbcType="INTEGER"/>
            <result property="trainResultEname" column="TRAIN_RESULT_ENAME" jdbcType="VARCHAR"/>
            <result property="testitemEnglish" column="TESTITEM_ENGLISH" jdbcType="VARCHAR"/>
            <result property="germMemoEname" column="GERM_MEMO_ENAME" jdbcType="VARCHAR"/>
            <result property="isGroupCheck" column="IS_GROUP__CHECK" jdbcType="INTEGER"/>
            <result property="groupCheckUserCode" column="GROUP_CHECK_USER_CODE" jdbcType="VARCHAR"/>
            <result property="groupCheckUserName" column="GROUP_CHECK_USER_NAME" jdbcType="VARCHAR"/>
            <result property="groupCheckDate" column="GROUP_CHECK_DATE" jdbcType="TIMESTAMP"/>
            <result property="isGroupCheck2" column="IS_GROUP_CHECK2" jdbcType="INTEGER"/>
            <result property="groupCheckUserCode2" column="GROUP_CHECK_USER_CODE2" jdbcType="VARCHAR"/>
            <result property="groupCheckUserName2" column="GROUP_CHECK_USER_NAME2" jdbcType="VARCHAR"/>
            <result property="groupCheckDate2" column="GROUP_CHECK_DATE2" jdbcType="TIMESTAMP"/>
            <result property="testId" column="TEST_ID" jdbcType="INTEGER"/>
            <result property="outTestitemCode" column="OUT_TESTITEM_CODE" jdbcType="VARCHAR"/>
            <result property="isExistcrisis" column="IS_EXISTCRISIS" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        [BARCODE],[MACHINE_CODE],[SAMPLE_NO],
        [TEST_DATE],[TESTITEM_CODE],[TESTITEM_NAME],
        [TRAIN_RESULT],[RESULT_TYPE],[M1],
        [M2],[WHONET_AGE],[WHONET_HSP],
        [WHONET_WARDSTYPE],[WHONET_DEPT],[WHONET_SAMPLECAUSE],
        [WHONET_BLOODTYPE],[WHONET_WARDS],[WHONET_SECOMMNET],
        [GERM_RESULT_MAIN_ID],[GERM_CODE],[GERM_NAME],
        [OPER_TYPE],[RULE],[TRAIN_RESULT_CODE],
        [GERM_TYPE_CODE],[SHOW_NUMBER],[GERM_COUNT],
        [GERM_MEMO_CODE],[EXPERTCOMMENTS],[TEST_MAIN_ID],
        [GERM_ENAME],[GERM_ENAME_SX],[TRAIN_RESULTNO],
        [TRAIN_RESULT_ENAME],[TESTITEM_ENGLISH],[GERM_MEMO_ENAME],
        [IS_GROUP__CHECK],[GROUP_CHECK_USER_CODE],[GROUP_CHECK_USER_NAME],
        [GROUP_CHECK_DATE],[IS_GROUP_CHECK2],[GROUP_CHECK_USER_CODE2],
        [GROUP_CHECK_USER_NAME2],[GROUP_CHECK_DATE2],[TEST_ID],
        [OUT_TESTITEM_CODE],[IS_EXISTCRISIS]
    </sql>

    <insert id="backup">
        INSERT INTO LIS_GERM_RESULT_MAIN_BACKUP(<include refid="Base_Column_List" />)
        SELECT <include refid="Base_Column_List" /> FROM LIS_GERM_RESULT_MAIN
        WHERE TEST_MAIN_ID IN
        <foreach collection="testMainIds" item="testMainId" separator="," open="(" close=")">
            #{testMainId}
        </foreach>
    </insert>

    <insert id="batchSave">
        INSERT INTO LIS_GERM_RESULT_MAIN(<include refid="Base_Column_List" />)
        VALUES
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.barcode},#{entity.machineCode},#{entity.sampleNo},
            #{entity.testDate},#{entity.testitemCode},#{entity.testitemName},
            #{entity.trainResult},#{entity.resultType},#{entity.m1},
            #{entity.m2},#{entity.whonetAge},#{entity.whonetHsp},
            #{entity.whonetWardstype},#{entity.whonetDept},#{entity.whonetSamplecause},
            #{entity.whonetBloodtype},#{entity.whonetWards},#{entity.whonetSecommnet},
            #{entity.germResultMainId},#{entity.germCode},#{entity.germName},
            #{entity.operType},#{entity.rule},#{entity.trainResultCode},
            #{entity.germTypeCode},#{entity.showNumber},#{entity.germCount},
            #{entity.germMemoCode},#{entity.expertcomments},#{entity.testMainId},
            #{entity.germEname},#{entity.germEnameSx},#{entity.trainResultno},
            #{entity.trainResultEname},#{entity.testitemEnglish},#{entity.germMemoEname},
            #{entity.isGroupCheck},#{entity.groupCheckUserCode},#{entity.groupCheckUserName},
            #{entity.groupCheckDate},#{entity.isGroupCheck2},#{entity.groupCheckUserCode2},
            #{entity.groupCheckUserName2},#{entity.groupCheckDate2},#{entity.testId},
            #{entity.outTestitemCode},#{entity.isExistcrisis})
        </foreach>
    </insert>

</mapper>
