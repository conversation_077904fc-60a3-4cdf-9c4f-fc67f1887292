package com.labway.busniess.center.middlebase.jianyan.config;

import com.labway.busniess.center.middlebase.jianyan.config.cxf.InInterceptor;
import com.labway.busniess.center.middlebase.jianyan.config.cxf.OutInterceptor;
import com.labway.busniess.center.middlebase.jianyan.service.HuaYinServiceSoap;
import lombok.extern.slf4j.Slf4j;
import org.apache.cxf.Bus;
import org.apache.cxf.bus.spring.SpringBus;
import org.apache.cxf.jaxws.EndpointImpl;
import org.apache.cxf.transport.servlet.CXFServlet;
import org.springframework.boot.web.servlet.ServletRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import javax.annotation.Resource;
import javax.xml.ws.Endpoint;
import javax.xml.ws.soap.SOAPBinding;
import java.util.HashMap;
import java.util.Map;


/**
 *  cxf 配置类定义外送妇幼webservice服务的发布地址
 */
@Slf4j
@Configuration
public class CxfConfig {

    @Resource
    private HuaYinServiceSoap huaYinServiceSoap;

    @Bean
    public ServletRegistrationBean disServlet() {
        return new ServletRegistrationBean(new CXFServlet(), "/ws/*");
    }

    /**
     * 创建SpringBus对象，并将其命名为"cxf"
     */
    @Bean(name = Bus.DEFAULT_BUS_ID)
    public SpringBus springBus() {
        SpringBus springBus = new SpringBus();
        springBus.getInInterceptors().add(new InInterceptor());
        springBus.getOutInterceptors().add(new OutInterceptor());
        // springBus.getOutInterceptors().add(new RemoveNamespaceOutInterceptor());
        return springBus;
    }

    /**
     * 创建Endpoint对象，并将其发布到"Applymainservice.asmx"路径下
     * Endpoint对象用于发布WebService服务
     */
    @Bean
    public Endpoint huaYinEndpoint() {
        EndpointImpl endpoint = new EndpointImpl(springBus(), huaYinServiceSoap);
        // 设置使用 SOAP 1.2 协议
        endpoint.setBindingUri(SOAPBinding.SOAP12HTTP_BINDING);

        // 添加以下配置去除命名空间前缀
        Map<String, Object> properties = new HashMap<>();
        properties.put("soap.force.doclit.bare", true);
        properties.put("disable.outputstream.optimization", true);
        endpoint.setProperties(properties);

        endpoint.publish("/HuaYinService.asmx");
        log.info("服务发布成功！地址为：http://localhost:9880/ws/HuaYinService.asmx?wsdl");
        return endpoint;
    }
}
