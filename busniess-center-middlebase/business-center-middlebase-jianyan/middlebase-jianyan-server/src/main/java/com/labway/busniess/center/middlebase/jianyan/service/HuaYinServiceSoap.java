package com.labway.busniess.center.middlebase.jianyan.service;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import javax.jws.soap.SOAPBinding;

/**
 * <pre>
 * HuaYinServiceSoap
 * 广州华银webservice接口
 * </pre>
 *
 * <AUTHOR>
 * @since 2025/5/8 10:52
 */
@WebService(name = "HuaYinServiceSoap", targetNamespace = HuaYinServiceSoap.targetNamespace)
@SOAPBinding(style = SOAPBinding.Style.DOCUMENT)
public interface HuaYinServiceSoap {
    /**
     * 目标命名空间
     */
    String targetNamespace =  "http://tempuri.org/";

    String matherAction = targetNamespace;

    /**
     * 通过条形码获取申请主要信息
     * @param HospSampleID 条码号
     * @return 申请主要信息的字符串形式 主要是返回的xml字符类型的
     */
    @WebMethod(action = matherAction + "GetLisRequest")
    @WebResult(name = "GetLisRequestResult")
    String GetLisRequest(@WebParam(name = "HospSampleID", targetNamespace = HuaYinServiceSoap.targetNamespace) String HospSampleID);

    /**
     * 确认获取标本信息成功接口
     * @param HospSampleID 条码号
     * @return 申请主要信息的字符串形式 主要是返回的xml字符类型的
     */
    @WebMethod(action = matherAction + "AffirmRequest")
    @WebResult(name = "AffirmRequestResult")
    String AffirmRequest(@WebParam(name = "HospSampleID", targetNamespace = HuaYinServiceSoap.targetNamespace) String HospSampleID);

    /**
     * 临检项目结果回传服务接口
     * @param resultXML 回传结果参数
     * @return 申请主要信息的字符串形式 主要是返回的xml字符类型的
     */
    @WebMethod(action = matherAction + "UploadLisRepData")
    @WebResult(name = "UploadLisRepDataResult")
    String UploadLisRepData(@WebParam(name = "resultXML", targetNamespace = HuaYinServiceSoap.targetNamespace) String resultXML) throws Exception;

    /**
     * 微生物项目结果回传接口
     * @param resultXML 回传结果参数
     * @return 申请主要信息的字符串形式 主要是返回的xml字符类型的
     */
    // @WebMethod(action = matherAction + "UploadLisBacteriaRepData")
    // @WebResult(name = "UploadLisBacteriaRepDataResult")
    // String UploadLisBacteriaRepData(@WebParam(name = "resultXML", targetNamespace = HuaYinServiceSoap.targetNamespace) String resultXML) throws Exception;

}
