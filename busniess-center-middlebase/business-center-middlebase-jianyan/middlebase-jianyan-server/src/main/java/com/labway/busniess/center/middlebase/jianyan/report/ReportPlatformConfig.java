package com.labway.busniess.center.middlebase.jianyan.report;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * <pre>
 * ReportPlatformConfig
 * 描述信息
 * </pre>
 *
 * <AUTHOR>
 * @since 2025/1/22 17:46
 */
@Data
@RefreshScope
@Configuration
@ConfigurationProperties(prefix = "report")
public class ReportPlatformConfig {

    // @Value("${report.platform.url}")
    private String platformUrl;

    // @Value("${report.result.authorization:98223935be704843b6ed99ef0c0f02cb}")
    private String authorization = "98223935be704843b6ed99ef0c0f02cb";

    /**
     * 推送结果到报告平台请求path
     */
    private String pushReportRequestPath = "/sample/biz/upload-result/upload";

}
