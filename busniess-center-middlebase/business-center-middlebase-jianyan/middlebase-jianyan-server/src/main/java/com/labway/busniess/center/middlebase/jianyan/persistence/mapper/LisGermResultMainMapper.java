package com.labway.busniess.center.middlebase.jianyan.persistence.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.labway.busniess.center.middlebase.jianyan.persistence.entity.LisGermResultMain;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【LIS_GERM_RESULT_MAIN】的数据库操作Mapper
* @createDate 2023-07-12 21:18:17
*/
@Mapper
public interface LisGermResultMainMapper extends BaseMapper<LisGermResultMain> {

    int backup(@Param("testMainIds") List<String> testMainIds);

    int batchSave(@Param("entities") List<LisGermResultMain> entities);

}
