package com.labway.busniess.center.middlebase.jianyan.report;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.labway.business.center.core.enums.ResultCode;
import com.swak.frame.dto.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import static com.labway.business.center.core.enums.ResultCode.REPORT_RESULT_QUERY_ERROR;
import static com.labway.business.center.core.enums.ResultCode.REPORT_RESULT_QUERY_NULL;

/**
 * <pre>
 * ReportPlatformClient
 * 报告平台Client
 * </pre>
 *
 * <AUTHOR>
 * @since 2025/1/22 17:23
 */
@Slf4j
@Component
public class ReportPlatformClient {

    @Autowired
    private ReportPlatformConfig reportPlatformConfig;

    /**
     * 报告平台查询成功code
     */
    private static final String REPORT_QUERY_SUCCESS_CODE = "0";

    @SuppressWarnings("unchecked")
    public <T, R> Response<R> doCall(String requestPath, T param, Class<R> clazz) {
        try {
            // 参数封装
            Map<String, String> paramMap = new HashMap<>();
            paramMap.put("data", ReportDecryptUtil.encrypt(JSON.toJSONString(param)));
            paramMap.put("type", "aes");
            String jsonString = JSONObject.toJSONString(paramMap);

            log.info("调用报告平台请求参数 加密前 [{}] 加密后 [{}]", JSON.toJSONString(param), jsonString);
            // 向报告平台发起http请求
            JSONObject bodyJsonObject;
            try (HttpResponse response = HttpRequest.post(reportPlatformConfig.getPlatformUrl() + requestPath)
                    .body(jsonString)
                    .header(ReportDecryptUtil.TOKEN_KEY, reportPlatformConfig.getAuthorization())
                    .execute()) {

                bodyJsonObject = JSONObject.parseObject(response.body());
                String data = bodyJsonObject.getString("data");

                String decrypt = ReportDecryptUtil.decrypt(data);
                if (log.isDebugEnabled()) {
                    log.debug("报告平台结果响应 解密后数据：{}", decrypt);
                }

                // 解析为JSON
                JSONObject responseJson = JSONObject.parseObject(decrypt);
                String dataCode = responseJson.getString("code");

                // 检查code
                if (!REPORT_QUERY_SUCCESS_CODE.equals(dataCode)) {
                    return Response.fail(REPORT_RESULT_QUERY_ERROR.getCode(),
                            REPORT_RESULT_QUERY_ERROR.getMsg() + ":" + responseJson.getString("message"));
                }

                // 报告结果字符串
                String dataResult = responseJson.getString("data");
                if (StringUtils.isBlank(dataResult)) {
                    return Response.fail(REPORT_RESULT_QUERY_NULL);
                }

                if (Objects.equals(String.class,  clazz)) {
                    return Response.success((R) dataResult);
                }

                return Response.success(JSONObject.parseObject(dataResult, clazz));
            }

        } catch (Exception e) {
            log.error("调用报告平台接口发生异常，入参：{}， 异常信息：", JSON.toJSONString(param), e);
            return Response.fail(ResultCode.APPLY_RESULT_QUERY_ERROR);
        }
    }

}
