package com.labway.busniess.center.middlebase.jianyan.repository;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.busniess.center.middlebase.jianyan.persistence.entity.LisReportFile;
import com.labway.busniess.center.middlebase.jianyan.persistence.mapper.ReportFileMapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * ResultRepository
 * 结果
 * </p>
 *
 * <AUTHOR>
 * @since 2023/7/13 13:47
 */
@Component
public class ReportFileRepository {

    @Resource
    private ReportFileMapper reportFileMapper;

    public int saveReportFile(LisReportFile entity) {
        return reportFileMapper.insert(entity);
    }

    public int batchSaveReportFiles(List<LisReportFile> entities) {
        int result = 0;
        for (LisReportFile entity : entities) {
            result += reportFileMapper.insert(entity);
        }
        return result;
    }

    public int deleteReportFileByBarcode(String barcode) {
        reportFileMapper.backupByBarcode(barcode);
        return reportFileMapper.delete(
                Wrappers.lambdaQuery(LisReportFile.class).eq(LisReportFile::getBarcode, barcode));
    }

    public int deleteReportFileByTestMainIds(List<String> testMainIds) {
        reportFileMapper.backup(testMainIds);
        return reportFileMapper.delete(
                Wrappers.lambdaQuery(LisReportFile.class).in(LisReportFile::getTestMainId, testMainIds));
    }
}
