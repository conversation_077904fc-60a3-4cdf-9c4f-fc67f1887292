package com.labway.busniess.center.middlebase.jianyan;

import com.labway.business.center.core.config.ObsConfig;
import com.labway.business.center.core.util.NotifyUtil;
import com.labway.business.center.core.util.ObsUtil;
import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.Import;

@Import({ObsUtil.class, ObsConfig.class, NotifyUtil.class})
@EnableDubbo
@SpringBootApplication()
@EnableAspectJAutoProxy(exposeProxy = true)
public class MiddlebaseJianyanApplication {

    public static void main(String[] args) {
        SpringApplication.run(MiddlebaseJianyanApplication.class, args);
    }

}
