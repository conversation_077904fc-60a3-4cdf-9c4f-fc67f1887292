package com.labway.busniess.center.middlebase.jianyan.persistence.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 * @TableName LIS_GERM_RESULT
 */
@TableName(value ="LIS_GERM_RESULT")
@Data
public class LisGermResult implements Serializable {
    /**
     * 仪器编码
     */
    private String machineCode;

    /**
     * 样本单号
     */
    private String sampleNo;

    /**
     * 检验日期,做主键之一
     */
    private String testDate;

    /**
     * 抗生素编码
     */
    private String antiCode;

    /**
     * 抗生素名称
     */
    private String antiName;

    /**
     * 结果
     */
    private String result;

    /**
     * 标记
     */
    private String flag;

    /**
     * 上限
     */
    private String upValue;

    /**
     * 下限
     */
    private String downValue;

    /**
     * 细菌编码
     */
    private String germRecordId;

    /**
     * 单位
     */
    private String unit;

    /**
     * 检验结果录入时间
     */
    private Date operateDate;

    /**
     * 检验操作方法标志(0:手工.1:仪器MIC法.2:仪器KB法)
     */
    private Integer operType;

    /**
     * 检验方法规则(KB法、MIC法)
     */
    @TableField("[rule]")
    private String rule;

    /**
     * 耐药情况
     */
    private String memo;

    /**
     * 排序
     */
    private Integer showOrder;

    /**
     * 条码编码
     */
    private String barcode;

    /**
     * 主键ID
     */
    private String germResultMainId;

    /**
     * 检验项目编码
     */
    private String testitemCode;

    /**
     * 参考范围
     */
    private String range;

    /**
     * 报告单主键ID
     */
    private String testMainId;

    /**
     * 抗生素英文名称
     */
    private String antiename;

    /**
     * 抗生素英文缩写
     */
    private String antiEnameSx;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

}