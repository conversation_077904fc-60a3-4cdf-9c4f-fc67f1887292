package com.labway.busniess.center.middlebase.jianyan.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.http.HttpDownloader;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.labway.business.center.core.enums.ResultCode;
import com.labway.business.center.core.enums.YesOrNoEnum;
import com.labway.business.center.core.util.PdfUtils;
import com.labway.busniess.center.middlebase.jianyan.aop.SwitchDS;
import com.labway.busniess.center.middlebase.jianyan.config.BusinessConfig;
import com.labway.busniess.center.middlebase.jianyan.converter.ResultConverter;
import com.labway.busniess.center.middlebase.jianyan.enums.IsAdd;
import com.labway.busniess.center.middlebase.jianyan.enums.ItemTypeEnum;
import com.labway.busniess.center.middlebase.jianyan.enums.OnetwoColumn;
import com.labway.busniess.center.middlebase.jianyan.enums.PageKindEnum;
import com.labway.busniess.center.middlebase.jianyan.enums.ReportEnable;
import com.labway.busniess.center.middlebase.jianyan.enums.ReportFileTypeEnum;
import com.labway.busniess.center.middlebase.jianyan.enums.ReportOperType;
import com.labway.busniess.center.middlebase.jianyan.enums.ReportType;
import com.labway.busniess.center.middlebase.jianyan.enums.UseCenterLabTitle;
import com.labway.busniess.center.middlebase.jianyan.persistence.entity.ApplyMain;
import com.labway.busniess.center.middlebase.jianyan.persistence.entity.LisGermResult;
import com.labway.busniess.center.middlebase.jianyan.persistence.entity.LisGermResultMain;
import com.labway.busniess.center.middlebase.jianyan.persistence.entity.LisHiResult;
import com.labway.busniess.center.middlebase.jianyan.persistence.entity.LisReportFile;
import com.labway.busniess.center.middlebase.jianyan.persistence.entity.LisResult;
import com.labway.busniess.center.middlebase.jianyan.persistence.entity.TestMain;
import com.labway.busniess.center.middlebase.jianyan.persistence.param.SearchTestMain;
import com.labway.busniess.center.middlebase.jianyan.repository.ApplyRepository;
import com.labway.busniess.center.middlebase.jianyan.repository.LisGermRepository;
import com.labway.busniess.center.middlebase.jianyan.repository.LisHiResultRepository;
import com.labway.busniess.center.middlebase.jianyan.repository.LisResultRepository;
import com.labway.busniess.center.middlebase.jianyan.repository.LisTestMainRepository;
import com.labway.busniess.center.middlebase.jianyan.repository.ReportFileRepository;
import com.labway.busniess.center.middlebase.jianyan.reqeust.CallbackResultRequest;
import com.labway.busniess.center.middlebase.jianyan.reqeust.LisGermResultMainRequest;
import com.labway.busniess.center.middlebase.jianyan.reqeust.LisGermResultRequest;
import com.labway.busniess.center.middlebase.jianyan.reqeust.LisTestMainRequest;
import com.labway.busniess.center.middlebase.jianyan.service.ResultService;
import com.swak.frame.dto.Response;
import com.swak.frame.util.StringPool;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.aop.framework.AopContext;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <p>
 * ResultServiceImpl
 * 样本检验结果
 * </p>
 *
 * <AUTHOR>
 * @since 2023/7/13 16:12
 */
@Slf4j
@DubboService
public class ResultServiceImpl implements ResultService {

    @Resource
    private ApplyRepository applyRepository;
    @Resource
    private LisResultRepository resultRepository;
    @Resource
    private LisHiResultRepository lisHiResultRepository;
    @Resource
    private LisGermRepository lisGermRepository;
    @Resource
    private LisTestMainRepository testMainRepository;
    @Resource
    private ReportFileRepository reportFileRepository;
    @Resource
    private ResultConverter resultConverter;
    @Resource
    private BusinessConfig businessConfig;
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 接收回传结果（源于LIMS）
     * @param requests {@link LisTestMainRequest}
     * @return
     */
    @Override
    public Response<?> receiveLimsResult(List<LisTestMainRequest> requests) {
        if (log.isDebugEnabled()) {
            log.debug("结果回传【参数：{}】", JSON.toJSONString(requests));
        }

        log.info("结果回传【样本数量：{}】", CollectionUtils.isEmpty(requests) ? 0 : requests.size());

        List<String> errHspOrgCodes = new ArrayList<>();
        requests.stream()
                .collect(Collectors.groupingBy(LisTestMainRequest::getHspOrgCode))
                .forEach((lwHspOrgCode, lisTestMains) -> {
                    for (LisTestMainRequest lisTestMain : lisTestMains) {
                        Response<?> response = ((ResultService) AopContext.currentProxy()).doReceiveLimsResult(lwHspOrgCode, Lists.newArrayList(lisTestMain));
                        if (!response.isSuccess()) {
                            errHspOrgCodes.add(lwHspOrgCode);
                        }
                    }
                });

        if (CollectionUtils.isNotEmpty(errHspOrgCodes)) {
            log.error("结果回传保存异常【兰卫机构编码：{}】", String.join(StringPool.COMMA, errHspOrgCodes));
            return Response.fail(ResultCode.LJMIDDLE_ORG_CODE_RESULT_ERROR);
        }

        return Response.success();
    }

    @SwitchDS("#lwHspOrgCode")
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Response<?> doReceiveLimsResult(String lwHspOrgCode, List<LisTestMainRequest> requests) {
        // 中间库 送检机构编码
        String hspOrgCode = businessConfig.getMappingCode(lwHspOrgCode);
        log.info("结果回传【兰卫编码：{}，中间库编码：{}，参数：{}】", lwHspOrgCode, hspOrgCode, JSON.toJSONString(requests));

        String barcode = "";
        try {
            for (LisTestMainRequest testMainRequest : requests) {
                // 医院条码号
                barcode = testMainRequest.getBarcode();
                // 实验室条码号
                final String limsBarcode = testMainRequest.getOutBarcode();

                // 妇幼，骨伤，为民，中医 这四家二级医院 LIS_TEST_MAIN中barcode实验室条码，outBarcode医院条码
                barcodeSwap(lwHspOrgCode, testMainRequest);

                String groupCode = testMainRequest.getGroupCode();
                String machineCode = testMainRequest.getMachineCode();
                String sampleNo = testMainRequest.getSampleNo();
                String testDate = testMainRequest.getTestDate();
                long emptyCnt =
                        Stream.of(barcode, groupCode, machineCode, sampleNo, testDate)
                                .filter(StringUtils::isEmpty).count();
                if (emptyCnt > 0) {
                    log.error("结果回传，跳过处理【医院条码号：{}，groupCode：{}，machineCode：{}，sampleNo：{}，testDate：{}】",
                            barcode, groupCode, machineCode, sampleNo, testDate);
                    continue;
                }

                ApplyMain applyMain = applyRepository.getApplyMainByBarcodeAndHspOrgCode(barcode, hspOrgCode);
                if (Objects.isNull(applyMain)) {
                    log.error("没有查询到申请单信息【医院条码号：{}，送检机构编码：{}】", barcode, hspOrgCode);
                    throw new RuntimeException(String.format("没有查询到申请单【条码：%s】信息", barcode));
                }

                SearchTestMain.SearchTestMainBuilder searchTestMainBuilder = SearchTestMain.builder();
                searchTestMainBuilder
                        .barcode(testMainRequest.getBarcode())
                        .outBarcode(testMainRequest.getOutBarcode())
                        .hspOrgCode(hspOrgCode)
                        .groupCode(groupCode)
                        .machineCode(machineCode)
                        .sampleNo(sampleNo);
                /*TestMain testMain = testMainRepository.queryTestMain(searchTestMainBuilder.build());
                if (Objects.nonNull(testMain)) {
                    testMainRepository.deleteTestMain(testMain);
                }*/

                testMainRepository.deleteTestMain(testMainRequest.getBarcode(), testMainRequest.getOutBarcode(), hspOrgCode, groupCode, machineCode, sampleNo);

                // 生成testMainId
                String testMainId = String.join(StringPool.UNDERLINE, limsBarcode, sampleNo, testDate);
                // 是否使用中心实验室标题(1是0否，默认为0)
                testMainRequest.setIsusercenterlabtitle(UseCenterLabTitle.YES.getCode());
                // 单双列模式(1为单列，2为双列，默认为单列，值为空也是单列，目前先暂时只支持单列模式)
                testMainRequest.setOnetwocolumn(OnetwoColumn.ONE.getCode());
                testMainRequest.setTestMainId(testMainId);
                // testMainRequest.setOutBarcode(applyMain.getLwBarcode());
                testMainRequest.setHspOrgCode(hspOrgCode);

                TestMain testMain = resultConverter.convertRequest2Entity(testMainRequest);
                testMain.setUploadFlag(1);
                testMain.setUploadDate(new Date());
                // 保存检验主表信息
                int ret = testMainRepository.saveTestMain(testMain);
                log.info("保存检验主表信息【医院条码号：{}，结果：{}】", barcode, ret);

                ResultHandler resultHandler = null;
                // 样本检验类型
                String itemType = testMainRequest.getItemType();
                switch (ItemTypeEnum.getByName(itemType)) {
                    case ROUTINE: // 常规检验
                    case OUTSOURCING: // 外送检验
                        resultHandler = new RoutineResultHandler();
                        break;
                    case INFECTION:
                        // 院感检验
                        resultHandler = new InfectionResultHandler();
                        break;
                    case MICROBIOLOGY:
                    case BLOOD_CULTURE:
                        // 微生物
                        resultHandler = new MicrobiologyResultHandler();
                        break;
                }

                // 保存结果数据
                if (Objects.nonNull(resultHandler)) {
                    int result = resultHandler.doSave(testMainRequest);
                    log.info("保存检验结果【医院条码号：{}，结果：{}】", barcode, result);
                }

                // 删除以前的报告
                reportFileRepository.deleteReportFileByTestMainIds(List.of(testMainId));
                // 保存PDF报告数据
                List<String> reportUrls = testMainRequest.getReportUrls();
                if (CollectionUtils.isNotEmpty(reportUrls)) {
                    for (String reportUrl : reportUrls) {
                        LisReportFile reportFile = buildReportFile(testMainRequest, reportUrl);
                        int result = reportFileRepository.saveReportFile(reportFile);
                        log.info("保存PDF报告【医院条码号：{}，结果：{}，PDF报告：{}】", barcode, result, JSON.toJSONString(reportFile));
                    }

                    // 如果是手动上传的PDF报告，则更新申请单表中的uploadPdf字段
                    if (Objects.equals(YesOrNoEnum.YES.getCode(), testMainRequest.getManualUploadPdf())) {
                        applyMain.setUploadPdf(YesOrNoEnum.YES.getCode());
                        int result = applyRepository.updateUploadPdfByBarcodeAndHspOrgCode(applyMain);
                        log.info("手动上传PDF报告，更新ApplyMain.UploadPdf状态【医院条码号：{}，结果：{}】", barcode, result);
                    }

                }

                // 检验服务如果保存成功了，也放入缓存
                // String uniquerId = sample.getHspOrgCode() + "-" + sample.getOrgId() + "-" + sample.getOutBarcode() + "-" + checkDate;
                String checkDate = DateUtil.formatDateTime(testMainRequest.getCheckDate());
                String uniqueId = testMainRequest.getHspOrgCode() + "-" + testMainRequest.getCenterlaborgcode() + "-" + barcode + "-" + checkDate;
                Long add = stringRedisTemplate.opsForSet().add(getRedisKey(null), uniqueId);
                log.info("结果同步成功，保存redis，key：{} add：{}", uniqueId, add);
            }
        } catch (RuntimeException e) {
            log.error("结果回传，保存至中间库异常，【医院条码号：{}，Error：{}】", barcode, e.getMessage());
            // throw new RuntimeException(e);
        }

        return Response.success();
    }

    /**
     * 结果召回
     * @param request {@link CallbackResultRequest}
     * @return
     */
    @SwitchDS("#request.hspOrgCode")
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Response<?> callbackResult(CallbackResultRequest request) {
        // 医院条码号
        String barcode = request.getBarcode();
        // 兰卫 送检机构编码
        String lwHspOrgCode = request.getHspOrgCode();
        if (businessConfig.isBarcodeSwapLwHspOrgCode(lwHspOrgCode)) {
            barcode = request.getOuterBarcode();
        }
        // 中间库 送检机构编码
        String hspOrgCode = businessConfig.getMappingCode(lwHspOrgCode);
        log.info("送检机构编码对照【兰卫编码：{}，中间库编码：{}】", lwHspOrgCode, hspOrgCode);

        List<TestMain> testMains = testMainRepository.getTestMainByHspOrgCodeAndBarcode(hspOrgCode, barcode);

        if (CollectionUtils.isEmpty(testMains)) {
            log.warn("查询申请单样本信息，【条码号：{}，送检机构编码:{}】，没有查询到样本信息", barcode, hspOrgCode);
            return Response.fail(ResultCode.LJMIDDLE_TEST_MAIN_NOT_EXIST_ERROR);
        }
        List<String> testMainIds = testMains.stream().map(TestMain::getTestMainId).collect(Collectors.toList());

        // 删除样本数据
        testMainRepository.deleteTestMainByTestMainIds(testMainIds);
        // 删除常规检验结果
        resultRepository.deleteResultByTestMainIds(testMainIds);
        // 删除院感结果
        lisHiResultRepository.deleteHiResultByTestMainIds(testMainIds);
        // 微生物结果
        lisGermRepository.deleteGermResultMainByTestMainIds(testMainIds);
        lisGermRepository.deleteGermResultByTestMainIds(testMainIds);
        // pdf 报告数据删除
        reportFileRepository.deleteReportFileByTestMainIds(testMainIds);

        return Response.success();
    }

    public LisReportFile buildReportFile(LisTestMainRequest testMainRequest, String reportUrl) {
        LisReportFile reportFile = new LisReportFile();

        reportFile.setId(UUID.fastUUID().toString());
        // 文件内容
        reportFile.setFileBlob(HttpDownloader.downloadBytes(reportUrl));
        // 文件
        reportFile.setFileName(generateFileName(reportUrl));
        // 文件类型(PDF、XML)
        reportFile.setFileType(ReportFileTypeEnum.PDF.getType());
        // TODO Field 备注
        reportFile.setRemark(null);
        // 机构
        reportFile.setOrgId(testMainRequest.getHspOrgCode());
        // 报告类型(1、LIS，2、PIS)
        reportFile.setReportType(ReportType.LIS.getCodeStr());
        // 是否可用
        reportFile.setIsEnable(ReportEnable.ENABLE.getCode());
        // 操作类型(1、审核，2、取消审核)
        reportFile.setOperType(ReportOperType.AUDIT.getCode());

        // TODO Field 外键 ==> 条码+检验日期+样本号+专业小组
        String outKey =
                String.join(
                        StringPool.UNDERLINE,
                        testMainRequest.getBarcode(),
                        testMainRequest.getTestDate(),
                        testMainRequest.getSampleNo(),
                        testMainRequest.getSpecialMicrogroupCode());
        reportFile.setOutKey(outKey);
        // TODO Field 自定义字段1
        reportFile.setZdy1(null);
        // TODO Field 自定义字段2
        reportFile.setZdy2(null);
        // TODO Field 自定义字段3
        reportFile.setZdy3(null);
        // TODO Field 自定义字段4
        reportFile.setZdy4(null);
        // TODO Field 自定义字段5
        reportFile.setZdy5(null);
        // 实验室条码号
        reportFile.setBarcode(testMainRequest.getBarcode());
        // 医院条码号
        reportFile.setOutBarcode(testMainRequest.getOutBarcode());
        // 报告类型
        reportFile.setPageKind(PdfUtils.getPdfInfo(reportUrl).getPageKind().getType());
        // 报告单主键
        reportFile.setTestMainId(testMainRequest.getTestMainId());

        return reportFile;
    }

    /**
     * 根据报告文件地址截取生成文件名字
     * @param reportUrl 报告文件地址
     * @return
     */
    public String generateFileName(String reportUrl) {
        int indexOf = reportUrl.lastIndexOf(StringPool.FORWARD_SLASH);
        String filaname;
        if (indexOf > 0) {
            filaname = reportUrl.substring(indexOf + 1);
        } else {
            filaname= UUID.randomUUID().toString().replace(StringPool.DASH, StringPool.EMPTY);
        }
        return filaname + StringPool.PERIOD + ReportFileTypeEnum.PDF.getFileExtension();
    }
    
    interface ResultHandler {
        int doSave(LisTestMainRequest testMainRequest);
    }

    /**
     * 常规检验结果处理
     */
    class RoutineResultHandler implements ResultHandler {
        @Override
        public int doSave(LisTestMainRequest testMainRequest) {
            String hspOrgCode = testMainRequest.getHspOrgCode();
            List<Object> testResults = testMainRequest.getTestResults();
            List<LisResult> results = JSON.parseArray(JSON.toJSONString(testResults), LisResult.class);
            results.forEach(e -> {
                e.setTestMainId(testMainRequest.getTestMainId());
                e.setBarcode(testMainRequest.getBarcode());
                if (Objects.isNull(e.getIsAdd())) {
                    e.setIsAdd(IsAdd.NO.getCode());
                }
                if (businessConfig.checkLabPackageIdOrg(hspOrgCode)) {
                    e.setLabPackageId(e.getOutTestitemCode());
                }
            });
            resultRepository.deleteResultByTestMainId(testMainRequest.getTestMainId());
            return resultRepository.batchSaveResult(results);
        }
    }

    /**
     * 院感结果处理
     */
    class InfectionResultHandler implements ResultHandler {
        @Override
        public int doSave(LisTestMainRequest testMainRequest) {
            List<Object> testResults = testMainRequest.getTestResults();
            List<LisHiResult> results = JSON.parseArray(JSON.toJSONString(testResults), LisHiResult.class);
            results.forEach(e -> {
                e.setTestMainId(testMainRequest.getTestMainId());
                e.setBarcode(testMainRequest.getBarcode());
            });
            lisHiResultRepository.deleteHiResultByTestMainIds(Lists.newArrayList(testMainRequest.getTestMainId()));
            return lisHiResultRepository.batchSave(results);
        }
    }

    /**
     * 微生物结果处理
     */
    class MicrobiologyResultHandler implements ResultHandler {
        @Override
        public int doSave(LisTestMainRequest testMainRequest) {
            int result = 0;
            String hspOrgCode = testMainRequest.getHspOrgCode();
            List<Object> testResults = testMainRequest.getTestResults();
            List<LisGermResultMainRequest> results =
                    JSON.parseArray(JSON.toJSONString(testResults), LisGermResultMainRequest.class);
            List<LisGermResultMain> lisGermResultMains =
                    results.stream()
                            .peek(e -> {
                                e.setTestMainId(testMainRequest.getTestMainId());
                                e.setBarcode(testMainRequest.getBarcode());
                                e.setGermResultMainId(e.getGermTypeCode() + UUID.fastUUID());
                                configGermRemark(hspOrgCode, e);
                            }).map(resultConverter::convertGermResultMainRequest2Entity).collect(Collectors.toList());
            lisGermRepository.deleteGermResultMainByTestMainIds(Lists.newArrayList(testMainRequest.getTestMainId()));
            result += lisGermRepository.batchSaveGermResultMain(lisGermResultMains);

            lisGermRepository.deleteGermResultByTestMainIds(Lists.newArrayList(testMainRequest.getTestMainId()));
            for (LisGermResultMainRequest germResultMain : results) {
                List<LisGermResultRequest> germResultRequests = germResultMain.getGermResultRequests();
                List<LisGermResult> germResults =
                        germResultRequests.stream()
                                .peek(e -> e.setBarcode(testMainRequest.getBarcode()))
                                .map(resultConverter::convertGermResultRequest2Entity)
                                .collect(Collectors.toList());
                germResults.forEach(e -> {
                    e.setGermResultMainId(germResultMain.getGermResultMainId());
                    e.setTestMainId(testMainRequest.getTestMainId());
                });
                result += lisGermRepository.batchSaveGermResult(germResults);
            }

            return result;
        }

        public void configGermRemark(String hspOrgCode, LisGermResultMainRequest request) {
            // 目前沟通下来，只有骨伤会用到WHONET_SECOMMNE，且字段长度可以够放细菌备注
            // 在这里处理一下，判断如果 不是骨伤（可以配置） 就将其设置为空
            if (!businessConfig.checkConfigGermRemarkOrg(hspOrgCode)) {
                request.setWhonetSecommnet(null);
            }
        }
    }

    /**
     * 妇幼，骨伤，为民，中医 这四家二级医院 LIS_TEST_MAIN中barcode实验室条码，outBarcode医院条码
     */
    public void barcodeSwap(String lwHspOrgCode, LisTestMainRequest testMainRequest) {
        if (businessConfig.isBarcodeSwapLwHspOrgCode(lwHspOrgCode)) {
            // 回传数据 - 医院条码号
            String barcode = testMainRequest.getBarcode();
            // 回传数据 - 实验室条码号
            String outBarcode = testMainRequest.getOutBarcode();

            // 实验室条码号
            testMainRequest.setBarcode(outBarcode);
            // 医院条码号
            testMainRequest.setOutBarcode(barcode);
        }
    }

}
