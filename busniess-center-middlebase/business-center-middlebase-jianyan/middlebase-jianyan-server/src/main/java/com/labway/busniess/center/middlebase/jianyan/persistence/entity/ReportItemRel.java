package com.labway.busniess.center.middlebase.jianyan.persistence.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 
 * @TableName REPORT_ITEM_REL
 */
@TableName(value ="REPORT_ITEM_REL")
@Data
public class ReportItemRel implements Serializable {
    /**
     * 
     */
    private String detailid;

    /**
     * 
     */
    private String detailcode;

    /**
     * 
     */
    private String detailname;

    /**
     * 
     */
    private String detailen;

    /**
     * 
     */
    private String detailabbrname;

    /**
     * 
     */
    private String detailunit;

    /**
     * 
     */
    private String suitsex;

    /**
     * 
     */
    private String labItemCode;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

}