package com.labway.busniess.center.middlebase.jianyan.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.busniess.center.middlebase.jianyan.persistence.entity.TestMain;
import com.labway.busniess.center.middlebase.jianyan.persistence.mapper.LisTestMainMapper;
import com.labway.busniess.center.middlebase.jianyan.persistence.param.SearchTestMain;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * LisTestMainRepository
 * 检验主表
 * </p>
 *
 * <AUTHOR>
 * @since 2023/7/13 13:47
 */
@Component
public class LisTestMainRepository {

    @Resource
    private LisTestMainMapper testMainMapper;


    public TestMain queryTestMain(SearchTestMain param) {
        LambdaQueryWrapper<TestMain> queryWrapper = Wrappers.lambdaQuery(TestMain.class);
        queryWrapper
                .eq(TestMain::getBarcode, param.getBarcode())
                .eq(TestMain::getOutBarcode, param.getOutBarcode())
                .eq(TestMain::getHspOrgCode, param.getHspOrgCode())
                .eq(TestMain::getGroupCode, param.getGroupCode())
                .eq(TestMain::getMachineCode, param.getMachineCode())
                .eq(TestMain::getSampleNo, param.getSampleNo());

        return testMainMapper.selectOne(queryWrapper);
    }

    public int deleteTestMain(String barcode, String outBarcode, String hspOrgCode, String groupCode, String machineCode, String sampleNo){
        LambdaQueryWrapper<TestMain> wrapper = Wrappers.lambdaQuery(TestMain.class)
                .eq(TestMain::getBarcode, barcode)
                .eq(TestMain::getOutBarcode, outBarcode)
                .eq(TestMain::getHspOrgCode, hspOrgCode)
                .eq(TestMain::getGroupCode, groupCode)
                .eq(TestMain::getMachineCode, machineCode)
                .eq(TestMain::getSampleNo, sampleNo);
        return testMainMapper.delete(wrapper);
    }

    public int deleteTestMain(TestMain entity){
        return testMainMapper.delete(Wrappers.lambdaQuery(entity));
    }

    public int saveTestMain(TestMain entity) {
        return testMainMapper.insert(entity);
    }

    public List<TestMain> getTestMainByHspOrgCodeAndBarcode(String hspOrgCode, String barcode) {
        LambdaQueryWrapper<TestMain> wrapper =
                Wrappers
                        .lambdaQuery(TestMain.class)
                        .eq(TestMain::getHspOrgCode, hspOrgCode)
                        .eq(TestMain::getBarcode, barcode);
        return testMainMapper.selectList(wrapper);
    }

    public int deleteTestMainByTestMainIds(List<String> testMainIds) {
        testMainMapper.backup(testMainIds);
        return testMainMapper.delete(
                Wrappers.lambdaUpdate(TestMain.class).in(TestMain::getTestMainId, testMainIds));
    }
}
