package com.labway.busniess.center.middlebase.jianyan.persistence.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 送检项目对照的检验项目信息表(TbOrgApplySampleMainItemMapping)表实体类
 *
 * <AUTHOR>
 * @since 2023-10-23 09:45:28
 */
@Data
@NoArgsConstructor
@SuppressWarnings("serial")
public class TbOrgApplySampleMainItemMapping extends Model<TbOrgApplySampleMainItemMapping> {
    //送检项目对照表id
    @TableId(type = IdType.INPUT)
    private String mappingId;
    //申请单表id
    private String mainId;
    //送检项目表id
    private String mainItemId;
    //送检项目编码
    private String outTestItemCode;
    //送检项目名称
    private String outTestItemName;
    //创建人id
    private String createBy;
    //创建时间
    private Date createTime;
    //更新人id
    private String updateBy;
    //更新时间
    private Date updateTime;
    //删除标识 0未删除 1删除
    @TableLogic(value = "0", delval = "1")
    private Integer deleteFlag;
    // 中台检验项目编码
    private String testItemCode;
    // 中台检验项目名称
    private String testItemName;
}

