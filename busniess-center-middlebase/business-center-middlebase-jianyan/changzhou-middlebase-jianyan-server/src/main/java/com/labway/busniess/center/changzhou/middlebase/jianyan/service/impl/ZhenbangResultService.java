package com.labway.busniess.center.changzhou.middlebase.jianyan.service.impl;

import cn.hutool.core.date.StopWatch;
import cn.hutool.core.util.XmlUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpStatus;
import com.alibaba.cloud.commons.lang.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.labway.business.center.compare.dto.SampleItemTestResultDTO;
import com.labway.business.center.compare.dto.SampleTestResultDTO;
import com.labway.business.center.compare.request.ConfirmSampleReportRequest;
import com.labway.business.center.compare.request.QuerySampleResultRequest;
import com.labway.business.center.compare.service.TbOrgApplySampleMainService;
import com.labway.busniess.center.changzhou.middlebase.jianyan.config.OrganCodeMapConfig;
import com.labway.busniess.center.middlebase.jianyan.dto.PushSampleResultDto;
import com.labway.busniess.center.changzhou.middlebase.jianyan.dto.zhenbang.ZhenBangRedisSampleDto;
import com.labway.busniess.center.changzhou.middlebase.jianyan.dto.zhenbang.ZhenbangSampleResultDto;
import com.labway.busniess.center.changzhou.middlebase.jianyan.service.ResultService;
import com.labway.busniess.center.middlebase.jianyan.reqeust.CallbackResultRequest;
import com.labway.busniess.center.middlebase.jianyan.reqeust.LisTestMainRequest;
import com.swak.frame.dto.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.w3c.dom.Document;
import org.w3c.dom.NodeList;

import javax.annotation.Resource;
import java.net.SocketTimeoutException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@RefreshScope
@Service
public class ZhenbangResultService implements ResultService {

    @Value("${hsp.zhenbang.url}")
    private String url;
    @Value("${hsp.zhenbang.namespace}")
    private String namespace;
    @Value("${hsp.zhenbang.hspOrgCode}")
    private String hspOrgCode;
    @Value("${hsp.zhenbang.iscdata:false}")
    private Boolean iscdata;

    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @DubboReference
    private TbOrgApplySampleMainService tbOrgApplySampleMainService;

    @Resource
    private OrganCodeMapConfig organCodeMapConfig;

    /**
     * 查询接收样本的检验结果
     * @param requests {@link LisTestMainRequest}
     * @return
     */
    @Override
    public Response<?> receiveLimsResult(List<LisTestMainRequest> requests) {
        if (CollectionUtils.isEmpty(requests)){
            return Response.success();
        }

        // 手动回传样本结果
        List<String> collect = requests.stream().map(e -> e.getBarcode()).collect(Collectors.toList());
        log.info("手动回传振邦样本结果，条码号：{}",JSONObject.toJSONString(collect));
        PushSampleResultDto pushSampleResultDto = new PushSampleResultDto();
        pushSampleResultDto.setBarcodes(collect);
        return this.pushSampleResult(pushSampleResultDto);
    }

    /**
     * 召回样本的检验结果
     * @param request {@link CallbackResultRequest}
     * @return
     */
    @Override
    public Response<?> callbackResult(CallbackResultRequest request) {
        return null;
    }


    /**
     * 推送社区样本检验结果信息
     * @param pushSampleResultDto
     * @return
     */
    @Override
    public Response<?> pushSampleResult(PushSampleResultDto pushSampleResultDto) {

        String[] hspOrgCodes = this.hspOrgCode.split(",");
        if (hspOrgCodes!=null && hspOrgCodes.length>0){
            for (int i = 0; i < hspOrgCodes.length; i++) {
                String hspOrgCode = hspOrgCodes[i];
                log.info("开始同步振邦送检机构{}的样本结果！",hspOrgCode);
                // 调用业务中台查询样本结果信息
                QuerySampleResultRequest querySampleResultRequest = new QuerySampleResultRequest();
                querySampleResultRequest.setHspOrgCode(hspOrgCode);
                querySampleResultRequest.setBarcodeList(pushSampleResultDto.getBarcodes());
                Response<List<SampleTestResultDTO>> resultResponse = tbOrgApplySampleMainService.querySampleResult(querySampleResultRequest);
                if (!resultResponse.isSuccess()){
                    log.error("振邦社区【编码：{}】查询业务中台样本结果失败，失败信息：{}",hspOrgCode,resultResponse.getMsg());
                    continue;
                }

                List<SampleTestResultDTO> data = resultResponse.getData();
                if (CollectionUtils.isEmpty(data)){
                    log.warn("未查询到振邦社区【编码：{}】的样本结果数据！！！",hspOrgCode);
                    continue;
                }


                List<String> confirmBarcode = new ArrayList<>();
                Map<String, SampleTestResultDTO> barcodeGroup = data.stream().collect(Collectors.toMap(e -> e.getBarcode(),p->p,(o1,o2)->o2));
                // 单个样本处理结果回传
                for (Map.Entry<String, SampleTestResultDTO> stringListEntry : barcodeGroup.entrySet()) {
                    String tempBarcode = stringListEntry.getKey();
                    SampleTestResultDTO tempSampleResult = stringListEntry.getValue();

                    List<ZhenbangSampleResultDto> pushResultDto = new ArrayList<>();
                    for (SampleItemTestResultDTO result : tempSampleResult.getItemTestResultList()) {
                        List<String> outReportItemCodes = result.getOutReportItemCodes();
                        if (CollectionUtils.isEmpty(outReportItemCodes)){
                            log.warn("样本【条码号：{}】的报告项目【编码：{}】未做项目回传对照，检验结果无法回传，请先进行回传项目的对照！！！",result.getBarcode(),result.getReportItemId());
                            continue;
                        }

                        for (String outReportItemCode : outReportItemCodes) {

                            Object obj = stringRedisTemplate.opsForHash().get(ZhenbangApplyService.sampleKey + hspOrgCode + ":" + tempBarcode, outReportItemCode);
                            if (obj==null){
                                // 这里是校验出了多余的回传项目信息结果（振邦送检时没有该项报告项目，可是结果却多出了这个报告项目）
                                log.warn("振邦样本【{}】不存在送检的报告项目【{}】,这有可能是实验室额外回传的报告项目结果！！！",tempBarcode,outReportItemCode);
                                continue;
                            }
                            String jsonStr = (String)obj;
                            ZhenBangRedisSampleDto zhenBangRedisSampleDto = JSONObject.parseObject(jsonStr, ZhenBangRedisSampleDto.class);
                            ZhenbangSampleResultDto dto = new ZhenbangSampleResultDto();
                            dto.setCheckinTargetId(zhenBangRedisSampleDto.getCheckinTargetId());
                            dto.setCheckinId(zhenBangRedisSampleDto.getCheckinId());
                            dto.setOrganCode(organCodeMapConfig.getOrganCode(hspOrgCode));
                            dto.setResult(result.getTestResult());
                            dto.setReference(result.getReferenceValue());
                            dto.setHighLowMarks(String.valueOf(convertTestJudge(result.getTestJudge())));
                            dto.setRecorder("e4b92f5e5b2c445c8ae3be69e1d4e178");
                            pushResultDto.add(dto);
                        }

                    }

                    String jsonParam = JSONObject.toJSONString(pushResultDto);
                    if (iscdata){
                        jsonParam = "<![CDATA[" + jsonParam + "]]>";
                    }

                    // 样本结果推送
                    Response<String> stringResponse = doWsHttpZhenbang(url, namespace, "saveLabExaminationData", jsonParam);
                    String resultData = stringResponse.getData();
                    // 推送结果成功判断
                    if (!stringResponse.isSuccess() || StringUtils.isBlank(resultData) || !Objects.equals(JSONObject.parseObject(resultData).getString("code"),"1")){ // todo
                        log.warn("振邦社区样本【样本号：{}】结果回传失败,失败原因：【{}】,推送响应数据：{}",tempBarcode,stringResponse.getMsg(),resultData);
                        continue;
                    }

                    // 添加样本结果确认
                    confirmBarcode.add(tempBarcode);
                }

                if (CollectionUtils.isEmpty(confirmBarcode)){
                    log.info("振邦社区【编码：{}】样本结果推送执行结束，没有需要确认的样本结果信息！！！",hspOrgCode);
                    continue;
                }

                // 确认样本结果
                ConfirmSampleReportRequest confirmRequest = new ConfirmSampleReportRequest();
                confirmRequest.setHspOrgCode(hspOrgCode);
                confirmRequest.setOptId("system");
                confirmRequest.setOptName("system");
                confirmRequest.setBarcodes(confirmBarcode);
                Response<?> response = tbOrgApplySampleMainService.confirmSampleReport(confirmRequest);
                if (!response.isSuccess()){
                    log.error("振邦社区【编码：{}】样本结果推送成功，但是业务中台结果确认失败,失败信息：{}",hspOrgCode,response.getMsg());
//                    return Response.fail(response.getCode(),"振邦样本结果推送成功，但是业务中台结果确认失败!失败信息：【"+response.getMsg()+"】！");
                }
            }
        }else {
            log.warn("振邦送检机构配置为空，不进行结果回传！！！");
        }

        return Response.success();
    }


    //==================================================================================================================


    /**
     * 发起webservice请求
     */
    private Response<String> doWsHttpZhenbang(String url, String namespaceURI, String method, String jsonParam){
        log.info("开始推送邦外送样本结果信息：查询入参信息：{}",jsonParam);

        String body = "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:web=\"http://webservice.medicalExam.zebone.com/\">\n" +
                "   <soapenv:Header/>\n" +
                "   <soapenv:Body>\n" +
                "      <web:" + method +">\n" +
                "         <jsonParam>"+ jsonParam +"</jsonParam>\n" +
                "      </web:" + method +">\n" +
                "   </soapenv:Body>\n" +
                "</soapenv:Envelope>";


        HttpRequest request = HttpRequest.post(url)
                .header("Content-Type", "text/xml")
                .header("Accept-Charset", "charset=urf-8")
                .body(body);
        final StopWatch watch = new StopWatch();
        watch.start();

        try (HttpResponse response = request.execute()) {
            final String responseBody = response.body();
            watch.stop();

            log.info("推送振邦样本结果信息执行结束 URL [{}] 耗时 [{}ms] Body [{}] Status [{}] Response [{}]", url, watch.getTotalTimeMillis(), body, response.getStatus(), responseBody);

            final Document document = XmlUtil.parseXml(responseBody);

            if (response.getStatus() != HttpStatus.HTTP_OK) {
                log.warn("调用振邦样本信息结果推送响应状态码【{}】失败，响应信息：{}",response.getStatus(),responseBody);
                return Response.fail(response.getStatus(),"调用振邦样本信息结果推送，响应状态码错误,响应信息："+responseBody);
            }

            NodeList queryReports2Result = document.getElementsByTagName("return");
            String textContent = queryReports2Result.item(0).getTextContent();

            log.info("振邦样本结果信息推送结束,响应结果：{}",textContent);
            return Response.success(textContent);
        } catch (Exception e) {
            log.error("Webservice doWsRequest执行异常 URL [{}] 耗时 [{}ms] Body [{}] 失败", url, watch.getTotalTimeMillis(), request, e);
            if (e.getCause() instanceof SocketTimeoutException) {
                throw new IllegalStateException("推送振邦结果接口超时！");
            }
            throw e;
        }
    }


    // 转换检验结果是否为异常
    public Integer convertTestJudge(String testJudge) {
        if (StringUtils.isBlank(testJudge)) {
            return 1;
        }

        switch (testJudge) {
            case "正常":
                return 1;
            case "NORMAL":
                return 1;
            case "√":
                return 1;

            case "异常":
                return 2;
            case "×":
                return 2;

            case "高":
                return 3;
            case "UP":
                return 3;
            case "↑":
                return 3;

            case "低":
                return 4;
            case "DOWN":
                return 4;
            case "↓":
                return 4;

            default:
                return 1;
        }

    }


}
