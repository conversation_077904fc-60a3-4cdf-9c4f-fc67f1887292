package com.labway.busniess.center.changzhou.middlebase.jianyan.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.Map;

@Data
@RefreshScope
@Component
@ConfigurationProperties(prefix = "business")
public class OrganCodeMapConfig {

    private Map<String, String> organCodeMapping = Map.of();

    public String getOrganCode(String organCode) {
        return organCodeMapping.getOrDefault(organCode, organCode);
    }

}