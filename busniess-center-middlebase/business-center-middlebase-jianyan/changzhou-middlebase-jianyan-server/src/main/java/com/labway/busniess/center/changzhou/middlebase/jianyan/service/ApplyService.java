package com.labway.busniess.center.changzhou.middlebase.jianyan.service;

import com.labway.business.center.compare.dto.OutApplyInfoDTO;
import com.labway.business.center.compare.dto.TbPathologyApplyMainDTO;
import com.labway.business.center.compare.dto.middlebase.OrgApplySampleMainDTO;
import com.labway.busniess.center.middlebase.jianyan.reqeust.QueryApplyRequest;
import com.labway.busniess.center.middlebase.jianyan.reqeust.SignApplyInfoRequest;
import com.labway.busniess.center.middlebase.jianyan.reqeust.SignCancelApplyInfoRequest;
import com.swak.frame.dto.Response;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ApplyService {

    /**
     * 获取申请单
     *
     * @param request QueryApplyRequest
     * @return  OutApplyInfoDTO
     */
    Response<OutApplyInfoDTO> get(QueryApplyRequest request);

    /**
     * 获取申请单信息 FOR 病理
     * 按照数据库结构直接返回，不做处理
     * @param request
     * @return
     */
    Response<TbPathologyApplyMainDTO> getForPathology(QueryApplyRequest request);


    /**
     * 签收
     *
     * @param request {@link SignApplyInfoRequest}
     * @return
     */
    Response<OrgApplySampleMainDTO> sign(SignApplyInfoRequest request);

    /**
     * 取消签收
     * @param request   {@link SignCancelApplyInfoRequest}
     * @return
     */
    Response<?> cancelSign(SignCancelApplyInfoRequest request);


    /**
     * 获取医院编码
     */
    List<String> getHspOrgCode();

    /**
     * 服务心跳检测
     * @return
     */
    Response<?> serviceMonitor();

}
