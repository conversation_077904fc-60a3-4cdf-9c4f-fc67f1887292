package com.labway.busniess.center.nanjing.middlebase.jianyan.enums;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @ClassName WeiNingHighLowFlagEnum
 * @Description: 回传卫宁结果标志枚举
 * @Author: huang<PERSON>xiang
 * @CreateDate: 2024/7/26 11:55
 */
@Getter
@AllArgsConstructor
public enum WeiNingHighLowFlagEnum {
    UP("1", "高"),
    DOWN("2", "低"),
    EXCEPTION("3", "异常"),
    NORMAL("", "正常");

    private final String value;
    private final String description;

    // 根据描述获取对应的 value
    public static String getValueByDescription(String valueFlag) {
        // 结果正常的情况 1、testJudge = ""  2、testJudge = "NORMAL"
        if (StrUtil.isBlank(valueFlag)) {
            return NORMAL.getValue();
        }

        for (WeiNingHighLowFlagEnum flag : WeiNingHighLowFlagEnum.values()) {
            if (flag.name().equals(valueFlag.trim())) {
                return flag.getValue();
            }
        }
        return StrUtil.EMPTY; // 都没有匹配默认传空
    }
}