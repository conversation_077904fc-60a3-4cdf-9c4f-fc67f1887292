package com.labway.busniess.center.nanjing.middlebase.jianyan.persistence.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.labway.busniess.center.nanjing.middlebase.jianyan.persistence.entity.LisGermResult;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【LIS_GERM_RESULT】的数据库操作Mapper
* @createDate 2023-07-12 21:18:17
*/
//@Mapper
public interface LisGermResultMapper extends BaseMapper<LisGermResult> {

    int backup(@Param("testMainIds") List<String> testMainIds);

}
