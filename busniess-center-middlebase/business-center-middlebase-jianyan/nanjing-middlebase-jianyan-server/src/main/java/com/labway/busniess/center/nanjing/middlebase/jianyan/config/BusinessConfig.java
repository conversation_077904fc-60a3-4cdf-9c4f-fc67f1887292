package com.labway.busniess.center.nanjing.middlebase.jianyan.config;

import com.alibaba.fastjson.JSON;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <p>
 * BusinessConfig
 * 回传配置
 * </p>
 *
 * <AUTHOR>
 * @since 2023/7/14 15:03
 */
@Getter
@Setter
@Component
@RefreshScope
@ConfigurationProperties(prefix = "business")
public class BusinessConfig {

    public static final String signSampleCacheKey = "NANJING_DONGRUAN_SIGN_KEY:%s:%s";

    public static final String nanjingSignSampleCacheKey = "NANJING_SIGN_SAMPLE_KEY:%s:%s";

    /**
     * 需要设置微生物备注的机构，目前只有骨伤
     * 中间库送检机构
     */
    private List<String> configGermRemarkOrgs = List.of();

    /**
     * 配置该机构需要将outTestItemCode设置进labPackageId
     * 中间库送检机构（医院机构编码）
     */
    private List<String> labPackageIdOrgs = List.of();

    /**
     * 机构默认的申请单类型 门诊/住院（业务中台机构编码）
     * 当中间库数据为空时设置
     * 业务中台送检机构
     */
    private Map<String, String> defaultApplySourceMap = Map.of( "Z000027176", "体检");

    /**
     * 送检机构 => 数据源名称（业务中台机构编码）
     */
    private Map<String, String> orgDatasourceMap = Map.of();

    /**
     * 送检机构code->名称（业务中台机构编码）
     */
    private Map<String, String> orgCodeNameMap = Map.of();

    /**
     * 送检机构编码 -> 中台机构编码
     */
    private Map<String, String> orgCodeMap = Map.of();

    /**
     * 业务中台机构编码 --> 中间库结果表机构编码
     */
    private Map<String, String> orgCodeResultMap = Map.of();

    /**
     * 医院/实验室条码号对换（业务中台机构编码）
     */
    private List<String> swapBarcodeHspOrgCodes = List.of();

    /**
     * 需要将报告上传到FTP服务器（业务中台机构编码）
     * Z000006508 妇幼
     * Z000001660 杏和丹北镇新桥
     * Z000001739 杏和界牌镇
     */
    private List<String> uploadPdf2FtpOrgCodes = List.of();

    /**
     * FTP配置
     */
    private Map<String, String> ftpConfig = Map.of();


    /**
     * 根据code取名称
     */
    public String getOrgNameByCode(String orgCode) {
        return orgCodeNameMap.getOrDefault(orgCode, StringUtils.EMPTY);
    }

    /**
     * 根据送检机构取到对应的数据源
     */
    public String getDatasource(String orgCode) {
        return orgDatasourceMap.getOrDefault(orgCode, StringUtils.EMPTY);
    }

    /**
     * 根据中间库机构获取对应的实验室机构，也可以反过来查
     */
    public String getMappingCode(String hspOrgCode) {
        for (Map.Entry<String, String> entry : orgCodeMap.entrySet()) {
            if (entry.getKey().equals(hspOrgCode)) {
                return entry.getValue();
            }
            if (entry.getValue().equals(hspOrgCode)) {
                return entry.getKey();
            }
        }

        return hspOrgCode;
    }

    /**
     * 根据实验室送检机构编码获取中间库结果表的送检机构编码
     * NTOE 此编码有可能和申请单表编码不一样，如：妇幼 和 杏和社区
     */
    public String getResultMappingCode(String hspOrgCode) {
        return orgCodeResultMap.getOrDefault(hspOrgCode, hspOrgCode);
    }

    /**
     * true: 将结果表 LabPackageId 设置为 OutTestitemCode
     */
    public boolean checkLabPackageIdOrg(String hspOrgCode) {
        return CollectionUtils.isNotEmpty(labPackageIdOrgs) &&
                labPackageIdOrgs.contains(hspOrgCode);
    }

    /**
     * 体检 门诊 住院，如果没有取到返回配置的默认值
     */
    public String getApplySource(String hspOrgCode) {
        String defaultApplySource = "体检";
        if (Objects.isNull(defaultApplySourceMap)) {
            return defaultApplySource;
        }
        return Objects.requireNonNullElse(defaultApplySourceMap.get(hspOrgCode), defaultApplySource);
    }

    /**
     * false：将微生物结果的WhonetSecommnet设置为空
     */
    public boolean checkConfigGermRemarkOrg(String hspOrgCode) {
        return CollectionUtils.isNotEmpty(configGermRemarkOrgs) &&
                configGermRemarkOrgs.contains(hspOrgCode);
    }

    /**
     * true：判断机构是否要进行条码互换处理
     * LIS_TEST_MAIN         BARCODE = 实验室条码号 OUT_BARCODE = 医院条码号
     * LIS_REPORT_FILE       BARCODE = 实验室条码号 OUT_BARCODE = 医院条码号
     * LIS_RESULT            BARCODE = 实验室条码号
     * LIS_GERM_RESULT_MAIN  BARCODE = 实验室条码号
     * LIS_GERM_RESULT       BARCODE = 实验室条码号
     * LIS_HI_RESULT         BARCODE = 实验室条码号
     */
    public boolean shouldSwapBarcodeHspOrgCode(String hspOrgCode) {
        return CollectionUtils.isNotEmpty(swapBarcodeHspOrgCodes) && swapBarcodeHspOrgCodes.contains(hspOrgCode);
    }

    /**
     * true：判断该机构的报告是否要上传FTP
     */
    public boolean isUploadPdf2FtpOrgCode(String hspOrgCode) {
        return CollectionUtils.isNotEmpty(uploadPdf2FtpOrgCodes) && uploadPdf2FtpOrgCodes.contains(hspOrgCode);
    }

    public FtpConfig getFtpConfig(String hspOrgCode) {
        String ftpConfigStr = ftpConfig.get(hspOrgCode);

        if (StringUtils.isBlank(ftpConfigStr)) {
            return null;
        }

        return JSON.parseObject(ftpConfigStr, FtpConfig.class);
    }

    /**
     * ftp 配置
     */
    @Getter
    @Setter
    public static class FtpConfig {
        private String host;
        private int port;
        private String username;
        private String password;
        private String basePath;
    }

}
