package com.labway.busniess.center.nanjing.middlebase.jianyan.persistence.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 * @TableName LIS_TEST_MAIN
 */
@TableName(value ="LIS_TEST_MAIN")
@Data
public class TestMain implements Serializable {
    /**
     * 条码编码
     */
    private String barcode;

    /**
     * 条码的机构编码,即送检医院
     */
    private String hspOrgCode;

    /**
     * 专业组编码
     */
    private String groupCode;

    /**
     * 仪器编码
     */
    private String machineCode;

    /**
     * 样本单号
     */
    private String sampleNo;

    /**
     * 仪器类型
     */
    private String transType;

    /**
     * 检验人编码
     */
    private String testUserCode;

    /**
     * 检验人
     */
    private String testUserName;

    /**
     * 检验日期
     */
    private String testDate;

    /**
     * 一审人编码
     */
    private String oneCheckUserCode;

    /**
     * 一审人
     */
    private String oneCheckUserName;

    /**
     * 一审时间
     */
    private Date oneCheckDate;

    /**
     * 是否一审(0否1是)
     */
    private Integer isOneCheck;

    /**
     * 二审人编码
     */
    private String twoCheckUserCode;

    /**
     * 二审人
     */
    private String twoCheckUserName;

    /**
     * 二审时间
     */
    private Date twoCheckDate;

    /**
     * 是否二审(0否1是)
     */
    private Integer isTwoCheck;

    /**
     * 审核人编码
     */
    private String checkUserCode;

    /**
     * 审核人
     */
    private String checkUserName;

    /**
     * 审核时间
     */
    private Date checkDate;

    /**
     * 是否审核(0否1是)
     */
    private Integer isCheck;

    /**
     * 打印人编码
     */
    private String printUserCode;

    /**
     * 打印人
     */
    private String printUserName;

    /**
     * 最近一次打印时间
     */
    private Date lastPrintDate;

    /**
     * 是否打印(0否1是)
     */
    private String isPrint;

    /**
     * 检验评语
     */
    private String testDescribe;

    /**
     * 接收人编码(前处理)
     */
    private String recUserCode;

    /**
     * 接收人
     */
    private String recUserName;

    /**
     * 接收时间
     */
    private Date recDate;

    /**
     * 发送状态(0创建1上传成功2转发成功)
     */
    private String sendStatus;

    /**
     * 发送时间
     */
    private Date sendDate;

    /**
     * 模板编码(模板文件名称)
     */
    private String templateCode;

    /**
     * 小组编码
     */
    private String specialMicrogroupCode;

    /**
     * 小组名称
     */
    private String specialMicrogroupName;

    /**
     * 报告编号，通过SQ_LIS_TEST_MAIN_REPORT_NO生成，10位（前面不足补0），在中心实验室打印的，首位为0，在平台打印的，首位为1，社区打印的，首位为2
     */
    private String reportNo;

    /**
     * 是否合并样本号(0否1是)
     */
    private Integer isMerge;

    /**
     * 合并后的样本号
     */
    private String mergeSampleNo;

    /**
     * 专业组名称
     */
    private String groupName;

    /**
     * 是否使用中心实验室标题(1是0否，默认为0)
     */
    private Integer isusercenterlabtitle;

    /**
     * 自定义标题(如果不使用中心实验室标题，此值又为空时，则使用机构名称+"检验报告")
     */
    private String customreporttitle;

    /**
     * 单双列模式(1为单列，2为双列，默认为单列，值为空也是单列，目前先暂时只支持单列模式)
     */
    private Integer onetwocolumn;

    /**
     * 中心实验室编码
     */
    private String centerlaborgcode;

    /**
     * 院感收费数量
     */
    private Integer testitemSum;

    /**
     * 检验时间
     */
    private Date testTime;

    /**
     * 是否上传报告单平台(0否1是)
     */
    private String isSend;

    /**
     * 备注
     */
    private String memo;

    /**
     * 取消一审人编码
     */
    private String oneCheckUserCode2;

    /**
     * 取消一审人
     */
    private String oneCheckUserName2;

    /**
     * 取消一审时间
     */
    private Date oneCheckDate2;

    /**
     * 取消一审（0否1是 如果专业组只需一审，则0：未取消 1：未打印取消一审 2：已打印取消一审，默认为0）
     */
    private String isOneCheck2;

    /**
     * 取消二审人编码
     */
    private String twoCheckUserCode2;

    /**
     * 取消二审人
     */
    private String twoCheckUserName2;

    /**
     * 取消二审时间
     */
    private Date twoCheckDate2;

    /**
     * 取消二审（0：未取消 1：未打印取消二审 2：已打印取消二审，默认为0）
     */
    private String isTwoCheck2;

    /**
     * 该条记录生成时间
     */
    private Date createDate;

    /**
     * 是否已经导出电子报告(0否1是)
     */
    private String isExport;

    /**
     * 备注(对于院感是存储的“建议与意见”)
     */
    private String memo1;

    /**
     * 二次分拣报告项目数量
     */
    private Integer twiceItemNum;

    /**
     * 是否自助打印(0否1是)
     */
    private String isAutoprint;

    /**
     * 自助打印人编码
     */
    private String autoprintUserCode;

    /**
     * 自助打印人
     */
    private String autoprintUserName;

    /**
     * 最近一次自助打印时间
     */
    private Date lastAutoprintDate;

    /**
     * 标本来源，1表示是回传的报告单不能取消审核，0是正常的本地标本
     */
    private Integer source;

    /**
     * 报告单ID
     */
    private String testMainId;

    /**
     *
     */
    private String realTestUserCode;

    /**
     *
     */
    private String realOneCheckUserCode;

    /**
     *
     */
    private String realTwoCheckUserCode;

    /**
     *
     */
    private String realCheckUserCode;

    /**
     * 结果是否存在危急值(0否1是)
     */
    private String isExistcrisis;

    /**
     * 邮件发送批次号
     */
    private String mailBatchNo;

    /**
     * 复查标识(默认为空，1待复查，2复查完成)
     */
    private String reviewFlag;

    /**
     * 检验ID
     */
    private Integer testId;

    /**
     * 检验记录表ID
     */
    private Integer testRegId;

    /**
     * 外部条码
     */
    private String outBarcode;

    /**
     * 签收时间
     */
    private Date receiveDate;

    /**
     * 操作类型（1新增、2获取成功）
     */
    private String operFlag;

    /**
     * 
     */
    private Integer lisExecCount;

    /**
     * 
     */
    private String testMainOuterId;

    /**
     * 套餐ID
     */
    private String labPackageId;

    /**
     * 套餐名称
     */
    private String labPackageName;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

}