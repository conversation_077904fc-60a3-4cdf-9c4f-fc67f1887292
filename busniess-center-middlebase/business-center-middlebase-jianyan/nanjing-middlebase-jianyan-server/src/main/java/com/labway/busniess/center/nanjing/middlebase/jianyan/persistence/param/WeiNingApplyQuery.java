package com.labway.busniess.center.nanjing.middlebase.jianyan.persistence.param;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

@XmlRootElement(name = "root")
@XmlAccessorType(XmlAccessType.FIELD) // 使用字段访问方式
public class WeiNingApplyQuery {

    @XmlElement(name = "parameter")
    private Parameter parameter;

    public Parameter getParameter() {
        return parameter;
    }

    public void setParameter(Parameter parameter) {
        this.parameter = parameter;
    }

    public static class Parameter {
        @XmlElement(name = "RequestBase")
        private RequestBase requestBase;

        @XmlElement(name = "BARTYPE")
        private int bartype;

        @XmlElement(name = "BARCODE")
        private String barcode;

        // 提供一个构造函数来初始化字段
        public Parameter(RequestBase requestBase, int bartype, String barcode) {
            this.requestBase = requestBase;
            this.bartype = bartype;
            this.barcode = barcode;
        }
    }

    public static class RequestBase {
        @XmlElement(name = "QQJGDM")
        private String qqjgdm;

        @XmlElement(name = "QQRYDM")
        private String qqrydm;

        @XmlElement(name = "QQRYXM")
        private String qqryxm;

        // 提供一个构造函数来初始化字段
        public RequestBase(String qqjgdm, String qqrydm, String qqryxm) {
            this.qqjgdm = qqjgdm;
            this.qqrydm = qqrydm;
            this.qqryxm = qqryxm;
        }
    }
}
