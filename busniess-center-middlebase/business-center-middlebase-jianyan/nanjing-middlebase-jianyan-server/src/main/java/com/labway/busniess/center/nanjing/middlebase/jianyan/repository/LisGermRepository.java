package com.labway.busniess.center.nanjing.middlebase.jianyan.repository;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.busniess.center.nanjing.middlebase.jianyan.persistence.entity.LisGermResult;
import com.labway.busniess.center.nanjing.middlebase.jianyan.persistence.entity.LisGermResultMain;
import com.labway.busniess.center.nanjing.middlebase.jianyan.persistence.mapper.LisGermResultMainMapper;
import com.labway.busniess.center.nanjing.middlebase.jianyan.persistence.mapper.LisGermResultMapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * LisGermRepository 描述信息
 *
 * <AUTHOR>
 * @since 2023/7/18 20:44
 */
//@Component
public class LisGermRepository {
    
//    @Resource
    private LisGermResultMainMapper germResultMainMapper;
//    @Resource
    private LisGermResultMapper germResultMapper;


    public int batchSaveGermResultMain(List<LisGermResultMain> resultMains) {
        int result = 0;
        for (LisGermResultMain resultMain : resultMains) {
            result += germResultMainMapper.insert(resultMain);
        }
        return result;
    }
    
    public int batchSaveGermResult(List<LisGermResult> results) {
        int ret = 0;
        for (LisGermResult result : results) {
            ret += germResultMapper.insert(result);
        }
        return ret;
    }

    public int deleteGermResultMainByTestMainIds(List<String> testMainIds) {
        germResultMainMapper.backup(testMainIds);
        return germResultMainMapper.delete(
                Wrappers.lambdaQuery(LisGermResultMain.class).in(LisGermResultMain::getTestMainId, testMainIds));
    }

    public int deleteGermResultByTestMainIds(List<String> testMainIds) {
        germResultMapper.backup(testMainIds);
        return germResultMapper.delete(
                Wrappers.lambdaQuery(LisGermResult.class).in(LisGermResult::getTestMainId, testMainIds));
    }

}
