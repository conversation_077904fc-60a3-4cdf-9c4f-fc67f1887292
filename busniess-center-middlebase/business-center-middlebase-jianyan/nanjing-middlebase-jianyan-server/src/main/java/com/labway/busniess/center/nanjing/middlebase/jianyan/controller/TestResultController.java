package com.labway.busniess.center.nanjing.middlebase.jianyan.controller;

import com.alibaba.fastjson.JSONObject;
import com.labway.busniess.center.middlebase.jianyan.dto.PushSampleResultDto;
import com.labway.busniess.center.middlebase.jianyan.reqeust.CallbackResultRequest;
import com.labway.busniess.center.middlebase.jianyan.reqeust.LisTestMainRequest;
import com.labway.busniess.center.nanjing.middlebase.jianyan.service.NanjingResultService;
import com.labway.busniess.center.nanjing.middlebase.jianyan.service.impl.DongRuanResultServiceImpl;
import com.labway.busniess.center.nanjing.middlebase.jianyan.service.impl.HongShanResultServiceImpl;
import com.swak.frame.dto.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * TestResultController
 * 常规检验结果
 * </p>
 *
 * <AUTHOR>
 * @since 2023/7/13 9:35
 */
@Slf4j
@RestController
@RequestMapping("/jianyan/middle/result")
public class TestResultController  implements InitializingBean {
    private Collection<NanjingResultService> nanjingResultServices;

    @Resource
    private ApplicationContext applicationContext;
    @Resource
    private DongRuanResultServiceImpl dongRuanResultServiceImpl;

    @Resource
    private HongShanResultServiceImpl hongShanResultServiceImpl;

    @PostMapping("/receive")
    public Response<?> receiveLimsResult(@RequestBody @Valid @NotNull @Size(min = 1) List<LisTestMainRequest> requests) {
        log.info("南京中间程序接收样本结果，调用入参：{}", JSONObject.toJSONString(requests));
        try {
            requests.stream().collect(Collectors.groupingBy(LisTestMainRequest::getHspOrgCode)).forEach((hspOrgCode, requestsByHspOrgCode) -> {
                getResultService(hspOrgCode).receiveLimsResult(requestsByHspOrgCode);
            });
            return Response.success();
        } catch (Exception e) {
            return Response.fail(500, e.getMessage());
        }
    }

    /**
     * 结果召回
     * @param request
     * @return
     */
    @PostMapping("/callback")
    public Response<?> callbackResult(@RequestBody @Valid CallbackResultRequest request) {
        log.info("南京中间程序撤回样本结果，调用入参：{}", JSONObject.toJSONString(request));
        return getResultService(request.getHspOrgCode()).callbackResult(request);
    }


    /**
     * 手动拉取样本结果
     * @return
     */
    @PostMapping("/nanjingDongruanPushSampleResult")
    public Response<?> nanjingDongruanPushSampleResult(@RequestBody @Valid PushSampleResultDto pushSampleResultDto){
        log.info("手动拉取样本结果,调用入参：{}",JSONObject.toJSONString(pushSampleResultDto));
        return dongRuanResultServiceImpl.pushSampleResult(pushSampleResultDto);
    }

    /**
     * 手动拉取样本结果
     * @return
     */
    @PostMapping("/nanjingHongShanPushSampleResult")
    public Response<?> nanjingHongShanPushSampleResult(@RequestBody @Valid PushSampleResultDto pushSampleResultDto){
        log.info("手动拉取样本结果,调用入参：{}",JSONObject.toJSONString(pushSampleResultDto));
        return hongShanResultServiceImpl.pushSampleResult(pushSampleResultDto);
    }

    //==================================================================================================================


    @Override
    public void afterPropertiesSet(){
        this.nanjingResultServices = Collections.unmodifiableCollection(applicationContext.getBeansOfType(NanjingResultService.class).values());
        for (NanjingResultService e : this.nanjingResultServices) {
            log.info("初始化送检机构 [{}] [{}]", e.getHspOrgCode(), e);
            Objects.requireNonNull(e.getHspOrgCode(), e.toString());
        }
    }

    private NanjingResultService getResultService(String hspOrgCode) {
        for (NanjingResultService resultService : nanjingResultServices) {
            if (resultService.getHspOrgCode().contains(hspOrgCode)) {
                return resultService;
            }
        }

        throw new UnsupportedOperationException(String.format("无法识别送检机构编码 [%s]", hspOrgCode));
    }

}
