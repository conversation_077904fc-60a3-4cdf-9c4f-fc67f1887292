package com.labway.busniess.center.nanjing.middlebase.jianyan.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.busniess.center.nanjing.middlebase.jianyan.persistence.entity.LisResult;
import com.labway.busniess.center.nanjing.middlebase.jianyan.persistence.mapper.LisResultMapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * ResultRepository
 * 结果
 * </p>
 *
 * <AUTHOR>
 * @since 2023/7/13 13:47
 */
//@Component
public class LisResultRepository {

//    @Resource
    private LisResultMapper resultMapper;

    public int deleteResultByTestMainId(String testMainId) {
        return resultMapper.delete(Wrappers.lambdaQuery(LisResult.class).eq(LisResult::getTestMainId, testMainId));
    }

    public int saveResult(LisResult entity) {
        return resultMapper.insert(entity);
    }

    public int batchSaveResult(List<LisResult> entities) {
        int result = 0;
        for (LisResult entity : entities) {
            result += resultMapper.insert(entity);
        }
        return result;
    }

    public int deleteResultByTestMainIds(List<String> testMainIds) {
        resultMapper.backup(testMainIds);
        LambdaQueryWrapper<LisResult> wrapper =
                Wrappers.lambdaQuery(LisResult.class).in(LisResult::getTestMainId, testMainIds);
        return resultMapper.delete(wrapper);
    }
}
