package com.labway.busniess.center.nanjing.middlebase.jianyan.controller;

import com.labway.busniess.center.middlebase.jianyan.dto.PushSampleResultDto;
import com.labway.busniess.center.nanjing.middlebase.jianyan.service.impl.HongShanResultServiceImpl;
import com.swak.frame.dto.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;


/**
 * <p> 南京红山学院手动回传接口 </p>
 */
@Slf4j
@RestController
@RequestMapping("/jianyan/middle/hongshan")
public class NanjingHongShanController {

    @Resource
    private HongShanResultServiceImpl hongShanResultService;

    @PostMapping("/pushSampleResult")
    public Response pushSampleResult(@RequestBody PushSampleResultDto pushSampleResultDto) {
        Response<?> response = hongShanResultService.pushSampleResult(pushSampleResultDto);
        return response;
    }
}
