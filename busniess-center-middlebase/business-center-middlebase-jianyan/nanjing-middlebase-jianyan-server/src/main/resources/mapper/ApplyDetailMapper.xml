<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.busniess.center.nanjing.middlebase.jianyan.persistence.mapper.ApplyDetailMapper">

    <resultMap id="BaseResultMap" type="com.labway.busniess.center.nanjing.middlebase.jianyan.persistence.entity.ApplyDetail">
            <result property="barcode" column="BARCODE" jdbcType="VARCHAR"/>
            <result property="outTestitemCode" column="OUT_TESTITEM_CODE" jdbcType="VARCHAR"/>
            <result property="outTestitemName" column="OUT_TESTITEM_NAME" jdbcType="VARCHAR"/>
            <result property="testitemCode" column="TESTITEM_CODE" jdbcType="VARCHAR"/>
            <result property="hspOrgCode" column="HSP_ORG_CODE" jdbcType="VARCHAR"/>
            <result property="isFee" column="IS_FEE" jdbcType="VARCHAR"/>
            <result property="isForbidden" column="IS_FORBIDDEN" jdbcType="INTEGER"/>
            <result property="feeNum" column="FEE_NUM" jdbcType="INTEGER"/>
            <result property="property1" column="PROPERTY1" jdbcType="VARCHAR"/>
            <result property="property2" column="PROPERTY2" jdbcType="VARCHAR"/>
            <result property="property3" column="PROPERTY3" jdbcType="VARCHAR"/>
            <result property="property4" column="PROPERTY4" jdbcType="VARCHAR"/>
            <result property="property5" column="PROPERTY5" jdbcType="VARCHAR"/>
            <result property="feeType" column="FEE_TYPE" jdbcType="INTEGER"/>
            <result property="showNo" column="SHOW_NO" jdbcType="INTEGER"/>
            <result property="testitemName" column="TESTITEM_NAME" jdbcType="VARCHAR"/>
            <result property="lwHspOrgCode" column="LW_HSP_ORG_CODE" jdbcType="VARCHAR"/>
            <result property="lwBarcode" column="LW_BARCODE" jdbcType="VARCHAR"/>
            <result property="lwMainBarcode" column="LW_MAIN_BARCODE" jdbcType="VARCHAR"/>
            <result property="lwOperDate" column="LW_OPER_DATE" jdbcType="TIMESTAMP"/>
            <result property="lwTubeType" column="LW_TUBE_TYPE" jdbcType="VARCHAR"/>
            <result property="lwSampleType" column="LW_SAMPLE_TYPE" jdbcType="VARCHAR"/>
            <result property="lwCombBarCode" column="LW_COMB_BAR_CODE" jdbcType="VARCHAR"/>
            <result property="labPackageId" column="LAB_PACKAGE_ID" jdbcType="VARCHAR"/>
            <result property="labPackageName" column="LAB_PACKAGE_NAME" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        BARCODE,OUT_TESTITEM_CODE,OUT_TESTITEM_NAME,
        TESTITEM_CODE,HSP_ORG_CODE,IS_FEE,
        IS_FORBIDDEN,FEE_NUM,PROPERTY1,
        PROPERTY2,PROPERTY3,PROPERTY4,
        PROPERTY5,FEE_TYPE,SHOW_NO,
        TESTITEM_NAME,LW_HSP_ORG_CODE,LW_BARCODE,
        LW_MAIN_BARCODE,LW_OPER_DATE,LW_TUBE_TYPE,
        LW_SAMPLE_TYPE,LW_COMB_BAR_CODE,LAB_PACKAGE_ID,
        LAB_PACKAGE_NAME
    </sql>
</mapper>
