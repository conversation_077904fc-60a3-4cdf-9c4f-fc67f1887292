<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.busniess.center.nanjing.middlebase.jianyan.persistence.mapper.LisHiResultMapper">

    <resultMap id="BaseResultMap" type="com.labway.busniess.center.nanjing.middlebase.jianyan.persistence.entity.LisHiResult">
            <result property="testMainId" column="TEST_MAIN_ID" jdbcType="VARCHAR"/>
            <result property="barcode" column="BARCODE" jdbcType="VARCHAR"/>
            <result property="itemCode" column="ITEM_CODE" jdbcType="VARCHAR"/>
            <result property="itemName" column="ITEM_NAME" jdbcType="VARCHAR"/>
            <result property="machineCode" column="MACHINE_CODE" jdbcType="VARCHAR"/>
            <result property="sampleNo" column="SAMPLE_NO" jdbcType="VARCHAR"/>
            <result property="testDate" column="TEST_DATE" jdbcType="VARCHAR"/>
            <result property="reportName" column="REPORT_NAME" jdbcType="VARCHAR"/>
            <result property="result" column="RESULT" jdbcType="VARCHAR"/>
            <result property="resultJudge" column="RESULT_JUDGE" jdbcType="VARCHAR"/>
            <result property="unit" column="UNIT" jdbcType="VARCHAR"/>
            <result property="flag" column="FLAG" jdbcType="VARCHAR"/>
            <result property="upValue" column="UP_VALUE" jdbcType="VARCHAR"/>
            <result property="downValue" column="DOWN_VALUE" jdbcType="VARCHAR"/>
            <result property="machineValue" column="MACHINE_VALUE" jdbcType="VARCHAR"/>
            <result property="range" column="RANGE" jdbcType="VARCHAR"/>
            <result property="testitemCode" column="TESTITEM_CODE" jdbcType="VARCHAR"/>
            <result property="testitemName" column="TESTITEM_NAME" jdbcType="VARCHAR"/>
            <result property="itemEnglish" column="ITEM_ENGLISH" jdbcType="VARCHAR"/>
            <result property="itemEnglishSp" column="ITEM_ENGLISH_SP" jdbcType="VARCHAR"/>
            <result property="showNo" column="SHOW_NO" jdbcType="INTEGER"/>
            <result property="testitemNum" column="TESTITEM_NUM" jdbcType="INTEGER"/>
            <result property="outTestitemCode" column="OUT_TESTITEM_CODE" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        [TEST_MAIN_ID],[BARCODE],[ITEM_CODE],
        [ITEM_NAME],[MACHINE_CODE],[SAMPLE_NO],
        [TEST_DATE],[REPORT_NAME],[RESULT],
        [RESULT_JUDGE],[UNIT],[FLAG],
        [UP_VALUE],[DOWN_VALUE],[MACHINE_VALUE],
        [RANGE],[TESTITEM_CODE],[TESTITEM_NAME],
        [ITEM_ENGLISH],[ITEM_ENGLISH_SP],[SHOW_NO],
        [TESTITEM_NUM],[OUT_TESTITEM_CODE]
    </sql>

    <insert id="backup">
        INSERT INTO LIS_HI_RESULT_BACKUP(<include refid="Base_Column_List" />)
        SELECT <include refid="Base_Column_List" /> FROM LIS_HI_RESULT
        WHERE TEST_MAIN_ID IN
        <foreach collection="testMainIds" item="testMainId" separator="," open="(" close=")">
            #{testMainId}
        </foreach>
    </insert>

</mapper>
