<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.busniess.center.nanjing.middlebase.jianyan.persistence.mapper.TestMainFailMapper">

    <resultMap id="BaseResultMap" type="com.labway.busniess.center.nanjing.middlebase.jianyan.persistence.entity.TestMainFail">
            <result property="testMainId" column="TEST_MAIN_ID" jdbcType="VARCHAR"/>
            <result property="testMainOuterId" column="TEST_MAIN_OUTER_ID" jdbcType="VARCHAR"/>
            <result property="barcode" column="BARCODE" jdbcType="VARCHAR"/>
            <result property="outerBarcode" column="OUTER_BARCODE" jdbcType="VARCHAR"/>
            <result property="hspOrgCode" column="HSP_ORG_CODE" jdbcType="VARCHAR"/>
            <result property="errorMsg" column="ERROR_MSG" jdbcType="VARCHAR"/>
            <result property="operateDate" column="OPERATE_DATE" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        TEST_MAIN_ID,TEST_MAIN_OUTER_ID,BARCODE,
        OUTER_BARCODE,HSP_ORG_CODE,ERROR_MSG,
        OPERATE_DATE
    </sql>
</mapper>
