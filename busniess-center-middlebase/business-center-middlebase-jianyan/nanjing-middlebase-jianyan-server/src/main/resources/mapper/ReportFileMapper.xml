<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.busniess.center.nanjing.middlebase.jianyan.persistence.mapper.ReportFileMapper">

    <resultMap id="BaseResultMap" type="com.labway.busniess.center.nanjing.middlebase.jianyan.persistence.entity.LisReportFile">
            <result property="id" column="ID" jdbcType="VARCHAR"/>
            <result property="fileBlob" column="FILE_BLOB" jdbcType="OTHER"/>
            <result property="fileName" column="FILE_NAME" jdbcType="VARCHAR"/>
            <result property="fileType" column="FILE_TYPE" jdbcType="VARCHAR"/>
            <result property="remark" column="REMARK" jdbcType="VARCHAR"/>
            <result property="orgId" column="ORG_ID" jdbcType="VARCHAR"/>
            <result property="reportType" column="REPORT_TYPE" jdbcType="VARCHAR"/>
            <result property="isEnable" column="IS_ENABLE" jdbcType="INTEGER"/>
            <result property="operType" column="OPER_TYPE" jdbcType="INTEGER"/>
            <result property="outKey" column="OUT_KEY" jdbcType="VARCHAR"/>
            <result property="zdy1" column="ZDY1" jdbcType="VARCHAR"/>
            <result property="zdy2" column="ZDY2" jdbcType="VARCHAR"/>
            <result property="zdy3" column="ZDY3" jdbcType="VARCHAR"/>
            <result property="zdy4" column="ZDY4" jdbcType="VARCHAR"/>
            <result property="zdy5" column="ZDY5" jdbcType="VARCHAR"/>
            <result property="barcode" column="BARCODE" jdbcType="VARCHAR"/>
            <result property="outBarcode" column="OUT_BARCODE" jdbcType="VARCHAR"/>
            <result property="pageKind" column="PAGE_KIND" jdbcType="VARCHAR"/>
            <result property="testMainId" column="TEST_MAIN_ID" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        [ID],[FILE_BLOB],[FILE_NAME],
        [FILE_TYPE],[REMARK],[ORG_ID],
        [REPORT_TYPE],[IS_ENABLE],[OPER_TYPE],
        [OUT_KEY],[ZDY1],[ZDY2],
        [ZDY3],[ZDY4],[ZDY5],
        [BARCODE],[OUT_BARCODE],[PAGE_KIND],
        [TEST_MAIN_ID]
    </sql>

    <insert id="backup">
        INSERT INTO LIS_REPORT_FILE_BACKUP(<include refid="Base_Column_List" />)
        SELECT <include refid="Base_Column_List" /> FROM LIS_REPORT_FILE
        WHERE TEST_MAIN_ID IN
        <foreach collection="testMainIds" item="testMainId" separator="," open="(" close=")">
            #{testMainId}
        </foreach>
    </insert>
    <insert id="backupByBarcode">
        INSERT INTO LIS_REPORT_FILE_BACKUP(<include refid="Base_Column_List" />)
        SELECT <include refid="Base_Column_List" /> FROM LIS_REPORT_FILE
        WHERE BARCODE = #{barcode}
    </insert>

</mapper>
