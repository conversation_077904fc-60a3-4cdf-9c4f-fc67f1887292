package com.labway.business.center.shanghai.middlebase.jianyan.dto.deda;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@XmlRootElement(name = "Request")
@XmlAccessorType(XmlAccessType.FIELD)
public class NotifySignRequest {

    @XmlElement(name = "Header")
    private Header header;

    @XmlElement(name = "Body")
    private Body body;


    @Data
    @AllArgsConstructor
    @XmlAccessorType(XmlAccessType.FIELD)
    public static class Header {

        @XmlElement(name = "SourceSystem")
        private String SourceSystem;

        @XmlElement(name = "MessageID")
        private String MessageID;
    }

    @Data
    @AllArgsConstructor
    @XmlAccessorType(XmlAccessType.FIELD)
    public static class Body {

        @XmlElement(name = "SpecimenReceiveRt")
        private SpecimenReceiveRt SpecimenReceiveRt;

    }

    @Data
    @AllArgsConstructor
    @XmlAccessorType(XmlAccessType.FIELD)
    public static class SpecimenReceiveRt {

        @XmlElement(name = "SpecimenID")
        private String SpecimenID;

        @XmlElement(name = "OrderItemIDs")
        private List<OrderItemID> OrderItemIDs;

        @XmlElement(name = "ExecUserCode")
        private String ExecUserCode;

        @XmlElement(name = "SpeciReceiveDate")
        private String SpeciReceiveDate;

        @XmlElement(name = "SpeciReceiveTime")
        private String SpeciReceiveTime;

    }

    @Data
    @AllArgsConstructor
    @XmlAccessorType(XmlAccessType.FIELD)
    public static class OrderItemID {

        @XmlElement(name = "OrderItemID")
        private String OrderItemID;

    }
}
