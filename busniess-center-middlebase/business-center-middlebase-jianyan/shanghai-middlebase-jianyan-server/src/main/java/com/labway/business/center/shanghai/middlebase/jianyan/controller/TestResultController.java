package com.labway.business.center.shanghai.middlebase.jianyan.controller;

import com.alibaba.fastjson.JSONObject;
import com.labway.business.center.shanghai.middlebase.jianyan.config.BusinessConfig;
import com.labway.business.center.shanghai.middlebase.jianyan.request.PushResultJobFromLjDto;
import com.labway.business.center.shanghai.middlebase.jianyan.service.ShanghaiResultService;
import com.labway.business.center.shanghai.middlebase.jianyan.service.impl.PuZhongXinResultServiceImpl;
import com.labway.busniess.center.middlebase.jianyan.dto.PushSampleResultDto;
import com.labway.busniess.center.middlebase.jianyan.reqeust.CallbackResultRequest;
import com.labway.busniess.center.middlebase.jianyan.reqeust.LisTestMainRequest;
import com.swak.frame.dto.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.StringJoiner;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/jianyan/middle/result")
public class TestResultController implements InitializingBean {

    private Collection<ShanghaiResultService> shanghaiResultServices;
    @Resource
    private ApplicationContext applicationContext;
    @Resource
    private PuZhongXinResultServiceImpl puZhongXinResultService;
    @Resource
    private BusinessConfig businessConfig;

    @Override
    public void afterPropertiesSet() {
        this.shanghaiResultServices = Collections.unmodifiableCollection(applicationContext.getBeansOfType(ShanghaiResultService.class).values());
        for (ShanghaiResultService e : this.shanghaiResultServices) {
            log.info("初始化送检机构 [{}] [{}]", e.getHspOrgCode(), e);
            Objects.requireNonNull(e.getHspOrgCode(), e.toString());
        }
    }

    /**
     * 手动拉取样本结果 （从业务中台推）
     *
     * @return
     */
    @PostMapping("/shanghaiPuZhongXinPushSampleResult")
    public Response<?> shanghaiPuZhongXinPushSampleResult(@RequestBody @Valid PushSampleResultDto pushSampleResultDto) {
        log.info("手动拉取样本结果,调用入参：{}", JSONObject.toJSONString(pushSampleResultDto));
        return puZhongXinResultService.pushSampleResult(pushSampleResultDto);
    }

    /**
     * 手动拉取样本结果 （从业务中台推）
     *
     * @return
     */
    @PostMapping("/pushSampleResultFromLj2Pzx")
    public Response<?> pushSampleResultFromLj2Pzx(@RequestBody @Valid PushResultJobFromLjDto pushSampleResultDto) {
        log.info("手动从Lj 拉取样本结果,调用入参：{}", JSONObject.toJSONString(pushSampleResultDto));
        return puZhongXinResultService.pushPuZhongXinSampleResultFromLj(pushSampleResultDto);
    }

    @PostMapping("/receive")
    public Response<?> receiveLimsResult(@RequestBody @Valid @NotNull @Size(min = 1) List<LisTestMainRequest> requests) {
        log.info("上海中间程序接收样本结果，调用入参：{}", JSONObject.toJSONString(requests));

        final Map<String, List<LisTestMainRequest>> collect = requests.stream().collect(Collectors.groupingBy(LisTestMainRequest::getHspOrgCode));

        for (Map.Entry<String, List<LisTestMainRequest>> entrie : collect.entrySet()) {
            Response<?> response = getResultService(entrie.getKey()).receiveLimsResult(entrie.getValue());
            if(!response.isSuccess()){
                return response;
            }
        }
        return Response.success();
    }

    @PostMapping("/callback")
    public Response<?> callbackResult(@RequestBody @Valid CallbackResultRequest request) {
        log.info("上海中间程序撤回样本结果，调用入参：{}", JSONObject.toJSONString(request));
        return getResultService(request.getHspOrgCode()).callbackResult(request);
    }

    private ShanghaiResultService getResultService(String hspOrgCode) {

        if (businessConfig.getNoNotifyHspOrgCodes().contains(hspOrgCode)) {
            throw new UnsupportedOperationException(String.format("该机构 [%s] 样本结果无需回传", hspOrgCode));
        }

        for (ShanghaiResultService resultService : shanghaiResultServices) {
            if (resultService.getHspOrgCode().contains(hspOrgCode)) {
                return resultService;
            }
        }
        throw new UnsupportedOperationException(String.format("无法识别送检机构编码 [%s]", hspOrgCode));
    }

}
