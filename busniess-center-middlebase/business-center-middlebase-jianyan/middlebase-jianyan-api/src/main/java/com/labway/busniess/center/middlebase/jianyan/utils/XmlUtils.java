package com.labway.busniess.center.middlebase.jianyan.utils;

import cn.hutool.core.util.XmlUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.w3c.dom.Element;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Marshaller;
import javax.xml.bind.Unmarshaller;
import java.io.StringReader;
import java.io.StringWriter;
import java.util.Objects;
import java.util.regex.Pattern;
import java.util.regex.Matcher;

/**
 * <AUTHOR>
 */
@Slf4j
public class XmlUtils {

    /**
     * 将返回数据转换为XML格式(格式化、不省略头信息)
     */
    public static String responseToXml(Object obj) {
        return responseToXml(obj, "UTF-8");
    }

    /**
     * 将返回数据转换为XML格式(格式化、不省略头信息)
     */
    public static String responseToXml(Object obj, String encoding) {
        try {
            JAXBContext context = JAXBContext.newInstance(obj.getClass());

            Marshaller marshaller = context.createMarshaller();
            // //编码格式
            marshaller.setProperty(Marshaller.JAXB_ENCODING, encoding);
            // 是否格式化生成的xml串
            marshaller.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT, false);
            // 是否省略xm头声明信息
            marshaller.setProperty(Marshaller.JAXB_FRAGMENT, false);

            StringWriter writer = new StringWriter();
            marshaller.marshal(obj, writer);
            // 手动修改XML声明以包含standalone属性
            return writer.toString();
        } catch (Exception ex) {
            throw new RuntimeException(ex);
        }
    }

    /**
     * 将返回数据转换为XML格式(不格式化、省略头信息)
     */
    public static String responseToNoHeadXml(Object obj) {
        try {
            JAXBContext context = JAXBContext.newInstance(obj.getClass());

            Marshaller marshaller = context.createMarshaller();
            //编码格式
            marshaller.setProperty(Marshaller.JAXB_ENCODING, "UTF-8");
            // 是否格式化生成的xml串
            marshaller.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT, false);
            // 是否省略xm头声明信息
            marshaller.setProperty(Marshaller.JAXB_FRAGMENT, true);
            StringWriter writer = new StringWriter();
            marshaller.marshal(obj, writer);
            return writer.toString();
        } catch (Exception ex) {
            throw new RuntimeException(ex);
        }
    }

    public static String getXmlContent(Element element,String tagName){
        if (Objects.isNull(element)){
            return "";
        }
        return XmlUtil.getElement(element,tagName).getTextContent();
    }


    /**
     * xml 转 对象
     */
    public static <T> T xml2Bean(String xmlStr, Class<T> clazz) {
        T obj = null;
        try {
            JAXBContext jaxbContext = JAXBContext.newInstance(clazz);
            Unmarshaller unmarshaller = jaxbContext.createUnmarshaller();
            obj = (T) unmarshaller.unmarshal(new StringReader(xmlStr));
        } catch (JAXBException ex) {
            log.error("xml 转 对象异常", ex);
        }
        return obj;
    }

    /**
     * 去除xml文件中的前缀
     * @param xml
     * @return
     */
    public static String removeXmlDeclaration(String xml) {
        String pattern = "<\\?xml[^>]+\\?>";
        Pattern xmlDeclarationPattern = Pattern.compile(pattern);
        Matcher matcher = xmlDeclarationPattern.matcher(xml);
        return matcher.replaceAll("");
    }

    public static String unescapeXml(String input) {
        if (StringUtils.isBlank(input)) {
            return input;
        }
        input = input.replaceAll("&lt;", "<");
        input = input.replaceAll("&gt;", ">");
        input = input.replaceAll("&quot;", "\"");
        input = input.replaceAll("&apos;", "'");
        input = input.replaceAll("&amp;", "&");
        return input;
    }

}
