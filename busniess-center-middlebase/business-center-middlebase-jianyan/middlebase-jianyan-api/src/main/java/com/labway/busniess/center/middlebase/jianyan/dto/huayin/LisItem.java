package com.labway.busniess.center.middlebase.jianyan.dto.huayin;

import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import java.util.Date;
import java.util.List;

@Getter
@Setter
@XmlAccessorType(XmlAccessType.FIELD)
public class LisItem {

    // 送检项目编码
    @XmlElement(name = "lis_item_code")
    private String lisItemCode;

    // 送检项目名称
    @XmlElement(name = "lis_item_name")
    private String lisItemName;

    // 子项目列表
    // @XmlElementWrapper(name = "OUT_APPLY_ITEM_LIST")
    @XmlElement(name = "SubItems")
    private List<SubItem> subItems;

}
