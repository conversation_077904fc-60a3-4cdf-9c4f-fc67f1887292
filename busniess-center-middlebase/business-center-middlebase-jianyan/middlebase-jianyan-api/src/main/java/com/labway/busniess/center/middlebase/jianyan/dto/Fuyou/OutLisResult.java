package com.labway.busniess.center.middlebase.jianyan.dto.Fuyou;

import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import java.io.Serializable;

@Getter
@Setter
@XmlAccessorType(XmlAccessType.FIELD)
public class OutLisResult implements Serializable {

    private static final long serialVersionUID = 1L;


    // 报告项目ID
    @XmlElement(name = "REPORT_ITEM_ID")
    private String reportItemId;

    // 报告项目名称
    @XmlElement(name = "REPORT_ITEM_NAME")
    private String reportItemName;

    // 结果值
    @XmlElement(name = "RESULT_VALUE")
    private String resultValue;

    // 结果单位
    @XmlElement(name = "RESULT_UNIT")
    private String resultUnit;

    // 结果标记
    @XmlElement(name = "RESULT_MARK")
    private String resultMark;

    // 结果参考范围
    @XmlElement(name = "REF_RANGES_TEXT_CN")
    private String refRangesTextCn;
}
