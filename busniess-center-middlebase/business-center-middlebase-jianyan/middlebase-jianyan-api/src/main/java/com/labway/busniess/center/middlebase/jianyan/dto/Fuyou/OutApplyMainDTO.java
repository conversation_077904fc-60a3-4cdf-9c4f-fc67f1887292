package com.labway.busniess.center.middlebase.jianyan.dto.Fuyou;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.*;
import java.util.Date;
import java.util.List;

/**
 *  外送检验申单数据
 */
@XmlRootElement(name = "OUT_APPLY_MAIN")
@XmlAccessorType(XmlAccessType.FIELD)
@Data
public class OutApplyMainDTO {

    // 申请机构名称
    @XmlElement(name = "APP_ORG_NAME")
    private String appOrgName;

    // 申请机构CODE
    @XmlElement(name = "APP_ORG_ID")
    private String appOrgId;

    // 录入机构名称
    @XmlElement(name = "CREATE_ORG_NAME")
    private String createOrgName;

    // 录入机构ID
    @XmlElement(name = "CREATE_ORG_ID")
    private String createOrgId;

    // 申请单外部编码
    @XmlElement(name = "APP_OUTER_CODE")
    private String appOuterCode;

    // 申请病区编码
    @XmlElement(name = "INPATIENT_AREA_OUTER_ID")
    private String inpatientAreaOuterId;

    // 申请病区
    @XmlElement(name = "INPATIENT_AREA")
    private String inpatientArea;

    // 申请科室编码
    @XmlElement(name = "APP_DEPT_OUTER_ID")
    private String appDeptOuterId;

    // 申请科室
    @XmlElement(name = "APP_DEPT")
    private String appDept;

    // 申请医生编码
    @XmlElement(name = "APP_DOCTOR_OUTER_ID")
    private String appDoctorOuterId;

    // 申请医生姓名
    @XmlElement(name = "APP_DOCTOR_NAME")
    private String appDoctorName;

    // 申请时间
    @XmlElement(name = "APP_DATE")
    private Date appDate;

    // 临床诊断
    @XmlElement(name = "DIAG")
    private String diag;

    // 是否加急
    @XmlElement(name = "IS_URGENT")
    private String isUrgent;

    // 病人LIS编号
    @XmlElement(name = "PATIENT_LIS_ID")
    private String patientLisId;

    // 病人姓名
    @XmlElement(name = "PATIENT_NAME")
    private String patientName;

    // 性别
    @XmlElement(name = "SEX")
    private String sex;

    // 年龄
    @XmlElement(name = "AGE")
    private String age;

    // 生日
    @XmlElement(name = "BIRTHDAY")
    private Date birthday;

    // 身份证号
    @XmlElement(name = "IDENTITY_CARD_NUMBER")
    private String identityCardNumber;

    // 地址
    @XmlElement(name = "ADDRESS")
    private String address;

    // 电话
    @XmlElement(name = "TEL")
    private String tel;

    // 床号
    @XmlElement(name = "BED")
    private String bed;

    // 备注
    @XmlElement(name = "MEMO")
    private String memo;

    // 是否有效
    @XmlElement(name = "ENABLED")
    private String enabled;

    // 条码列表
    @XmlElementWrapper(name = "OUT_APPLY_BARCODE_LIST")
    @XmlElement(name = "OUT_APPLY_BARCODE")
    private List<OutApplyBarcode> outApplyBarcodeList;

}

