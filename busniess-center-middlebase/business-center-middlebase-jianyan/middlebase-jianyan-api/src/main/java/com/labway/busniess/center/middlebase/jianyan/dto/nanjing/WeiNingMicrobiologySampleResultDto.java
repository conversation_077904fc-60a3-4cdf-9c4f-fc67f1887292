package com.labway.busniess.center.middlebase.jianyan.dto.nanjing;

import com.fasterxml.jackson.annotation.JsonRootName;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 *  卫宁报告未生物检验结果信息 -xml
 */
@Getter
@Setter
@JsonRootName("root")
public class WeiNingMicrobiologySampleResultDto {
    private WeiNingRoutineSampleResultDto.RequestBase requestBase;
    @Getter
    @Setter
    public static class RequestBase {
        // 请求调用者的机构代码
        private String qqjgdm;
        // 请求调用者的代码
        private String qqrydm;
        // 请求调用者的姓名
        private String qqryxm;
        // 微生物结果标志 0-常规检验报告 1-微生物报告
        private Integer germflag;
        // 微生物报告类型  2-涂片报告 3-阴性报告 4-阳性报告
        private String germtype;
    }

    private List<Appraisal> appraisalList;

    /**
     *  微生物检验项目信息
     */
    @Getter
    @Setter
    public static class Appraisal{
        // 送检机构代码
        private String sjjgdm;
        // 报告单号
        private String applyNo;
        // 标本条形码
        private String barcode;
        //病人唯一码
        private String patientId;
        //就诊号
        private String cureNo;
        // 病人类别 0-门诊1-住院 3、4-体检
        private String wardOrReg;
        //卡类型
        private String cardType;
        // 病员号/住院号
        private String hospNo;
        // 磁卡号
        private String cardNo;
        // 患者姓名
        private String patName;
        // 鉴定/涂片ID
        private String appraisalId;
        // 涂片方法代码
        private String smearCode;
        // 涂片方法名称
        private String smearName;
        // 涂片结果
        private String smearResult;
        // 鉴定结果代码
        private String organismCode;
        // 鉴定结果名称
        private String organismName;
        // 菌落计数
        private String quantity;
        // 结果
        private String result;
        // 药敏方法代码
        private Integer testMethodCode;
        // 药敏方法名称
        private String testMethodName;
        // 耐药机制代码
        private String antiMethodCode;
        // 耐药机制名称
        private String antiMethodName;
        // 备注
        private String remark;
        // 综合评价3834
        private String summingUp;
        // 鉴定/涂片人代码
        private String appraisalCode;
        // 鉴定/涂片人姓名
        private String appraisalName;
        // 鉴定时间
        private String appraisalTime;
        // 阳性才有的-药敏结果
        private List<Allergy> allergyList;
    }

    /**
     * 药敏结果（报告为阳性时）
     */
    @Getter
    @Setter
    public static class Allergy{
        // 药敏ID
        private String appraisalAntiId;
        // 鉴定ID
        private String appraisalId;
        //抗生素代码
        private String antiCode;
        //抗生素名称
        private String antiName;
        //测定值
        private String antiValue;
        //参考范围
        private String referValue;
        //药敏结果
        private String antiResult;
        //备注
        private String remark;
        //抗生素分组
        private String antiGroup;
        //打印标志 0-不打印 1-打印
        private String printFlag;
        // 打印顺序
        private Integer printOrder;
    }
    private List<ReportImage> reportImageList;
    /**
     *  pdf报告
     */
    @Getter
    @Setter
    public static class ReportImage {
        // 送检机构代码
        private String sjjgdm;
        // 报告单号
        private String applyNo;
        // 条形码
        private String barcode;
        // 图像名
        private String fileName;
        // 图像格式
        private Integer imageFormat;
        // 图像二进制数据
        private String imageData;
    }
}
