package com.labway.busniess.center.middlebase.jianyan.dto.huayin;

import lombok.Getter;
import lombok.Setter;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import java.io.Serializable;

@Getter
@Setter
@XmlAccessorType(XmlAccessType.FIELD)
public class ResultInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    // 序号
    @XmlElement(name = "result_seq")
    private String resultSeq;

    // 组合项目代码
    @XmlElement(name = "ext_combine_code")
    private String extCombineCode;

    // 组合项目名称
    @XmlElement(name = "ext_combine_name")
    private String extCombineName;

    // 结果项目代码
    @XmlElement(name = "ext_item_code")
    private String extItemCode;

    // 结果项目名称
    @XmlElement(name = "ext_item_name")
    private String extItemName;

    // 结果值
    @XmlElement(name = "result")
    private String result;

    // 结果单位
    @XmlElement(name = "result_unit")
    private String resultUnit;

    // 高低标记
    @XmlElement(name = "result_flag")
    private String resultFlag;

    // 结果参考范围
    @XmlElement(name = "result_reference")
    private String resultReference;

    // 报告时间
    @XmlElement(name = "result_date")
    private String resultDate;

    // 检测仪器
    @XmlElement(name = "result_intstrmt_name")
    private String resultIntstrmtName;

    // 检测方法
    @XmlElement(name = "result_test_method")
    private String resultTestMethod;

    // 建议与解释
    @XmlElement(name = "result_suggestion")
    private String resultSuggestion;

    // 备注
    @XmlElement(name = "result_remark")
    private String resultRemark;

    // 医院组合项目代码
    @XmlElement(name = "lis_combine_code")
    private String lisCombineCode;

    // 医院组合项目名称
    @XmlElement(name = "lis_combine_name")
    private String lisCombineName;

    // 医院结果项目代码
    @XmlElement(name = "lis_item_code")
    private String lisItemCode;

    // 医院结果项目名称
    @XmlElement(name = "lis_item_name")
    private String lisItemName;

}
