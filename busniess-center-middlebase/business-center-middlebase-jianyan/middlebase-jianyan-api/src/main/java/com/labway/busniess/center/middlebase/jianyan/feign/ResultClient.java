package com.labway.busniess.center.middlebase.jianyan.feign;


import com.labway.busniess.center.middlebase.jianyan.reqeust.CallbackResultRequest;
import com.labway.busniess.center.middlebase.jianyan.reqeust.LisTestMainRequest;
import com.swak.frame.dto.Response;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

/**
 * <p>
 * ResultClient
 * 检验结果
 * </p>
 *
 * <AUTHOR>
 * @since 2023/10/21 16:12
 */
public interface ResultClient {

    /**
     * 接收回传结果（源于LIMS）
     * @param requests {@link LisTestMainRequest}
     * @return
     */
    @RequestMapping(value = "/jianyan/middle/result/receive", method = RequestMethod.POST)
    Response<?> receiveLimsResult(@RequestBody List<LisTestMainRequest> requests);

    /**
     * 结果召回
     * @param request {@link CallbackResultRequest}
     * @return
     */
    @RequestMapping(value = "/jianyan/middle/result/callback", method = RequestMethod.POST)
    Response<?> callbackResult(@RequestBody CallbackResultRequest request);

}
