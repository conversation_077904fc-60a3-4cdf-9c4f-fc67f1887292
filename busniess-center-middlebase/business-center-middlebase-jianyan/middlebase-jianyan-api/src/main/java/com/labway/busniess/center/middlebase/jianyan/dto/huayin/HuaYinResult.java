package com.labway.busniess.center.middlebase.jianyan.dto.huayin;


import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.labway.busniess.center.middlebase.jianyan.utils.XmlUtils;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

@XmlRootElement(name = "Data")
@XmlAccessorType(XmlAccessType.FIELD)
@Data
@NoArgsConstructor
public class HuaYinResult {

    public static final Integer SUCCESS = 0;
    public static final Integer FAIL = -1;

    @XmlElement(name = "Data_Row")
    private DataRow outApplyMainDTO;

    private Integer code;
    private String message;
    private Object data;

    public HuaYinResult(DataRow outApplyMainDTO){
        this.outApplyMainDTO = outApplyMainDTO;
    }

    public HuaYinResult(Integer code, String message, Object data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }

    public static HuaYinResult success(String message, Object data) {
        return new HuaYinResult(SUCCESS, message, data);
    }

    public static HuaYinResult success(String message) {
        return new HuaYinResult(SUCCESS, message, null);
    }

    public static HuaYinResult fail(String message, Object data) {
        return new HuaYinResult(FAIL, message, data);
    }

    public static HuaYinResult fail(String message) {
        return new HuaYinResult(FAIL, message, null);
    }

    public static void main(String[] args) {

        DataRow dataRow = new DataRow();

        dataRow.setLisBarcode("xxxxxx");
        dataRow.setPatName("病人姓名");
        dataRow.setBloodTime(DateUtil.formatDateTime(new DateTime()));
        dataRow.setDoctorName("Doctor Wang.");
        dataRow.setLisItems(List.of(new LisItem() {{
            setLisItemCode("11111");
            setLisItemName("11111");
            setSubItems(List.of());
        }}, new LisItem() {{
            setLisItemCode("22222");
            setLisItemName("22222");
            setSubItems(List.of());
        }}));

        System.out.println(XmlUtils.responseToXml(new HuaYinResult(dataRow)));

    }

}
