package com.labway.busniess.center.middlebase.jianyan.reqeust;


import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 微生物结果主表
 */
@Data
public class LisGermResultMainRequest implements Serializable {
    /**
     * 条码编码
     */
    private String barcode;

    /**
     * 仪器编码
     */
    private String machineCode;

    /**
     * 样本单号
     */
    private String sampleNo;

    /**
     * 检验日期,做主键之一
     */
    private String testDate;

    /**
     * 检验项目编码
     */
    private String testitemCode;

    /**
     * 检验项目名称
     */
    private String testitemName;

    /**
     * 培养结果(生长状况)
     */
    private String trainResult;

    /**
     * 结果类型(1阴性2阳性药敏3涂片)
     */
    private String resultType;

    /**
     * β-内酰胺酶 + - 空
     */
    private String m1;

    /**
     * 超广谱B-内酰胺酶 + - 空
     */
    private String m2;

    /**
     * WHONET 年龄类别
     */
    private String whonetAge;

    /**
     * WHONET 医院
     */
    private String whonetHsp;

    /**
     * WHONET 病区类型
     */
    private String whonetWardstype;

    /**
     * WHONET 科别
     */
    private String whonetDept;

    /**
     * WHONET 样本选择原因
     */
    private String whonetSamplecause;

    /**
     * WHONET 血清类型
     */
    private String whonetBloodtype;

    /**
     * WHONET 病区
     */
    private String whonetWards;

    /**
     * 选择评语
     */
    private String whonetSecommnet;

    /**
     * 主键ID
     */
    private String germResultMainId;

    /**
     * 细菌编码
     */
    private String germCode;

    /**
     * 细菌名称
     */
    private String germName;

    /**
     * 检验操作方法标志(0:手工.1:仪器MIC法.2:仪器KB法)
     */
    private Integer operType;

    /**
     * 检验方法规则(KB法、MIC法)
     */
    private String rule;

    /**
     * 培养结果编码
     */
    private String trainResultCode;

    /**
     * 细菌类型
     */
    private String germTypeCode;

    /**
     * 显示顺序
     */
    private Integer showNumber;

    /**
     * 菌落计数
     */
    private String germCount;

    /**
     * 细菌备注编码(字典表)
     */
    private String germMemoCode;

    /**
     * 专家评语
     */
    private String expertcomments;

    /**
     * 报告单主键
     */
    private String testMainId;

    /**
     * 细菌英文名称
     */
    private String germEname;

    /**
     * 细菌英文名缩写
     */
    private String germEnameSx;

    /**
     * 细菌培养描述索引
     */
    private Integer trainResultno;

    /**
     * 细菌培养描述英文
     */
    private String trainResultEname;

    /**
     * 检验项目英文名称
     */
    private String testitemEnglish;

    /**
     * 检验项目英文名称
     */
    private String germMemoEname;

    /**
     * 是否分级审核(0否1是)
     */
    private Integer isGroupCheck;

    /**
     * 分级审核人编码
     */
    private String groupCheckUserCode;

    /**
     * 分级审核人
     */
    private String groupCheckUserName;

    /**
     * 分级审核时间
     */
    private Date groupCheckDate;

    /**
     * 取消分级审核
     */
    private Integer isGroupCheck2;

    /**
     * 取消分级审核人编码
     */
    private String groupCheckUserCode2;

    /**
     * 取消分级审核人
     */
    private String groupCheckUserName2;

    /**
     * 取消分级审核时间
     */
    private Date groupCheckDate2;

    /**
     * 检验ID
     */
    private Integer testId;

    /**
     * 外部检测项目编码
     */
    private String outTestitemCode;

    /**
     * 结果是否存在危急值(0否1是)
     */
    private String isExistcrisis;

    /**
     * 微生物耐药结果
     */
    private List<LisGermResultRequest> germResultRequests;

    private static final long serialVersionUID = 1L;

}