package com.labway.busniess.center.middlebase.jianyan.reqeust;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <p>
 * CallbackResultRequest
 * 取消审核
 * </p>
 *
 * <AUTHOR>
 * @since 2023/7/17 13:28
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CallbackResultRequest implements Serializable {

    // 送检机构编码
    private String hspOrgCode;

    // 检验机构编码
    private String orgCode;

    // 条码号
    private String barcode;

    // 外部条码号（兰卫）
    private String outerBarcode;

    // 报告单编号
    private String reportNo;

    // 操作人
    private String operatorCode;
    private String operatorName;

}
