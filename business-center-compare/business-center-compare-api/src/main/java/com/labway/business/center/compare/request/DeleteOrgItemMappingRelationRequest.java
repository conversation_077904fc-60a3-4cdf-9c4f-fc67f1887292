package com.labway.business.center.compare.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2023/04/23 16:05
 * @description
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DeleteOrgItemMappingRelationRequest implements Serializable {

    //外部项目编码
    @NotBlank(message = "外部项目编码不能为空！")
    private String itemOutsideCode;

    //客商编码
    @NotBlank(message = "客商编码不能为空！")
    private String customerCode;

    //业务单元编码
    @NotBlank(message = "业务单元编码不能为空！")
    private String orgCode;

    // 检验项目
    @NotEmpty(message = "")
    private List<String> itemTestCodeList;

    // 操作用户id
    private String optUserId;

    // 操作用户名称
    private String optUserName;


}