package com.labway.business.center.compare.dto.changzhou.tianning;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;

/**
 * <p>
 * ReportDownLoadRequest
 * 报告单下载请求结构
 * </p>
 *
 * <AUTHOR>
 * @since 2023/11/10 10:13
 */
@XmlRootElement(name = "Root")
@XmlAccessorType(XmlAccessType.FIELD)
public class ReportDownLoadRequest implements Serializable {
    // 机构编码（必填）
    @XmlElement(name = "HspOrgCode")
    private String hspOrgCode;
    // 病人编号（必填）
    @XmlElement(name = "OutBarcode")
    private String outBarcode;
    // 检验开始时间（必填）格式yyyy-MM-dd HH:mm:ss
    @XmlElement(name = "TestTimeBegin")
    private String testTimeBegin;
    // 检验结束时间（必填）格式yyyy-MM-dd HH:mm:ss
    @XmlElement(name = "TestTimeEnd")
    private String testTimeEnd;

    public String getHspOrgCode() {return hspOrgCode;}
    public void setHspOrgCode(String hspOrgCode) {this.hspOrgCode = hspOrgCode;}
    public String getOutBarcode() {return outBarcode;}
    public void setOutBarcode(String outBarcode) {this.outBarcode = outBarcode;}
    public String getTestTimeBegin() {return testTimeBegin;}
    public void setTestTimeBegin(String testTimeBegin) {this.testTimeBegin = testTimeBegin;}
    public String getTestTimeEnd() {return testTimeEnd;}
    public void setTestTimeEnd(String testTimeEnd) {this.testTimeEnd = testTimeEnd;}

/*
<Root>
  <HspOrgCode>11601</HspOrgCode>机构编码（必填）
  <OutBarcode>E04830054</OutBarcode>病人编号（必填）
  <TestTimeBegin>2022-05-21 00:00:00</TestTimeBegin>检验开始时间（必填）格式yyyy-MM-dd HH:mm:ss
  <TestTimeEnd>2022-05-23 23:59:59</TestTimeEnd>检验结束时间（必填）格式yyyy-MM-dd HH:mm:ss
</Root>
 */

}
