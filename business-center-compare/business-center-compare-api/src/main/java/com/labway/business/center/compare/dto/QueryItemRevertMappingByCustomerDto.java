package com.labway.business.center.compare.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class QueryItemRevertMappingByCustomerDto implements Serializable {

    //回传对照关系表id 主键
    private String mappingId;
    //送检机构编码
    private String customerCode;
    //送检客商名称
    private String customerName;
    //送检客商项目编码
    private String customerItemCode;
    //送检客商项目名称
    private String customerItemName;
    //检验机构编码
    private String orgCode;
    //检验机构名称
    private String orgName;
    //检验机构项目编码
    private String orgItemCode;
    //检验机构项目名称
    private String orgItemName;
    //回传对照类型 1报告项目2细菌2药物
    private Integer mappingType;
    //创建时间
    private Date createTime;
    //创建人id
    private String createBy;
    //更新时间
    private Date updateTime;
    //更新人id
    private String updateBy;



}
