package com.labway.business.center.compare.dto.lims;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * 微生物细菌
 */
@Getter
@Setter
public class MicrobiologyGermDto implements Serializable {

	private Long germResultId;

	/**
	 * 检验项目编码
	 */
	private String testItemCode;

	/**
	 * 检验项目名称
	 */
	private String testItemName;

	/**
	 * 细菌id
	 */
	private String germId;

	/**
	 * 细菌菌属编码
	 */
	private String germGenusCode;

	/**
	 * 菌落计数
	 */
	private String germCount;

	/**
	 * 细菌菌属id
	 */
	private String germGenusId;

	/**
	 * 细菌编码
	 */
	private String germCode;

	/**
	 * 样本号
	 */
	private String sampleNo;

	/**
	 * 仪器名称
	 */
	private String machineName;

	/**
	 * 仪器编码
	 */
	private String machineCode;

	/**
	 * 细菌名称
	 */
	private String germName;

	/**
	 * 细菌备注编码
	 */
	private String germRemarkCode;

	/**
	 * 细菌备注
	 */
	private String germRemark;

	/**
	 * 专家评语
	 */
	private String comments;

	/**
	 * 阴性 阳性 涂片
	 */
	private String resultType;

	/**
	 * 细菌英文名称
	 */
	private String germEnName;

	/**
	 * WHONET 年龄类别
	 */
	private String whonetAge;

	/**
	 * WHONET 医院
	 */
	private String whonetHsp;

	/**
	 * WHONET 病区类型
	 */
	private String whonetWardstype;

	/**
	 * WHONET 科别
	 */
	private String whonetDept;

	/**
	 * WHONET 样本选择原因
	 */
	private String whonetSampleCause;

	/**
	 * 打印顺序，越小越靠前
	 */
	private Integer printSort;

    /**
     * 药物
     */
    private List<MicrobiologyMedicineDto> medicines;

}
