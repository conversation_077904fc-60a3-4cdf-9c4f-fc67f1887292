package com.labway.business.center.compare.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
@NoArgsConstructor
public class QueryOrgDictTreeRequest implements Serializable {


    /**
     * id
     */
    private String dictId;

    /**
     * 字典编码
     */
    private String dictCode;

    /**
     * 字典值
     */
    private String dictValue;

    /**
     * 字典名称
     */
    private String dictName;

    /**
     * 字典类型
     */
    private String dictType;

    /**
     * 父级id 默认0
     */
    private String dictParentId;

    /**
     * 是否启用
     */
    private Integer enabled;

    /**
     * 机构编码
     */
    @NotBlank(message = "机构编码不能为空！")
    private String orgCode;




}
