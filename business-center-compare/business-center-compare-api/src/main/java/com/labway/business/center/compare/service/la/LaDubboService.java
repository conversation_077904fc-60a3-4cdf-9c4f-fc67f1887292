package com.labway.business.center.compare.service.la;


import com.labway.business.center.compare.dto.OutApplyInfoDTO;
import com.labway.business.center.compare.dto.middlebase.OrgApplySampleMainDTO;
import com.labway.business.center.compare.request.QueryApplyRequest;
import com.labway.business.center.compare.request.SignApplyInfoRequest;
import com.swak.frame.dto.Response;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.List;

public interface LaDubboService {

    /**
     * 丹阳两癌系统登录接口
     */
    Response<String> lALogin(String region, String loginCode, String password);

    /**
     * 丹阳两癌样本查询接口3.2
     */
    Response<String> laQuerySampleInfo3(String region, String sampleCode);

    /**
     * 丹阳两癌样本查询接口1.2
     */
    Response<OutApplyInfoDTO> laQuerySampleInfo1(QueryApplyRequest request);

    /**
     * 丹阳两癌样本签收接口1.2
     */
    Response<OrgApplySampleMainDTO> laSignSampleInfo1(SignApplyInfoRequest request);

    /**
     * 丹阳两癌推送结果信息接口4.2
     */
    Response<String> laPushSampleResultInfo(String region, List<String> sampleCodes);
}
