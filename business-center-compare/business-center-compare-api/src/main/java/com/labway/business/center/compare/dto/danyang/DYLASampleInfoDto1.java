package com.labway.business.center.compare.dto.danyang;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class DYLASampleInfoDto1 implements Serializable {

    // 唯一主键，必传，唯一标识该记录，使用GUID
    private String sysId;

    // 分库键
    private String dbKey;

    // 分表键
    private String tbKey;

    // 数据来源--区域名称+公司名(如静安区臻鼎)
    private String src;

    // 扩展字段
    private String ext;

    // 数据上传时间--yyyy-mm-dd hh24:mi:ss
    private String uploadTime;

    // 检查机构代码--妇幼系统机构代码
    private String organCode;

    // 检查机构名称--妇幼系统机构名称
    private String organ;

    // 检查医生身份证号
    private String doctorCode;

    // 检查医生姓名
    private String doctor;

    // 检查日期--yyyy-mm-dd hh24:mi:ss
    private Date checkDate;

    // 妇幼系统机构代码
    private String inputOrganCode;

    // 登记机构名称--妇幼系统机构名称
    private String inputOrgan;

    // 登记医生身份证号
    private String inputDoctorCode;

    // 登记医生姓名
    private String inputDoctor;

    // 登记日期时间--yyyy-mm-dd hh24:mi:ss
    private Date inputDate;

    // 更新机构代码--妇幼系统机构代码
    private String updateOrganCode;

    // 更新机构名称--妇幼系统机构名称
    private String updateOrgan;

    // 更新医生身份证号
    private String updateDoctorCode;

    // 更新医生姓名
    private String updateDoctor;

    // 更新日期时间--yyyy-mm-dd hh24:mi:ss
    private Date updateDate;

    // 编号--由省妇幼系统统一生成
    private String no;

    // 姓名
    private String name;

    // 身份证号
    private String idcard;

    //出生日期
    private Date birthday;

    // 证件类型--(01居民省份证 02居民户口簿 03护照 04军官证 05驾驶证 06港澳居民往来内地通行证 07台湾居民来往大陆通行证 99其他法定有效证件)
    private String cardType;

    // 证件号码
    private String cardNo;

    // 就诊号
    private String patientNo;

    // 门诊号
    private String outpatientNo;

    // 宫颈癌检查标志
    private String checkCervical;

    // 乳腺癌检查标志
    private String checkBreast;

    // 市民卡号
    private String citizenNo;

    // 低保证号
    private String lowInsuranceNo;

    // 特困妇女--0-否 1-是
    private Integer poor;

    // 自愿免费检查--0-否 1-是
    private Integer freeCheck;

    // 年龄
    private Integer age;

    // 职业--字典编码见(ZYFLDMB职业分类代码)
    private String occupation;

    // 文化程度--01-研究生 02-大学本科 03-大学专科和专科学校 04-中等专业学校 05-技工学校 06-高中 07-初中 08-小学 09-文盲或半文盲 99-不详 字典编码见(WHCDDMB文化程度代码)
    private String education;

    // 国籍
    private String nationality;

    // 民族
    private String nation;

    // 医保类型
    private String hosType;

    // 医保卡号
    private String idcardHos;

    // 居住地址代码
    private String addrCode;

    // 居住地省
    private String prov;

    // 居住地市
    private String city;

    // 居住地县区
    private String county;

    // 居住地乡(街道)
    private String town;

    // 居住地村
    private String village;

    // 居住地组号
    private String teamNo;

    // 居住地小区门牌号
    private String team;

    // 邮编
    private String postCode;
    // 工作单位
    private String workOrgan;

    // 工作单位联系电话
    private String workTel;

    // 户籍地址
    private String domicileAddr;

    // 户籍类别--1户籍常住地 2非户籍常住地 3港澳台常驻 4外籍常驻 5非常住居民
    private String domicileType;

    // 户口类别--1本区县 2本市外区 3本省外市 4外省 5港澳台 6外籍
    private String domicileMode;

    // 外部编号--历史数据对接时传外部系统编号
    private String outNo;

    // 样本编号
    private String sampleNo;

    // 采样日期
    private Date sampleDate;

    // 快速登记--0-否 1-是
    private Integer isQuick;

    // 送检机构代码
    private String checkOrganCode;

    // 送检机构名称
    private String checkOrgan;

    // 是否农村适龄妇女两癌检查项目
    private String womanCancerCheck;

    // 联系电话
    private String tel;

    // TCT编号
    private String tctNo;

    // HPV编号
    private String hpvNo;

    // 令牌
    private String token;

    // 身高
    private BigDecimal height;

    // 体重
    private BigDecimal weight;

    // 身体质量指数
    private BigDecimal bodyMassIndex;

    // 月经史_初潮年龄
    private BigDecimal mensesAge;

    // 月经史_末次月经
    private Date lmp;

    // 月经史_是否绝经--1-否 2-是 3-不确定
    private Integer menopause;

    // 月经史_绝经年龄
    private BigDecimal menopauseAge;

    // 孕产史_是否生产过--1-是 0-否
    private Integer delivery;

    // 孕产史_初产年龄
    private BigDecimal deliveryAge;

    // 孕产史_是否哺乳--1-是 0-否
    private Integer lactation;

    // 母乳喂养持续时间
    private BigDecimal breastfeedingTime;

    // 经期
    private String menstrual;

    // 月经周期（天）
    private String cyc;

    // 避孕方法_未避孕
    private String larcControl;

    // 避孕方法_避孕套--1-是 0-否
    private Integer larcCondom;

    // 避孕方法_避孕药--1-是 0-否
    private Integer larcAcyeterion;

    // 避孕方法_避孕药_年
    private BigDecimal larcAcyeYear;

    // 避孕方法_宫内节育器--1-是 0-否
    private Integer larcIud;

    // 避孕方法_宫内节育器
    private BigDecimal larcIudYear;

    // 避孕方法_其他方式--1-是 0-否
    private Integer larcOther;

    // 避孕方法_其他方式_详
    private String larcOtherCont;

    // 孕次
    private Integer gravidityTimes;

    // 分娩
    private Integer deliverTimes;




}
