package com.labway.business.center.compare.dto.diAn;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */

@XmlAccessorType(XmlAccessType.FIELD)
@NoArgsConstructor
public class DiAnSampleResultDto implements Serializable {

    //
    @XmlElement(name = "BARCODE")
    private String barcode;
    // 送检单位
    @XmlElement(name = "SAMPLEFROM")
    private String samplefrom;
    // 样本类型
    @XmlElement(name = "SAMPLETYPE")
    private String sampletype;
    // 采样时间
    @XmlElement(name = "COLLECTDDATE")
    private Date collectddate;
    // 提交时间
    @XmlElement(name = "SUBMITDATE")
    private Date submitdate;
    // 测试项目编码
    @XmlElement(name = "TESTCODE")
    private String testcode;
    // 发布时间
    @XmlElement(name = "APPRDATE")
    private Date apprdate;
    // 所属子公司
    @XmlElement(name = "DEPT")
    private String dept;
    // 检测科室
    @XmlElement(name = "SERVGRP")
    private String servgrp;
    // 检测人
    @XmlElement(name = "USRNAM")
    private String usrnam;
    // 审核人
    @XmlElement(name = "APPRVEDBY")
    private String apprvedby;
    // 病人姓名
    @XmlElement(name = "PATIENTNAME")
    private String patientname;
    // 门诊号
    @XmlElement(name = "CLINICID")
    private String clinicid;
    // 床位
    @XmlElement(name = "BEDNO")
    private String bedno;
    // 病人类型（门诊/体检/住院等）
    @XmlElement(name = "PATIENTCATEGORY")
    private String patientcategory;
    // 送检医生
    @XmlElement(name = "DOCTOR")
    private String doctor;
    // 性别
    @XmlElement(name = "SEX")
    private String sex;
    // 年龄
    @XmlElement(name = "AGE")
    private String age;
    // 年龄单位（岁/月/天）
    @XmlElement(name = "AGEUNIT")
    private String ageunit;
    // 分析项目名称
    @XmlElement(name = "SINONYM")
    private String sinonym;
    // 分析项目简称
    @XmlElement(name = "SHORTNAME")
    private String shortname;
    // 单位
    @XmlElement(name = "UNITS")
    private String units;
    // 结果
    @XmlElement(name = "FINAL")
    private String FINAL;
    // 分析项目
    @XmlElement(name = "ANALYTE")
    private String analyte;
    // 参考范围
    @XmlElement(name = "DISPLOWHIGH")
    private String displowhigh;
    // 女参考值
    @XmlElement(name = "DISPLOWHIGH_F")
    private String displowhigh_f;
    // 男参考值
    @XmlElement(name = "DISPLOWHIGH_M")
    private String displowhigh_m;
    // 结果异常标志位（H/L等）
    @XmlElement(name = "RN10")
    private String rn10;
    // 结果异常标记位（H对应↑，L对应↓，+±等）
    @XmlElement(name = "RN20")
    private String rn20;
    // 对接唯一标识字段
    @XmlElement(name = "S")
    private String s;
    // 建议与解释或实验室对此样本信息的注释说明
    @XmlElement(name = "COMMENTS")
    private String comments;
    // 危急值标志（有危急值时为Y,否则为N）
    @XmlElement(name = "RANGE_FLG")
    private String RANGE_FLG;
    // 危急值描述（RANGE_FLG为Y时有内容，否则为空）
    @XmlElement(name = "RANGE_DESC")
    private String RANGE_DESC;
    // 项目排序
    @XmlElement(name = "SORTER")
    private String sorter;
    // 方法学
    @XmlElement(name = "METHODNAME")
    private String METHODNAME;

    // 下限值
    @XmlElement(name = "LOWB")
    private String LOWB;
    // 上限值
    @XmlElement(name = "HIGHB")
    private String HIGHB;



    public String getBarcode() {
        return barcode;
    }

    public void setBarcode(String barcode) {
        this.barcode = barcode;
    }

    public String getSamplefrom() {
        return samplefrom;
    }

    public void setSamplefrom(String samplefrom) {
        this.samplefrom = samplefrom;
    }

    public String getSampletype() {
        return sampletype;
    }

    public void setSampletype(String sampletype) {
        this.sampletype = sampletype;
    }

    public Date getCollectddate() {
        return collectddate;
    }

    public void setCollectddate(Date collectddate) {
        this.collectddate = collectddate;
    }

    public Date getSubmitdate() {
        return submitdate;
    }

    public void setSubmitdate(Date submitdate) {
        this.submitdate = submitdate;
    }

    public String getTestcode() {
        return testcode;
    }

    public void setTestcode(String testcode) {
        this.testcode = testcode;
    }

    public Date getApprdate() {
        return apprdate;
    }

    public void setApprdate(Date apprdate) {
        this.apprdate = apprdate;
    }

    public String getDept() {
        return dept;
    }

    public void setDept(String dept) {
        this.dept = dept;
    }

    public String getServgrp() {
        return servgrp;
    }

    public void setServgrp(String servgrp) {
        this.servgrp = servgrp;
    }

    public String getUsrnam() {
        return usrnam;
    }

    public void setUsrnam(String usrnam) {
        this.usrnam = usrnam;
    }

    public String getApprvedby() {
        return apprvedby;
    }

    public void setApprvedby(String apprvedby) {
        this.apprvedby = apprvedby;
    }

    public String getPatientname() {
        return patientname;
    }

    public void setPatientname(String patientname) {
        this.patientname = patientname;
    }

    public String getClinicid() {
        return clinicid;
    }

    public void setClinicid(String clinicid) {
        this.clinicid = clinicid;
    }

    public String getBedno() {
        return bedno;
    }

    public void setBedno(String bedno) {
        this.bedno = bedno;
    }

    public String getPatientcategory() {
        return patientcategory;
    }

    public void setPatientcategory(String patientcategory) {
        this.patientcategory = patientcategory;
    }

    public String getDoctor() {
        return doctor;
    }

    public void setDoctor(String doctor) {
        this.doctor = doctor;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getAge() {
        return age;
    }

    public void setAge(String age) {
        this.age = age;
    }

    public String getAgeunit() {
        return ageunit;
    }

    public void setAgeunit(String ageunit) {
        this.ageunit = ageunit;
    }

    public String getSinonym() {
        return sinonym;
    }

    public void setSinonym(String sinonym) {
        this.sinonym = sinonym;
    }

    public String getShortname() {
        return shortname;
    }

    public void setShortname(String shortname) {
        this.shortname = shortname;
    }

    public String getUnits() {
        return units;
    }

    public void setUnits(String units) {
        this.units = units;
    }

    public String getFINAL() {
        return FINAL;
    }

    public void setFINAL(String FINAL) {
        this.FINAL = FINAL;
    }

    public String getAnalyte() {
        return analyte;
    }

    public void setAnalyte(String analyte) {
        this.analyte = analyte;
    }

    public String getDisplowhigh() {
        return displowhigh;
    }

    public void setDisplowhigh(String displowhigh) {
        this.displowhigh = displowhigh;
    }

    public String getDisplowhigh_f() {
        return displowhigh_f;
    }

    public void setDisplowhigh_f(String displowhigh_f) {
        this.displowhigh_f = displowhigh_f;
    }

    public String getDisplowhigh_m() {
        return displowhigh_m;
    }

    public void setDisplowhigh_m(String displowhigh_m) {
        this.displowhigh_m = displowhigh_m;
    }

    public String getRn10() {
        return rn10;
    }

    public void setRn10(String rn10) {
        this.rn10 = rn10;
    }

    public String getRn20() {
        return rn20;
    }

    public void setRn20(String rn20) {
        this.rn20 = rn20;
    }

    public String getS() {
        return s;
    }

    public void setS(String s) {
        this.s = s;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public String getRANGE_FLG() {
        return RANGE_FLG;
    }

    public void setRANGE_FLG(String RANGE_FLG) {
        this.RANGE_FLG = RANGE_FLG;
    }

    public String getRANGE_DESC() {
        return RANGE_DESC;
    }

    public void setRANGE_DESC(String RANGE_DESC) {
        this.RANGE_DESC = RANGE_DESC;
    }

    public String getSorter() {
        return sorter;
    }

    public void setSorter(String sorter) {
        this.sorter = sorter;
    }

    public String getMETHODNAME() {
        return METHODNAME;
    }

    public void setMETHODNAME(String METHODNAME) {
        this.METHODNAME = METHODNAME;
    }

    public String getLOWB() {
        return LOWB;
    }

    public void setLOWB(String LOWB) {
        this.LOWB = LOWB;
    }

    public String getHIGHB() {
        return HIGHB;
    }

    public void setHIGHB(String HIGHB) {
        this.HIGHB = HIGHB;
    }
}
