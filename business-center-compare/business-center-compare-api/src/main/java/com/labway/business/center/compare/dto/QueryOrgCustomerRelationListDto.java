package com.labway.business.center.compare.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2023/06/30 23:51
 * @description
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class QueryOrgCustomerRelationListDto implements Serializable {

    //机构（业务单元）编码-关联业务单元表的org_id
    private String orgCode;
    //机构（业务单元）名称
    private String orgName;

    // 客商信息
    private List<OrgCustomerRelationDto> orgCustomerRelationDtos;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class OrgCustomerRelationDto implements Serializable{
        //关系表主键
        private String relationId;
        //客商编码
        private String customerCode;
        //客商名称
        private String customerName;

        //lis客商编码
        private String lisCustomerCode;
        //lis客商名称
        private String lisCustomerName;

    }

}