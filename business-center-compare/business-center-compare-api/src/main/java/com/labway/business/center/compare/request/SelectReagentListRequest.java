package com.labway.business.center.compare.request;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;

/**
 * 主数据获取试剂耗材数据接口 Vi
 *
 * <AUTHOR>
 * @since 2023/3/2 10:13
 */
@Getter
@Setter
public class SelectReagentListRequest {
    /**
     * 最后修改时间 从 yyyy-MM-dd HH:mm:ss
     */
    private String modifiedTimeStart;
    
    /**
     * 最后修改时间 至 yyyy-MM-dd HH:mm:ss
     */
    private String modifiedTimeEnd;
    
    /**
     * 创建时间时间 从 yyyy-MM-dd HH:mm:ss
     */
    private String creationtimeStart;
    
    /**
     * 最后修改时间 至 yyyy-MM-dd HH:mm:ss
     */
    private String creationtimeEnd;
    
    /**
     * 同时根据 修改时间、创建时间一起查看
     */
    private Boolean creationAndModified = Boolean.FALSE;
    
    /**
     * 当前页码
     */
    private Integer current = 1;
    /**
     * 页大小
     */
    private Integer size = 10;
    
}
