package com.labway.business.center.compare.dto.danyang;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class DYLASampleQueryDto1<T> implements Serializable {

    private String source;

    private String remark;

    private String operate;

    private T data;

    @Data
    public static class DYLASampleQueryDataDtoTctNo implements Serializable {
        private String tctNo;

        public DYLASampleQueryDataDtoTctNo(String tctNo) {
            this.tctNo = tctNo;
        }
    }

    @Data
    public static class DYLASampleQueryDataDtoHpvNo implements Serializable {
        private String hpvNo;

        public DYLASampleQueryDataDtoHpvNo(String hpvNo) {
            this.hpvNo = hpvNo;
        }
    }

    @Data
    public static class DYLASampleQueryDataDtoNo implements Serializable {
        private String no;

        public DYLASampleQueryDataDtoNo(String no) {
            this.no = no;
        }
    }

}
