package com.labway.business.center.compare.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class QueryItemRevertMappingByCustomerRequest implements Serializable {

    @NotBlank(message = "客商编码不能为空！")
    private String customerCode;

    @NotBlank(message = "回传项目编码不能为空！")
    private String itemRevertCode;

    @NotBlank(message = "机构编码不能为空！")
    private String orgCode;

    @NotNull(message = "回传对照关系类型不能为空(1报告 2细菌 3药物)！")
    @Range(min = 1,max = 9,message = "回传对照关系类型错误(1报告 2细菌 3药物)！")
    private Integer itemRevertType;


}
