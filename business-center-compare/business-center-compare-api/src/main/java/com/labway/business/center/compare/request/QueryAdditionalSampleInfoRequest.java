package com.labway.business.center.compare.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2023/05/16 19:30
 * @description
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class QueryAdditionalSampleInfoRequest implements Serializable {

    /**
     * 送检客商编码
     */
    private String hspOrgCode;
    /**
     * 外送的条码号
     */
    private List<String> barCodes;

}