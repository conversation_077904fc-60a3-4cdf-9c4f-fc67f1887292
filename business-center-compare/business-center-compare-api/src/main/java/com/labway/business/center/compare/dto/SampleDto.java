package com.labway.business.center.compare.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2023/05/09 14:50
 * @description
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SampleDto implements Serializable {

    private static final long serialVersionUID = 1L;

    // 样本pdf是否合并回传 默认为false 如果合并回传 则处理样本pdf的时候只处理第一个样本的pdf
    private Integer isMerge;

    // lims的分条码数据
    private List<String> limsSplitBarcodes;

    // 报告结果是否出全部(如果未出全，但是实验室未配置合并打印，则也算是全部出，允许社区同步结果)
    private Integer isReport;

    /**
     * customBarcode 分表字段 ，全局唯一
     */
    private String customBarcode;

    /**
     * 身份证号
     */
    private String idNumBer;
    /**
     * 医保卡号
     */
    private String medNumber;
    /**
     * 电话
     */
    private String phone;
    /**
     * 病人姓名
     */
    private String patientName;

    /**
     * 病历号
     */
    private String visitCardNo;

    /**
     * 性别
     *
     */
    private int sex;

    /**
     * 年龄
     */
    private int age;

    /**
     * 送检机构编码
     */
    private String sendOrgCode;

    /**
     * 送检机构名称
     */
    private String sendOrgName;

    /**
     * 检验机构编码
     */
    private String orgCode;

    /**
     * 检验机构名称
     */
    private String orgName;

    /**
     * 原始机构编码
     */
    private String originalOutOrgCode;

    /**
     * 原始机构
     */
    private String originalOutOrgName;

    /**
     * 条码号
     */
    private String barcode;

    /**
     * 外部条码号
     */
    private String outBarcode;

    /**
     * 科室
     */
    private String deptName;

    /**
     * 专业组编码
     */
    private String professionalGroupCode;

    /**
     * 专业组名称
     */
    private String professionalGroupName;

    /**
     * 就诊类型编码
     */
    private String visitTypeCode;

    /**
     * 区域编码
     */
    private String regionCode;

    /**
     * 就诊类型名称
     */
    private String visitTypeName;

    /**
     * 床号
     */
    private String bed;

    /**
     * 临床诊断
     */
    private String diag;

    /**
     * 备注
     */
    private String memo;

    /**
     * 采样时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date extractDate;

    /**
     * 检验时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date testDate;

    /**
     * 审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date auditDate;

    /**
     * 打印时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date printDate;

    /**
     * 导出时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date exportDate;

    /**
     * 送检日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date submitDate;

    /**
     * 送检人
     */
    private String submitUserName;

    /**
     * 送检人编码
     */
    private String submitUserCode;

    /**
     * 打印状态
     *
     */
    private Integer printStatus;

    /**
     * 导出状态
     *
     */
    private Integer exportStatus;

    /**
     * 一审人 编码
     */
    private String oneCheckUserCode;

    /**
     * 一审人 名称
     */
    private String oneCheckUserName;

    /**
     * 二审人 编码
     */
    private String twoCheckUserCode;

    /**
     * 二审人 名称
     */
    private String twoCheckUserName;

    /**
     * 审核人
     */
    private String checkUserName;

    /**
     * 审核人编码
     */
    private String checkUserCode;

    /**
     * 检验人
     */
    private String testUserName;

    /**
     * 检验人code
     */
    private String testUserCode;

    /**
     * 样本号
     */
    private String sampleNo;

    /**
     * 申请病区
     */
    private String inpatientArea;

    /**
     * 是否包含异常结果
     */
    private Boolean hasAbnormalResult;

    /**
     * testItemSum
     */
    private Integer testItemSum;

    /**
     * 一审时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date oneAuditDate;

    /**
     * 二审时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date twoAuditDate;

    /**
     * 是否一审(0否1是)
     */
    private Integer isFirstAudit;

    /**
     * 是否二审
     */
    private Integer isSecAudit;

    /**
     * auditCode
     */
    private String auditCode;

    /**
     * 审核名
     */
    private String auditName;

    /**
     * 是否审核(0否1是)
     */
    private Integer isChecked;

    /**
     * 机器CODE
     */
    private String instrumentCode;

    /**
     * testDateStr(存储langjia的TestDate)
     */
    private String testDateStr;

    // 管型
    private String tubeCode;

    // 管型
    private String tubeName;

    /**
     * 检验项目
     */
    private List<SampleDto.TestItem> testItems;

    /**
     * createDate
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate;

    /**
     * updateDate
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateDate;

    /**
     * 检验结果
     *
     * @see com.labway.report.common.dto.sample.es.SampleDto.Result
     */
    private List<SampleDto.Result> results;

    /**
     * 报告单地址
     */
    private List<String> reportUrls;

    /**
     * 图片地址
     */
    private List<String> imgUrls;

    /**
     * 结果备注 建议与解释等
     */
    private String resultRemark;

    /**
     * 审核人身份证号
     */
    private String checkIdCard;

    /**
     * 一审人身份证号
     */
    private String oneCheckIdCard;

    /**
     * 一审人身份证号
     */
    private String twoCheckIdCard;

    /**
     * 样本报告总数量
     */
    private Integer allReportsCount;

    /**
     * 当前样本报告数量
     */
    private Integer currentReportsCount;

    /**
     * 结果
     */
    @Getter
    @Setter
    public static class Result {
        /**
         * id
         */

        private String id;

        /**
         * 检验项目ID
         */
        private String testItemId;

        /**
         * 编码
         */
        private String code;

        /**
         * 名称
         */
        private String name;

        /**
         * 检验项目名称
         */
        private String testItemName;

        /**
         * 检验项目编码
         */
        private String testItemCode;

        /**
         * 外部检验项目编码
         */
        private String outTestItemCode;
        /**
         * 外部检验项目名称
         */
        private String outTestItemName;

        /**
         * 结果值
         */
        private String result;

        /**
         * 单位
         */
        private String unit;

        /**
         * 参考范围
         */
        private String referenceRange;

        /**
         * 提示
         */
        private String testJudge;

        /**
         * 机器CODE
         */
        private String instrumentCode;

        /**
         * 机器结果
         */
        private String machineResult;

        /**
         * isAdd
         */
        private Boolean isAdd;

        /**
         * recordDate
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private Date recordDate;

        /**
         * upvalue
         */
        private String upValue;

        /**
         * downvalue;
         */
        private String downValue;
        /**
         * 打印顺序，越小越靠前
         */
        private Integer printSort;
        /**
         *  检验方法学
         */
        private String testMethod;

        /**
         * 结果类型 数值 阴阳性等
         */
        private String resultType;

    }

    /**
     * 检验项目
     */
   @Data
   @NoArgsConstructor
   @AllArgsConstructor
    public static class TestItem {
        /**
         * id
         */
        private String id;


        /**
         * 编码
         */
        private String code;

        /**
         * 检验项目code
         */
        private String testItemCode;

        /**
         * 名称
         */
        private String name;

    }
}