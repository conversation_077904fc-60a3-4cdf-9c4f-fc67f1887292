package com.labway.business.center.compare.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 2023/04/23 13:47
 * @description
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class QueryOrgItemMappingRelationRequest implements Serializable {

    // 业务单元编码
    @NotBlank(message = "业务单元编码不能为空！")
    private String orgCode;

    // 客商编码
    @NotBlank(message = "客商编码不能为空！")
    private String customerCode;

    // 外部项目名称
    @NotBlank(message = "外部项目编码不能为空！")
    private String itemOutsideCode;


}