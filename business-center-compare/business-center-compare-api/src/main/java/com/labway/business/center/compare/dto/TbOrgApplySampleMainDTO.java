package com.labway.business.center.compare.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * 外送申请单样本主表
 */
@Data
@Accessors(chain = true)
public class TbOrgApplySampleMainDTO {

    private Long mainId;
    //外送到中台的条码号
    private String barcode;
    //送检机构编码（对应中台的客商编码）
    private String hspOrgCode;
    //送检机构名称（对应中台的客商名称）
    private String hspOrgName;
    //所属业务单元编码
    private String orgCode;
    //所属业务单元名称
    private String orgName;
    //申请单类型 门诊/住院
    private Integer applyType;
    //门诊/住院 号
    private String patientVisitCard;
    //是否加急 0否1是
    private Integer urgent;
    //样本类型
    private String sampleType;
    //样本形状
    private String sampleProperty;
    //申请科室
    private String dept;
    //病区
    private String inpatientArea;
    //患者名称
    private String patientName;
    //性别 1男2女
    private Integer patientSex;
    //年龄 xx岁
    private Integer patientAge;
    //子年龄 xxx天|xxx周|xxx月|
    private Integer patientSubage;
    //子年龄单位
    private String patientSubageUnit;
    //生日
    private Date patientBirthday;
    //床号
    private String patientBed;
    //临床诊断
    private String clinicalDiagnosis;
    //身份证
    private String patientCard;
    //证件类型
    private String patientCardType;
    //住址
    private String patientAsddress;
    //手机号
    private String patientMobile;
    //送检医生
    private String sendDoctor;
    //申请时间（送检时间）
    private Date applyDate;
    //采样时间
    private Date samplingDate;
    //备注
    private String remark;
    //删除标识 0未删除 1删除
    private Integer deleteFlag;
    /**
     * @see com.labway.business.center.core.enums.ApplySampleStatus
     */
    //1待对照 2待签收 3已签收 4取消外送
    private Integer status;

    // 签收人编码
    private String receiveUserCode;
    // 签收人名称
    private String receiveUserName;
    // 签收时间
    private Date receiveTime;
    // 签收机构编码
    private String signOrgCode;
    // 签收机构名称
    private String signOrgName;
    // 签收机构条码
    private String signBarcode;
    // 签收主条码（暂无用）
    private String signMainBarcode;

    // 外送样本来源标识（his,物流，社区等）
    private String sampleSource;
    // 样本送往的目标机构编码(业务单元)
    private String targetOrgCode;
    // 样本送往的目标机构名称(业务单元)
    private String targetOrgName;

    /**
     * 外部主表id
     */
    private String outMainId;
    /**
     * 审核人id
     */
    private String auditorId;
    /**
     * 审核人
     */
    private String auditor;
    /**
     * 审核时间
     */
    private Date auditDate;

    private String instrumentCode;
    /**
     * 样本号
     */
    private String sampleNo;
    /**
     * 检验日期-字符串
     */
    private String testDate;
    /**
     * 检验时间
     */
    private Date testTime;
    /**
     * 检验项目数量
     */
    private Integer testItemSum;
    /**
     * 是否一审
     */
    private Integer isFirstAudit;
    /**
     * 一审人编码
     */
    private String firstAuditUserCode;
    /**
     * 一审人名称
     */
    private String firstAuditUserName;
    /**
     * 一审时间
     */
    private Date firstAuditTime;
    /**
     * 是否二审
     */
    private Integer isSecAudit;
    /**
     * 二审人编码
     */
    private String secAuditUserCode;
    /**
     * 二审人名称
     */
    private String secAuditUserName;
    /**
     * 二审时间
     */
    private Date secAuditTime;
    /**
     * 检验人名称
     */
    private String testUserCode;
    /**
     * 检验人名称
     */
    private String testUserName;

    private Integer isCheck;
    //外送申请单信息id
    private String formId;
    //申请单编码
    private String formCode;
    /**
     * 管型
     */
    private String tubeType;

    // 是否终止检验 0否 1是
    private Integer isTerminateTest;

    // 报告单urls
    private String reportUrls;
    
    /**
     * 检查项
     */
    private List<TbOrgApplySampleMainItemDTO> applySampleMainItems;
}

