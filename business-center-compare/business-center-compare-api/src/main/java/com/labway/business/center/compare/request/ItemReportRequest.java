package com.labway.business.center.compare.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 2023/04/21 15:40
 * @description
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ItemReportRequest implements Serializable {

    @NotBlank(message = "检验项目编码不能为空！")
    private String itemTestCode;


    private String provinceCode;

    private String itemTestId;

}