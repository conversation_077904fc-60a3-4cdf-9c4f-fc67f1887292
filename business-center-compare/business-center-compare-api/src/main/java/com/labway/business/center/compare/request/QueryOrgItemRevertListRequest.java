package com.labway.business.center.compare.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class QueryOrgItemRevertListRequest implements Serializable {

    //回传项目编码
    private String revertItemCode;

    //回传项目名称
    private String revertItemName;

    //客商编码
    @NotBlank(message = "客商编码不能为空!")
    private String customerCode;

    //机构编码
    @NotBlank(message = "机构编码不能为空!")
    private String orgCode;

    private String orgName;

    //回传项目类型 1报告项目 2细菌 3药物
    @NotNull(message = "项目类型不能为空(1报告项目 2细菌 3药物)!")
    @Range(min = 1,max = 9,message = "项目类型错误(1报告项目 2细菌 3药物 9其它)!")
    private Integer revertItemType;



}
