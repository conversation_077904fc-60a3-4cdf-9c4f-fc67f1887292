package com.labway.business.center.compare.request.statistics;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class QueryStatisticsSampleESRequest implements Serializable {
    //样本统计表id
    private String statisticsId;
    private String statisticsIdLike;

    //样本编号-全局唯一
    private String sampleCode;
    private String sampleCodeLike;

    //病人姓名
    private String patientName;
    private String patientNameLike;

    //病人性别 0未知 1男 2女
    private Integer patientSex;
    private Integer patientSexLike;

    //病人年龄
    private Integer patientAge;
    private Integer patientAgeBegin;
    private Integer patientAgeEnd;

    //年龄单位
    private String ageUnit;
    private String ageUnitLike;

    //病人生日
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date patientBirthday;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date patientBirthdayBegin;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date patientBirthdayEnd;

    //门诊/住院登记号
    private String patientRegistrationNo;
    private String patientRegistrationNoLike;

    //就诊卡号
    private String treatmentCard;
    private String treatmentCardLike;

    //就诊类型 门诊/住院等
    private String treatmentType;
    private String treatmentTypeLike;

    //床号
    private String bedNo;
    private String bedNoLike;

    //样本科室
    private String dept;
    private String deptLike;

    //样本条码号（本系统条码号）
    private String barcode;
    private String barcodeLike;

    //外部条码号（送检机构的条码号）
    private String outBarcode;
    private String outBarcodeLike;

    //His条码号（一般情况下同out_barcode）
    private String hisBarcode;
    private String hisBarcodeLike;

    //样本创建时间（三方系统的创建时间）
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date sampleCreateDate;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @NotNull(message = "接收时间不能为空！")
    private Date sampleCreateDateBegin;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @NotNull(message = "接收时间不能为空！")
    private Date sampleCreateDateEnd;

    //送检时间
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date sendTime;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date sendTimeBegin;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date sendTimeEnd;

    //送检医生
    private String sendDoctorCode;
    private String sendDoctorCodeLike;

    //送检医生名称
    private String sendDoctorName;
    private String sendDoctorNameLike;

    //检验时间
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date testTime;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date testTimeBegin;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date testTimeEnd;

    //检验人编码
    private String testUserCode;
    private String testUserCodeLike;

    //检验人名称
    private String testUserName;
    private String testUserNameLike;

    //审核时间
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date auditTime;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date auditTimeBegin;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date auditTimeEnd;

    //审核人编码
    private String auditUserCode;
    private String auditUserCodeLike;

    //审核人名称
    private String auditUserName;
    private String auditUserNameLike;

    //检验项目编码
    private String testItemCode;
    private String testItemCodeLike;

    //检验项目名称
    private String testItemName;
    private String testItemNameLike;

    //外部项目编码
    private String outTestItemCode;
    private String outTestItemCodeLike;

    //外部项目名称
    private String outTestItemName;
    private String outTestItemNameLike;

    //数量
    private Integer itemCount;
    private Integer itemCountBegin;
    private Integer itemCountEnd;

    //标准单价
    private BigDecimal standardPrice;
    private BigDecimal standardPriceBegin;
    private BigDecimal standardPriceEnd;

    //合计金额
    private BigDecimal totalAmount;
    private BigDecimal totalAmountBegin;
    private BigDecimal totalAmountEnd;

    //结算金额
    private BigDecimal settlementAmount;
    private BigDecimal settlementAmountBegin;
    private BigDecimal settlementAmountEnd;

    //结算类型编码
    private String settlementTypeCode;
    private String settlementTypeCodeLike;
    private Set<String> settlementTypeCodes;

    //结算类型名称
    private String settlementTypeName;
    private String settlementTypeNameLike;

    //结算备注
    private String settlementRemark;
    private String settlementRemarkLike;

    //折扣
    private String discount;
    private String discountLike;

    //创建时间
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTimeBegin;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTimeEnd;

    //创建人
    private String createBy;
    private String createByLike;

    //更新时间
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTimeBegin;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTimeEnd;

    //更新人
    private String updateBy;
    private String updateByLike;

    //删除标识 0未删除 1删除
    private Integer deleteFlag;

    // 机构编码（推送样本的机构）
    @NotBlank(message = "样本机构编码不能为空")
    private String orgCode;

    // 机构名称（推送样本的机构）
    private String orgName;

    // 机构类型 1,lims 2,lis
    private Integer orgType;

    // 原始送检机构编码
    private String originalOrgCode;
    private String originalOrgCodeLike;

    // 原始送检机构名称
    private String originalOrgName;
    private String originalOrgNameLike;


    // 查询页数
    @NotNull(message = "查询页数不能为空！")
    private Integer page;
    // 每页显示条数
    @NotNull(message = "每页显示条数不能为空！")
    private Integer size;


    // 送检机构编码
    private List<String> hspOrgCodes;
    // 通用条码匹配查询
    private String barcodeMatch;
    // 搜索关键字
    private String queryKeyword;


    // 排序字段
    private List<SortField> sortFields;

    // 滚动ID
    private String scrollId;


    @Data
    @NoArgsConstructor
    public static class SortField implements Serializable{
        // 字段名
        private String field;
        // 排序类型 1,升序 2,降序
        // 排序类型
        private SortTypeEnum sortType;

        // 子排序
        private SortField childSortField;
    }

    public enum SortTypeEnum {
        ASC,
        DESC;
    }

}
