package com.labway.business.center.compare.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PathologySampleFormDto implements Serializable {

    //外送申请单信息id
    private String formId;
    //申请单编码
    private String formCode;
    //送检机构的编码（对应业务中台的客商编码）
    private String hspOrgCode;
    //送检机构名称（对应业务中台的客商名称）
    private String hspOrgName;
    //所属业务单元编码
    private String orgCode;
    //所属业务单元名称
    private String orgName;
    //申请单来源（his,社区，物流等）
    private String formSource;
    //样本送往的目标机构编码（业务单元）
    private String targetOrgCode;
    //样本送往的目标机构名称（业务单元）
    private String targetOrgName;
    //申请单外送的样本数量
    private Integer sendSampleCount;
    //物流员是否取样 0否1是
    private Integer isTakeSample;
    //物流员取样数量
    private Integer takeSampleCount;
    //物流员是否妥投样本 0否1是
    private Integer isSampleDeliver;
    //物流员妥投样本数量
    private Integer sampleDeliverCount;
    //创建人id
    private String createBy;
    //创建时间
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    //跟新人id
    private String updateBy;
    //跟新时间
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    //删除标识 0否1是
    private Integer deleteFlag;
    //是否取消外送 0未取消 1取消
    private Integer cancelFlag;
    // 取样时间
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date takeSampleTime;
    // 妥投时间
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date sampleDeliverTime;
    // 物流员id
    private String staffId;
    // 物流员名称
    private String staffName;
    // 物流运单号
    private String deliveryCode;
    // 取样照片
    private String imageUrl;
    // 手动录入样本取样照片（多张英文逗号分割）
    private String handReceivePicCode;

    // lis客商编码
    private String lisCustomerCode;
    // lis客商名称
    private String lisCustomerName;

    // 是否是手工单申请单 0否1是
    private Integer isHandForm;

}
