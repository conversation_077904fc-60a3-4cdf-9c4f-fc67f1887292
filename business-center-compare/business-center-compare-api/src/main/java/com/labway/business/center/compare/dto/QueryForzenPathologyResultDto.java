package com.labway.business.center.compare.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data@NoArgsConstructor
@AllArgsConstructor
public class QueryForzenPathologyResultDto implements Serializable {
    //冰冻结果表 id
    private String forzenId;
    //结果主表id
    private String resultId;
    //冰冻报告序号
    private Integer fBdBgxh;
    //冰冻报告医生
    private String fBdBgys;
    //冰冻审核医生
    private String fBdShys;
    //冰冻报告日期
    private String fBdBgrq;
    //冰冻报告状态
    private String fBdBgzt;
    //冰冻诊断
    private String fBdzd;
    //冰冻制片人
    private String fBdZpr;
    //冰冻报告收到时间
    private String fBdSdrq;
    //冰冻取材医生
    private String fBdQcys;
    // 冰冻报告迟发原因
    private String fBdCfyy;
    //冰冻制片数
    private String fBdZps;
    //冰冻备注
    private String fBdBz;
    //冰冻复诊医生
    private String fBdFzys;
    //冰冻打印状态
    private String fBdDyzt;
    //冰冻签收本打印状态
    private String fBdQsbDyzt;
    //冰冻报告位置
    private String fBdBgwz;
    //冰冻报告位置确认时间
    private String fBdBgwzQrsj;
    //冰冻报告位置操作员
    private String fBdBgwzQrczy;
    //冰冻制片时间
    private String fZpsj;
    //冰冻离体时间
    private String fLtsj;
    //制片开始时间
    private String fZpkssj;
    //冰冻报告发布时间
    private String fBdFbsj;
    //冰冻报告发布医生
    private String fBdFbys;

    private Integer deleteFlag;

    // 创建时间
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    // 送检机构编码
    private String hspOrgCode;
    // 条码号
    private String barcode;
    // 病理号
    private String fBdBlh;

    // 更新时间
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;




    public String getForzenId() {
        return forzenId;
    }

    public void setForzenId(String forzenId) {
        this.forzenId = forzenId;
    }

    public String getResultId() {
        return resultId;
    }

    public void setResultId(String resultId) {
        this.resultId = resultId;
    }

    public Integer getfBdBgxh() {
        return fBdBgxh;
    }

    public void setfBdBgxh(Integer fBdBgxh) {
        this.fBdBgxh = fBdBgxh;
    }

    public String getfBdBgys() {
        return fBdBgys;
    }

    public void setfBdBgys(String fBdBgys) {
        this.fBdBgys = fBdBgys;
    }

    public String getfBdShys() {
        return fBdShys;
    }

    public void setfBdShys(String fBdShys) {
        this.fBdShys = fBdShys;
    }

    public String getfBdBgrq() {
        return fBdBgrq;
    }

    public void setfBdBgrq(String fBdBgrq) {
        this.fBdBgrq = fBdBgrq;
    }

    public String getfBdBgzt() {
        return fBdBgzt;
    }

    public void setfBdBgzt(String fBdBgzt) {
        this.fBdBgzt = fBdBgzt;
    }

    public String getfBdzd() {
        return fBdzd;
    }

    public void setfBdzd(String fBdzd) {
        this.fBdzd = fBdzd;
    }

    public String getfBdZpr() {
        return fBdZpr;
    }

    public void setfBdZpr(String fBdZpr) {
        this.fBdZpr = fBdZpr;
    }

    public String getfBdSdrq() {
        return fBdSdrq;
    }

    public void setfBdSdrq(String fBdSdrq) {
        this.fBdSdrq = fBdSdrq;
    }

    public String getfBdQcys() {
        return fBdQcys;
    }

    public void setfBdQcys(String fBdQcys) {
        this.fBdQcys = fBdQcys;
    }

    public String getfBdCfyy() {
        return fBdCfyy;
    }

    public void setfBdCfyy(String fBdCfyy) {
        this.fBdCfyy = fBdCfyy;
    }

    public String getfBdZps() {
        return fBdZps;
    }

    public void setfBdZps(String fBdZps) {
        this.fBdZps = fBdZps;
    }

    public String getfBdBz() {
        return fBdBz;
    }

    public void setfBdBz(String fBdBz) {
        this.fBdBz = fBdBz;
    }

    public String getfBdFzys() {
        return fBdFzys;
    }

    public void setfBdFzys(String fBdFzys) {
        this.fBdFzys = fBdFzys;
    }

    public String getfBdDyzt() {
        return fBdDyzt;
    }

    public void setfBdDyzt(String fBdDyzt) {
        this.fBdDyzt = fBdDyzt;
    }

    public String getfBdQsbDyzt() {
        return fBdQsbDyzt;
    }

    public void setfBdQsbDyzt(String fBdQsbDyzt) {
        this.fBdQsbDyzt = fBdQsbDyzt;
    }

    public String getfBdBgwz() {
        return fBdBgwz;
    }

    public void setfBdBgwz(String fBdBgwz) {
        this.fBdBgwz = fBdBgwz;
    }

    public String getfBdBgwzQrsj() {
        return fBdBgwzQrsj;
    }

    public void setfBdBgwzQrsj(String fBdBgwzQrsj) {
        this.fBdBgwzQrsj = fBdBgwzQrsj;
    }

    public String getfBdBgwzQrczy() {
        return fBdBgwzQrczy;
    }

    public void setfBdBgwzQrczy(String fBdBgwzQrczy) {
        this.fBdBgwzQrczy = fBdBgwzQrczy;
    }

    public String getfZpsj() {
        return fZpsj;
    }

    public void setfZpsj(String fZpsj) {
        this.fZpsj = fZpsj;
    }

    public String getfLtsj() {
        return fLtsj;
    }

    public void setfLtsj(String fLtsj) {
        this.fLtsj = fLtsj;
    }

    public String getfZpkssj() {
        return fZpkssj;
    }

    public void setfZpkssj(String fZpkssj) {
        this.fZpkssj = fZpkssj;
    }

    public String getfBdFbsj() {
        return fBdFbsj;
    }

    public void setfBdFbsj(String fBdFbsj) {
        this.fBdFbsj = fBdFbsj;
    }

    public String getfBdFbys() {
        return fBdFbys;
    }

    public void setfBdFbys(String fBdFbys) {
        this.fBdFbys = fBdFbys;
    }

    public Integer getDeleteFlag() {
        return deleteFlag;
    }

    public void setDeleteFlag(Integer deleteFlag) {
        this.deleteFlag = deleteFlag;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getHspOrgCode() {
        return hspOrgCode;
    }

    public void setHspOrgCode(String hspOrgCode) {
        this.hspOrgCode = hspOrgCode;
    }

    public String getBarcode() {
        return barcode;
    }

    public void setBarcode(String barcode) {
        this.barcode = barcode;
    }

    public String getfBdBlh() {
        return fBdBlh;
    }

    public void setfBdBlh(String fBdBlh) {
        this.fBdBlh = fBdBlh;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

}
