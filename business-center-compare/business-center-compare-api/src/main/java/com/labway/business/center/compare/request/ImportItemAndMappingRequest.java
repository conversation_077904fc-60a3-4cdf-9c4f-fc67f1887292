package com.labway.business.center.compare.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 2023/05/05 19:10
 * @description
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ImportItemAndMappingRequest implements Serializable {

    // 文件保存的路径
    @NotBlank
    private String url;

    // 业务单元编码
    @NotBlank
    private String orgCode;

    // 业务单元名称
    @NotBlank
    private String orgName;

    // 客商编码
    @NotBlank
    private String customerCode;

    // 客商名称
    @NotBlank
    private String customerName;

    private String clientId;

    private String userName;
}
