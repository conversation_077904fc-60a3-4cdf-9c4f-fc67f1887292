package com.labway.business.center.compare.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class QueryOrgItemRevertListDto implements Serializable {

    //回传项目id 主键
    private String revertId;
    //客商/机构编码
    private String belongCode;
    //客商/机构名称
    private String belongName;
    //回传项目编码
    private String revertItemCode;
    //回传项目名称
    private String revertItemName;
    //回传项目类型 1报告项目 2细菌 3药物
    private Integer revertItemType;
    //1送检机构 2检验机构
    private Integer belongType;
    //创建时间
    private Date createTime;
    //创建人id
    private String createBy;
    //更新时间
    private Date updateTime;
    //更新人id
    private String updateBy;

    // 是否对照 0未对照1已对照
    private Integer isMapping;
    // 是否启用 0未启用1启用
    private Integer enable;

}
