package com.labway.business.center.compare.service.impl;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.labway.business.center.compare.dto.OrgApplyResultSamplesDTO;
import com.labway.business.center.compare.dto.QueryOrgItemInfoDto;
import com.labway.business.center.compare.dto.QueryOrgItemMappingInfoDto;
import com.labway.business.center.compare.dto.SaveOrgItemMappingInfoDto;
import com.labway.business.center.compare.request.*;
import com.labway.business.center.compare.service.ILimsOutSourceSampleService;
import com.swak.frame.dto.Response;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;

import java.util.List;

@RefreshScope
public class LimsOutSourceSampleClientService implements ILimsOutSourceSampleService {

    @Value("${business.center.compare-api:}")
    private String businessHttpUrl;

    @Override
    public Response<List<OrgApplyResultSamplesDTO>> queryResultSamples(QueryLimsOutSourceSampleInfoRequest queryLimsOutSourceSampleInfoRequest) {
        if (StringUtils.isBlank(businessHttpUrl)) {
            throw new IllegalStateException("请设置请求地址");
        }
        HttpRequest request = HttpUtil.createPost(businessHttpUrl + "/lims/queryOutSourceSampleInfo")
                .body(JSON.toJSONString(queryLimsOutSourceSampleInfoRequest));
        try (HttpResponse execute = request.execute()) {
            return JSONObject.parseObject(execute.body(), new TypeReference<Response<List<OrgApplyResultSamplesDTO>>>() {
            });
        } catch (Exception e) {
            throw new IllegalStateException("调用业务中台方法报错", e);
        }
    }

    @Override
    public Response<List<QueryOrgItemInfoDto>> queryOrgItemInfo(QueryOrgItemInfoRequest queryOrgItemInfoRequest) {
        if (StringUtils.isBlank(businessHttpUrl)) {
            throw new IllegalStateException("请设置请求地址");
        }
        HttpRequest request = HttpUtil.createPost(businessHttpUrl + "/lims/queryOrgItemInfo")
                .body(JSON.toJSONString(queryOrgItemInfoRequest));
        try (HttpResponse execute = request.execute()) {
            return JSONObject.parseObject(execute.body(), new TypeReference<Response<List<QueryOrgItemInfoDto>>>() {
            });
        } catch (Exception e) {
            throw new IllegalStateException("调用业务中台方法报错", e);
        }
    }

    @Override
    public Response<String> saveOrgItemMappingInfo(SaveOrgItemMappingInfoRequest saveOrgItemMappingInfoRequest) {
        if (StringUtils.isBlank(businessHttpUrl)) {
            throw new IllegalStateException("请设置请求地址");
        }
        HttpRequest request = HttpUtil.createPost(businessHttpUrl + "/lims/saveOrgItemMappingInfo")
                .body(JSON.toJSONString(saveOrgItemMappingInfoRequest));
        try (HttpResponse execute = request.execute()) {
            return JSONObject.parseObject(execute.body(), new TypeReference<Response<String>>() {
            });
        } catch (Exception e) {
            throw new IllegalStateException("调用业务中台方法报错", e);
        }
    }

    @Override
    public Response<List<QueryOrgItemMappingInfoDto>> queryOrgItemMappingInfo(QueryOrgItemMappingInfoRequest queryOrgItemMappingInfoRequest) {
        if (StringUtils.isBlank(businessHttpUrl)) {
            throw new IllegalStateException("请设置请求地址");
        }
        HttpRequest request = HttpUtil.createPost(businessHttpUrl + "/lims/queryOrgItemMappingInfo")
                .body(JSON.toJSONString(queryOrgItemMappingInfoRequest));
        try (HttpResponse execute = request.execute()) {
            return JSONObject.parseObject(execute.body(), new TypeReference<Response<List<QueryOrgItemMappingInfoDto>>>() {
            });
        } catch (Exception e) {
            throw new IllegalStateException("调用业务中台方法报错", e);
        }
    }

    /**
     * 删除项目对照关系
     * @param deleteOrgItemMappingInfoRequest
     * @return
     */
    @Override
    public Response<String> deleteOrgItemMappingInfo(DeleteOrgItemMappingInfoRequest deleteOrgItemMappingInfoRequest) {
        if (StringUtils.isBlank(businessHttpUrl)) {
            throw new IllegalStateException("请设置请求地址");
        }
        HttpRequest request = HttpUtil.createPost(businessHttpUrl + "/lims/deleteOrgItemMappingInfo")
                .body(JSON.toJSONString(deleteOrgItemMappingInfoRequest));
        try (HttpResponse execute = request.execute()) {
            return JSONObject.parseObject(execute.body(), new TypeReference<Response<String>>() {
            });
        } catch (Exception e) {
            throw new IllegalStateException("调用业务中台方法报错", e);
        }
    }

    /**
     * 查询实验室外送样本的报告结果信息
     * @param queryRequest
     * @return
     */
    @Override
    public Response<List<OrgApplyResultSamplesDTO>> queryResultSample(QueryLimsOutSourceSampleInfoRequest queryRequest) {
        if (StringUtils.isBlank(businessHttpUrl)) {
            throw new IllegalStateException("请设置请求地址");
        }
        HttpRequest request = HttpUtil.createPost(businessHttpUrl + "/lims/queryResultSample")
                .body(JSON.toJSONString(queryRequest));
        try (HttpResponse execute = request.execute()) {
            return JSONObject.parseObject(execute.body(), new TypeReference<Response<List<OrgApplyResultSamplesDTO>>>() {
            });
        } catch (Exception e) {
            throw new IllegalStateException("调用业务中台方法报错", e);
        }
    }
}
