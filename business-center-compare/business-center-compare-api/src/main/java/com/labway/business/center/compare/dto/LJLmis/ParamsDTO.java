package com.labway.business.center.compare.dto.LJLmis;


import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
public class ParamsDTO implements Serializable {

    private static final long serialVersionUID = -871789189686956255L;

    /**
     *加密后的参数字符串
     */
    @NotBlank(message = "content字段不能为空")
    private String content;

    /**
     * 密钥
     */
    @NotBlank(message = "key字段不能为空")
    private String key;

}
