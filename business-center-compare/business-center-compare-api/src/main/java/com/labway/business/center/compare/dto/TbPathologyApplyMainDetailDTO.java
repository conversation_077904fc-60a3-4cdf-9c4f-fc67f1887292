package com.labway.business.center.compare.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * (TbPathologyApplyMainDetailDTO)表实体类
 *
 * <AUTHOR>
 * @since 2023-07-13 09:10:19
 */
@NoArgsConstructor
@AllArgsConstructor
public class TbPathologyApplyMainDetailDTO implements Serializable {
    //病理申请单明细表 id
    @JsonProperty("DETAIL_ID")
    private String detailId;
    //条码编码
    @JsonProperty("BARCODE")
    private String barcode;
    //外部检验项目编码
    @JsonProperty("OUT_TESTITEM_CODE")
    private String outTestitemCode;
    //外部检验项目名称
    @JsonProperty("OUT_TESTITEM_NAME")
    private String outTestitemName;
    //兰卫检验项目编码
    @JsonProperty("TESTITEM_CODE")
    private String testitemCode;
    //条码的机构编码
    @JsonProperty("HSP_ORG_CODE")
    private String hspOrgCode;
    //收费标志(0否1是)
    @JsonProperty("IS_FEE")
    private String isFee;
    //是否禁用(0正常 1禁用)默认0
    @JsonProperty("IS_FORBIDDEN")
    private String isForbidden;
    //收费次数
    @JsonProperty("FEE_NUM")
    private Integer feeNum;
    //属性1
    @JsonProperty("PROPERTY1")
    private String property1;
    //属性2
    @JsonProperty("PROPERTY2")
    private String property2;
    //属性3
    @JsonProperty("PROPERTY3")
    private String property3;
    //属性4
    @JsonProperty("PROPERTY4")
    private String property4;
    //属性5
    @JsonProperty("PROPERTY5")
    private String property5;
    //收费类型(0:普通 1：公卫)
    @JsonProperty("FEE_TYPE")
    private String feeType;
    //显示顺序
    @JsonProperty("SHOW_NO")
    private Integer showNo;
    //兰卫检验项目名称
    @JsonProperty("TESTITEM_NAME")
    private String testitemName;
    //条码的机构编码
    @JsonProperty("LW_HSP_ORG_CODE")
    private String lwHspOrgCode;
    //兰卫条码编码
    @JsonProperty("LW_BARCODE")
    private String lwBarcode;
    //主条码
    @JsonProperty("LW_MAIN_BATCODE")
    private String lwMainBatcode;
    //操作时间
    @JsonProperty("LW_OPER_DATE")
    private Date lwOperDate;
    //容器类型
    @JsonProperty("LW_TUBE_TYPE")
    private String lwTubeType;
    //标本类型
    @JsonProperty("LW_SAMPLE_TYPE")
    private String lwSampleType;
    //合并条码分组编码(暂时体检用)
    @JsonProperty("LW_COMB_BAR_CODE")
    private String lwCombBarCode;
    //套餐id
    @JsonProperty("LAB_PACKAGE_ID")
    private String labPackageId;
    //套餐名称
    @JsonProperty("LAB_PACKAGE_NAME")
    private String labPackageName;


    public String getDetailId() {
        return detailId;
    }

    public void setDetailId(String detailId) {
        this.detailId = detailId;
    }

    public String getBarcode() {
        return barcode;
    }

    public void setBarcode(String barcode) {
        this.barcode = barcode;
    }

    public String getOutTestitemCode() {
        return outTestitemCode;
    }

    public void setOutTestitemCode(String outTestitemCode) {
        this.outTestitemCode = outTestitemCode;
    }

    public String getOutTestitemName() {
        return outTestitemName;
    }

    public void setOutTestitemName(String outTestitemName) {
        this.outTestitemName = outTestitemName;
    }

    public String getTestitemCode() {
        return testitemCode;
    }

    public void setTestitemCode(String testitemCode) {
        this.testitemCode = testitemCode;
    }

    public String getHspOrgCode() {
        return hspOrgCode;
    }

    public void setHspOrgCode(String hspOrgCode) {
        this.hspOrgCode = hspOrgCode;
    }

    public String getIsFee() {
        return isFee;
    }

    public void setIsFee(String isFee) {
        this.isFee = isFee;
    }

    public String getIsForbidden() {
        return isForbidden;
    }

    public void setIsForbidden(String isForbidden) {
        this.isForbidden = isForbidden;
    }

    public Integer getFeeNum() {
        return feeNum;
    }

    public void setFeeNum(Integer feeNum) {
        this.feeNum = feeNum;
    }

    public String getProperty1() {
        return property1;
    }

    public void setProperty1(String property1) {
        this.property1 = property1;
    }

    public String getProperty2() {
        return property2;
    }

    public void setProperty2(String property2) {
        this.property2 = property2;
    }

    public String getProperty3() {
        return property3;
    }

    public void setProperty3(String property3) {
        this.property3 = property3;
    }

    public String getProperty4() {
        return property4;
    }

    public void setProperty4(String property4) {
        this.property4 = property4;
    }

    public String getProperty5() {
        return property5;
    }

    public void setProperty5(String property5) {
        this.property5 = property5;
    }

    public String getFeeType() {
        return feeType;
    }

    public void setFeeType(String feeType) {
        this.feeType = feeType;
    }

    public Integer getShowNo() {
        return showNo;
    }

    public void setShowNo(Integer showNo) {
        this.showNo = showNo;
    }

    public String getTestitemName() {
        return testitemName;
    }

    public void setTestitemName(String testitemName) {
        this.testitemName = testitemName;
    }

    public String getLwHspOrgCode() {
        return lwHspOrgCode;
    }

    public void setLwHspOrgCode(String lwHspOrgCode) {
        this.lwHspOrgCode = lwHspOrgCode;
    }

    public String getLwBarcode() {
        return lwBarcode;
    }

    public void setLwBarcode(String lwBarcode) {
        this.lwBarcode = lwBarcode;
    }

    public String getLwMainBatcode() {
        return lwMainBatcode;
    }

    public void setLwMainBatcode(String lwMainBatcode) {
        this.lwMainBatcode = lwMainBatcode;
    }

    public Date getLwOperDate() {
        return lwOperDate;
    }

    public void setLwOperDate(Date lwOperDate) {
        this.lwOperDate = lwOperDate;
    }

    public String getLwTubeType() {
        return lwTubeType;
    }

    public void setLwTubeType(String lwTubeType) {
        this.lwTubeType = lwTubeType;
    }

    public String getLwSampleType() {
        return lwSampleType;
    }

    public void setLwSampleType(String lwSampleType) {
        this.lwSampleType = lwSampleType;
    }

    public String getLwCombBarCode() {
        return lwCombBarCode;
    }

    public void setLwCombBarCode(String lwCombBarCode) {
        this.lwCombBarCode = lwCombBarCode;
    }

    public String getLabPackageId() {
        return labPackageId;
    }

    public void setLabPackageId(String labPackageId) {
        this.labPackageId = labPackageId;
    }

    public String getLabPackageName() {
        return labPackageName;
    }

    public void setLabPackageName(String labPackageName) {
        this.labPackageName = labPackageName;
    }
}
