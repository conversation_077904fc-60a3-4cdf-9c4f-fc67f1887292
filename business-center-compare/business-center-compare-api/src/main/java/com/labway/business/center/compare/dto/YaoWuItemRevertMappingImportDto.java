package com.labway.business.center.compare.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class YaoWuItemRevertMappingImportDto implements ItemRevertMappingImportDto,Serializable {

    // 回传项目编码
    @ExcelProperty("外部药物编码")
    private String customerItemCode;

    // 回传项目名称
    @ExcelProperty("外部药物名称")
    private String customerItemName;

    @ExcelProperty("机构药物编码")
    private String orgItemCode;

    @ExcelProperty("机构药物名称")
    private String orgItemName;

    // 所在行号
    private int rowNum;

}
