package com.labway.business.center.compare.dto.diAn;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
 */
@XmlRootElement
@XmlAccessorType(XmlAccessType.FIELD)
public class GetOutSideApplyByBarcodeDto implements Serializable{


    // 条码号--251400001960
    @XmlElement(name = "BARCODE")
    private String BARCODE;

    // 外送机构编码--11
    @XmlElement(name = "OUTSIDE_ORG_CODE")
    private String OUTSIDE_ORG_CODE;

    // 申请机构编码--2514
    @XmlElement(name = "HSP_ORG_CODE")
    private String HSP_ORG_CODE;

    // 申请机构--XX实验室
    @XmlElement(name = "HSP_ORG_NAME")
    private String HSP_ORG_NAME;

    // 管型--专用采集管
    @XmlElement(name = "TUBE_TYPE")
    private String TUBE_TYPE;

    // 患者类型--门诊
    @XmlElement(name = "SOURCE_TYPE")
    private String SOURCE_TYPE;

    // 样本类型--分泌物
    @XmlElement(name = "SAMPLE_TYPE")
    private String SAMPLE_TYPE;

    // 样本状态--正常
    @XmlElement(name = "SAMPLE_STATUS")
    private String SAMPLE_STATUS;
    // 申请科室--产科
    @XmlElement(name = "APP_DEPT")
    private String APP_DEPT;
    // 患者编号--9007291
    @XmlElement(name = "PATIENT_NO")
    private String PATIENT_NO;
    // 患者名称--张三
    @XmlElement(name = "PATIENT_NAME")
    private String PATIENT_NAME;
    // 性别--女
    @XmlElement(name = "SEX")
    private String SEX;
    // 年龄--46岁
    @XmlElement(name = "AGE")
    private String AGE;
    // 出生日期--1972-07-18
    @XmlElement(name = "BIRTHDAY")
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    private Date BIRTHDAY;

    // 床号--2床
    @XmlElement(name = "BED")
    private String BED;
    // 诊断--
    @XmlElement(name = "DIAG")
    private String DIAG;
    // 身份证号
    @XmlElement(name = "ID_NUMBER")
    private String ID_NUMBER;
    // 地址
    @XmlElement(name = "ADDRESS")
    private String ADDRESS;
    // 电话
    @XmlElement(name = "TEL")
    private String TEL;
    // 备注
    @XmlElement(name = "MEMO")
    private String MEMO;
    // 申请医生--丁冬云
    @XmlElement(name = "APP_USER_NAME")
    private String APP_USER_NAME;

    // 申请时间--2018-07-18
    @XmlElement(name = "APP_DATE")
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    private Date APP_DATE;
    // 采样人
    @XmlElement(name = "EXTRACT_USER_NAME")
    private String EXTRACT_USER_NAME;
    // 采样时间
    @XmlElement(name = "EXTRACT_DATE")
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    private Date EXTRACT_DATE;

    // 申请单明细
    @XmlElement(name = "TESTITEMS")
    private List<ApplyDetail> TESTITEMS;


    @XmlAccessorType(XmlAccessType.FIELD)
    public static class ApplyDetail implements Serializable{

        // 条码号--251400001696
        @XmlElement(name = "BARCODE")
        private String BARCODE;

        // 检验项目编码--8602730
        @XmlElement(name = "TESTITEM_CODE")
        private String TESTITEM_CODE;
        // 检验项目名称--血红蛋白F（HbF）
        @XmlElement(name = "TESTITEM_NAME")
        private String TESTITEM_NAME;

        public String getBARCODE() {
            return BARCODE;
        }

        public void setBARCODE(String BARCODE) {
            this.BARCODE = BARCODE;
        }

        public String getTESTITEM_CODE() {
            return TESTITEM_CODE;
        }

        public void setTESTITEM_CODE(String TESTITEM_CODE) {
            this.TESTITEM_CODE = TESTITEM_CODE;
        }

        public String getTESTITEM_NAME() {
            return TESTITEM_NAME;
        }

        public void setTESTITEM_NAME(String TESTITEM_NAME) {
            this.TESTITEM_NAME = TESTITEM_NAME;
        }
    }


    public String getBARCODE() {
        return BARCODE;
    }

    public void setBARCODE(String BARCODE) {
        this.BARCODE = BARCODE;
    }

    public String getOUTSIDE_ORG_CODE() {
        return OUTSIDE_ORG_CODE;
    }

    public void setOUTSIDE_ORG_CODE(String OUTSIDE_ORG_CODE) {
        this.OUTSIDE_ORG_CODE = OUTSIDE_ORG_CODE;
    }

    public String getHSP_ORG_CODE() {
        return HSP_ORG_CODE;
    }

    public void setHSP_ORG_CODE(String HSP_ORG_CODE) {
        this.HSP_ORG_CODE = HSP_ORG_CODE;
    }

    public String getHSP_ORG_NAME() {
        return HSP_ORG_NAME;
    }

    public void setHSP_ORG_NAME(String HSP_ORG_NAME) {
        this.HSP_ORG_NAME = HSP_ORG_NAME;
    }

    public String getTUBE_TYPE() {
        return TUBE_TYPE;
    }

    public void setTUBE_TYPE(String TUBE_TYPE) {
        this.TUBE_TYPE = TUBE_TYPE;
    }

    public String getSOURCE_TYPE() {
        return SOURCE_TYPE;
    }

    public void setSOURCE_TYPE(String SOURCE_TYPE) {
        this.SOURCE_TYPE = SOURCE_TYPE;
    }

    public String getSAMPLE_TYPE() {
        return SAMPLE_TYPE;
    }

    public void setSAMPLE_TYPE(String SAMPLE_TYPE) {
        this.SAMPLE_TYPE = SAMPLE_TYPE;
    }

    public String getSAMPLE_STATUS() {
        return SAMPLE_STATUS;
    }

    public void setSAMPLE_STATUS(String SAMPLE_STATUS) {
        this.SAMPLE_STATUS = SAMPLE_STATUS;
    }

    public String getAPP_DEPT() {
        return APP_DEPT;
    }

    public void setAPP_DEPT(String APP_DEPT) {
        this.APP_DEPT = APP_DEPT;
    }

    public String getPATIENT_NO() {
        return PATIENT_NO;
    }

    public void setPATIENT_NO(String PATIENT_NO) {
        this.PATIENT_NO = PATIENT_NO;
    }

    public String getPATIENT_NAME() {
        return PATIENT_NAME;
    }

    public void setPATIENT_NAME(String PATIENT_NAME) {
        this.PATIENT_NAME = PATIENT_NAME;
    }

    public String getSEX() {
        return SEX;
    }

    public void setSEX(String SEX) {
        this.SEX = SEX;
    }

    public String getAGE() {
        return AGE;
    }

    public void setAGE(String AGE) {
        this.AGE = AGE;
    }

    public Date getBIRTHDAY() {
        return BIRTHDAY;
    }

    public void setBIRTHDAY(Date BIRTHDAY) {
        this.BIRTHDAY = BIRTHDAY;
    }

    public String getBED() {
        return BED;
    }

    public void setBED(String BED) {
        this.BED = BED;
    }

    public String getDIAG() {
        return DIAG;
    }

    public void setDIAG(String DIAG) {
        this.DIAG = DIAG;
    }

    public String getID_NUMBER() {
        return ID_NUMBER;
    }

    public void setID_NUMBER(String ID_NUMBER) {
        this.ID_NUMBER = ID_NUMBER;
    }

    public String getADDRESS() {
        return ADDRESS;
    }

    public void setADDRESS(String ADDRESS) {
        this.ADDRESS = ADDRESS;
    }

    public String getTEL() {
        return TEL;
    }

    public void setTEL(String TEL) {
        this.TEL = TEL;
    }

    public String getMEMO() {
        return MEMO;
    }

    public void setMEMO(String MEMO) {
        this.MEMO = MEMO;
    }

    public String getAPP_USER_NAME() {
        return APP_USER_NAME;
    }

    public void setAPP_USER_NAME(String APP_USER_NAME) {
        this.APP_USER_NAME = APP_USER_NAME;
    }

    public Date getAPP_DATE() {
        return APP_DATE;
    }

    public void setAPP_DATE(Date APP_DATE) {
        this.APP_DATE = APP_DATE;
    }

    public String getEXTRACT_USER_NAME() {
        return EXTRACT_USER_NAME;
    }

    public void setEXTRACT_USER_NAME(String EXTRACT_USER_NAME) {
        this.EXTRACT_USER_NAME = EXTRACT_USER_NAME;
    }

    public Date getEXTRACT_DATE() {
        return EXTRACT_DATE;
    }

    public void setEXTRACT_DATE(Date EXTRACT_DATE) {
        this.EXTRACT_DATE = EXTRACT_DATE;
    }

    public List<ApplyDetail> getApply_Detail() {
        return TESTITEMS;
    }

    public void setApply_Detail(List<ApplyDetail> apply_Detail) {
        TESTITEMS = apply_Detail;
    }
}
