package com.labway.business.center.compare.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class OrgApplyResultSamplesDTO implements Serializable {
    //申请单样本表id
    private String mainId;
    //外送到中台的条码号
    private String barcode;
    // 样本编号
    private String sampleNo;
    // 检验时间
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date testDate;
    // 检验时间
    private String testDate2;
    //患者名称
    private String patientName;
    //送检机构编码
    private String hspOrgCode;
    //送检机构名称
    private String hspOrgName;
    // 样本送往的目标机构编码(检验机构编码)
    private String targetOrgCode;
    // 样本送往的目标机构名称(检验机构名称)
    private String targetOrgName;
    // 外送时间
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date applyDate;
    // 报告单urls
    private String reportUrls;

    /**
     * 一审人编码
     */
    private String firstAuditUserCode;
    /**
     * 一审人名称
     */
    private String firstAuditUserName;
    /**
     * 一审时间
     */
    private Date firstAuditTime;
    /**
     * 二审人编码
     */
    private String secAuditUserCode;
    /**
     * 二审人名称
     */
    private String secAuditUserName;
    /**
     * 二审时间
     */
    private Date secAuditTime;
    /**
     * 检验人编码
     */
    private String testUserCode;
    /**
     * 检验人名称
     */
    private String testUserName;
    /**
     * 签收机构编码
     */
    private String signOrgCode;


    // 报告单urls
    private String imgUrls;
    // 结果备注，建议与解释等
    private String resultRemark;
    // 回传结果
    private List<TbOrgApplySampleMainIteResultDTO> tbOrgApplySampleMainIteResultDTOS;
}
