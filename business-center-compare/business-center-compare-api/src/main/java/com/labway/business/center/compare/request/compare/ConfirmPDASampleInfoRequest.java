package com.labway.business.center.compare.request.compare;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
public class ConfirmPDASampleInfoRequest implements Serializable {

    /**
     * 机构编码
     */
    @NotBlank(message = "机构编码不能为空！")
    private String orgCode;

    /**
     * 机构编码
     */
    private String orgName;

    /**
     * 实验室样本条码/也是样本送检的条码barcode
     */
    @NotEmpty(message = "实验室样本条码不能为空！")
    private List<String> limsBarcodes;

    /**
     * 确认状态 0 未确认 1 已确认
     */
    @NotNull(message = "确认状态不能为空！")
    @Range(min = 0, max = 1, message = "确认状态错误，0 未确认 1 已确认！")
    private Integer confirmStatus;

    /**
     * 确认人编码
     */
    private String confirmUserCode;

    /**
     * 确认人名称
     */
    private String confirmUserName;



}
