package com.labway.business.center.compare.dto.danyang;

import com.alibaba.fastjson.JSON;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * DanyangHPVTCTTokenInfo
 * 丹阳两癌授权码Token信息
 * </p>
 *
 * <AUTHOR>
 * @since 2023/11/7 17:56
 */
@Data
public class DanyangHPVTCTTokenInfo implements Serializable {

    /**
     * token值
     */
    private String token;
    /**
     * 机构代码,后续数据推送需要用到
     */
    private String inputOrganCode;
    /**
     * 机构名称,后续数据推送需要用到
     */
    private String inputOrganName;
    /**
     * 医生身份证号,后续数据推送需要用到
     */
    private String userIdcard;
    /**
     * 医生姓名,后续数据推送需要用到
     */
    private String userName;

    public static DanyangHPVTCTTokenInfo parse(String token) {
        return JSON.parseObject(token, DanyangHPVTCTTokenInfo.class);
    }
}
