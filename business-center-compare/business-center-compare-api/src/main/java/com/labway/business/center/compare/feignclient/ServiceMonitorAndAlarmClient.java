package com.labway.business.center.compare.feignclient;

import com.labway.business.center.compare.dto.monitoralarm.MonitorParam;
import com.swak.frame.dto.Response;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <pre>
 * ServiceMonitorAndAlarmClient
 * </pre>
 *
 * <AUTHOR>
 * @since 2025/4/21 14:32
 */
@FeignClient(name = "monitor-service", url = "${monitor-url:http://10.140.0.75:9999}", path = "monitor-alarm")
public interface ServiceMonitorAndAlarmClient {

    @PostMapping("/monitor")
    Response<?> monitor(@RequestBody MonitorParam monitorParam);

}
