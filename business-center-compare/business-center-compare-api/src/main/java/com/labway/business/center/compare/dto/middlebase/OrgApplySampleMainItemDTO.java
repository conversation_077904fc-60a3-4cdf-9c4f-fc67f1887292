package com.labway.business.center.compare.dto.middlebase;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 外送生清单-送检项目信息表(TbOrgApplySampleMainItemDTO)表实体类
 *
 * <AUTHOR>
 * @since 2023-04-24 13:20:25
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrgApplySampleMainItemDTO implements Serializable {

    /**
     * 送检样本条码号
     */
    private String barcode;
    /**
     * 送检机构编码（对应中台客商编码）
     */
    private String hspOrgCode;
    /**
     * 送检机构名称（对应中台客商名称）
     */
    private String hspOrgName;
    /**
     * 外部项目编码
     */
    private String outTestItemCode;
    /**
     * 外部项目名称
     */
    private String outTestItemName;

    /**
     * 对照关系
     */
    private List<OrgApplySampleMainItemMappingDTO> sampleMainItemMappings;
}

