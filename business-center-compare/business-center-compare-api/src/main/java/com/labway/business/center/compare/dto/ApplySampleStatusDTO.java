package com.labway.business.center.compare.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.util.Date;
import java.util.List;

/**
 * 外送申请单样本主表
 */
@Data
public class ApplySampleStatusDTO {

    // 送检机构编码（对应中台的客商编码）
    private String hspOrgCode;

    // 送检机构名称（对应中台的客商名称）
    private String hspOrgName;

    // 申请单号
    private String formCode;

    // 外送到中台的条码号
    private String barcode;

    // 1待对照 2待签收 3已签收 4取消外送
    private Integer status;

    // 取消外送原因 0默认 1物流未取样
    private Integer cancelReason;

    // 样本流转状态 10已发送 20已取样 30已妥投 40前处理交接 50前处理签收 60已报告 70取消外送
    private Integer flowStatus;

    // 是否已取样 0否 1是
    private Integer isTake;

}

