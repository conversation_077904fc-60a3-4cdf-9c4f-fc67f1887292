package com.labway.business.center.compare.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TbOrgItemMappingRelationDTO implements Serializable {
    //检验项目编码
    private String itemTestCode;
    //检验项目名称
    private String itemTestName;
    //外部检验项目编码
    private String itemOutsideCode;
    //外部检验项目名称
    private String itemOutsideName;
    //客商编码
    private String customerCode;
    //客商名称
    private String customerName;
}
