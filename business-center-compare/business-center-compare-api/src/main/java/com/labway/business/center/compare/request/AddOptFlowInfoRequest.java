package com.labway.business.center.compare.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2023/06/16 09:57
 * @description
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AddOptFlowInfoRequest implements Serializable {

    @Valid
    @NotEmpty(message = "样本操作记录不能为空1")
    private List<AddOptFlow> flowList;

    // 样本流转状态
    @NotBlank(message = "样本流转状态不能为空")
    private Integer flowStatus;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class AddOptFlow implements Serializable{

        //申请单id
        private String formId;
        //申请单编码
        private String formCode;
        //样本条码号
        private String barcode;
        //实验室条码号
        private String lwBarcode;
        //送检机构编码
        private String hspOrgCode;
        //送检机构名称
        private String hspOrgName;
        //病人姓名
        private String patientName;
        //病人编号
        private String patientCode;
        //病人性别
        private String sex;
        //病人年龄
        private String age;
        //床号
        private String patientBed;
        //就诊类型
        private String applyType;
        //申请科室
        private String dept;
        //送检医生
        private String sendDoctor;
        //是否加急
        private String urgent;
        //备注
        private String remark;
        //操作人id
        private String optId;
        //操作人名称
        private String optName;
        //操作时间
        private Date optTime;

    }


}