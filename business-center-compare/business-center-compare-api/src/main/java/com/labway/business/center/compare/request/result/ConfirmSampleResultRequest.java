package com.labway.business.center.compare.request.result;

import com.labway.business.center.core.enums.ItemTypeEnum;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * 查询样本结果请求
 *
 * <AUTHOR> on 2024/12/26.
 */
@Data
public class ConfirmSampleResultRequest implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 送检机构编码
	 */
	@NotBlank(message = "送检机构编码不能为空!")
	private String hspOrgCode;

	/**
	 * 条码号
	 */
	@NotEmpty(message = "条码号不能为空!")
	private List<String> barcodeList;


	/**
	 * 项目类型
	 * @see ItemTypeEnum
	 */
	private String itemType;


}
