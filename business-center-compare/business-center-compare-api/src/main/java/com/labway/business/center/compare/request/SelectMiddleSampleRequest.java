package com.labway.business.center.compare.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.labway.business.center.core.enums.MiddleSampleLisStatus;
import lombok.Data;

import java.io.Serializable;
import java.util.Collection;
import java.util.Date;
import java.util.Set;

/**
 * 社区 查询 中间库 申请单
 *
 * <AUTHOR>
 * @since 2023/8/21 17:33
 */
@Data
public class SelectMiddleSampleRequest implements Serializable {

    private static final long serialVersionUID = -4466600937183937066L;
    /**
     * 条码号
     */
    private Collection<String> barcodeList;

    /**
     * 创建时间范围查询
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startDate;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endDate;

    /**
     * 送检机构编码
     */
    private String hspOrgCode;

    /**
     * 签收机构编码
     */
    private String signOrgCode;

    /**
     * 签收机构名称
     */
    private String signOrgName;

    /**
     * 快速搜索(条形码/姓名)
     */
    private String quickSearch;


    /**
     * 查询时间雷类型: 1 签收时间 2 确认时间
     */
    private Integer dateType;

    /**
     * 样本状态
     *
     * @see MiddleSampleLisStatus
     */
    private Integer sampleStatus;

    /**
     * 操作人
     */
    private Set<String> operatorIds;

}
