package com.labway.business.center.compare.request.daAn;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;

@Data
@NoArgsConstructor
@XmlRootElement(name = "BarcodeInfo")
@XmlAccessorType(XmlAccessType.FIELD)
public class GetSpecimenInfoRequest implements Serializable {

    // 外部条码号
    @XmlElement(name = "HospitalBarcode")
    private String HospitalBarcode;

}
