package com.labway.business.center.compare.dto.danyang;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * HPVSampleSaveInfo
 * 两癌基本信息
 * </p>
 *
 * <AUTHOR>
 * @since 2023/11/8 11:41
 */
@Data
public class HPVSampleSaveInfo implements Serializable {

    // 唯一ID Y   唯一主键，必传，唯一标识该记录，使用GUID
    private String sysId;

    // 分库键 Y    ‘0‘
    private String dbKey;

    // 分表键 Y    ‘0‘
    private String tbKey;

    // 数据来源 Y   区域名称+公司名(如静安区臻鼎)
    private String src;

    // 扩展字段
    private String ext;

    // 数据上传时间   yyyy-mm-dd hh24:mi:ss
    private Date uploadTime;

    // 检查机构代码   妇幼系统机构代码
    private String organCode;

    // 检查机构名称   妇幼系统机构名称
    private String organ;

    // 检查医生身份证号
    private String doctorCode;

    // 检查医生姓名
    private String doctor;

    // 检查日期 yyyy-mm-dd hh24:mi:ss
    private Date checkDate;

    // 登记机构代码 Y        妇幼系统机构代码
    private String inputOrganCode;

    // 登记机构名称 Y        妇幼系统机构名称
    private String inputOrgan;

    // 登记医生身份证号 Y
    private String inputDoctorCode;

    // 登记医生姓名   Y
    private String inputDoctor;

    // 登记日期时间 Y yyyy-mm-dd hh24:mi:ss
    private Date inputDate;

    // 更新机构代码   妇幼系统机构代码
    private String updateOrganCode;

    // 更新机构名称   妇幼系统机构名称
    private String updateOrgan;

    // 更新医生身份证号
    private String updateDoctorCode;

    // 更新医生姓名
    private String updateDoctor;

    // 更新日期时间   yyyy-mm-dd hh24:mi:ss
    private Date updateDate;

    // 编号   由省妇幼系统统一生成
    private String no;

    // 姓名   Y
    private String name;

    // 身份证号
    private String idcard;

    // 出生日期 Y
    private Date birthday;

    // 证件类型 Y   字典编码见(SFZJLBDMB身份证件类别代码表)
    private String cardType;

    // 证件号码 Y
    private String cardNo;

    // 就诊号
    private String patientNo;

    // 门诊号
    private String outpatientNo;

    // 宫颈癌检查标志
    private String checkCervical;

    // 乳腺癌检查标志
    private String checkBreast;

    // 市民卡号
    private String citizenNo;

    // 低保证号
    private String lowInsuranceNo;

    // 特困妇女 0-否 1-是
    private String poor;

    // 自愿免费检查   0-否 1-是
    private String freeCheck;

    // 年龄   Y   NUMBER(2,0)
    private Integer age;

    // 职业 Y 字典编码见(ZYFLDMB职业分类代码)
    private String occupation;

    // 文化程度 Y   01-研究生 02-大学本科 03-大学专科和专科学校 04-中等专业学校 05-技工学校 06-高中 07-初中 08-小学 09-文盲或半文盲 99-不详 字典编码见(WHCDDMB文化程度代码)
    private String education;

    // 国籍
    private String nationality;

    // 民族    Y
    private String nation;

    // 医保类型
    private String hosType;

    // 医保卡号
    private String idcardHos;

    // 居住地址代码   Y
    private String addrCode;

    // 居住地省 Y
    private String prov;

    // 居住地市 Y
    private String city;

    // 居住地县区    Y
    private String county;

    // 居住地乡(街道) Y
    private String town;

    // 居住地村
    private String village;

    // 居住地组号
    private String teamNo;

    // 居住地小区门牌号
    private String team;

    // 邮编
    private String postCode;

    // 工作单位
    private String workOrgan;

    // 工作单位联系电话
    private String workTel;

    // 户籍地址
    private String domicileAddr;

    // 户籍类别 字典编码见(HJBZDMB户籍标志)
    private String domicileType;

    // 户口类别 字典编码见(HKLBDMB户口类别)
    private String domicileMode;

    // 外部编号 历史数据对接时传外部系统编号
    private String outNo;

    // 样本编号
    private String sampleNo;

    // 采样日期
    private Date sampleDate;

    // 快速登记 0-否 1-是
    private String isQuick;

    // 送检机构代码
    private String checkOrganCode;

    // 送检机构名称
    private String checkOrgan;

    // 是否农村适龄妇女两癌检查项目
    private String womanCancerCheck;

    // 联系电话 Y
    private String tel;

    // TCT编号
    private String tctNo;

    // HPV编号
    private String hpvNo;

    // 令牌
    private String token;

    // 身高   NUMBER(5,2)
    private Integer height;

    // 体重   NUMBER(5,0)
    private Integer weight;

    // 身体质量指数   NUMBER(5,1)
    private Integer bodyMassIndex;

    // 月经史_初潮年龄 NUMBER(2,0)
    private Integer mensesAge;

    // 月经史_末次月经
    private Date lmp;

    // 月经史_是否绝经 1-否 2-是 3-不确定
    private String menopause;

    // 月经史_绝经年龄 NUMBER(2,0)
    private Integer menopauseAge;

    // 孕产史_是否生产过    1-是 0-否
    private String delivery;

    // 孕产史_初产年龄 NUMBER(2,0)
    private Integer deliveryAge;

    // 孕产史_是否哺乳 1-是 0-否
    private String lactation;

    // 母乳喂养持续时间 NUMBER(2,0)
    private Integer breastfeedingTime;

    // 经期
    private String menstrual;

    // 月经周期（天）
    private String cyc;

    // 避孕方法_未避孕
    private String larcControl;

    // 避孕方法_避孕套 1-是 0-否
    private String larcCondom;

    // 避孕方法_避孕药 1-是 0-否
    private String larcAcyeterion;

    // 避孕方法_避孕药_年   NUMBER(2,0)
    private Integer larcAcyeYear;

    // 避孕方法_宫内节育器   1-是 0-否
    // 避孕方法_宫内节育器   1-是 0-否
    private String larcIud;

    // 避孕方法_宫内节育器   NUMBER(2,0)
    private Integer larcIudYear;

    // 避孕方法_其他方式    1-是 0-否
    private String larcOther;

    // 避孕方法_其他方式_详
    private String larcOtherCont;

    // 孕次   NUMBER(2,0)
    private Integer gravidityTimes;

    // 分娩   NUMBER(2,0)
    private Integer deliverTimes;

}
