package com.labway.business.center.compare.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DeleteItemRevertMappingRequest implements Serializable {

    //送检机构编码
    @NotBlank(message = "客商编码不能为空！")
    private String customerCode;
    //送检客商项目编码
    @NotBlank(message = "客商回传项目编码不能为空!")
    private String customerItemCode;
    //回传对照类型 1报告项目2细菌2药物
    @NotNull(message = "回传对照类型不能为空(1报告项目2细菌2药物)!")
    @Range(min = 1, max = 9,message = "回传对照类型错误(1报告项目2细菌2药物)!")
    private Integer mappingType;

    //检验机构编码
    @NotBlank(message = "检验机构编码不能为空！")
    private String orgCode;
    @Valid
    @NotEmpty(message = "机构回传项目不能为空！")
    private List<String> orgItemCodes;


}
