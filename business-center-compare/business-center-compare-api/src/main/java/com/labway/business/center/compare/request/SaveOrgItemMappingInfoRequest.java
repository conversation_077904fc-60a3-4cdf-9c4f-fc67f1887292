package com.labway.business.center.compare.request;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
public class SaveOrgItemMappingInfoRequest implements Serializable {

    // 送检机构编码
    @NotBlank(message = "送检机构编码不能为空（例如迪安）!")
    private String hspOrgCode;
    // 送检机构编码
    @NotBlank(message = "送检机构名称不能为空!")
    private String hspOrgName;

    // 实验室编码
    @NotBlank(message = "实验室编码不能为空(例如丹阳实验室)！")
    private String orgCode;
    // 实验室编码
    @NotBlank(message = "实验室名称不能为空！")
    private String orgName;

    // 项目类型 1检验项目 2报告项目
    @NotNull(message = "项目类型不能为空（1检验项目 2报告项目）!")
    @Range(min = 1,max = 2,message = "项目类型错误（1检验项目 2报告项目）!")
    private Integer itemType;

    // 项目id
    private String itemId;

    // 项目编码
    @NotBlank(message = "项目编码不能为空！")
    private String itemCode;

    // 项目名称
    @NotBlank(message = "项目名称不能为空！")
    private String itemName;

    @Valid
    @NotEmpty(message = "项目对照信息不能为空！")
    private List<ItemMappingInfo> itemMappingInfo;

    // 操作用户id
    private String optUserId;

    // 操作用户名称
    private String optUserName;

    // 需要更新对照结果得样本信息
    private List<UpdateSampleInfo> updateSampleInfos;


    @Data
    @NoArgsConstructor
    public static class ItemMappingInfo implements Serializable{

        // 项目id
        private String itemId;

        // 项目编码
        @NotBlank(message = "项目编码不能为空！")
        private String itemCode;

        // 项目名称
        @NotBlank(message = "项目名称不能为空！")
        private String itemName;

    }

    @Data
    @NoArgsConstructor
    public static class UpdateSampleInfo implements Serializable{

//        // 送检机构编码
//        private String hspOrgCode;

        // 样本条码号
        private String barcode;

    }


}
