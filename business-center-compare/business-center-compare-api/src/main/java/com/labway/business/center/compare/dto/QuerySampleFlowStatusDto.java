package com.labway.business.center.compare.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class QuerySampleFlowStatusDto implements Serializable {

    // 送检机构编码
    private String hspOrgCode;

    // 申请单号
    private String formCode;

    // 样本条码号
    private String barcode;

    // 样本状态流
    private List<SampleStatus> flowStatus;

    @Data
    @NoArgsConstructor
    public static class SampleStatus implements Serializable{

        // 状态编码
        private String statusCode;

        // 转态名称
        private String statusName;

        // 状态时间
        private Date statusTime;

    }


}
