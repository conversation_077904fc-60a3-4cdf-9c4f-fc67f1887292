package com.labway.business.center.compare.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * 移除外送申请单 中间库模式
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CancelSendMiddleSampleApplyRequest implements Serializable {

    private static final long serialVersionUID = -8855452394545820088L;

    @NotBlank(message = "送检机构编码不能为空！")
    private String hspOrgCode;

    @NotEmpty(message = "样本信息不能为空！")
    private List<SampleInfo> sampleInfoList;

    //操作人id
    @NotBlank(message = "取消人id不能为空！")
    private String optId;
    //操作人名称
    @NotBlank(message = "取消人名称不能为空！")
    private String optName;


    @Data
    public static class SampleInfo {

        @NotBlank(message = "申请单号不能为空！")
        private String formCode;

        @NotBlank(message = "样本条码号不能为空！")
        private String barcode;
    }
}