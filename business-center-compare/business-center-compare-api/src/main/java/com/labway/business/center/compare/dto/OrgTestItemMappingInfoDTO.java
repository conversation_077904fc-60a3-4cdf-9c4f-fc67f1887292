package com.labway.business.center.compare.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2023/04/20 08:57
 * @description
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OrgTestItemMappingInfoDTO implements Serializable {

    // 检验项目id
    private String itemTestId;
    // 检验项目编码
    private String itemTestCode;
    // 检验项目名称
    private String itemTestName;
    // 检验项目英文名称
    private String englishName;
    // 样本类型编码
    private String sampleTypeCode;
    // 样本类型名称
    private String sampleTypeName;
    // 样本存放说明
    private String saveDescription;
    // 检验方法编码
    private String testMethod;
    // 检验方法名称
    private String testMethodName;

    // 报告项目信息
    private List<ReportItemInfoDTO> reportItems;


}