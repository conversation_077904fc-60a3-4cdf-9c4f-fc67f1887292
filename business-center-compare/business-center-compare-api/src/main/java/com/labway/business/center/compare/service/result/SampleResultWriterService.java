package com.labway.business.center.compare.service.result;

import com.labway.business.center.compare.dto.TbOrgApplySampleMainDTO;
import com.labway.business.center.compare.dto.report.SampleResponse;
import com.labway.report.common.dto.sample.ReportPrintQueryDto;
import com.swak.frame.dto.Response;

/**
 * 查询报告平台结果
 *
 * <AUTHOR> on 2024/12/27.
 */
public interface SampleResultWriterService {

	/**
	 * 新版同步报告结果
	 * @param queryDto {@link ReportPrintQueryDto}
	 */
	Response<String> syncReportResultNew(ReportPrintQueryDto queryDto);

	/**
	 * 清除样本结果
	 * @param queryDto {@link ReportPrintQueryDto}
	 */
	Response<String> cleanReport(ReportPrintQueryDto queryDto);

	/**
	 * 处理老的同步
	 * @param queryDto {@link ReportPrintQueryDto}
	 * @param outBarcode {@link TbOrgApplySampleMainDTO#getBarcode() 外部条码号}
	 */
	void handlerOldSync(ReportPrintQueryDto queryDto, String outBarcode);
}
