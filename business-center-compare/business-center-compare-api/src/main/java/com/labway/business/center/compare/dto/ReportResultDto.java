package com.labway.business.center.compare.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2023/05/09 14:48
 * @description
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ReportResultDto implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 当前页
     */
    private Integer current;

    /**
     * 大小
     */
    private Integer size;


    /**
     * 样本数
     */
    private Long sampleCount;

    /**
     * { key: 送检机构编码 , value: 结果列表 }
     */
    private List<ReportInfoDto> hospitals;


}