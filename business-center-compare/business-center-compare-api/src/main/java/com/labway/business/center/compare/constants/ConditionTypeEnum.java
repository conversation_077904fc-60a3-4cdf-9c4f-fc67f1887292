package com.labway.business.center.compare.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 *
 * <AUTHOR>
 * @Description 结算类型的匹配方式
 * @Date 2024/12/2 15:29
 */
@Getter
@AllArgsConstructor
public enum ConditionTypeEnum {

	OR("or","或"),
	AND("and","且"),
	OTHER("other", "未知");

	private final String code;
	private final String desc;

	public boolean isOtherType() {
		return !Arrays.stream(values()).anyMatch(type -> type.getCode().equals(this.getCode()));
	}

	public static ConditionTypeEnum getEnumByCode(String code) {
		return Arrays.stream(values()).filter(type -> type.getCode().equals(code)).findFirst().orElse(OTHER);
	}
}
