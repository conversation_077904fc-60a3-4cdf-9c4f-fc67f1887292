package com.labway.business.center.compare.request;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@NoArgsConstructor
public class QueryOrgItemMappingInfoRequest implements Serializable {

    // 送检机构编码
    @NotBlank(message = "送检机构编码不能为空（例如迪安）!")
    private String hspOrgCode;

    // 实验室编码
    @NotBlank(message = "实验室编码不能为空（例如丹阳实验室）！")
    private String orgCode;

    // 外部项目编码
    @NotBlank(message = "项目编码不能为空!")
    private String itemCode;

    // 项目类型 1检验项目 2报告项目
    @NotNull(message = "项目类型不能为空（1检验项目 2报告项目）!")
    @Range(min = 1,max = 2,message = "项目类型错误（1检验项目 2报告项目）!")
    private Integer itemType;


}
