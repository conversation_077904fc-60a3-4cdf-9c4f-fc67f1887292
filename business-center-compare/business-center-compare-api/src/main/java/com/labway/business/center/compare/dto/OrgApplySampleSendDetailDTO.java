package com.labway.business.center.compare.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2023/05/08
 * @description
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class OrgApplySampleSendDetailDTO implements Serializable {

    // 申请单样本表id
    private String mainId;

    // 条码号
    private String barcode;

    // 状态（编码）
    private Integer statusCode;

    // 状态
    private String status;

    // 流转状态
    private String flowStatus;

    // 条码信息
    private OrgApplySampleSendBarcodeInfo barcodeInfo;

    // 病人信息
    private OrgApplySampleSendPatientInfo patientInfo;

    // 项目信息
    private List<OrgApplySampleSendItemInfo> itemInfoList;


    /**
     * 条码信息
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class OrgApplySampleSendBarcodeInfo implements Serializable {

        // 外送申请单id
        private String formId;

        // 外送申请单号
        private String formCode;

        // 实验室条码号
        private String signBarcode;

        // 样本类型
        private String sampleType;

        // 管型
        private String tubeType;

        // 送检机构编码
        private String hspOrgCode;

        // 送检机构名称
        private String hspOrgName;

        // 检验机构编码
        private String targetOrgCode;

        // 检验机构名称
        private String targetOrgName;

        // 外送时间
        private Date applyDate;

        // 外送人（=送检医生？）
        private String sendDoctor;

        // 取样时间（表中暂无）
        private Date takeSampleTime;

        // 妥投时间（表中暂无）
        private Date sampleDeliverTime;

        // 物流员（表中暂无）
        private String staffName;

        // 前处理签收时间
        private Date receiveTime;

        // 签收人编码
        private String receiveUserCode;

        // 签收人名称
        private String receiveUserName;

        //备注
        private String remark;

        private String mainId;
        //外送到中台的条码号
        private String barcode;
        //所属业务单元编码
        private String orgCode;
        //所属业务单元名称
        private String orgName;
        //申请单类型 门诊/住院
        private String applyType;
        //门诊/住院 号
        private String patientVisitCard;
        //是否加急 0否1是
        private Integer urgent;
        //样本形状
        private String sampleProperty;
        //申请科室
        private String dept;
        //病区
        private String inpatientArea;
        //患者名称
        private String patientName;
        //性别 1男2女
        private Integer patientSex;
        //年龄 xx岁
        private Integer patientAge;
        //子年龄 xxx天|xxx周|xxx月|
        private Integer patientSubage;
        //子年龄单位
        private String patientSubageUnit;
        //生日
        private Date patientBirthday;
        //床号
        private String patientBed;
        //临床诊断
        private String clinicalDiagnosis;
        //身份证
        private String patientCard;
        //证件类型
        private String patientCardType;
        //住址
        private String patientAsddress;
        //手机号
        private String patientMobile;
        //采样时间
        private Date samplingDate;


        // 外送样本来源标识（his,物流，社区等）
        private String sampleSource;

    }

    /**
     * 病人信息
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class OrgApplySampleSendPatientInfo implements Serializable {

        // 病人姓名
        private String patientName;

        // 病人编号
        private String patientVisitCard;

        // 性别（编码）
        private Integer patientSexCode;

        // 性别（编码）
        private String patientSex;

        // 年龄
        private Integer patientAge;

        // 床号
        private String patientBed;

        // 就诊类型
        private String applyType;

        // 科室
        private String dept;

        // 送检医生
        private String sendDoctor;

        // 是否加急（编码）
        private Integer urgentCode;

        // 是否加急
        private String urgent;

        //子年龄 xxx天|xxx周|xxx月|
        private Integer patientSubage;
        //子年龄单位
        private String patientSubageUnit;
        //生日
        private Date patientBirthday;
        //临床诊断
        private String clinicalDiagnosis;
        //身份证
        private String patientCard;
        //证件类型
        private String patientCardType;
        //住址
        private String patientAsddress;
        //手机号
        private String patientMobile;

        // 医保卡号
        private String visitCardNo;


    }

    /**
     * 检验项目
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class OrgApplySampleSendItemInfo implements Serializable {

        // 送检项目表id
        private String mainItemId;

        // 检验项目编码
        private String itemTestCode;

        // 检验项目名称
        private String itemTestName;

        // 签收项目编码
        private String outTestItemCode;

        // 签收项目名称
        private String outTestItemName;

    }
}
