package com.labway.business.center.compare.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.*;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
public class ExportOutTestItemMappingDto implements Serializable {


    private String CustomerCode;

    private String CustomerName;

    private List<ItemOutsideMappingDto> itemOutsideMapping;


    @Data
    @NoArgsConstructor
    @ColumnWidth(25)
    @HeadRowHeight(14)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentRowHeight(12)
    @ContentFontStyle(fontHeightInPoints = 10)
    public static class ItemOutsideMappingDto implements Serializable {
        //是否有对照关系 0否 1是
        @ExcelProperty(value = "对照状态")
        private String isMapping;

        @ExcelProperty(value = "送检机构编码")
        @ExcelIgnore
        private String customerCode;

        @ExcelProperty(value = "送检机构名称")
        private String customerName;

        @ExcelProperty(value = "外部检验项目编码")
        private String itemOutsideCode;

        @ExcelProperty(value = "外部检验项目名称")
        private String itemOutsideName;

        //状态是否启用 0未启用 1启用
        @ExcelProperty(value = "启用状态")
        private String status;

        @ExcelProperty(value = "兰卫项目编码")
        private String itemTestCode;

        @ExcelProperty(value = "兰卫项目名称")
        private String itemTestName;

        //样本类型编码
        @ExcelProperty(value = "样本类型编码")
        @ExcelIgnore
        private String sampleTypeCode;

        //样本类型名称
        @ExcelProperty(value = "样本类型")
        private String sampleTypeName;

        // 检验方法编码
        @ExcelProperty(value = "检验方法编码")
        @ExcelIgnore
        private String testMethod;

        // 检验方法名称呢
        @ExcelProperty(value = "检验方法")
        private String testMethodName;

    }


}
