package com.labway.business.center.compare.dto.LJLmis;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ReceiveOutsideApplySampleDto implements Serializable {

    @NotBlank(message = "条码编码不能为空！")
    private String barcode;
    @NotBlank(message = "条码机构编码不能为空！")
    private String sendOrgCode;
    @NotBlank(message = "接收人编号不能为空！")
    private String receiveUserCode;
    @NotBlank(message = "接收人不能为空！")
    private String receiveUserName;
    @NotBlank(message = "条码的机构编码不能为空！")
    private String lwTestOrgCode;
    @NotBlank(message = "条码的机构名称不能为空！")
    private String lwTestOrgName;
    @NotBlank(message = "兰卫条码编码不能为空！")
    private String lwBarcode;

    private String lwMainBarcode;
    private String summaryHandoverCode;


}
