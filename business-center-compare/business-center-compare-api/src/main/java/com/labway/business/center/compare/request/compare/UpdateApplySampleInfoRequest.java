package com.labway.business.center.compare.request.compare;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@NoArgsConstructor
public class UpdateApplySampleInfoRequest implements Serializable {

    //申请单样本表id
    private String mainId;
    //外送到中台的条码号
    private String barcode;
    //送检机构编码（对应中台的客商编码）
    private String hspOrgCode;
    //送检机构名称（对应中台的客商名称）
    private String hspOrgName;
    //申请单类型 门诊/住院
    private String applyType;
    //门诊/住院 号
    private String patientVisitCard;
    //是否加急 0否1是
    private Integer urgent;
    //样本类型
    private String sampleType;
    //样本形状
    private String sampleProperty;
    //申请科室
    private String dept;
    //病区
    private String inpatientArea;
    //患者名称
    private String patientName;
    //性别 1男2女
    private Integer patientSex;
    //年龄 xx岁
    private Integer patientAge;
    //子年龄 xxx天|xxx周|xxx月|
    private Integer patientSubage;
    //子年龄单位
    private String patientSubageUnit;
    //生日
    private Date patientBirthday;
    //床号
    private String patientBed;
    //临床诊断
    private String clinicalDiagnosis;
    //身份证
    private String patientCard;
    //证件类型
    private String patientCardType;
    //住址
    private String patientAsddress;
    //手机号
    private String patientMobile;
    //送检医生
    private String sendDoctor;
    //申请时间（送检时间）
    private Date applyDate;
    //采样时间
    private Date samplingDate;
    //备注
    private String remark;
    // 签收机构编码
    private String signOrgCode;
    // 签收机构条码
    private String signBarcode;
    // 管型
    private String tubeType;
    // 医保卡号
    private String visitCardNo;
    // 样本个数
    private Integer sampleNum;

    /**
     * 操作人编码
     */
    private String limsUserCode;

    /**
     * 操作人名称
     */
    private String limsUserName;

}
