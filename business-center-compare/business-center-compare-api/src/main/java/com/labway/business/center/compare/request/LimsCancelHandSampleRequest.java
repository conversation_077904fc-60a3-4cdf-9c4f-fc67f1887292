package com.labway.business.center.compare.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LimsCancelHandSampleRequest implements Serializable {

    //业务单元编码
    @NotBlank(message = "机构编码不能为空！")
    private String orgCode;

    @NotEmpty(message = "样本的实验室签收条码不能为空！")
    private List<String> signBarcodes;

    // 操作人id
    private String optUserId;
    // 操作人名称
    private String optUserName;


}
