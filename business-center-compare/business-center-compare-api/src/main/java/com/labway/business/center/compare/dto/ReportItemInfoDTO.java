package com.labway.business.center.compare.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 2023/04/20 09:06
 * @description
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ReportItemInfoDTO implements Serializable {

    // 报告项目id
    private String itemReportId;
    //报告项目编码;唯一不重复
    private String itemReportCode;
    //报告项目名称;唯一不重复
    private String itemReportName;


}