package com.labway.business.center.monitor.controller;

import com.labway.business.center.monitor.job.BusinessMonitoringJob;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/test")
public class TestController {

    @Resource
    private BusinessMonitoringJob businessMonitoringJob;

    /**
     * 测试统计报告样本
     * @return
     */
    @PostMapping("/testReportMonitor")
    public String testReportMonitor() {
        businessMonitoringJob.sampleReportMonitoring();
        return "success";
    }


}
