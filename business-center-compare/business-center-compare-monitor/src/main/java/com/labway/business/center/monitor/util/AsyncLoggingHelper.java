package com.labway.business.center.monitor.util;

import com.alibaba.cloud.commons.lang.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.concurrent.Executors;
import java.util.concurrent.ExecutorService;
import java.util.logging.Level;
import java.util.logging.Logger;


public class AsyncLoggingHelper {

    private static final ExecutorService LOGGING_EXECUTOR = Executors.newSingleThreadExecutor();

    private static final Logger LOGGER = Logger.getLogger(AsyncLoggingHelper.class.getName());

    /**
     * 异步记录INFO级别日志
     *
     * @param message 日志消息
     */
    public static void logInfo(String ...message) {
        LOGGING_EXECUTOR.submit(() -> LOGGER.log(Level.INFO, formatLogMessage(message)));
    }

    /**
     * 异步记录WARNING级别日志
     *
     * @param message 日志消息
     */
    public static void logWarning(String... message) {
        LOGGING_EXECUTOR.submit(() -> LOGGER.log(Level.WARNING, formatLogMessage(message)));
    }

    /**
     * 异步记录ERROR级别日志
     *
     * @param message 日志消息
     */
    public static void logError(String... message) {
        LOGGING_EXECUTOR.submit(() -> LOGGER.log(Level.SEVERE, formatLogMessage(message)));
    }

    /**
     * 关闭日志线程池
     */
    public static void shutdown() {
        LOGGING_EXECUTOR.shutdown();
    }


    /**
     * 处理兼容日志打印格式
     */
    public static String formatLogMessage(String... message) {
        return String.format(message[0].replace("{}", "%s"), Arrays.copyOfRange(message, 1, message.length));
    }

}


