package com.labway.business.center.monitor.controller;

import com.alibaba.fastjson.JSONObject;
import com.labway.business.center.compare.dto.monitoralarm.MonitorParam;
import com.labway.business.center.core.util.NotifyUtil;
import com.labway.business.center.monitor.service.ServiceMonitorAndAlarmService;
import com.swak.frame.dto.Response;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;

/**
 * 服务监控和告警
 * <AUTHOR>
 * @since 2025/4/18 18:00
 */
@RestController
@RequestMapping("/monitor-alarm")
public class ServiceMonitorAndAlarmController {

    @Resource
    private ServiceMonitorAndAlarmService serviceMonitorAndAlarmService;
    @Resource
    private NotifyUtil notifyUtil;

    @PostMapping("/monitor")
    public Response<?> monitor(@RequestBody MonitorParam monitorParam) {
        Objects.requireNonNull(monitorParam.getServiceName(), "服务不能为空");
        serviceMonitorAndAlarmService.monitor(monitorParam);
        return Response.success();
    }

    @PostMapping("notify")
    public Response<?> notify(@RequestBody Map<String, String> param) {
        notifyUtil.notifyByRegion("report-platform", param.get("content"));
        return Response.success();
    }

    @PostMapping("alertmanager")
    public Object alertmanager(@RequestBody JSONObject alert) {
        Integer alertmanager = serviceMonitorAndAlarmService.alertmanager(alert);
        return Map.of("status", "success", "message", "告警已接收", "processed_alerts", alertmanager);
    }

}
