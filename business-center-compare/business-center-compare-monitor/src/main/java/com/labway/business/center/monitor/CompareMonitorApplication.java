package com.labway.business.center.monitor;

import com.labway.business.center.core.util.DingerSenderFactory;
import com.labway.business.center.core.util.NotifyUtil;
import com.ulisesbocchio.jasyptspringboot.annotation.EnableEncryptableProperties;
import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.Import;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;

/**
 * <AUTHOR>
 */
@Import({NotifyUtil.class, DingerSenderFactory.class})
@SpringBootApplication
@MapperScan( basePackages = {"com.labway.business.center.monitor.**.mapper.**"})
@ComponentScan(basePackages = {"com.labway.business.center.monitor.**"})
@EnableDubbo
@EnableWebMvc
@EnableEncryptableProperties
@EnableAsync
@EnableAspectJAutoProxy(exposeProxy = true)
public class CompareMonitorApplication {
	public static void main(String[] args) {
		SpringApplication application = new SpringApplication(CompareMonitorApplication.class);
		application.run(args);

	}
}
