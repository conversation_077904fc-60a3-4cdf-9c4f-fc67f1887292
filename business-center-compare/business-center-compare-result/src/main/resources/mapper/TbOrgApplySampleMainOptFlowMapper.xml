<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.business.center.result.persistence.mapper.TbOrgApplySampleMainOptFlowMapper">

    <resultMap type="com.labway.business.center.result.persistence.entity.TbOrgApplySampleMainOptFlow" id="TbOrgApplySampleMainOptFlowMap">
        <result property="optFlowId" column="opt_flow_id" jdbcType="VARCHAR"/>
        <result property="formId" column="form_id" jdbcType="VARCHAR"/>
        <result property="formCode" column="form_code" jdbcType="VARCHAR"/>
        <result property="barcode" column="barcode" jdbcType="VARCHAR"/>
        <result property="lwBarcode" column="lw_barcode" jdbcType="VARCHAR"/>
        <result property="flowStatus" column="flow_status" jdbcType="INTEGER"/>
        <result property="hspOrgCode" column="hsp_org_code" jdbcType="VARCHAR"/>
        <result property="hspOrgName" column="hsp_org_name" jdbcType="VARCHAR"/>
        <result property="patientName" column="patient_name" jdbcType="VARCHAR"/>
        <result property="patientCode" column="patient_code" jdbcType="VARCHAR"/>
        <result property="sex" column="sex" jdbcType="VARCHAR"/>
        <result property="age" column="age" jdbcType="VARCHAR"/>
        <result property="patientBed" column="patient_bed" jdbcType="VARCHAR"/>
        <result property="applyType" column="apply_type" jdbcType="VARCHAR"/>
        <result property="dept" column="dept" jdbcType="VARCHAR"/>
        <result property="sendDoctor" column="send_doctor" jdbcType="VARCHAR"/>
        <result property="urgent" column="urgent" jdbcType="VARCHAR"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="deleteFalg" column="delete_falg" jdbcType="INTEGER"/>
        <result property="optId" column="opt_id" jdbcType="VARCHAR"/>
        <result property="optName" column="opt_name" jdbcType="VARCHAR"/>
        <result property="optTime" column="opt_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="optFlowId" useGeneratedKeys="true">
        insert into tb_org_apply_sample_main_opt_flow(opt_flow_id,form_id, form_code, barcode, lw_barcode, flow_status, hsp_org_code, hsp_org_name, patient_name, patient_code, sex, age, patient_bed, apply_type, dept, send_doctor, urgent, remark, create_time, create_by, update_time, update_by, delete_falg, opt_id, opt_name, opt_time)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.optFlowId}, #{entity.formId}, #{entity.formCode}, #{entity.barcode}, #{entity.lwBarcode}, #{entity.flowStatus}, #{entity.hspOrgCode}, #{entity.hspOrgName}, #{entity.patientName}, #{entity.patientCode}, #{entity.sex}, #{entity.age}, #{entity.patientBed}, #{entity.applyType}, #{entity.dept}, #{entity.sendDoctor}, #{entity.urgent}, #{entity.remark}, #{entity.createTime}, #{entity.createBy}, #{entity.updateTime}, #{entity.updateBy}, #{entity.deleteFalg}, #{entity.optId}, #{entity.optName}, #{entity.optTime})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="optFlowId" useGeneratedKeys="true">
        insert into business-account.tb_org_apply_sample_main_opt_flow(form_id, form_code, barcode, lw_barcode, flow_status, hsp_org_code, hsp_org_name, patient_name, patient_code, sex, age, patient_bed, apply_type, dept, send_doctor, urgent, remark, create_time, create_by, update_time, update_by, delete_falg, opt_id, opt_name, opt_time)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.formId}, #{entity.formCode}, #{entity.barcode}, #{entity.lwBarcode}, #{entity.flowStatus}, #{entity.hspOrgCode}, #{entity.hspOrgName}, #{entity.patientName}, #{entity.patientCode}, #{entity.sex}, #{entity.age}, #{entity.patientBed}, #{entity.applyType}, #{entity.dept}, #{entity.sendDoctor}, #{entity.urgent}, #{entity.remark}, #{entity.createTime}, #{entity.createBy}, #{entity.updateTime}, #{entity.updateBy}, #{entity.deleteFalg}, #{entity.optId}, #{entity.optName}, #{entity.optTime})
        </foreach>
        on duplicate key update
         form_id = values(form_id) , form_code = values(form_code) , barcode = values(barcode) , lw_barcode = values(lw_barcode) , flow_status = values(flow_status) , hsp_org_code = values(hsp_org_code) , hsp_org_name = values(hsp_org_name) , patient_name = values(patient_name) , patient_code = values(patient_code) , sex = values(sex) , age = values(age) , patient_bed = values(patient_bed) , apply_type = values(apply_type) , dept = values(dept) , send_doctor = values(send_doctor) , urgent = values(urgent) , remark = values(remark) , create_time = values(create_time) , create_by = values(create_by) , update_time = values(update_time) , update_by = values(update_by) , delete_falg = values(delete_falg) , opt_id = values(opt_id) , opt_name = values(opt_name) , opt_time = values(opt_time)     </insert>

    <update id="updateFlowStatus">
        <foreach collection="list" item="item" separator=";">
            update tb_org_apply_sample_main
            set flow_status = #{item.flowStatus}, update_time = NOW()
            where
            barcode = #{item.barcode} and  hsp_org_code = #{item.hspOrgCode}) and form_code = #{item.formCode}
        </foreach>
    </update>


</mapper>

