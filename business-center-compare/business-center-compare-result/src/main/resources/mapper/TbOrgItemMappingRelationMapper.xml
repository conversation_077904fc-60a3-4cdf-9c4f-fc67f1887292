<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.business.center.result.persistence.mapper.TbOrgItemMappingRelationMapper">

    <resultMap type="com.labway.business.center.result.persistence.entity.TbOrgItemMappingRelation" id="TbOrgItemMappingRelationMap">
        <result property="mappingId" column="mapping_id" jdbcType="VARCHAR"/>
        <result property="itemTestCode" column="item_test_code" jdbcType="VARCHAR"/>
        <result property="itemTestName" column="item_test_name" jdbcType="VARCHAR"/>
        <result property="itemOutsideCode" column="item_outside_code" jdbcType="VARCHAR"/>
        <result property="itemOutsideName" column="item_outside_name" jdbcType="VARCHAR"/>
        <result property="customerCode" column="customer_code" jdbcType="VARCHAR"/>
        <result property="customerName" column="customer_name" jdbcType="VARCHAR"/>
        <result property="orgCode" column="org_code" jdbcType="VARCHAR"/>
        <result property="orgName" column="org_name" jdbcType="VARCHAR"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="deleteFlag" column="delete_flag" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="mappingId" useGeneratedKeys="true">
        insert into tb_org_item_mapping_relation(mapping_id,item_test_code, item_test_name, item_outside_code, item_outside_name, customer_code, customer_name, org_code, org_name, create_by, create_time, update_by, update_time, delete_flag)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.mappingId}, #{entity.itemTestCode}, #{entity.itemTestName}, #{entity.itemOutsideCode}, #{entity.itemOutsideName}, #{entity.customerCode}, #{entity.customerName}, #{entity.orgCode}, #{entity.orgName}, #{entity.createBy}, #{entity.createTime}, #{entity.updateBy}, #{entity.updateTime}, #{entity.deleteFlag})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="mappingId" useGeneratedKeys="true">
        insert into business-account.tb_org_item_mapping_relation(item_test_code, item_test_name, item_outside_code, item_outside_name, customer_code, customer_name, org_code, org_name, create_by, create_time, update_by, update_time, delete_flag)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.itemTestCode}, #{entity.itemTestName}, #{entity.itemOutsideCode}, #{entity.itemOutsideName}, #{entity.customerCode}, #{entity.customerName}, #{entity.orgCode}, #{entity.orgName}, #{entity.createBy}, #{entity.createTime}, #{entity.updateBy}, #{entity.updateTime}, #{entity.deleteFlag})
        </foreach>
        on duplicate key update
         item_test_code = values(item_test_code) , item_test_name = values(item_test_name) , item_outside_code = values(item_outside_code) , item_outside_name = values(item_outside_name) , customer_code = values(customer_code) , customer_name = values(customer_name) , org_code = values(org_code) , org_name = values(org_name) , create_by = values(create_by) , create_time = values(create_time) , update_by = values(update_by) , update_time = values(update_time) , delete_flag = values(delete_flag)     </insert>

    <!-- 根据外部项目编码和检验项目编码，查询对照关系 -->
    <select id="selectListByOutsideAndTestCodes" resultMap="TbOrgItemMappingRelationMap">
        select mapping_id, item_test_code, item_outside_code
        from tb_org_item_mapping_relation
        where (item_test_code, item_outside_code) in (
            <foreach collection="entities" item="entity" separator=",">
                (#{entity.itemTestCode}, #{entity.itemOutsideCode})
            </foreach>
        )
        and org_code = #{orgCode}
        and customer_code = #{customerCode}
        and delete_flag = 0
    </select>
</mapper>

