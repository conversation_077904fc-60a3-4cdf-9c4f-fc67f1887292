<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.business.center.result.persistence.mapper.TbPathologyApplyMainResultAddtionMapper">

    <resultMap type="com.labway.business.center.result.persistence.entity.TbPathologyApplyMainResultAddtion" id="TbPathologyApplyMainResultAddtionMap">
        <result property="addtionId" column="addtion_id" jdbcType="VARCHAR"/>
        <result property="resultId" column="result_id" jdbcType="VARCHAR"/>
        <result property="fBcBgxh" column="f_bc_bgxh" jdbcType="INTEGER"/>
        <result property="fBcBgys" column="f_bc_bgys" jdbcType="VARCHAR"/>
        <result property="fBcShys" column="f_bc_shys" jdbcType="VARCHAR"/>
        <result property="fBcBgrq" column="f_bc_bgrq" jdbcType="VARCHAR"/>
        <result property="fBczd" column="f_bczd" jdbcType="VARCHAR"/>
        <result property="fBcBgzt" column="f_bc_bgzt" jdbcType="VARCHAR"/>
        <result property="fBcFzys" column="f_bc_fzys" jdbcType="VARCHAR"/>
        <result property="fBcDyzt" column="f_bc_dyzt" jdbcType="VARCHAR"/>
        <result property="fBcQsbDyzt" column="f_bc_qsb_dyzt" jdbcType="VARCHAR"/>
        <result property="fBcBgwz" column="f_bc_bgwz" jdbcType="VARCHAR"/>
        <result property="fBcBgwzQrsj" column="f_bc_bgwz_qrsj" jdbcType="VARCHAR"/>
        <result property="fBcBgwzQrczy" column="f_bc_bgwz_qrczy" jdbcType="VARCHAR"/>
        <result property="fBcBz" column="f_bc_bz" jdbcType="VARCHAR"/>
        <result property="fBcSpare5" column="f_bc_spare5" jdbcType="VARCHAR"/>
        <result property="fBcJxsj" column="f_bc_jxsj" jdbcType="VARCHAR"/>
        <result property="fBcTsjc" column="f_bc_tsjc" jdbcType="VARCHAR"/>
        <result property="fBcBggs" column="f_bc_bggs" jdbcType="VARCHAR"/>
        <result property="fBcFbsj" column="f_bc_fbsj" jdbcType="VARCHAR"/>
        <result property="fBcFbys" column="f_bc_fbys" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="addtionId" useGeneratedKeys="true">
        insert into tb_pathology_apply_main_result_addtion(result_id, f_bc_bgxh, f_bc_bgys, f_bc_shys, f_bc_bgrq, f_bczd, f_bc_bgzt, f_bc_fzys, f_bc_dyzt, f_bc_qsb_dyzt, f_bc_bgwz, f_bc_bgwz_qrsj, f_bc_bgwz_qrczy, f_bc_bz, f_bc_spare5, f_bc_jxsj, f_bc_tsjc, f_bc_bggs, f_bc_fbsj, f_bc_fbys)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.resultId}, #{entity.fBcBgxh}, #{entity.fBcBgys}, #{entity.fBcShys}, #{entity.fBcBgrq}, #{entity.fBczd}, #{entity.fBcBgzt}, #{entity.fBcFzys}, #{entity.fBcDyzt}, #{entity.fBcQsbDyzt}, #{entity.fBcBgwz}, #{entity.fBcBgwzQrsj}, #{entity.fBcBgwzQrczy}, #{entity.fBcBz}, #{entity.fBcSpare5}, #{entity.fBcJxsj}, #{entity.fBcTsjc}, #{entity.fBcBggs}, #{entity.fBcFbsj}, #{entity.fBcFbys})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="addtionId" useGeneratedKeys="true">
        insert into business-account.tb_pathology_apply_main_result_addtion(result_id, f_bc_bgxh, f_bc_bgys, f_bc_shys, f_bc_bgrq, f_bczd, f_bc_bgzt, f_bc_fzys, f_bc_dyzt, f_bc_qsb_dyzt, f_bc_bgwz, f_bc_bgwz_qrsj, f_bc_bgwz_qrczy, f_bc_bz, f_bc_spare5, f_bc_jxsj, f_bc_tsjc, f_bc_bggs, f_bc_fbsj, f_bc_fbys)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.resultId}, #{entity.fBcBgxh}, #{entity.fBcBgys}, #{entity.fBcShys}, #{entity.fBcBgrq}, #{entity.fBczd}, #{entity.fBcBgzt}, #{entity.fBcFzys}, #{entity.fBcDyzt}, #{entity.fBcQsbDyzt}, #{entity.fBcBgwz}, #{entity.fBcBgwzQrsj}, #{entity.fBcBgwzQrczy}, #{entity.fBcBz}, #{entity.fBcSpare5}, #{entity.fBcJxsj}, #{entity.fBcTsjc}, #{entity.fBcBggs}, #{entity.fBcFbsj}, #{entity.fBcFbys})
        </foreach>
        on duplicate key update
         result_id = values(result_id) , f_bc_bgxh = values(f_bc_bgxh) , f_bc_bgys = values(f_bc_bgys) , f_bc_shys = values(f_bc_shys) , f_bc_bgrq = values(f_bc_bgrq) , f_bczd = values(f_bczd) , f_bc_bgzt = values(f_bc_bgzt) , f_bc_fzys = values(f_bc_fzys) , f_bc_dyzt = values(f_bc_dyzt) , f_bc_qsb_dyzt = values(f_bc_qsb_dyzt) , f_bc_bgwz = values(f_bc_bgwz) , f_bc_bgwz_qrsj = values(f_bc_bgwz_qrsj) , f_bc_bgwz_qrczy = values(f_bc_bgwz_qrczy) , f_bc_bz = values(f_bc_bz) , f_bc_spare5 = values(f_bc_spare5) , f_bc_jxsj = values(f_bc_jxsj) , f_bc_tsjc = values(f_bc_tsjc) , f_bc_bggs = values(f_bc_bggs) , f_bc_fbsj = values(f_bc_fbsj) , f_bc_fbys = values(f_bc_fbys)     </insert>

</mapper>

