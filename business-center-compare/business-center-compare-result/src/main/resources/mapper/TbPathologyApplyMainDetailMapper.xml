<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.business.center.result.persistence.mapper.TbPathologyApplyMainDetailMapper">

    <resultMap type="com.labway.business.center.result.persistence.entity.TbPathologyApplyMainDetail" id="TbPathologyApplyMainDetailMap">
        <result property="detailId" column="detail_id" jdbcType="VARCHAR"/>
        <result property="barcode" column="barcode" jdbcType="VARCHAR"/>
        <result property="outTestitemCode" column="out_testitem_code" jdbcType="VARCHAR"/>
        <result property="outTestitemName" column="out_testitem_name" jdbcType="VARCHAR"/>
        <result property="testitemCode" column="testitem_code" jdbcType="VARCHAR"/>
        <result property="hspOrgCode" column="hsp_org_code" jdbcType="VARCHAR"/>
        <result property="isFee" column="is_fee" jdbcType="VARCHAR"/>
        <result property="isForbidden" column="is_forbidden" jdbcType="VARCHAR"/>
        <result property="feeNum" column="fee_num" jdbcType="INTEGER"/>
        <result property="property1" column="property1" jdbcType="VARCHAR"/>
        <result property="property2" column="property2" jdbcType="VARCHAR"/>
        <result property="property3" column="property3" jdbcType="VARCHAR"/>
        <result property="property4" column="property4" jdbcType="VARCHAR"/>
        <result property="property5" column="property5" jdbcType="VARCHAR"/>
        <result property="feeType" column="fee_type" jdbcType="VARCHAR"/>
        <result property="showNo" column="show_no" jdbcType="INTEGER"/>
        <result property="testitemName" column="testitem_name" jdbcType="VARCHAR"/>
        <result property="lwHspOrgCode" column="lw_hsp_org_code" jdbcType="VARCHAR"/>
        <result property="lwBarcode" column="lw_barcode" jdbcType="VARCHAR"/>
        <result property="lwMainBatcode" column="lw_main_batcode" jdbcType="VARCHAR"/>
        <result property="lwOperDate" column="lw_oper_date" jdbcType="TIMESTAMP"/>
        <result property="lwTubeType" column="lw_tube_type" jdbcType="VARCHAR"/>
        <result property="lwSampleType" column="lw_sample_type" jdbcType="VARCHAR"/>
        <result property="lwCombBarCode" column="lw_comb_bar_code" jdbcType="VARCHAR"/>
        <result property="labPackageId" column="lab_package_id" jdbcType="VARCHAR"/>
        <result property="labPackageName" column="lab_package_name" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="detailId" useGeneratedKeys="true">
        insert into tb_pathology_apply_main_detail(detail_id,barcode, out_testitem_code, out_testitem_name, testitem_code, hsp_org_code, is_fee, is_forbidden, fee_num, property1, property2, property3, property4, property5, fee_type, show_no, testitem_name, lw_hsp_org_code, lw_barcode, lw_main_batcode, lw_oper_date, lw_tube_type, lw_sample_type, lw_comb_bar_code, lab_package_id, lab_package_name,main_id)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.detailId}, #{entity.barcode}, #{entity.outTestitemCode}, #{entity.outTestitemName}, #{entity.testitemCode}, #{entity.hspOrgCode}, #{entity.isFee}, #{entity.isForbidden}, #{entity.feeNum}, #{entity.property1}, #{entity.property2}, #{entity.property3}, #{entity.property4}, #{entity.property5}, #{entity.feeType}, #{entity.showNo}, #{entity.testitemName}, #{entity.lwHspOrgCode}, #{entity.lwBarcode}, #{entity.lwMainBatcode}, #{entity.lwOperDate}, #{entity.lwTubeType}, #{entity.lwSampleType}, #{entity.lwCombBarCode}, #{entity.labPackageId}, #{entity.labPackageName}, #{entity.mainId})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="detailId" useGeneratedKeys="true">
        insert into business-account.tb_pathology_apply_main_detail(barcode, out_testitem_code, out_testitem_name, testitem_code, hsp_org_code, is_fee, is_forbidden, fee_num, property1, property2, property3, property4, property5, fee_type, show_no, testitem_name, lw_hsp_org_code, lw_barcode, lw_main_batcode, lw_oper_date, lw_tube_type, lw_sample_type, lw_comb_bar_code, lab_package_id, lab_package_name)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.barcode}, #{entity.outTestitemCode}, #{entity.outTestitemName}, #{entity.testitemCode}, #{entity.hspOrgCode}, #{entity.isFee}, #{entity.isForbidden}, #{entity.feeNum}, #{entity.property1}, #{entity.property2}, #{entity.property3}, #{entity.property4}, #{entity.property5}, #{entity.feeType}, #{entity.showNo}, #{entity.testitemName}, #{entity.lwHspOrgCode}, #{entity.lwBarcode}, #{entity.lwMainBatcode}, #{entity.lwOperDate}, #{entity.lwTubeType}, #{entity.lwSampleType}, #{entity.lwCombBarCode}, #{entity.labPackageId}, #{entity.labPackageName})
        </foreach>
        on duplicate key update
         barcode = values(barcode) , out_testitem_code = values(out_testitem_code) , out_testitem_name = values(out_testitem_name) , testitem_code = values(testitem_code) , hsp_org_code = values(hsp_org_code) , is_fee = values(is_fee) , is_forbidden = values(is_forbidden) , fee_num = values(fee_num) , property1 = values(property1) , property2 = values(property2) , property3 = values(property3) , property4 = values(property4) , property5 = values(property5) , fee_type = values(fee_type) , show_no = values(show_no) , testitem_name = values(testitem_name) , lw_hsp_org_code = values(lw_hsp_org_code) , lw_barcode = values(lw_barcode) , lw_main_batcode = values(lw_main_batcode) , lw_oper_date = values(lw_oper_date) , lw_tube_type = values(lw_tube_type) , lw_sample_type = values(lw_sample_type) , lw_comb_bar_code = values(lw_comb_bar_code) , lab_package_id = values(lab_package_id) , lab_package_name = values(lab_package_name)     </insert>

</mapper>

