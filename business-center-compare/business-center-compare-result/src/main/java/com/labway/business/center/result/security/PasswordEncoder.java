 package com.labway.business.center.result.security;

 /**
  * 私钥加密解密
  * <AUTHOR>
  * @date 2023/03/28
  */
 public class PasswordEncoder {
     /**
      * 私钥对数据进行加密
      */
     private static final String PRIVATE_KEY_STRING = "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBANxMadpnZqz7Jo6u6FxbuOcAwe5BYR/Nxdd5Sodz4j69DWbjhtDAuzHYOA9ArPjq4UbmfFrV2pJTDYOAnKVBBQasgdYfnELQJ2bcy1kzSu59Ojw33jWXPhaY28C8rBv+gnmVsOsjWpkuNgIx9zuHUShPhONTFU4ojkP/zyFXHywHAgMBAAECgYBp274x2OYZ0UWh4qJ9XwXm2xJVbs1A/xxGgl4x4JmjFC1Qd5zcywAo34Fwst9rbVJ7Q14WOBUjow0nV7HuJuRGf/CHalk8tTuRzAcMEWIfafpMg0H8bMSWUCaK9+y3TM62a9DCjJlGS2MInX97sp3bCPnYXnjnoZ1IYAlVqHavYQJBAP5q3qiZio9Jo7u0p09anomo2OpyiUOMsLYsamKClXx9SifJmBiE0tnOkIpIGMgGVMgxLafsekHfm2YiCDMn2m0CQQDdqzaWg3WClEiUOL0SjJQjF7l9feKCUB30I59UvAy47HqvCkrm+JvLC3z3YWmM6X3sSFm+I3pdEPitTVMBNBfDAkEA3uA4VnLLk2LrcNB9kWLNZ8Lm49uaq/ZgB/c46zuCQSBxChHsj+4r2GrA7KLkMrZ5JzcNzAG5yGNe+0Xs8QOEsQJAJnQyJxAtDXb91EC3hAVBGYlfE0cISUTuvVCErmVk65uSeNUXQ1QzapjWl++dKOXEg33Vm6d57bUo/p9gW+AWkwJAFAGvM5c1c8DuC/arDD84+V5CP4qzt38yZ69L7nnyKXxbRjvBS+d5WyUgXG7WUH9GVBrxGEs2HU32Vmwur1s3ww==";
       
     private static final String PUBKEY="MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDcTGnaZ2as+yaOruhcW7jnAMHuQWEfzcXXeUqHc+I+vQ1m44bQwLsx2DgPQKz46uFG5nxa1dqSUw2DgJylQQUGrIHWH5xC0Cdm3MtZM0rufTo8N941lz4WmNvAvKwb/oJ5lbDrI1qZLjYCMfc7h1EoT4TjUxVOKI5D/88hVx8sBwIDAQAB";
     public static void main(String[] args) throws Exception {
          //密码明文，也就是数据库的密码
            String plainText = "Huawei@1234!";
            String pass = SecurityTools.encrypt(PRIVATE_KEY_STRING, plainText);
            System.out.println(pass);
            System.out.println(SecurityTools.decrypt(PUBKEY, pass));
     }
}
