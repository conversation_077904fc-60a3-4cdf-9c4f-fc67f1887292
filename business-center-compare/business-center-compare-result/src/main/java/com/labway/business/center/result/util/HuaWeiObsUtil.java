package com.labway.business.center.result.util;

import cn.hutool.core.util.IdUtil;
import com.obs.services.ObsClient;
import com.obs.services.model.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDate;

/**
 * 华为云OBS
 * <AUTHOR>
 */
@Slf4j
@Component
public class HuaWeiObsUtil {

    @Resource
    private ObsClient obsClient;

    @Value("${huawei.obs.bucketName}")
    private String bucketName;
    @Value("${huawei.labway.baseUrl}")
    private String baseUrl;


    /**
     * 上传文件，multipartFile就是你要的文件，
     * objectKey就是文件名，如果桶中有文件夹的话，如往test文件上传test.txt文件，那么objectKey就是test/test.txt
     *
     * @param multipartFile
     * @param objectKey
     * @return
     * @throws Exception
     */
    public String uploadFile(MultipartFile multipartFile, String objectKey) throws Exception {
        InputStream inputStream = multipartFile.getInputStream();


        PutObjectRequest putObjectRequest = new PutObjectRequest();
        putObjectRequest.setBucketName(bucketName);
        putObjectRequest.setObjectKey(objectKey);
        putObjectRequest.setInput(inputStream);
        putObjectRequest.setAutoClose(true);
        putObjectRequest.setAcl(AccessControlList.REST_CANNED_PUBLIC_READ);

        PutObjectResult putObjectResult = obsClient.putObject(putObjectRequest);
        inputStream.close();

        return replacePath(putObjectResult.getObjectKey());
    }

    /**
     * 上传文件，multipartFile就是你要的文件，
     * objectKey就是文件名，如果桶中有文件夹的话，如往test文件上传test.txt文件，那么objectKey就是test/test.txt
     *
     * @param file
     * @param objectKey
     * @return
     * @throws Exception
     */
    public String uploadFile(File file, String objectKey) throws Exception {
        InputStream inputStream = new FileInputStream(file);


        PutObjectRequest putObjectRequest = new PutObjectRequest();
        putObjectRequest.setBucketName(bucketName);
        putObjectRequest.setObjectKey(objectKey);
        putObjectRequest.setInput(inputStream);
        putObjectRequest.setAutoClose(true);
        putObjectRequest.setAcl(AccessControlList.REST_CANNED_PUBLIC_READ);

        PutObjectResult putObjectResult = obsClient.putObject(putObjectRequest);
        inputStream.close();

        return replacePath(putObjectResult.getObjectKey());
    }

    /**
     * 上传文件
     *
     * @param mediaType {@link MediaType}
     * @param expires   过期时间，天数。如果是 1 那么 1 天后对象会被清理掉，-1 表示永久。通常临时生成的文件会设置一个过期时间。
     */
    public String uploadStream(InputStream is, String mediaType,String objKey, int expires) {
        final ObjectMetadata metadata = new ObjectMetadata();
        metadata.setContentType(MediaType.parseMediaType(mediaType).toString());

        final PutObjectRequest request = new PutObjectRequest();
        // 跨域
//        request.addUserHeaders("access-control-allow-origin", "*");
        request.setBucketName(bucketName);
        request.setObjectKey(objKey);
        request.setInput(is);
        request.setAutoClose(true);
        // 单独配置此对象共公读取
        request.setAcl(AccessControlList.REST_CANNED_PUBLIC_READ);
        request.setMetadata(metadata);
        request.setExpires(expires);

        return path(obsClient.putObject(request).getObjectKey());
    }


    public void deleteFile(String objectKey) throws Exception {
        obsClient.deleteObject(bucketName, objectKey);
        obsClient.close();
    }

    /**
     * 根据文件地址获取名称下载File类型的文件
     *
     * @param fileUrl
     * @return
     */
    public File downloadFileByUrl(String fileUrl) throws IOException {

        // filename就是objectKey
        String fileName = getObjectKeyByUrl(fileUrl);
        ObsObject obsObject = obsClient.getObject(bucketName, fileName);

        // 文件流保存到本地
        InputStream inputStream = obsObject.getObjectContent();
        String savePath = System.getProperty("user.dir") + "/file/" + fileName;
        File toFile = new File(savePath);
        // 如果文件已存在，则删除它
        if (toFile.exists()) {
            toFile.delete();
        }
        FileUtils.copyInputStreamToFile(inputStream, toFile);

        return toFile;
    }

    private String replacePath(String key) {
        return baseUrl + key;
    }

    /**
     * 根据下载地址url获取文件名称
     *
     * @param url
     */
    private String getObjectKeyByUrl(String url) {

        String fileName = url.replace(baseUrl, "");
        return fileName;
    }

    // 地址替换
    public String path(String name) {
        return baseUrl + name;
    }


}
