package com.labway.business.center.result.persistence.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * (TbPathologyApplyMainResult)表实体类
 *
 * <AUTHOR>
 * @since 2023-07-13 09:11:45
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@SuppressWarnings("serial")
public class TbPathologyApplyMainResult extends Model<TbPathologyApplyMainResult> {
    //病理结果表 id
    @TableId(type = IdType.INPUT)
    private String resultId;
    //检验机构名称
    private String testOrgName;
    //检验机构id
    private String testOrgId;
    //申请机构名称
    private String appOrgName;
    //申请机构id
    private String appOrgId;
    //病理科
    private String fBlk;
    //病理号
    private String fBlh;
    //病人编号
    private String fBrbh;
    //申请序号
    private String fSqxh;
    //医嘱id
    private String fYzid;
    //医嘱项目
    private String fYzxm;
    //f_study_uid
    private String fStudyUid;
    //姓名
    private String fXm;
    //性别
    private String fXb;
    //年龄
    private String fNl;
    //岁数
    private Integer fAge;
    //行业
    private String fHy;
    //名族
    private String fMz;
    //职业
    private String fZy;
    //身份证号
    private String fSfzh;
    //联系信息
    private String fLxxx;
    //病人类别
    private String fBrlb;
    //费别
    private String fFb;
    //住院号
    private String fZyh;
    //门诊号
    private String fMzh;
    //病区
    private String fBq;
    //送检科室
    private String fSjks;
    //床号    
    private String fCh;
    //送检单位
    private String fSjdw;
    //送检医生
    private String fSjys;
    //收到日期
    private String fSdrq;
    //接收员  
    private String fJsy;
    //标本类型
    private String fBblx;
    //标本取块
    private String fBbqk;
    //拒收原因
    private String fJsyy;
    //收费    
    private Integer fSf;
    //标本名称
    private String fBbmc;
    //临床诊断
    private String fLczd;
    //临床资料
    private String fLczl;
    //取材医生
    private String fQcys;
    //取材日期
    private String fQcrq;
    //记录员  
    private String fJly;
    //待包埋数
    private Integer fLkzs;
    //材块总数
    private Integer fCkzs;
    //附言    
    private String fFy;
    //镜像所见
    private String fJxsj;
    //病理诊断
    private String fBlzd;
    //特殊检查                                                                              
    private String fTsjc;
    //报告医生                                                                              
    private String fBgys;
    //审核医生                                                                              
    private String fShys;
    //报告日期                                                                              
    private String fBgrq;
    //初诊意见                                                                              
    private String fCzyj;
    //修改意见                                                                              
    private String fXgyj;
    //诊断关键词                                                                            
    private String fZdgjc;
    //阴阳性                                                                                
    private String fYyx;
    //未发报告原因                                                                          
    private String fWfbgyy;
    //备注                                                                                  
    private String fBz;
    //冰冻是否符合                                                                          
    private String fBdSffh;
    //报告状态                                                                              
    private String fBgzt;
    //是否采图                                                                              
    private String fSfct;
    //是否打印                                                                              
    private String fSfdy;
    //报告格式                                                                              
    private String fBggs;
    //归档状态                                                                              
    private String fGdzt;
    //科内会诊                                                                              
    private Integer fKnhz;
    //专家意见                                                                              
    private String fZjyj;
    //外院意见                                                                              
    private String fWyyj;
    //随访状态                                                                              
    private Integer fSfzt;
    //随访结果                                                                              
    private String fSfjg;
    //疾病编码（中文）                                                                      
    private String fJbbmCn;
    //疾病编码（英文）                                                                      
    private String fJbbmEng;
    //疾病名称                                                                              
    private String fJbmc;
    //原病理号                                                                              
    private String fYblh;
    //送检材料                                                                              
    private String fSjcl;
    //原病理诊断                                                                            
    private String fYblzd;
    //报告发送方式                                                                          
    private String fBgfsfs;
    //收藏医生                                                                              
    private String fScys;
    //是否符合                                                                              
    private String fSffh;
    //复片状态                                                                              
    private String fSpare1;
    //复片医生                                                                              
    private String fSpare2;
    //评价医生                                                                              
    private String fSpare3;
    //复片结果                                                                              
    private String fSpare4;
    //审核时间                                                                              
    private String fSpare5;
    //复片申请时间                                                                          
    private String fSpare6;
    //报告到期时间                                                                          
    private String fSpare7;
    //F_SPARE8                                                                              
    private String fSpare8;
    //F_SPARE9                                                                              
    private String fSpare9;
    //结构化报告中的备注                                                                    
    private String fSpare10;
    //备用1                                                                                 
    private String fBy1;
    //备用2                                                                                 
    private String fBy2;
    //图像目录                                                                              
    private String fTxml;
    //制片状态                                                                              
    private String fZpzt;
    //末次月经                                                                              
    private String fMcyj;
    //是否绝经                                                                              
    private String fSfjj;
    //结构化报告ID                                                                          
    private Integer fTbsid;
    //结构化报告名称                                                                        
    private String fTbsmc;
    //签收本打印状态                                                                        
    private String fQsbDyzt;
    //报告位置                                                                              
    private String fBgwz;
    //报告位置的确认时间                                                                    
    private String fBgwzQrsj;
    //报告位置的确认操作员                                                                  
    private String fBgwzQrczy;
    //标本位置                                                                              
    private String fBbwz;
    //蜡块位置                                                                              
    private String fLkwz;
    //切片位置                                                                              
    private String fQpwz;
    //归档操作员                                                                            
    private String fGdczy;
    //归档时间                                                                              
    private String fGdsj;
    //归档备注                                                                              
    private String fGdbz;
    //报告录入员                                                                            
    private String fBglry;
    //复诊医生                                                                              
    private String fFzys;
    //会诊符合                                                                              
    private String fYl1;
    //报告核对状态                                                                          
    private String fYl2;
    //报告核对员                                                                            
    private String fYl3;
    //拼音码                                                                                
    private String fYl4;
    //责任医生                                                                              
    private String fYl5;
    //危急值发送记录
    private String fYl6;
    //自助打印记录
    private String fYl7;
    //用于记录申请复片的申请医生
    private String fYl8;
    //蜡块块数
    private String fYl9;
    //初步诊断
    private String fYl10;
    //ICD10编码
    private String fIcd10Bm1;
    //ICD10名称
    private String fIcd10Mc1;
    //特检医嘱申请医生
    private String fBl1;
    //科内会诊的申请医生姓名
    private String fBl2;
    //随访的申请医生姓名
    private String fBl3;
    //F_BL4
    private String fBl4;
    //F_BL5 
    private String fBl5;
    //F_BL6
    private String fBl6;
    //F_BL7
    private String fBl7;
    //F_BL8
    private String fBl8;
    //F_BL9
    private String fBl9;
    //F_BL10
    private String fBl10;
    //F_ICD10_BM2
    private String fIcd10Bm2;
    //F_ICD10_MC2
    private String fIcd10Mc2;
    //发布时间
    private String fFbsj;
    //发布医生
    private String fFbys;
    //肉眼所见
    private String fRysj;

    private Date createDate;
    //下载状态（默认为1，主要针对光华的数据使用当前字段，如果数据为光华数据，则该字段改为0）
    private String downloadStatus;
    //光华医院新接口下载状态
    private String ghdownloadStatus;

    private String uploadFlag;
    private Date uploadDate;

//    @TableLogic(value = "0",delval = "1")
    private Integer deleteFlag;

    // 报告单唯一编码
    private String resultUniqueCode;

    // 创建时间
    private Date createTime;
    // 更新时间
    private Date updateTime;


    public String getResultId() {
        return resultId;
    }

    public void setResultId(String resultId) {
        this.resultId = resultId;
    }

    public String getTestOrgName() {
        return testOrgName;
    }

    public void setTestOrgName(String testOrgName) {
        this.testOrgName = testOrgName;
    }

    public String getTestOrgId() {
        return testOrgId;
    }

    public void setTestOrgId(String testOrgId) {
        this.testOrgId = testOrgId;
    }

    public String getAppOrgName() {
        return appOrgName;
    }

    public void setAppOrgName(String appOrgName) {
        this.appOrgName = appOrgName;
    }

    public String getAppOrgId() {
        return appOrgId;
    }

    public void setAppOrgId(String appOrgId) {
        this.appOrgId = appOrgId;
    }

    public String getfBlk() {
        return fBlk;
    }

    public void setfBlk(String fBlk) {
        this.fBlk = fBlk;
    }

    public String getfBlh() {
        return fBlh;
    }

    public void setfBlh(String fBlh) {
        this.fBlh = fBlh;
    }

    public String getfBrbh() {
        return fBrbh;
    }

    public void setfBrbh(String fBrbh) {
        this.fBrbh = fBrbh;
    }

    public String getfSqxh() {
        return fSqxh;
    }

    public void setfSqxh(String fSqxh) {
        this.fSqxh = fSqxh;
    }

    public String getfYzid() {
        return fYzid;
    }

    public void setfYzid(String fYzid) {
        this.fYzid = fYzid;
    }

    public String getfYzxm() {
        return fYzxm;
    }

    public void setfYzxm(String fYzxm) {
        this.fYzxm = fYzxm;
    }

    public String getfStudyUid() {
        return fStudyUid;
    }

    public void setfStudyUid(String fStudyUid) {
        this.fStudyUid = fStudyUid;
    }

    public String getfXm() {
        return fXm;
    }

    public void setfXm(String fXm) {
        this.fXm = fXm;
    }

    public String getfXb() {
        return fXb;
    }

    public void setfXb(String fXb) {
        this.fXb = fXb;
    }

    public String getfNl() {
        return fNl;
    }

    public void setfNl(String fNl) {
        this.fNl = fNl;
    }

    public Integer getfAge() {
        return fAge;
    }

    public void setfAge(Integer fAge) {
        this.fAge = fAge;
    }

    public String getfHy() {
        return fHy;
    }

    public void setfHy(String fHy) {
        this.fHy = fHy;
    }

    public String getfMz() {
        return fMz;
    }

    public void setfMz(String fMz) {
        this.fMz = fMz;
    }

    public String getfZy() {
        return fZy;
    }

    public void setfZy(String fZy) {
        this.fZy = fZy;
    }

    public String getfSfzh() {
        return fSfzh;
    }

    public void setfSfzh(String fSfzh) {
        this.fSfzh = fSfzh;
    }

    public String getfLxxx() {
        return fLxxx;
    }

    public void setfLxxx(String fLxxx) {
        this.fLxxx = fLxxx;
    }

    public String getfBrlb() {
        return fBrlb;
    }

    public void setfBrlb(String fBrlb) {
        this.fBrlb = fBrlb;
    }

    public String getfFb() {
        return fFb;
    }

    public void setfFb(String fFb) {
        this.fFb = fFb;
    }

    public String getfZyh() {
        return fZyh;
    }

    public void setfZyh(String fZyh) {
        this.fZyh = fZyh;
    }

    public String getfMzh() {
        return fMzh;
    }

    public void setfMzh(String fMzh) {
        this.fMzh = fMzh;
    }

    public String getfBq() {
        return fBq;
    }

    public void setfBq(String fBq) {
        this.fBq = fBq;
    }

    public String getfSjks() {
        return fSjks;
    }

    public void setfSjks(String fSjks) {
        this.fSjks = fSjks;
    }

    public String getfCh() {
        return fCh;
    }

    public void setfCh(String fCh) {
        this.fCh = fCh;
    }

    public String getfSjdw() {
        return fSjdw;
    }

    public void setfSjdw(String fSjdw) {
        this.fSjdw = fSjdw;
    }

    public String getfSjys() {
        return fSjys;
    }

    public void setfSjys(String fSjys) {
        this.fSjys = fSjys;
    }

    public String getfSdrq() {
        return fSdrq;
    }

    public void setfSdrq(String fSdrq) {
        this.fSdrq = fSdrq;
    }

    public String getfJsy() {
        return fJsy;
    }

    public void setfJsy(String fJsy) {
        this.fJsy = fJsy;
    }

    public String getfBblx() {
        return fBblx;
    }

    public void setfBblx(String fBblx) {
        this.fBblx = fBblx;
    }

    public String getfBbqk() {
        return fBbqk;
    }

    public void setfBbqk(String fBbqk) {
        this.fBbqk = fBbqk;
    }

    public String getfJsyy() {
        return fJsyy;
    }

    public void setfJsyy(String fJsyy) {
        this.fJsyy = fJsyy;
    }

    public Integer getfSf() {
        return fSf;
    }

    public void setfSf(Integer fSf) {
        this.fSf = fSf;
    }

    public String getfBbmc() {
        return fBbmc;
    }

    public void setfBbmc(String fBbmc) {
        this.fBbmc = fBbmc;
    }

    public String getfLczd() {
        return fLczd;
    }

    public void setfLczd(String fLczd) {
        this.fLczd = fLczd;
    }

    public String getfLczl() {
        return fLczl;
    }

    public void setfLczl(String fLczl) {
        this.fLczl = fLczl;
    }

    public String getfQcys() {
        return fQcys;
    }

    public void setfQcys(String fQcys) {
        this.fQcys = fQcys;
    }

    public String getfQcrq() {
        return fQcrq;
    }

    public void setfQcrq(String fQcrq) {
        this.fQcrq = fQcrq;
    }

    public String getfJly() {
        return fJly;
    }

    public void setfJly(String fJly) {
        this.fJly = fJly;
    }

    public Integer getfLkzs() {
        return fLkzs;
    }

    public void setfLkzs(Integer fLkzs) {
        this.fLkzs = fLkzs;
    }

    public Integer getfCkzs() {
        return fCkzs;
    }

    public void setfCkzs(Integer fCkzs) {
        this.fCkzs = fCkzs;
    }

    public String getfFy() {
        return fFy;
    }

    public void setfFy(String fFy) {
        this.fFy = fFy;
    }

    public String getfJxsj() {
        return fJxsj;
    }

    public void setfJxsj(String fJxsj) {
        this.fJxsj = fJxsj;
    }

    public String getfBlzd() {
        return fBlzd;
    }

    public void setfBlzd(String fBlzd) {
        this.fBlzd = fBlzd;
    }

    public String getfTsjc() {
        return fTsjc;
    }

    public void setfTsjc(String fTsjc) {
        this.fTsjc = fTsjc;
    }

    public String getfBgys() {
        return fBgys;
    }

    public void setfBgys(String fBgys) {
        this.fBgys = fBgys;
    }

    public String getfShys() {
        return fShys;
    }

    public void setfShys(String fShys) {
        this.fShys = fShys;
    }

    public String getfBgrq() {
        return fBgrq;
    }

    public void setfBgrq(String fBgrq) {
        this.fBgrq = fBgrq;
    }

    public String getfCzyj() {
        return fCzyj;
    }

    public void setfCzyj(String fCzyj) {
        this.fCzyj = fCzyj;
    }

    public String getfXgyj() {
        return fXgyj;
    }

    public void setfXgyj(String fXgyj) {
        this.fXgyj = fXgyj;
    }

    public String getfZdgjc() {
        return fZdgjc;
    }

    public void setfZdgjc(String fZdgjc) {
        this.fZdgjc = fZdgjc;
    }

    public String getfYyx() {
        return fYyx;
    }

    public void setfYyx(String fYyx) {
        this.fYyx = fYyx;
    }

    public String getfWfbgyy() {
        return fWfbgyy;
    }

    public void setfWfbgyy(String fWfbgyy) {
        this.fWfbgyy = fWfbgyy;
    }

    public String getfBz() {
        return fBz;
    }

    public void setfBz(String fBz) {
        this.fBz = fBz;
    }

    public String getfBdSffh() {
        return fBdSffh;
    }

    public void setfBdSffh(String fBdSffh) {
        this.fBdSffh = fBdSffh;
    }

    public String getfBgzt() {
        return fBgzt;
    }

    public void setfBgzt(String fBgzt) {
        this.fBgzt = fBgzt;
    }

    public String getfSfct() {
        return fSfct;
    }

    public void setfSfct(String fSfct) {
        this.fSfct = fSfct;
    }

    public String getfSfdy() {
        return fSfdy;
    }

    public void setfSfdy(String fSfdy) {
        this.fSfdy = fSfdy;
    }

    public String getfBggs() {
        return fBggs;
    }

    public void setfBggs(String fBggs) {
        this.fBggs = fBggs;
    }

    public String getfGdzt() {
        return fGdzt;
    }

    public void setfGdzt(String fGdzt) {
        this.fGdzt = fGdzt;
    }

    public Integer getfKnhz() {
        return fKnhz;
    }

    public void setfKnhz(Integer fKnhz) {
        this.fKnhz = fKnhz;
    }

    public String getfZjyj() {
        return fZjyj;
    }

    public void setfZjyj(String fZjyj) {
        this.fZjyj = fZjyj;
    }

    public String getfWyyj() {
        return fWyyj;
    }

    public void setfWyyj(String fWyyj) {
        this.fWyyj = fWyyj;
    }

    public Integer getfSfzt() {
        return fSfzt;
    }

    public void setfSfzt(Integer fSfzt) {
        this.fSfzt = fSfzt;
    }

    public String getfSfjg() {
        return fSfjg;
    }

    public void setfSfjg(String fSfjg) {
        this.fSfjg = fSfjg;
    }

    public String getfJbbmCn() {
        return fJbbmCn;
    }

    public void setfJbbmCn(String fJbbmCn) {
        this.fJbbmCn = fJbbmCn;
    }

    public String getfJbbmEng() {
        return fJbbmEng;
    }

    public void setfJbbmEng(String fJbbmEng) {
        this.fJbbmEng = fJbbmEng;
    }

    public String getfJbmc() {
        return fJbmc;
    }

    public void setfJbmc(String fJbmc) {
        this.fJbmc = fJbmc;
    }

    public String getfYblh() {
        return fYblh;
    }

    public void setfYblh(String fYblh) {
        this.fYblh = fYblh;
    }

    public String getfSjcl() {
        return fSjcl;
    }

    public void setfSjcl(String fSjcl) {
        this.fSjcl = fSjcl;
    }

    public String getfYblzd() {
        return fYblzd;
    }

    public void setfYblzd(String fYblzd) {
        this.fYblzd = fYblzd;
    }

    public String getfBgfsfs() {
        return fBgfsfs;
    }

    public void setfBgfsfs(String fBgfsfs) {
        this.fBgfsfs = fBgfsfs;
    }

    public String getfScys() {
        return fScys;
    }

    public void setfScys(String fScys) {
        this.fScys = fScys;
    }

    public String getfSffh() {
        return fSffh;
    }

    public void setfSffh(String fSffh) {
        this.fSffh = fSffh;
    }

    public String getfSpare1() {
        return fSpare1;
    }

    public void setfSpare1(String fSpare1) {
        this.fSpare1 = fSpare1;
    }

    public String getfSpare2() {
        return fSpare2;
    }

    public void setfSpare2(String fSpare2) {
        this.fSpare2 = fSpare2;
    }

    public String getfSpare3() {
        return fSpare3;
    }

    public void setfSpare3(String fSpare3) {
        this.fSpare3 = fSpare3;
    }

    public String getfSpare4() {
        return fSpare4;
    }

    public void setfSpare4(String fSpare4) {
        this.fSpare4 = fSpare4;
    }

    public String getfSpare5() {
        return fSpare5;
    }

    public void setfSpare5(String fSpare5) {
        this.fSpare5 = fSpare5;
    }

    public String getfSpare6() {
        return fSpare6;
    }

    public void setfSpare6(String fSpare6) {
        this.fSpare6 = fSpare6;
    }

    public String getfSpare7() {
        return fSpare7;
    }

    public void setfSpare7(String fSpare7) {
        this.fSpare7 = fSpare7;
    }

    public String getfSpare8() {
        return fSpare8;
    }

    public void setfSpare8(String fSpare8) {
        this.fSpare8 = fSpare8;
    }

    public String getfSpare9() {
        return fSpare9;
    }

    public void setfSpare9(String fSpare9) {
        this.fSpare9 = fSpare9;
    }

    public String getfSpare10() {
        return fSpare10;
    }

    public void setfSpare10(String fSpare10) {
        this.fSpare10 = fSpare10;
    }

    public String getfBy1() {
        return fBy1;
    }

    public void setfBy1(String fBy1) {
        this.fBy1 = fBy1;
    }

    public String getfBy2() {
        return fBy2;
    }

    public void setfBy2(String fBy2) {
        this.fBy2 = fBy2;
    }

    public String getfTxml() {
        return fTxml;
    }

    public void setfTxml(String fTxml) {
        this.fTxml = fTxml;
    }

    public String getfZpzt() {
        return fZpzt;
    }

    public void setfZpzt(String fZpzt) {
        this.fZpzt = fZpzt;
    }

    public String getfMcyj() {
        return fMcyj;
    }

    public void setfMcyj(String fMcyj) {
        this.fMcyj = fMcyj;
    }

    public String getfSfjj() {
        return fSfjj;
    }

    public void setfSfjj(String fSfjj) {
        this.fSfjj = fSfjj;
    }

    public Integer getfTbsid() {
        return fTbsid;
    }

    public void setfTbsid(Integer fTbsid) {
        this.fTbsid = fTbsid;
    }

    public String getfTbsmc() {
        return fTbsmc;
    }

    public void setfTbsmc(String fTbsmc) {
        this.fTbsmc = fTbsmc;
    }

    public String getfQsbDyzt() {
        return fQsbDyzt;
    }

    public void setfQsbDyzt(String fQsbDyzt) {
        this.fQsbDyzt = fQsbDyzt;
    }

    public String getfBgwz() {
        return fBgwz;
    }

    public void setfBgwz(String fBgwz) {
        this.fBgwz = fBgwz;
    }

    public String getfBgwzQrsj() {
        return fBgwzQrsj;
    }

    public void setfBgwzQrsj(String fBgwzQrsj) {
        this.fBgwzQrsj = fBgwzQrsj;
    }

    public String getfBgwzQrczy() {
        return fBgwzQrczy;
    }

    public void setfBgwzQrczy(String fBgwzQrczy) {
        this.fBgwzQrczy = fBgwzQrczy;
    }

    public String getfBbwz() {
        return fBbwz;
    }

    public void setfBbwz(String fBbwz) {
        this.fBbwz = fBbwz;
    }

    public String getfLkwz() {
        return fLkwz;
    }

    public void setfLkwz(String fLkwz) {
        this.fLkwz = fLkwz;
    }

    public String getfQpwz() {
        return fQpwz;
    }

    public void setfQpwz(String fQpwz) {
        this.fQpwz = fQpwz;
    }

    public String getfGdczy() {
        return fGdczy;
    }

    public void setfGdczy(String fGdczy) {
        this.fGdczy = fGdczy;
    }

    public String getfGdsj() {
        return fGdsj;
    }

    public void setfGdsj(String fGdsj) {
        this.fGdsj = fGdsj;
    }

    public String getfGdbz() {
        return fGdbz;
    }

    public void setfGdbz(String fGdbz) {
        this.fGdbz = fGdbz;
    }

    public String getfBglry() {
        return fBglry;
    }

    public void setfBglry(String fBglry) {
        this.fBglry = fBglry;
    }

    public String getfFzys() {
        return fFzys;
    }

    public void setfFzys(String fFzys) {
        this.fFzys = fFzys;
    }

    public String getfYl1() {
        return fYl1;
    }

    public void setfYl1(String fYl1) {
        this.fYl1 = fYl1;
    }

    public String getfYl2() {
        return fYl2;
    }

    public void setfYl2(String fYl2) {
        this.fYl2 = fYl2;
    }

    public String getfYl3() {
        return fYl3;
    }

    public void setfYl3(String fYl3) {
        this.fYl3 = fYl3;
    }

    public String getfYl4() {
        return fYl4;
    }

    public void setfYl4(String fYl4) {
        this.fYl4 = fYl4;
    }

    public String getfYl5() {
        return fYl5;
    }

    public void setfYl5(String fYl5) {
        this.fYl5 = fYl5;
    }

    public String getfYl6() {
        return fYl6;
    }

    public void setfYl6(String fYl6) {
        this.fYl6 = fYl6;
    }

    public String getfYl7() {
        return fYl7;
    }

    public void setfYl7(String fYl7) {
        this.fYl7 = fYl7;
    }

    public String getfYl8() {
        return fYl8;
    }

    public void setfYl8(String fYl8) {
        this.fYl8 = fYl8;
    }

    public String getfYl9() {
        return fYl9;
    }

    public void setfYl9(String fYl9) {
        this.fYl9 = fYl9;
    }

    public String getfYl10() {
        return fYl10;
    }

    public void setfYl10(String fYl10) {
        this.fYl10 = fYl10;
    }

    public String getfIcd10Bm1() {
        return fIcd10Bm1;
    }

    public void setfIcd10Bm1(String fIcd10Bm1) {
        this.fIcd10Bm1 = fIcd10Bm1;
    }

    public String getfIcd10Mc1() {
        return fIcd10Mc1;
    }

    public void setfIcd10Mc1(String fIcd10Mc1) {
        this.fIcd10Mc1 = fIcd10Mc1;
    }

    public String getfBl1() {
        return fBl1;
    }

    public void setfBl1(String fBl1) {
        this.fBl1 = fBl1;
    }

    public String getfBl2() {
        return fBl2;
    }

    public void setfBl2(String fBl2) {
        this.fBl2 = fBl2;
    }

    public String getfBl3() {
        return fBl3;
    }

    public void setfBl3(String fBl3) {
        this.fBl3 = fBl3;
    }

    public String getfBl4() {
        return fBl4;
    }

    public void setfBl4(String fBl4) {
        this.fBl4 = fBl4;
    }

    public String getfBl5() {
        return fBl5;
    }

    public void setfBl5(String fBl5) {
        this.fBl5 = fBl5;
    }

    public String getfBl6() {
        return fBl6;
    }

    public void setfBl6(String fBl6) {
        this.fBl6 = fBl6;
    }

    public String getfBl7() {
        return fBl7;
    }

    public void setfBl7(String fBl7) {
        this.fBl7 = fBl7;
    }

    public String getfBl8() {
        return fBl8;
    }

    public void setfBl8(String fBl8) {
        this.fBl8 = fBl8;
    }

    public String getfBl9() {
        return fBl9;
    }

    public void setfBl9(String fBl9) {
        this.fBl9 = fBl9;
    }

    public String getfBl10() {
        return fBl10;
    }

    public void setfBl10(String fBl10) {
        this.fBl10 = fBl10;
    }

    public String getfIcd10Bm2() {
        return fIcd10Bm2;
    }

    public void setfIcd10Bm2(String fIcd10Bm2) {
        this.fIcd10Bm2 = fIcd10Bm2;
    }

    public String getfIcd10Mc2() {
        return fIcd10Mc2;
    }

    public void setfIcd10Mc2(String fIcd10Mc2) {
        this.fIcd10Mc2 = fIcd10Mc2;
    }

    public String getfFbsj() {
        return fFbsj;
    }

    public void setfFbsj(String fFbsj) {
        this.fFbsj = fFbsj;
    }

    public String getfFbys() {
        return fFbys;
    }

    public void setfFbys(String fFbys) {
        this.fFbys = fFbys;
    }

    public String getfRysj() {
        return fRysj;
    }

    public void setfRysj(String fRysj) {
        this.fRysj = fRysj;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public String getDownloadStatus() {
        return downloadStatus;
    }

    public void setDownloadStatus(String downloadStatus) {
        this.downloadStatus = downloadStatus;
    }

    public String getGhdownloadStatus() {
        return ghdownloadStatus;
    }

    public void setGhdownloadStatus(String ghdownloadStatus) {
        this.ghdownloadStatus = ghdownloadStatus;
    }

    public String getUploadFlag() {
        return uploadFlag;
    }

    public void setUploadFlag(String uploadFlag) {
        this.uploadFlag = uploadFlag;
    }

    public Date getUploadDate() {
        return uploadDate;
    }

    public void setUploadDate(Date uploadDate) {
        this.uploadDate = uploadDate;
    }

    public Integer getDeleteFlag() {
        return deleteFlag;
    }

    public void setDeleteFlag(Integer deleteFlag) {
        this.deleteFlag = deleteFlag;
    }

    public String getResultUniqueCode() {
        return resultUniqueCode;
    }

    public void setResultUniqueCode(String resultUniqueCode) {
        this.resultUniqueCode = resultUniqueCode;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}

