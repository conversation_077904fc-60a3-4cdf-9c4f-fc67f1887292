package com.labway.business.center.result.persistence.params;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 2023/04/21 11:25
 * @description
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class QueryOrgItemTestParam implements Serializable {

    // 业务单元（机构）编码
    private String orgCode;

    // 样本类型
    private String sampleTypeCode;

    // 检验项目名称
    private String itemTestName;


}