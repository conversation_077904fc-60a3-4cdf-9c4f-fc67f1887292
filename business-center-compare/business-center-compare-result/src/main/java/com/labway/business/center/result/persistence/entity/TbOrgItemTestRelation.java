package com.labway.business.center.result.persistence.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 机构下发的检验项目信息表(TbOrgItemTest)表实体类
 *
 * <AUTHOR>
 * @since 2023-04-21 09:41:38
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuppressWarnings("serial")
public class TbOrgItemTestRelation extends Model<TbOrgItemTestRelation> {
    //主键标识
    @TableId(type = IdType.AUTO)
    private String itemId;
    //检验项目id
    private String itemTestId;
    //检验项目编码;唯一不重复
    private String itemTestCode;
    //检验项目名称;唯一不重复
    private String itemTestName;
    //检验项目英文名称
    private String englishName;
    //样本类型编码
    private String sampleTypeCode;
    //样本类型名称
    private String sampleTypeName;
    //样本存放说明
    private String saveDescription;
    //检验项目启用状态 0未启用 1启用
    private Integer status;
    //创建时间
    private Date createTime;
    //修改时间
    private Date updateTime;
    //机构（业务单元）编码
    private String orgCode;
    //机构（业务单元）名称
    private String orgName;
    //删除标识（逻辑删）0未删除 1删除
    @TableLogic(value = "0",delval = "1")
    private Integer deleteFlag;
    //创建人id
    private String createBy;
    //更新人id
    private String updateBy;

    // 检验方法编码
    private String testMethod;
    // 检验方法名称呢
    private String testMethodName;
    // 省份code
    private String provinceCode;
    // 省份名称
    private String provinceName;

}

