package com.labway.business.center.result.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.labway.business.center.core.enums.DeleteFlagEnum;
import com.labway.business.center.result.persistence.entity.TbOrgApplySampleMainItem;
import com.labway.business.center.result.persistence.mapper.TbOrgApplySampleMainItemMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2023/04/24 14:46
 * @description
 **/
@Slf4j
@Repository
@RefreshScope
public class TbOrgApplySampleMainItemRepository {

    @Resource
    private TbOrgApplySampleMainItemMapper tbOrgApplySampleMainItemMapper;


    /**
     * 查询申请单的送检项目信息
     * @param mainIds
     * @return
     */
    public List<TbOrgApplySampleMainItem> queryItemInfoByMainIds(List<String> mainIds) {
        if (CollectionUtils.isEmpty(mainIds)) {
            return Collections.EMPTY_LIST;
        }

        return tbOrgApplySampleMainItemMapper.selectList(Wrappers.lambdaQuery(TbOrgApplySampleMainItem.class)
                .in(TbOrgApplySampleMainItem::getMainId,mainIds)
                .eq(TbOrgApplySampleMainItem::getDeleteFlag,DeleteFlagEnum.NO_DELETE.getCode())
                .orderByDesc(TbOrgApplySampleMainItem::getCreateTime,TbOrgApplySampleMainItem::getMainItemId));
    }

    /**
     * 查询申请单的送检项目信息
     */
    public List<TbOrgApplySampleMainItem> queryItemInfoByMainId(String mainId, String outTestItemCode) {
        if (StringUtils.isBlank(mainId) || StringUtils.isBlank(outTestItemCode)) {
            return Collections.EMPTY_LIST;
        }

        return tbOrgApplySampleMainItemMapper.selectList(Wrappers.lambdaQuery(TbOrgApplySampleMainItem.class)
                .eq(TbOrgApplySampleMainItem::getMainId,mainId)
                .eq(TbOrgApplySampleMainItem::getOutTestItemCode, outTestItemCode)
                .eq(TbOrgApplySampleMainItem::getDeleteFlag,DeleteFlagEnum.NO_DELETE.getCode()));
    }

}