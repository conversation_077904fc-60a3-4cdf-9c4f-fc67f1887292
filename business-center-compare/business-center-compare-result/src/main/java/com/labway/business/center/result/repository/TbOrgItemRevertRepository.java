package com.labway.business.center.result.repository;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.business.center.core.enums.DeleteFlagEnum;
import com.labway.business.center.core.util.NotifyUtil;
import com.labway.business.center.result.persistence.entity.TbOrgItemRevert;
import com.labway.business.center.result.persistence.mapper.TbOrgItemRevertMapper;
import com.labway.business.center.result.persistence.params.QueryCustomerItemRevertPageParam;
import com.labway.business.center.result.persistence.params.QueryOrgItemRevertListParam;
import com.labway.business.center.result.persistence.params.UpdateItemRevertParam;
import com.labway.sso.core.user.SsoUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Repository
public class TbOrgItemRevertRepository {

    @Resource
    private NotifyUtil notifyUtil;
    @Resource
    private TbOrgItemRevertMapper tbOrgItemRevertMapper;


    /**
     * 更新客商回传项目的对照状态
     * @param customerCode
     * @param customerItemCode
     * @param mapping
     * @return
     */
    public int setCustomerItemRevertIsMapping(String customerCode, String customerItemCode, int mapping,Integer mappingType,String orgCode,String optUser) {
        return tbOrgItemRevertMapper.update(null,Wrappers.lambdaUpdate(TbOrgItemRevert.class)
                .eq(TbOrgItemRevert::getBelongCode,customerCode)
                .eq(TbOrgItemRevert::getRevertItemCode,customerItemCode)
                .eq(TbOrgItemRevert::getBelongType,1)
                .eq(TbOrgItemRevert::getRevertItemType,mappingType)
                .eq(TbOrgItemRevert::getOrgCode,orgCode)
                        .set(TbOrgItemRevert::getUpdateTime,new Date())
                        .set(StringUtils.hasText(optUser),TbOrgItemRevert::getUpdateBy,optUser)
                .set(TbOrgItemRevert::getIsMapping,mapping));
    }

}
