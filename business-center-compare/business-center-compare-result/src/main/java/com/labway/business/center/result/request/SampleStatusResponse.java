package com.labway.business.center.result.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@NoArgsConstructor
public class SampleStatusResponse implements Serializable {
    /**
     * 条码
     */
    private String barcode;
    /**
     * 样本状态
     * @see com.labway.business.center.compare.webservice.enums.RegionSampleStatus
     */
    private Integer sampleStatus;

    /**
     * 状态描述
     */
    private String statusDes;

    /**
     * 签收时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" ,timezone = "GMT+8")
    private Date signTime;
}
