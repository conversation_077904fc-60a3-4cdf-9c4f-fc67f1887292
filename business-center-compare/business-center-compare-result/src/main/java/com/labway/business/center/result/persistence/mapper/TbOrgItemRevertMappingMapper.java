package com.labway.business.center.result.persistence.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.labway.business.center.compare.request.QueryItemRevertMappingForMiddleRequest;
import com.labway.business.center.result.persistence.entity.TbOrgItemRevert;
import com.labway.business.center.result.persistence.entity.TbOrgItemRevertMapping;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * (TbOrgItemRevertMapping)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-07-19 20:03:12
 */
public interface TbOrgItemRevertMappingMapper extends BaseMapper<TbOrgItemRevertMapping> {

/**
* 批量新增数据（MyBatis原生foreach方法）
*
* @param entities List<TbOrgItemRevertMapping> 实例对象列表
* @return 影响行数
*/
int insertBatch(@Param("entities") List<TbOrgItemRevertMapping> entities);

/**
* 批量新增或按主键更新数据（MyBatis原生foreach方法）
*
* @param entities List<TbOrgItemRevertMapping> 实例对象列表
* @return 影响行数
* @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
*/
int insertOrUpdateBatch(@Param("entities") List<TbOrgItemRevertMapping> entities);

    /**
     * 根据回传项目删除回传对照关系 -- 客商
     * @param customerItemReverts
     * @return
     */
    int deleteForCustomerItemReverts(@Param("entities") List<TbOrgItemRevert> customerItemReverts);

    /**
     * 根据回传项目删除回传对照关系 -- 机构
     * @param orgItemReverts
     * @return
     */
    int deleteForOrgItemReverts(@Param("entities") List<TbOrgItemRevert> orgItemReverts);

    /**
     *
     * @param queryItemRevertMappingForMiddleRequest
     * @return
     */
    List<TbOrgItemRevertMapping> queryItemRevertMappingForMiddle(@Param("param") QueryItemRevertMappingForMiddleRequest queryItemRevertMappingForMiddleRequest);

    /**
     *
     * @param queryItemRevertMappingForMiddleRequest
     * @return
     */
    List<TbOrgItemRevertMapping> queryItemRevertMappingForMiddle2(@Param("param") QueryItemRevertMappingForMiddleRequest queryItemRevertMappingForMiddleRequest);


    /**
     * 查询已经存在的对照关系
     * @param saveItemRevertMapping
     * @return
     */
    List<TbOrgItemRevertMapping> queryExistSameMapping(@Param("entities") List<TbOrgItemRevertMapping> saveItemRevertMapping);
}

