package com.labway.business.center.result.service.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.google.common.collect.Lists;
import com.labway.business.center.compare.dto.*;
import com.labway.business.center.compare.dto.monitoring.OrgApplySampleMainMonitoringDto;
import com.labway.business.center.compare.dto.monitoring.SampleReportMonitoringDto;
import com.labway.business.center.compare.dto.result.*;
import com.labway.business.center.compare.request.AddOptFlowInfoRequest;
import com.labway.business.center.compare.request.OrgBarcodeRelationReqeust;
import com.labway.business.center.compare.request.result.MergeReportRequest;
import com.labway.business.center.compare.service.SyncReportResultService;
import com.labway.business.center.compare.service.TbOrgApplySampleMainOptFlowService;
import com.labway.business.center.core.enums.*;
import com.labway.business.center.core.enums.DeleteFlagEnum;
import com.labway.business.center.core.util.LimsHttpUtil;
import com.labway.business.center.core.util.NotifyUtil;
import com.labway.business.center.core.util.ObsUtil;
import com.labway.business.center.result.constants.*;
import com.labway.business.center.result.persistence.entity.TbOrgApplySampleMain;
import com.labway.business.center.result.persistence.entity.TbOrgApplySampleMainItem;
import com.labway.business.center.result.persistence.entity.TbOrgApplySampleMainItemResult;
import com.labway.business.center.result.persistence.entity.TbOrgCustomerRelation;
import com.labway.business.center.result.repository.TbOrgApplySampleMainItemRepository;
import com.labway.business.center.result.repository.TbOrgApplySampleMainItemResultRepository;
import com.labway.business.center.result.repository.TbOrgApplySampleMainRepository;
import com.labway.business.center.result.repository.TbOrgCustomerRelationRepository;
import com.labway.business.center.result.security.ReportDecryptUtil;
import com.labway.business.center.result.service.TbOrgBarcodeRelationService;
import com.labway.lims.apply.api.dto.es.*;
import com.labway.report.common.dto.sample.ReportPrintQueryDto;
import com.swak.frame.dto.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutableTriple;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.pdfbox.io.MemoryUsageSetting;
import org.apache.pdfbox.multipdf.PDFMergerUtility;
import org.apache.pdfbox.pdmodel.PDDocumentInformation;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.labway.business.center.core.enums.Constants.*;
import static com.labway.business.center.core.enums.ResultCode.*;
import static com.labway.business.center.result.constants.BooleanFlagEnum.FALSE;
import static com.labway.business.center.result.constants.BooleanFlagEnum.TRUE;
import static com.labway.business.center.result.constants.ResponseCode.*;
import static org.apache.commons.lang3.StringUtils.EMPTY;

/**
 * <AUTHOR>
 * @version 2023/04/25 10:18
 * @description
 **/
@Slf4j
@DubboService
@RefreshScope
public class SyncReportResultServiceImpl implements SyncReportResultService {

    @Value("${report.result.url}")
    private String reportQueryUrl;

    @Value("${report.result.authorization:3fe58103fee7436892f0ea1e0f38fc51}")
    private String REPORT_QUERY_AUTHORIZATION;

    @Value("${report.result.partition:100}")
    private Integer reportPartition;

    @Value("${report.result.merge-url:/apply/report/merge-print-business}")
    private String limsMergeReport;
    @Value("${report.result.query-size:10000}")
    private Integer querySize;

    @Resource
    private ObsUtil obsUtil;
    @Resource
    private LimsHttpUtil limsHttpUtil;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private NotifyUtil notifyUtil;
    @Resource
    private TbOrgCustomerRelationRepository tbOrgCustomerRelationRepository;
    @Resource
    private TbOrgApplySampleMainRepository tbOrgApplySampleMainRepository;
    @Resource
    private TbOrgApplySampleMainItemResultRepository tbOrgApplySampleMainItemResultRepository;
    @Resource
    private TbOrgApplySampleMainItemRepository tbOrgApplySampleMainItemRepository;
    @Resource
    private TbOrgApplySampleMainOptFlowService tbOrgApplySampleMainOptFlowService;
    @Resource
    private TbOrgBarcodeRelationService tbOrgBarcodeRelationService;
    /**
     * 报告平台查询成功code
     */
    private static final String REPORT_QUERY_SUCCESS_CODE = "0";
    /**
     * 微生物
     */
    private static final String ITEM_TYPE_MICROBIOLOGY = "MICROBIOLOGY";


    /**
     * 样本报告结果拉取任务
     * 根据时间增量拉取报告结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Response<?> syncReportResult(ReportPrintQueryDto queryDto) {
        log.info("开始查询报告结果，入参：{}", queryDto);
        //获取送检机构编码
        List<String> customerCodes = queryHspOrgCodes(queryDto);
        //同时获取nacos中配置的送检机构，主要用于代码配置但是不显示在用户端的送检机构。比如常州实验室外送的情况
        List<String> collect = Arrays.stream(HspOrgCodeConvertEnum.values()).map(HspOrgCodeConvertEnum::getLJCode).collect(Collectors.toList());
        // 朗珈编码
        customerCodes.addAll(collect);
        customerCodes.add("20");

        log.info("需要回传报告的送检机构（客商）信息：{}", customerCodes);

        // 分割获取样本结果信息
        List<List<String>> partition = Lists.partition(customerCodes, reportPartition);
        List<ReportInfoDto> reportInfoList = partition.stream().flatMap(e -> {
            return ((SyncReportResultService) AopContext.currentProxy()).getReportInfoDtos(queryDto, e).stream();
        }).collect(Collectors.toList());

        // 检查报告结果条码号
        List<String> barcodes = reportInfoList.stream().map(ReportInfoDto::getReports)
                .filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream)
                .map(SampleDto::getOutBarcode).filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(barcodes)) {
            logWarnAndSend("报告结果查询成功，报告结果的外部条码号集合为空，不执行后续操作");
            return Response.success(ResultCode.APPLY_RESULT_BARCORE_NULL);
        }

        // 循环遍历每个机构的报告（这里是增量查询，所以查询到的结果都是最新的，也有可能是二审数据--二审结果要把之前的结果数据删除）
        // 主样本细信息是更新操作，样本结果则是删除和新增操作（删除旧结果，新增新结果）
        doSaveReportResult(reportInfoList);

        return Response.success();
    }

    // 查询获取报告结果
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<ReportInfoDto> getReportInfoDtos(ReportPrintQueryDto queryDto, List<String> e) {
        // 这里是 partition 之后的 customerCodes 客商code
        queryDto.setSendOrgCodes(e);
        // 去报告平台查询报告结果
        Response<ReportResultDto> queryResponse = doQueryReport(queryDto);

        if (!queryResponse.isSuccess()) {
            logErrorAndSend("访问报告平台查询报告结果失败：{}, {}", queryResponse.getCode(), queryResponse.getMsg());
            return Collections.emptyList();
        }

        return Objects.requireNonNullElse(queryResponse.getData().getHospitals(), Collections.emptyList());
    }

    /**
     * 查询外送样本的报告结果 补偿
     *
     * @param queryDto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Response<?> queryReportResultCompensation(ReportPrintQueryDto queryDto) {
        log.info("开始补偿报告结果，入参：{}", queryDto);

        // 查询封装未出报告的样本barcode 如果条码为空则不进行查询
        if (CollectionUtils.isEmpty(queryDto.getOutBarcodes())) {
            List<TbOrgApplySampleMain> unReportSample = tbOrgApplySampleMainRepository.queryUnReportSampleForCompensation(queryDto.getCreateDateStart(), queryDto.getCreateDateEnd());
            if (CollectionUtils.isEmpty(unReportSample)) {
                log.info("没有需要补偿的报告样本结果！");
                return Response.success();
            }
            queryDto.setOutBarcodes(unReportSample.stream().map(e -> e.getBarcode()).collect(Collectors.toList()));
        }
        queryDto.setCreateDateStart(null);
        queryDto.setCreateDateEnd(null);

        // 去报告平台查询报告结果
        Response<ReportResultDto> queryResponse = doQueryReport(queryDto);
        // 查询失败，直接返回
        if (!queryResponse.isSuccess()) {
            logErrorAndSend("补偿访问报告平台补偿报告结果失败：{}, {}", queryResponse.getCode(), queryResponse.getMsg());
            return queryResponse;
        }

        // 报告结果
        ReportResultDto reportResultDto = queryResponse.getData();
        if (reportResultDto == null || CollectionUtils.isEmpty(reportResultDto.getHospitals())) {
            logInfoAndSend("补偿查询报告结果为空，不执行后续操作，返回成功！");
            return Response.success();
        }

        List<ReportInfoDto> reportInfoList = reportResultDto.getHospitals();
        // 检查报告结果条码号
        List<String> barcodes = reportInfoList.stream().map(ReportInfoDto::getReports).filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).map(SampleDto::getOutBarcode).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(barcodes)) {
            logWarnAndSend("补偿报告结果查询成功，报告结果的外部条码号集合为空，不执行后续操作");
            return Response.success(ResultCode.APPLY_RESULT_BARCORE_NULL);
        }

        // 循环遍历每个机构的报告（这里是增量查询，所以查询到的结果都是最新的，也有可能是二审数据--二审结果要把之前的结果数据删除）
        // 主样本细信息是更新操作，样本结果则是删除和新增操作（删除旧结果，新增新结果）
        doSaveReportResult(reportInfoList);

        return Response.success();
    }

    /**
     * 查询监控统计的样本数据
     *
     * @param sampleReportMonitoringDto
     * @return
     */
    @Override
    public Response<List<OrgApplySampleMainMonitoringDto>> querySampleReportMonitoring(SampleReportMonitoringDto sampleReportMonitoringDto) {
        log.info("监控统计样本查询入参：{}", JSONObject.toJSONString(sampleReportMonitoringDto));
        List<TbOrgApplySampleMain> tbOrgApplySampleMains = tbOrgApplySampleMainRepository.querySampleReportMonitoring(sampleReportMonitoringDto);
        if (CollectionUtils.isEmpty(tbOrgApplySampleMains)) {
            return Response.success(Collections.emptyList());
        }

        return Response.success(JSONObject.parseArray(JSONObject.toJSONString(tbOrgApplySampleMains), OrgApplySampleMainMonitoringDto.class));
    }

    /**
     * 从LIS保存报告结果
     *
     * @param receiveLisReportResultDto
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Response<?> syncReportResultFromLis(ReceiveLisReportResultDto receiveLisReportResultDto) {
        String barcode = receiveLisReportResultDto.getBarcode();
        String hspOrgCode = receiveLisReportResultDto.getHspOrgCode();
        String orgCode = receiveLisReportResultDto.getOrgCode();
        // 尝试获取 互斥RedisKey
        String key = String.format(CompareConstants.LIS_REPORT_RECEIVE_KEY, orgCode, barcode);
        // 设置 barcode互斥的KEY
        // stringRedisTemplate.opsForValue().set(key, EMPTY);
        if (Boolean.FALSE.equals(stringRedisTemplate.opsForValue().setIfAbsent(key, EMPTY))) {
            return Response.fail(RESULT_CODE_IS_SYNCING);
        }
        try {
            // 查询样本主表请求dto
            QuerySampleMainDto querySampleMainDto = QuerySampleMainDto.builder().hspOrgCode(hspOrgCode).barcode(barcode).orgCode(orgCode).build();
            List<TbOrgApplySampleMain> tbOrgApplySampleMains = tbOrgApplySampleMainRepository.queryApplySampleMains(querySampleMainDto);
            if (CollectionUtils.isEmpty(tbOrgApplySampleMains)) {
                return Response.fail(RESULT_SAMPLE_STATUS_ERROR);
            }
            // 主表sample-main 更新该业务只有一条 sample_main 并拿到新的 sample-main （后面更新ItemResult要做参数）
            TbOrgApplySampleMain updateSampleMain = tbOrgApplySampleMains.get(0);
            // 封装新主表对象
            convertNewSampleMain(receiveLisReportResultDto, updateSampleMain);
            // 更新主表
            tbOrgApplySampleMainRepository.updateNewSampleMain(List.of(updateSampleMain));

            // 更新结果表部分
            // 从传入参数的 tbOrgApplySampleMains 中拿到 主表id 然后根据样本id在数据库中查询已经出报告结果的信息
            List<String> sampleMainIds = tbOrgApplySampleMains.stream().map(TbOrgApplySampleMain::getMainId).collect(Collectors.toList());
            List<TbOrgApplySampleMainItem> tbOrgApplySampleMainItemInDb = tbOrgApplySampleMainItemRepository.queryItemInfoByMainIds(sampleMainIds);
            List<TbOrgApplySampleMainItemResult> tbItemResultListInDb = tbOrgApplySampleMainItemResultRepository.queryItemResultByMainIds(sampleMainIds);

            // 根据mainID 和 barcode 拿到对应的 mainItem （mainItem 可能有多条,这里拿 item 只在插入结果 item_result 的时候拿到外键来插入外键 Id
            Map<String, List<TbOrgApplySampleMainItem>> byBarcodeExistItemMap = tbOrgApplySampleMainItemInDb.stream().collect(Collectors.groupingBy(TbOrgApplySampleMainItem::getBarcode));
            List<TbOrgApplySampleMainItem> existsItemList = byBarcodeExistItemMap.getOrDefault(barcode, ListUtil.empty());
            // 根据mainID 和 barcode 拿到对应的itemResult （itemResult 大概率有多条
            Map<String, List<TbOrgApplySampleMainItemResult>> byBarcodeExistResultMap = tbItemResultListInDb.stream().collect(Collectors.groupingBy(TbOrgApplySampleMainItemResult::getBarcode));
            List<TbOrgApplySampleMainItemResult> existsItemResultList = byBarcodeExistResultMap.getOrDefault(barcode, ListUtil.empty());
            // 从请求中拿到新检验项目和每一个报告项目结果
            List<LisReportResultDto> newReportResult = receiveLisReportResultDto.getResult();

            // 参数list初始化
            List<TbOrgApplySampleMainItemResult> delete = new ArrayList<>();
            // 直接删除再添加
            // toInsertList为封装完成需要insert的list
            List<TbOrgApplySampleMainItemResult> toInsertList = getLjResults2MainItemResult(newReportResult, existsItemList, updateSampleMain);
            if (CollectionUtils.isNotEmpty(existsItemResultList)) {
                delete.addAll(existsItemResultList);
            }
            //最后删除和添加
            tbOrgApplySampleMainItemResultRepository.deleteApplySampleMainItemBatch(delete);
            // 新的报告结果信息
            tbOrgApplySampleMainItemResultRepository.insertApplySampleMainItemBatch(toInsertList);
            // 记录Opt记录表
            recordOptFlowForReport(List.of(updateSampleMain));
        } catch (Exception e) {
            log.error("郎伽推送结果失败", e);
            return Response.fail(RESULT_SYNC_FAIL);
        } finally {
            // 无论正常与否 释放key
            stringRedisTemplate.delete(key);
        }
        return Response.success();
    }

    public void convertNewSampleMain(ReceiveLisReportResultDto receiveLisReportResultDto, TbOrgApplySampleMain sampleMain) {
        /**
         * 根据lis请求的json （要更新的main表中的字段）去封装一下
         */
        sampleMain.setResultRemark(receiveLisReportResultDto.getResultRemark());
        sampleMain.setUpdateTime(new Date());
        sampleMain.setUpdateBy(receiveLisReportResultDto.getOrgName());
        // 将 img 拼接成一个以逗号分隔的字符串
        List<String> reportImg = receiveLisReportResultDto.getReportImg();
        String imgUrl = reportImg.stream().collect(Collectors.joining(","));
        sampleMain.setImgUrls(imgUrl);
        List<String> reportPdfs = receiveLisReportResultDto.getReportPdf();
        // TODO 个别社区需要转pdf
        // downloadLjReportUtil.LjPdfsByUrlToBase64List(reportPdfs);
        String connectPdfUrls = reportPdfs.stream().collect(Collectors.joining(","));
        sampleMain.setReportUrls(connectPdfUrls);
        // TODO 审核时间 桃浦社区需要
        sampleMain.setAuditDate(receiveLisReportResultDto.getAuditDate());
        // TODO 报告结果状态字 社区搜索报告结果需要判断
        sampleMain.setIsReport(receiveLisReportResultDto.getIsReport());
    }

    public List<TbOrgApplySampleMainItemResult> getLjResults2MainItemResult(List<LisReportResultDto> ljResults, List<TbOrgApplySampleMainItem> existsItem, TbOrgApplySampleMain sampleMain) {
        List<TbOrgApplySampleMainItemResult> toInsertList = new LinkedList<>();
        // 拿每个检验项目
        for (LisReportResultDto dto : ljResults) {
            List<LisReportItemDto> reportResult = dto.getReportResult();
            // 拿每个检验项目中的报告项目
            reportResult.forEach(item -> {
                try {
                    TbOrgApplySampleMainItemResult entry = new TbOrgApplySampleMainItemResult();
                    entry.setResultId(IdEnum.TB_ORG_APPLY_SAMPLE_MAIN_ITEM_RESULT.getValue() + IdWorker.getId());
                    entry.setMainId(sampleMain.getMainId());
                    entry.setHspOrgCode(sampleMain.getHspOrgCode());
                    entry.setHspOrgName(sampleMain.getHspOrgName());
                    entry.setBarcode(sampleMain.getBarcode());
                    // 传入的mainItemList 已经是对应样本ID下对应barcode下所有的送检项目item了 这里只需找到对应OutTestItemCode下的那一个MainItemID即可
                    String mainItemId = EMPTY;
                    if (CollectionUtils.isNotEmpty(existsItem)) {
                        // 找到对应的检验项目编码 OUT_TEST_CODE 拿对应唯一的 MainItemId
                        for (TbOrgApplySampleMainItem tbOrgApplySampleMainItem : existsItem) {
                            if (tbOrgApplySampleMainItem.getOutTestItemCode().equals(dto.getOutTestItemCode())) {
                                mainItemId = tbOrgApplySampleMainItem.getMainItemId();
                                break;
                            }
                        }
                    }
                    entry.setItemId((mainItemId == null || mainItemId.equals(EMPTY)) ? EMPTY : mainItemId);
                    entry.setOutTestItemCode(dto.getOutTestItemCode() == null ? EMPTY : dto.getOutTestItemCode());
                    entry.setOutTestItemName(dto.getOutTestItemName() == null ? EMPTY : dto.getOutTestItemName());
                    entry.setItemTestCode(dto.getItemTestCode() == null ? EMPTY : dto.getItemTestCode());
                    entry.setItemTestName(dto.getItemTestName() == null ? EMPTY : dto.getItemTestName());
                    entry.setSampleNo(dto.getSampleNo() == null ? EMPTY : dto.getSampleNo());
                    entry.setCriticalStatus((item.getCriticalStatus() == null || item.getCriticalStatus().equals(EMPTY)) ? 0 : Integer.parseInt(item.getCriticalStatus()));
                    entry.setIsAdd((item.getIsAdd() == null || item.getIsAdd().equals(EMPTY)) ? 0 : Integer.parseInt(item.getIsAdd()));
                    entry.setIsRetest(item.getIsRetest() == null ? 0 : Integer.parseInt(item.getIsRetest()));
                    entry.setItemReportCode(item.getItemReportCode() == null ? EMPTY : item.getItemReportCode());
                    entry.setItemReportName(item.getItemReportName() == null ? EMPTY : item.getItemReportName());
                    entry.setInstrumentCode(item.getInstrumentCode() == null ? EMPTY : item.getInstrumentCode());
                    entry.setInstrumentName(item.getInstrumentName() == null ? EMPTY : item.getInstrumentName());
                    entry.setTestResult(item.getTestResult() == null ? EMPTY : item.getTestResult());
                    entry.setMachineResult(item.getMachineResult() == null ? EMPTY : item.getMachineResult());
                    entry.setRetestResult(item.getRetestResult() == null ? EMPTY : item.getRetestResult());
                    entry.setTestJudge(item.getTestJudge() == null ? EMPTY : item.getTestJudge());
                    entry.setResultUnit(item.getResultUnit() == null ? EMPTY : item.getResultUnit());
                    entry.setReferenceValue(item.getReferenceValue() == null ? EMPTY : item.getReferenceValue());
                    entry.setUpValue(item.getUpValue() == null ? EMPTY : item.getUpValue());
                    entry.setDownValue(item.getDownValue() == null ? EMPTY : item.getDownValue());
                    entry.setAdviseExplain(item.getAdviseExplain() == null ? EMPTY : item.getAdviseExplain());
                    entry.setClinicalBak(item.getClinicalBak() == null ? EMPTY : item.getClinicalBak());
                    entry.setRecordStatus(item.getRecordStatus() == null ? 0 : item.getRecordStatus());
                    entry.setRecordStatusDesc(item.getRecordStatusDesc() == null ? EMPTY : item.getRecordStatusDesc());
                    entry.setResultEn(item.getResultEn() == null ? EMPTY : item.getResultEn());
                    entry.setRecordDate(item.getRecordDate() == null ? new Date() : item.getRecordDate());
                    entry.setCriticalProcessResult(item.getCriticalProcessResult() == null ? EMPTY : item.getCriticalProcessResult());
                    entry.setCriticalProcessor(item.getCriticalProcessor() == null ? EMPTY : item.getCriticalProcessor());
                    entry.setCriticalProcessorId(item.getCriticalProcessorId() == null ? EMPTY : item.getCriticalProcessorId());
                    entry.setCriticalProcessorPhone(item.getCriticalProcessorPhone() == null ? EMPTY : item.getCriticalProcessorPhone());
                    entry.setCriticalProcessorContact(item.getCriticalProcessorContact() == null ? EMPTY : item.getCriticalProcessorContact());
                    entry.setCriticalReturn(item.getCriticalReturn() == null ? EMPTY : item.getCriticalReturn());
                    entry.setCriticalHandleTime(item.getCriticalHandleTime());
                    entry.setCreateBy(DefaultUserEnum.SYSTEMUSER.getUserId());
                    entry.setUpdateBy(DefaultUserEnum.SYSTEMUSER.getUserId());
                    entry.setDeleteFlag(DeleteFlagEnum.NO_DELETE.getCode());
                    // 和更新后的main时间保持一致
                    entry.setCreateTime(sampleMain.getCreateTime());
                    entry.setUpdateTime(sampleMain.getUpdateTime());
                    entry.setTestDate(item.getTestDate());
//                数据库没有该字段
//                entry.setTestUserCode(item.getTestTime())
//                entry.setTestUserCode(item.getTestUserCode());
//                entry.setTestUserName(item.getTestUserName());
                    toInsertList.add(entry);
                } catch (Exception e) {
                    log.error("样本结果{}参数解析异常", dto.getSampleNo(), e);
                }
            });
        }
        return toInsertList;
    }


    //==================================================================================================================

    /**
     * 获取报告结果查询参数-送检机构编码
     */
    private List<String> queryHspOrgCodes(ReportPrintQueryDto queryDto) {
        if (CollectionUtils.isNotEmpty(queryDto.getSendOrgCodes())) {
            return queryDto.getSendOrgCodes();
        }

        // 查询需要回传报告的送检机构（客商）信息
        List<TbOrgCustomerRelation> relationList = tbOrgCustomerRelationRepository.queryAllCustomerCodeList();
        List<String> customerCodes = relationList.stream().map(TbOrgCustomerRelation::getCustomerCode).distinct().collect(Collectors.toList());
        return customerCodes;
    }

    /**
     * 请求报告平台根据客商等信息查询报告结果
     */
    private Response<ReportResultDto> doQueryReport(ReportPrintQueryDto queryDto) {
        try {
            // 参数封装
            Map<String, String> paramMap = new HashMap<>();
            paramMap.put("data", ReportDecryptUtil.encrypt(JSON.toJSONString(queryDto)));
            paramMap.put("type", "aes");
            String jsonString = JSONObject.toJSONString(paramMap);

            log.info("查询报告平台请求参数（加密前）：{}", JSON.toJSONString(queryDto));
            // 向报告平台发起http请求
            HttpResponse response = HttpRequest.post(reportQueryUrl)
                    .body(jsonString)
                    .header(ReportDecryptUtil.TOKEN_KEY, REPORT_QUERY_AUTHORIZATION)
                    .execute();

            JSONObject bodyJsonObject = JSONObject.parseObject(response.body());
            String data = bodyJsonObject.getString("data");

            String decrypt = ReportDecryptUtil.decrypt(data);
            if (log.isDebugEnabled()) {
                log.debug("报告结果响应解密后数据：{}", decrypt);
            }

            // 解析为JSON
            JSONObject responseJson = JSONObject.parseObject(decrypt);
            String dataCode = responseJson.getString("code");

            // 检查code
            if (dataCode == null || !REPORT_QUERY_SUCCESS_CODE.equals(dataCode)) {
                return Response.fail(REPORT_RESULT_QUERY_ERROR.getCode(),
                        REPORT_RESULT_QUERY_ERROR.getMsg() + ":" + responseJson.getString("message"));
            }

            // 报告结果字符串
            String dataResult = responseJson.getString("data");
            if (StringUtils.isBlank(dataResult)) {
                return Response.fail(REPORT_RESULT_QUERY_NULL);
            }

            return Response.success(JSONObject.parseObject(dataResult, ReportResultDto.class));
        } catch (Exception e) {
            log.error("查询报告结果发生异常，查询入参：{}， 异常信息：", queryDto, e);
            notifyUtil.sendFormat("查询报告结果发生异常，查询入参：{}", queryDto);
            return Response.fail(ResultCode.APPLY_RESULT_QUERY_ERROR);
        }
    }

    /**
     * 根据条码号合并报告结果
     * 这里新增pdf合并逻辑 如果实验室配置了分血合并 那么这里只获取第一个样本的pdf地址 url
     *
     */
    private List<SampleDto> mergeSampleResults(List<SampleDto> reports) {
        List<SampleDto> mergedReports = new ArrayList<>();
        Map<String, List<SampleDto>> barcodeGroup = reports.stream().filter(x -> StringUtils.isNotBlank(x.getOutBarcode())).collect(Collectors.groupingBy(SampleDto::getOutBarcode));
        for (Map.Entry<String, List<SampleDto>> entry : barcodeGroup.entrySet()) {
            List<SampleDto> sampleList = entry.getValue();
            List<SampleDto.Result> resultList = sampleList.stream().map(SampleDto::getResults).filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).collect(Collectors.toList());
            Set<String> reportUrls = sampleList.stream().filter(x -> CollectionUtils.isNotEmpty(x.getReportUrls())).map(x -> x.getReportUrls()).flatMap(Collection::stream).collect(Collectors.toSet());
            Set<String> imgUrls = sampleList.stream().filter(x -> CollectionUtils.isNotEmpty(x.getImgUrls())).map(x -> x.getImgUrls()).flatMap(Collection::stream).collect(Collectors.toSet());

            // 如果分血合并回传 则只获取第一个样本的pdf地址
            if (sampleList.get(0).getIsMerge()!=null && Objects.equals(sampleList.get(0).getIsMerge(), YesOrNoEnum.YES.getCode())){
                reportUrls = new HashSet<>(sampleList.get(0).getReportUrls());
            }

            // 获取最新审核时间的样本
            SampleDto maxSampleDto = sampleList.stream().filter(e -> e.getAuditDate() != null).max(Comparator.comparing(SampleDto::getAuditDate)).orElse(sampleList.get(0));

            SampleDto sampleDto = new SampleDto();
            BeanUtils.copyProperties(maxSampleDto, sampleDto);
            sampleDto.setResults(resultList);
            sampleDto.setReportUrls(new ArrayList<>(reportUrls));
            sampleDto.setImgUrls(new ArrayList<>(imgUrls));
            mergedReports.add(sampleDto);
        }
        return mergedReports;
    }

    /**
     * 保存查询到的报告结果
     */
    private void doSaveReportResult(List<ReportInfoDto> reportInfoList) {
        log.info("开始保存查询到的报告结果...");

        List<TbOrgApplySampleMain> updateSampleList = new ArrayList<>();
        List<TbOrgApplySampleMainItemResult> deleteItemResultList = new ArrayList<>();
        List<TbOrgApplySampleMainItemResult> insertItemResultList = new ArrayList<>();

        // 对每个送检机构分别处理
        for (ReportInfoDto reportInfo : reportInfoList) {
            log.info("【{}-{}】样本结果开始处理...", reportInfo.getSendOrgCode(), reportInfo.getSendOrgName());

            // 送检机构编码转换
            doHspCodeConvert(reportInfo);

            // 处理报告结果
            Triple<List<TbOrgApplySampleMain>, List<TbOrgApplySampleMainItemResult>, List<TbOrgApplySampleMainItemResult>> triple = resolveReportResultByOrg(reportInfo);
            List<TbOrgApplySampleMain> updateSamples = triple.getLeft();
            List<TbOrgApplySampleMainItemResult> insertReports = triple.getMiddle();
            List<TbOrgApplySampleMainItemResult> deleteReports = triple.getRight();
            updateSampleList.addAll(updateSamples);
            insertItemResultList.addAll(insertReports);
            deleteItemResultList.addAll(deleteReports);

            log.info("【{}-{}】样本结果信息同步完成，主样本更新数量{}，样本结果新增数量{},样本结果删除数量{}", reportInfo.getSendOrgCode(), reportInfo.getSendOrgName(), updateSamples.size(), insertReports.size(), deleteReports.size());
        }

        // 更新主样本信息
        if (CollectionUtils.isNotEmpty(updateSampleList)) {
            tbOrgApplySampleMainRepository.updateApplySampleMainBatch(updateSampleList);
        }
        // 删除旧的报告结果信息
        if (CollectionUtils.isNotEmpty(deleteItemResultList)) {
            tbOrgApplySampleMainItemResultRepository.deleteApplySampleMainItemBatch(deleteItemResultList);
        }
        // 新增新的报告结果信息
        if (CollectionUtils.isNotEmpty(insertItemResultList)) {
            tbOrgApplySampleMainItemResultRepository.insertApplySampleMainItemBatch(insertItemResultList);
        }

        // 已报告， 记录业务中台样本外送日志
        if (CollectionUtils.isNotEmpty(updateSampleList)) {
            recordOptFlowForReport(updateSampleList);
        }

        log.info("保存报告结果完成");
    }


    private void doHspCodeConvert(ReportInfoDto reportInfo) {

//        if (Objects.equals(reportInfo.getSendOrgCode(), "21")) {
//            // 常州
//            extracted(reportInfo, "00010110000000001WLF");
//
//        } else if (Objects.equals(reportInfo.getSendOrgCode(), "20")) {
//            // 丹阳
//            extracted(reportInfo, "00010110000000001WND");
//        }

        String limsCodeByLJCode = HspOrgCodeConvertEnum.getLimsCodeByLJCode(reportInfo.getSendOrgCode());
        if (StringUtils.isNotBlank(limsCodeByLJCode)) {
            extracted(reportInfo, limsCodeByLJCode);
        }


    }

    // 编码替换
    private static void extracted(ReportInfoDto reportInfo, String sendOrgCode) {
        reportInfo.setSendOrgCode(sendOrgCode);
        for (SampleDto report : reportInfo.getReports()) {
            report.setSendOrgCode(sendOrgCode);
        }
    }

    /**
     * 对每个送检机构分别处理报告结果
     */
    private Triple<List<TbOrgApplySampleMain>, List<TbOrgApplySampleMainItemResult>, List<TbOrgApplySampleMainItemResult>> resolveReportResultByOrg(ReportInfoDto reportResultInfo) {

        // 要更新、新增、删除的数据
        List<TbOrgApplySampleMain> updateSampleList = new ArrayList<>();
        List<TbOrgApplySampleMainItemResult> deleteItemResultList = new ArrayList<>();
        List<TbOrgApplySampleMainItemResult> insertItemResultList = new ArrayList<>();
        // 返回值
        Triple<List<TbOrgApplySampleMain>, List<TbOrgApplySampleMainItemResult>, List<TbOrgApplySampleMainItemResult>> triple = new ImmutableTriple<>(updateSampleList, insertItemResultList, deleteItemResultList);

        // 送检机构
        String sendOrgCode = reportResultInfo.getSendOrgCode();
        String sendOrgName = reportResultInfo.getSendOrgName();
        String orgStr = concatOrgStr(sendOrgCode, sendOrgName);

        // 样本报告结果
        List<SampleDto> sampleResultList = reportResultInfo.getReports();
        if (StringUtils.isBlank(sendOrgCode) || CollectionUtils.isEmpty(sampleResultList)) {
            logWarnAndSend("{}送检机构编码为空，或该机构没有任何报告，跳过处理！", orgStr);
            return triple;
        }

        List<String> signBarcodes = sampleResultList.stream().map(SampleDto::getBarcode).filter(StringUtils::isNotBlank).collect(Collectors.toList());

        List<TbOrgApplySampleMain> tbOrgApplySampleMains = tbOrgApplySampleMainRepository.querySampleByHspOrgCodeSignBarcodes(sendOrgCode, signBarcodes);
        Map<String, TbOrgApplySampleMain> collect = tbOrgApplySampleMains.stream().collect(Collectors.toMap(TbOrgApplySampleMain::getSignBarcode, Function.identity(), (a, b) -> b));
        for (SampleDto sampleDto : sampleResultList) {
            String barcode = sampleDto.getBarcode();
            TbOrgApplySampleMain tbOrgApplySampleMain = collect.getOrDefault(barcode,new TbOrgApplySampleMain());
            if (StringUtils.isBlank(sampleDto.getOutBarcode())){
                sampleDto.setOutBarcode(tbOrgApplySampleMain.getBarcode());
            }
        }

        // ------ 根据外部条码号合并报告结果 ------
        List<SampleDto> mergedSampleResultList = mergeSampleResults(sampleResultList);

        // ------ 根据外部条码号查询样本、样本项目和已存在的报告结果 ------
        // 签收条码号
        List<String> outBarcodes = mergedSampleResultList.stream().map(SampleDto::getOutBarcode).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        // 根据外部条码号查询样本主信息
        List<TbOrgApplySampleMain> orgTbSampleList = tbOrgApplySampleMainRepository.querySampleMainListByCustomerAndBarcode(sendOrgCode, outBarcodes);
        if (CollectionUtils.isEmpty(orgTbSampleList)) {
            logWarnAndSend("{}根据外部条码号【{}】未查询到已签收状态的外送样本信息，跳过处理!", orgStr, outBarcodes);
            return triple;
        }

        // 根据样本id查询外送的项目信息
        List<String> sampleMainIds = orgTbSampleList.stream().map(TbOrgApplySampleMain::getMainId).collect(Collectors.toList());
        List<TbOrgApplySampleMainItem> orgTbItemList = tbOrgApplySampleMainItemRepository.queryItemInfoByMainIds(sampleMainIds);

        // 根据样本id查询已经出报告结果的报告信息
        List<TbOrgApplySampleMainItemResult> orgTbResultList = tbOrgApplySampleMainItemResultRepository.queryItemResultByMainIds(sampleMainIds);

        // 分组
        Map<String, List<TbOrgApplySampleMain>> byBarcodeTbSampleMap = orgTbSampleList.stream().collect(Collectors.groupingBy(TbOrgApplySampleMain::getBarcode));
        Map<String, List<TbOrgApplySampleMainItem>> bySampleIdTbItemMap = orgTbItemList.stream().collect(Collectors.groupingBy(TbOrgApplySampleMainItem::getMainId));
        Map<String, List<TbOrgApplySampleMainItemResult>> bySampleIdtbResultMap = orgTbResultList.stream().collect(Collectors.groupingBy(TbOrgApplySampleMainItemResult::getMainId));

        // 循环遍历处理每个报告--这里由于是增量查询，每条结果都是新的，所以对于样本主信息是更新操作，而样本结果只有删除和新增操作
        for (SampleDto sampleResult : mergedSampleResultList) {
            sampleResult.setIsReport(1);
            // 报告结果外部条码号
            String outBarcode = sampleResult.getOutBarcode();
            log.info("{}开始处理外部条码号【{}】的报告结果", orgStr, outBarcode);

            // 根据reportOutBarcode获取样本主表
            List<TbOrgApplySampleMain> barcodeSamples = byBarcodeTbSampleMap.get(outBarcode);
            if (CollectionUtils.isEmpty(barcodeSamples)) {
                logWarnAndSend("{}外部条码号【{}】的样本信息不存在，跳过处理！", orgStr, outBarcode);
                continue;
            }
            if (barcodeSamples.size() > 1) {
                logWarnAndSend("{}外部条码号【{}】的样本信息存在重复：【{}】，只处理第一个!", orgStr, outBarcode, barcodeSamples);
            }

            // 样本主表信息取第一条（实际业务场景不应该有多条）
            TbOrgApplySampleMain tbSample = barcodeSamples.get(0);


            // 判断样本是否分条码，如果分了条码，则需要根据外部条码号去查询所有样本结果信息--同步新逻辑改成根据条码同步，所以这里不需要校验分条码了，实验室互送也就解决了
//            sampleResult = validSplitSample(tbSample, sampleResult);


            // 不进行分血合并处理
//            // 判断样本是否是分血样本 如果是分血样本则需要获取分血后所有的样本重新同步合并
//            if (tbSample.getIsSplit() != null && Objects.equals(tbSample.getIsSplit(), YesOrNoEnum.YES.getCode()) && StringUtils.isNotBlank(tbSample.getSplitBarcode())) {
//                try {
//                    sampleResult = getSplitSampleResult(tbSample, sampleResult);
//                } catch (Exception e) {
//                    log.error("处理分血样本合并异常，条码号：{}，异常信息：", tbSample.getBarcode(), e);
//                }
//            }

            // 根据hash判断是否有改动，无改动则跳过处理
            String reportMd5Hex = calculateMd5(sampleResult);
            if (StringUtils.isNotBlank(reportMd5Hex) && reportMd5Hex.equals(tbSample.getReportMd5())) {
                log.info("{}外部条码号【{}】的报告结果Md5值无变动，跳过处理！", orgStr, outBarcode);
                continue;
            }

            // ------ 新增/删除报告项目结果信息 ------
            // 外送项目
            List<TbOrgApplySampleMainItem> barcodeItems = bySampleIdTbItemMap.get(tbSample.getMainId());
            // 根据检验项目编码分组
            Map<String, List<TbOrgApplySampleMainItem>> byCodeBarcodeItemsMap = CollectionUtils.isEmpty(barcodeItems) ? Collections.EMPTY_MAP : barcodeItems.stream().collect(Collectors.groupingBy(TbOrgApplySampleMainItem::getOutTestItemCode));
            // 已存在的报告结果
            List<TbOrgApplySampleMainItemResult> barcodeResults = bySampleIdtbResultMap.get(tbSample.getMainId());
            // 根据检验项目编码+报告项目编码分组
            Map<String, List<TbOrgApplySampleMainItemResult>> byCodeBarcodeResultsMap = CollectionUtils.isEmpty(barcodeResults) ? Collections.EMPTY_MAP : barcodeResults.stream().collect(Collectors.groupingBy(this::makeItemResultGroupKey));
            // 开始处理查询到的报告结果
            for (SampleDto.Result reportResult : sampleResult.getResults()) {

                // 根据外部项目编码和报告项目编码，查询是否存在已经同步过报告项目结果，存在则删除旧结果
                List<TbOrgApplySampleMainItemResult> existResults = byCodeBarcodeResultsMap.get(makeItemResultGroupKey(reportResult));
                if (CollectionUtils.isNotEmpty(existResults)) {
                    deleteItemResultList.addAll(existResults);
                }

                // 获取报告结果对应的送检项目信息
                List<TbOrgApplySampleMainItem> tbItems = byCodeBarcodeItemsMap.get(reportResult.getOutTestItemCode());
                if (CollectionUtils.isEmpty(tbItems)) {
                    logErrorAndSend("{}外部条码号【{}】，外部项目编码【{}】，样本报告结果存在，但未查询到对应的送检外部项目，ItemId暂填写空", orgStr, outBarcode, reportResult.getOutTestItemCode());
                }
                if (CollectionUtils.isNotEmpty(tbItems) && tbItems.size() > 1) {
                    logWarnAndSend("{}外部条码号【{}】，外部项目编码【{}】存在重复项目，只处理第一条", orgStr, outBarcode, reportResult.getOutTestItemCode());
                }
                // 以第一条的项目信息为准（实际业务场景不应该有多条）
                TbOrgApplySampleMainItem tbItem = CollectionUtils.isNotEmpty(tbItems) ? tbItems.get(0) : null;
                // 新增报告项目结果
                insertItemResultList.add(buildTbSampleMainItemResult(tbSample, tbItem, sampleResult, reportResult));
            }

            // ------ 更新样本主表信息 ------
            refreshSampleProperties(tbSample, sampleResult);
            updateSampleList.add(tbSample);
        }
        return triple;
    }


    // 这里校验实验室是否分条码，如果分条码，需要根据外部条码查询全部检验结果，合并之后再处理
    private SampleDto validSplitSample(TbOrgApplySampleMain tbSample, SampleDto sampleResult) {
        // 查询分条码的关联关系
        OrgBarcodeRelationReqeust relationReqeust = new OrgBarcodeRelationReqeust();
        relationReqeust.setHspOrgCode(tbSample.getHspOrgCode());
        relationReqeust.setOrgCode(tbSample.getOrgCode());
        relationReqeust.setBarcode(tbSample.getBarcode());
        List<OrgBarcodeRelationDto> orgBarcodeRelationDtos = tbOrgBarcodeRelationService.queryRelationBarcodeList(relationReqeust);
        if (CollectionUtils.isEmpty(orgBarcodeRelationDtos)) {
            return sampleResult;
        }

        // 所有的分条码后的 barcode(实验室的条码)
        List<String> limsSplitBarcodes = orgBarcodeRelationDtos.stream().map(e -> e.getSplitBarcode()).collect(Collectors.toList());

        // 根据条码号查询样本结果
        ReportPrintQueryDto queryDto = new ReportPrintQueryDto();
        queryDto.setSendOrgCodes(Collections.singletonList(HspOrgCodeConvertEnum.getLJCodeByLimsCode(tbSample.getHspOrgCode())));
        queryDto.setOutBarcodes(Collections.singletonList(tbSample.getBarcode()));
        queryDto.setPage(1);
        queryDto.setSize(querySize);
        // 查询ES样本数据
        List<ReportInfoDto> splitReportInfoDtos = getReportInfoDtos(queryDto, Collections.singletonList(HspOrgCodeConvertEnum.getLJCodeByLimsCode(tbSample.getHspOrgCode())));

        // 获取第一个送检机构的样本结果 这里是根据送检机构查询的，所以只有一个结果
        List<SampleDto> reports = splitReportInfoDtos.get(0).getReports();

        // 样本结果合并 根据条码号查询的，所以只会有一个样本结果
        sampleResult = mergeSampleResults(reports).get(0);

        // 填充实验室的分条码数据
        sampleResult.setLimsSplitBarcodes(limsSplitBarcodes);
        return sampleResult;
    }

    /**
     * 保存微生物同步结果
     */
    private void saveMicrobiologyResult(List<BaseSampleEsModelDto> samples) {

        if (CollectionUtils.isEmpty(samples)) {
            return;
        }
        // 查询样本主表
        Set<String> hspOrgCodes = samples.stream().map(BaseSampleEsModelDto::getHspOrgCode).collect(Collectors.toSet());
        Set<String> outBarcodes = samples.stream().map(BaseSampleEsModelDto::getOutBarcode).collect(Collectors.toSet());
        List<TbOrgApplySampleMain> sampleMainList = tbOrgApplySampleMainRepository.querySampleMainListByCustomersAndBarcode(hspOrgCodes, outBarcodes);
        Map<String, TbOrgApplySampleMain> sampleMainMap = sampleMainList.stream().collect(Collectors.toMap(sample -> sample.getHspOrgCode() + sample.getBarcode(), Function.identity(), (o1, o2) -> o1));

        List<TbOrgApplySampleMain> updateSampleList = new ArrayList<>();
        List<TbOrgApplySampleMainItemResult> insertItemResultList = new ArrayList<>();
        for (BaseSampleEsModelDto sample : samples) {

            log.info("【{}-{}】样本结果开始处理...", sample.getHspOrgCode(), sample.getOutBarcode());

            if (!ITEM_TYPE_MICROBIOLOGY.equals(sample.getItemType())) {
                log.info("【{}-{}】非微生物结果，跳过处理", sample.getHspOrgCode(), sample.getOutBarcode());
            }

            // 处理报告结果
            Pair<TbOrgApplySampleMain, List<TbOrgApplySampleMainItemResult>> pair = resolveMicrobiologyResult((MicrobiologyInspectionDto) sample, sampleMainMap);
            if (pair == null) {
                continue;
            }
            updateSampleList.add(pair.getLeft());
            insertItemResultList.addAll(pair.getRight());
        }
        log.info("同步结果成功的条码号数量：{}", updateSampleList.size());
        log.info("新增报告结果数量：{}", insertItemResultList.size());

        // 更新主样本信息
        if (CollectionUtils.isNotEmpty(updateSampleList)) {
            tbOrgApplySampleMainRepository.updateApplySampleMainBatch(updateSampleList);
        }
        // 删除旧的报告结果信息
        if (CollectionUtils.isNotEmpty(updateSampleList)) {
            tbOrgApplySampleMainItemResultRepository.deleteApplySampleMainItemByMainIds(updateSampleList.stream()
                    .map(TbOrgApplySampleMain::getMainId).collect(Collectors.toList()));
        }
        // 新增新的报告结果信息
        if (CollectionUtils.isNotEmpty(insertItemResultList)) {
            tbOrgApplySampleMainItemResultRepository.insertApplySampleMainItemBatch(insertItemResultList);
        }

        // 已报告，记录业务中台样本外送日志
        if (CollectionUtils.isNotEmpty(updateSampleList)) {
            recordOptFlowForReport(updateSampleList);
        }
    }

    /**
     * 处理微生物同步结果
     */
    private Pair<TbOrgApplySampleMain, List<TbOrgApplySampleMainItemResult>> resolveMicrobiologyResult(MicrobiologyInspectionDto microbiologySample, Map<String, TbOrgApplySampleMain> sampleMainMap) {

        Date now = new Date();

        // 根据送检机构编码+外部条码号查询样本主表
        String outBarcode = microbiologySample.getOutBarcode();
        String hspOrgCode = microbiologySample.getHspOrgCode();
        TbOrgApplySampleMain applySampleMain;
        if (StringUtils.isBlank(outBarcode) || StringUtils.isBlank(hspOrgCode) || (applySampleMain = sampleMainMap.get(hspOrgCode + outBarcode)) == null) {
            log.warn("【{}-{}】未查询到已签收状态的外送样本信息，跳过处理!", hspOrgCode, outBarcode);
            return null;
        }

        String md5Hex = DigestUtils.md5Hex(JSONUtil.toJsonStr(microbiologySample));
        if (StringUtils.isNotBlank(applySampleMain.getMicrobiologyReportMd5()) && md5Hex.equals(applySampleMain.getMicrobiologyReportMd5())) {
            log.info("【{}-{}】报告结果MD5值无变化，跳过处理!", hspOrgCode, outBarcode);
            return null;
        }

        // 根据样本id查询对应的样本项目表
        String outTestItemCode = CollectionUtils.isNotEmpty(microbiologySample.getTestItems()) ? microbiologySample.getTestItems().get(0).getOutTestItemCode() : EMPTY;
        String outTestItemName = CollectionUtils.isNotEmpty(microbiologySample.getTestItems()) ? microbiologySample.getTestItems().get(0).getOutTestItemName() : EMPTY;
        String mainId = applySampleMain.getMainId();
        List<TbOrgApplySampleMainItem> orgTbItemList = tbOrgApplySampleMainItemRepository.queryItemInfoByMainId(mainId, outTestItemCode);
        // 样本项目表id
        String itemId = CollectionUtils.isNotEmpty(orgTbItemList) ? orgTbItemList.get(0).getMainItemId() : EMPTY;

        // 细菌药物结果
        // 细菌
        List<MicrobiologyGermDto> germs = microbiologySample.getGerms();
        List<TbOrgApplySampleMainItemResult> addResultList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(germs)) {
            for (MicrobiologyGermDto germ : germs) {
                // 药物
                List<MicrobiologyMedicineDto> medicines = germ.getMedicines();
                if (CollectionUtils.isNotEmpty(medicines)) {
                    for (MicrobiologyMedicineDto medicine : medicines) {
                        // 构建报告结果
                        TbOrgApplySampleMainItemResult itemResult = TbOrgApplySampleMainItemResult.builder()
                                .resultId(IdEnum.TB_ORG_APPLY_SAMPLE_MAIN_ITEM_RESULT.getValue() + IdWorker.getId()) //报告结果表id
                                .mainId(mainId)  //外送申请单表id
                                .itemId(itemId)  //外送项目信息表id
                                .barcode(outBarcode) //样本条码号
                                .outTestItemCode(outTestItemCode) //外部项目编码
                                .outTestItemName(outTestItemName) //外部项目名称
                                .itemTestCode(germ.getTestItemCode()) //检验项目编码
                                .itemTestName(germ.getTestItemName()) //检验项目名称
                                .itemReportCode(EMPTY) //报告项目编码
                                .itemReportName(EMPTY) //报告项目名称
                                .hspOrgCode(hspOrgCode) //送检机构编码（对应中台客商编码）
                                .hspOrgName(microbiologySample.getHspOrgName()) //送检机构名称（对应中台客商名称）
                                .createBy(DefaultUserEnum.SYSTEMUSER.getUserId()) //创建人id
                                .createTime(now) //创建时间
                                .updateBy(DefaultUserEnum.SYSTEMUSER.getUserId()) //更新人id
                                .updateTime(now) //更新时间
                                .sampleNo(microbiologySample.getSampleNo()) //样本号
                                .instrumentCode(null) //仪器设备编码
                                .instrumentName(null) //仪器设备名称
                                .customerId(null)    //客商业务id
                                .customerName(null)  //客商名称
                                .testResult(medicine.getResult())    //检验结果值
                                .machineResult(null) //机器结果值
                                .isRetest(null)     //是否复测
                                .retestResult(null) //复测结果值
                                .testJudge(null)    //检验判断
                                .resultUnit(medicine.getUnit())   //单位
                                .referenceValue(medicine.getRange()) //参考值
                                .upValue(null)    //上限
                                .downValue(null)    //下限
                                .adviseExplain(null)  //建议与解释
                                .clinicalBak(null)   //临床意义
                                .recordStatus(null)  //结果记录状态
                                .recordStatusDesc(null) //结果记录状态描述
                                .resultEn(null)    //结果英文简称
                                .recordDate(null)  //记录日志
                                .testDate(null)   //检验日期
                                .criticalValue(null)  //危急值
                                .isCritical(null)  //是否危急值
                                .isException(null)  //是否危急值 0否（默认值）  1是
                                .criticalStatus(null) //危急值处理状态
                                .criticalProcessor(null) // 危急值处理结果
                                .criticalProcessResult(null) //危机处理人
                                .criticalProcessorId(null) //危机处理人id
                                .criticalProcessorPhone(null) //危机处理人联系电话
                                .criticalProcessorContact(null) //危急值联系人
                                .criticalReturn(null) //危急值对方反馈
                                .criticalHandleTime(null) //危急值处理时间
                                .isAdd(null) //是否加项（0否 1是）
                                .customBarcode(null) //自定义条码
                                .itemType(ITEM_TYPE_MICROBIOLOGY) // 项目类型
                                .germCode(germ.getGermCode()) // 细菌编码
                                .germGenusCode(germ.getGermGenusCode()) // 细菌菌属编码
                                .germName(germ.getGermName()) // 细菌名称
                                .germCount(germ.getGermCount()) // 细菌数量
                                .germRemark(germ.getGermRemark()) // 细菌备注
                                .testMethod(germ.getTestMethod()) // 检验方法
                                .medicineCode(medicine.getMedicineCode()) // 药物编码
                                .medicineName(medicine.getMedicineName()) // 药物名称
                                .medicineMethod(medicine.getMedicineMethod()) // 药物方法
                                .susceptibility(medicine.getSusceptibility()) // 敏感度
                                .formula(medicine.getFormula()) // 药物结果前缀
                                .deleteFlag(DeleteFlagEnum.NO_DELETE.getCode()) //删除标识 0未删除 1删除
                                .build();
                        addResultList.add(itemResult);
                    }
                }
            }
        }

        // 微生物结果
        List<BaseSampleEsModelDto.TestItem> testItems = microbiologySample.getTestItems();
        String testItemCode;
        String testItemName;
        if (CollectionUtils.isNotEmpty(testItems)) {
            BaseSampleEsModelDto.TestItem testItem = testItems.get(0);
            testItemCode = testItem.getTestItemCode();
            testItemName = testItem.getTestItemName();
        } else {
            testItemCode = EMPTY;
            testItemName = EMPTY;
        }
        List<MicrobiologyResultDto> results = microbiologySample.getResults();
        for (MicrobiologyResultDto result : results) {
            if (StringUtils.isBlank(result.getResult())) {
                continue;
            }

            // 构建报告结果
            TbOrgApplySampleMainItemResult itemResult = TbOrgApplySampleMainItemResult.builder()
                    .resultId(IdEnum.TB_ORG_APPLY_SAMPLE_MAIN_ITEM_RESULT.getValue() + IdWorker.getId()) //报告结果表id
                    .mainId(mainId)  //外送申请单表id
                    .itemId(itemId)  //外送项目信息表id
                    .barcode(outBarcode) //样本条码号
                    .outTestItemCode(outTestItemCode) //外部项目编码
                    .outTestItemName(outTestItemName) //外部项目名称
                    .itemTestCode(testItemCode) //检验项目编码
                    .itemTestName(testItemName) //检验项目名称
                    .itemReportCode(EMPTY) //报告项目编码
                    .itemReportName(EMPTY) //报告项目名称
                    .hspOrgCode(hspOrgCode) //送检机构编码（对应中台客商编码）
                    .hspOrgName(microbiologySample.getHspOrgName()) //送检机构名称（对应中台客商名称）
                    .createBy(DefaultUserEnum.SYSTEMUSER.getUserId()) //创建人id
                    .createTime(now) //创建时间
                    .updateBy(DefaultUserEnum.SYSTEMUSER.getUserId()) //更新人id
                    .updateTime(now) //更新时间
                    .sampleNo(microbiologySample.getSampleNo()) //样本号
                    .instrumentCode(null) //仪器设备编码
                    .instrumentName(null) //仪器设备名称
                    .customerId(null)    //客商业务id
                    .customerName(null)  //客商名称
                    .testResult(result.getResult())    //检验结果值
                    .machineResult(null) //机器结果值
                    .isRetest(null)     //是否复测
                    .retestResult(null) //复测结果值
                    .testJudge(null)    //检验判断
                    .resultUnit(null)   //单位
                    .referenceValue(null) //参考值
                    .upValue(null)    //上限
                    .downValue(null)    //下限
                    .adviseExplain(null)  //建议与解释
                    .clinicalBak(null)   //临床意义
                    .recordStatus(null)  //结果记录状态
                    .recordStatusDesc(null) //结果记录状态描述
                    .resultEn(null)    //结果英文简称
                    .recordDate(null)  //记录日志
                    .testDate(null)   //检验日期
                    .criticalValue(null)  //危急值
                    .isCritical(null)  //是否危急值
                    .isException(null)  //是否危急值 0否（默认值）  1是
                    .criticalStatus(null) //危急值处理状态
                    .criticalProcessor(null) // 危急值处理结果
                    .criticalProcessResult(null) //危机处理人
                    .criticalProcessorId(null) //危机处理人id
                    .criticalProcessorPhone(null) //危机处理人联系电话
                    .criticalProcessorContact(null) //危急值联系人
                    .criticalReturn(null) //危急值对方反馈
                    .criticalHandleTime(null) //危急值处理时间
                    .isAdd(null) //是否加项（0否 1是）
                    .customBarcode(null) //自定义条码
                    .itemType(ITEM_TYPE_MICROBIOLOGY) // 项目类型
                    .germCode(null) // 细菌编码
                    .germGenusCode(null) // 细菌菌属编码
                    .germName(null) // 细菌名称
                    .germCount(null) // 细菌数量
                    .germRemark(null) // 细菌备注
                    .testMethod(null) // 检验方法
                    .medicineCode(null) // 药物编码
                    .medicineName(null) // 药物名称
                    .medicineMethod(null) // 药物方法
                    .susceptibility(null) // 敏感度
                    .formula(null) // 药物结果前缀
                    .deleteFlag(DeleteFlagEnum.NO_DELETE.getCode()) //删除标识 0未删除 1删除
                    .build();
            addResultList.add(itemResult);
        }

        // 已报告
        applySampleMain.setIsReport(IS_TRUE);
        // 未下载
        applySampleMain.setIsDownload(IS_FALSE_INT);
        Long finalCheckerId = microbiologySample.getFinalCheckerId();
        applySampleMain.setAuditorId(String.valueOf(ObjectUtils.defaultIfNull(finalCheckerId, "")));
        applySampleMain.setAuditor(microbiologySample.getFinalCheckerName());
        applySampleMain.setAuditDate(microbiologySample.getFinalCheckDate());
        applySampleMain.setSampleNo(microbiologySample.getSampleNo());
        applySampleMain.setTestTime(microbiologySample.getTestDate());
        Long testerId = microbiologySample.getTesterId();
        applySampleMain.setTestUserCode(String.valueOf(ObjectUtils.defaultIfNull(testerId, "")));
        applySampleMain.setTestUserName(microbiologySample.getTesterName());
        applySampleMain.setMicrobiologyReportUrls(parseReportUrls(microbiologySample.getReports()));
        applySampleMain.setMicrobiologyReportMd5(md5Hex);
        applySampleMain.setItemType(ITEM_TYPE_MICROBIOLOGY);
        applySampleMain.setUpdateTime(new Date());
        applySampleMain.setTubeType(microbiologySample.getTubeName());

        return Pair.of(applySampleMain, addResultList);
    }

    private String parseReportUrls(List<BaseSampleEsModelDto.Report> reports) {
        if (CollectionUtils.isEmpty(reports)) {
            return JSON.toJSONString(Collections.EMPTY_LIST);
        }
        Set<String> urls = reports.stream().map(BaseSampleEsModelDto.Report::getUrl).collect(Collectors.toSet());
        return JSON.toJSONString(urls);
    }

    /**
     * 保存业务中台样本状态流转记录
     */
    private void recordOptFlowForSend(List<TbOrgApplySampleMain> mainList, String optUserName) {

        if (CollectionUtils.isEmpty(mainList)) {
            return;
        }

        AddOptFlowInfoRequest flowInfoRequest = new AddOptFlowInfoRequest();
        flowInfoRequest.setFlowStatus(FlowStatusEnum.REPORT.getCode());

        List<AddOptFlowInfoRequest.AddOptFlow> addOptFlowList = mainList.stream().map(x ->
                AddOptFlowInfoRequest.AddOptFlow.builder()
                        .formId(x.getFormId()) //申请单id
                        .formCode(x.getFormCode()) //申请单编码
                        .barcode(x.getBarcode()) //样本条码号
                        .lwBarcode(x.getSignBarcode()) //实验室条码号
                        .hspOrgCode(x.getHspOrgCode()) //送检机构编码
                        .hspOrgName(x.getHspOrgName()) //送检机构名称
                        .patientName(x.getPatientName()) //病人姓名
                        .patientCode(x.getPatientCard()) //病人编号
                        .sex(PatientSexEnum.getSexCodeStr(x.getPatientSex())) //病人性别
                        .age(String.valueOf(ObjectUtils.defaultIfNull(x.getPatientAge(), StringUtils.EMPTY))) //病人年龄
                        .patientBed(x.getPatientBed()) //床号
                        .applyType(x.getApplyType()) //就诊类型
                        .dept(x.getDept()) //申请科室
                        .sendDoctor(x.getSendDoctor()) //送检医生
                        .urgent(UrgentEnum.getUrgentCodeStr(x.getUrgent())) //是否加急
                        .remark(MessageConstants.ORG_CANCEL_AUDIT_REMARK) //备注
                        .optId(StringUtils.EMPTY) //操作人id
                        .optName(MessageConstants.ORG_USER_NAME_PREFIX + optUserName) //操作人名称
                        .optTime(new Date()) //操作时间
                        .build()).collect(Collectors.toList());
        flowInfoRequest.setFlowList(addOptFlowList);

        // 更新样本主表流转状态最终状态
        tbOrgApplySampleMainRepository.updateFlowStatus(flowInfoRequest.getFlowList(), flowInfoRequest.getFlowStatus());
        // 保存流转记录
        tbOrgApplySampleMainOptFlowService.addOptFlowInfo(flowInfoRequest);
        return;
    }

    /**
     * 过滤出来需要推送给中间程序的数据
     *
     * @param resultDTOS    所有
     * @param updateSamples 需要的
     * @return
     */
    private List<SampleTestResultDTO> filterNeedData(List<SampleTestResultDTO> resultDTOS, List<TbOrgApplySampleMain> updateSamples) {
        if (CollectionUtils.isEmpty(resultDTOS)) {
            return Collections.emptyList();
        }
        List<String> barcodeList = updateSamples.stream().map(TbOrgApplySampleMain::getBarcode).collect(Collectors.toList());
        return resultDTOS.stream().filter(a -> barcodeList.contains(a.getBarcode())).collect(Collectors.toList());
    }

    private List<SampleTestResultDTO> buildMiddleResultRequest(ReportInfoDto reportInfo) {
        List<SampleTestResultDTO> resultDTOS = Lists.newArrayList();
        SampleTestResultDTO.SampleTestResultDTOBuilder builder = SampleTestResultDTO.builder();
        List<String> barcodes = reportInfo.getReports().stream().map(SampleDto::getBarcode).collect(Collectors.toList());
        List<TbOrgApplySampleMain> applySampleMains = tbOrgApplySampleMainRepository.querySignApplySampleMainBySendOrgCodeAndBarcodes(barcodes, reportInfo.getSendOrgCode());
        Map<String, TbOrgApplySampleMain> applySampleMainsMap = applySampleMains.stream().collect(Collectors.toMap(TbOrgApplySampleMain::getBarcode, Function.identity()));
        for (SampleDto report : reportInfo.getReports()) {
            List<SampleItemTestResultDTO> testItemResultList = Lists.newArrayList();
            TbOrgApplySampleMain orgApplySampleMain = applySampleMainsMap.get(report.getBarcode());
            Map<String, SampleDto.TestItem> testItemMap = report.getTestItems().stream().collect(Collectors.toMap(SampleDto.TestItem::getCode, Function.identity()));
            for (SampleDto.Result result : report.getResults()) {
                // SampleItemTestResultDTO 检验项目结果
                SampleDto.TestItem testItem = testItemMap.get(result.getCode());
                SampleItemTestResultDTO.SampleItemTestResultDTOBuilder testItemResultBuilder = SampleItemTestResultDTO.builder();
                testItemResultBuilder.barcode(report.getBarcode()).itemCode(result.getOutTestItemCode()).itemName(result.getOutTestItemName())
                        .sampleNo(report.getSampleNo()).instrumentCode(result.getInstrumentCode()).reportItemId(result.getCode()).reportItemName(result.getName())
                        .testItemCode(result.getTestItemCode()).testItemName(result.getTestItemName()).testResult(result.getResult()).machineResult(result.getMachineResult())
                        .testJudge(result.getTestJudge()).resultUnit(result.getUnit()).referenceValue(result.getReferenceRange()).upValue(result.getUpValue())
                        .downValue(result.getDownValue()).recordDate(result.getRecordDate()).testDate(report.getTestDate()).isAdd(Objects.equals(Boolean.TRUE, result.getIsAdd()) ? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode())
                        .hspOrgCode(report.getSendOrgCode()).hspOrgName(report.getSendOrgName())
                        .masterBarcode(null).instrumentName(null).isRetest(null).adviseExplain(null).clinicalBak(null).recordStatus(null).recordStatusDesc(null).resultEn(null)
                        .criticalValue(null).isCritical(null).isException(null).retestResult(null).criticalStatus(null).criticalProcessResult(null).criticalProcessor(null)
                        .criticalProcessorId(null).criticalProcessorPhone(null).criticalProcessorContact(null).criticalReturn(null).criticalHandleTime(null).customerId(null).customerName(null);
                testItemResultList.add(testItemResultBuilder.build());
            }
            builder.barcode(report.getBarcode()).sendOrgCode(report.getSendOrgCode()).tubeType(orgApplySampleMain.getTubeType()).sourceType(orgApplySampleMain.getApplyType())
                    .isUrgent(UrgentEnum.getUrgentDesc(orgApplySampleMain.getUrgent()))
                    .sampleType(orgApplySampleMain.getSampleType()).appDept(orgApplySampleMain.getDept())
                    .inpatientArea(orgApplySampleMain.getInpatientArea()).patientName(orgApplySampleMain.getPatientVisitCard()).patientName(orgApplySampleMain.getPatientName())
                    .sex(PatientSexEnum.getSexStr(orgApplySampleMain.getPatientSex()))
                    .age(orgApplySampleMain.getPatientAge() + "岁")
                    .birthday(orgApplySampleMain.getPatientBirthday()).bed(orgApplySampleMain.getPatientBed()).diag(orgApplySampleMain.getClinicalDiagnosis())
                    .patientCard(orgApplySampleMain.getPatientCard()).medNumber(report.getMedNumber()).idNumber(report.getIdNumBer()).address(orgApplySampleMain.getPatientAsddress())
                    .tel(orgApplySampleMain.getPatientMobile()).memo(report.getMemo()).appUserName(orgApplySampleMain.getSendDoctor()).extractUserName(null).extractDate(report.getExtractDate())
                    .submitUserCode(report.getSubmitUserCode()).submitUserName(report.getSubmitUserName()).testItemSum(orgApplySampleMain.getTestItemSum()).combBarCode(null).groupMemo(null).sampleNum(orgApplySampleMain.getSampleNum())
                    .sendDate(orgApplySampleMain.getApplyDate()).summaryHandoverCode(null).origoutOrgCode(report.getOriginalOutOrgCode()).origoutOrgName(report.getOriginalOutOrgName())
                    .outMainId(orgApplySampleMain.getOutMainId()).auditorId(orgApplySampleMain.getAuditorId()).auditor(orgApplySampleMain.getAuditor()).isFirstAudit(report.getIsFirstAudit())
                    .firstAuditUserCode(report.getOneCheckUserCode()).firstAuditUserName(report.getOneCheckUserName()).firstAuditTime(report.getOneAuditDate()).isSecAudit(report.getIsSecAudit())
                    .secAuditUserCode(report.getTwoCheckUserCode()).secAuditUserName(report.getTwoCheckUserName()).secAuditTime(report.getTwoAuditDate()).auditDate(report.getAuditDate())
                    .sampleNo(orgApplySampleMain.getSampleNo()).testDate(report.getTestDate()).instrumentCode(report.getInstrumentCode()).isCheck(report.getIsChecked())
                    .testDateStr(report.getTestDateStr()).testUserName(report.getTestUserName()).testUserCode(report.getTestUserCode()).queryTimes(orgApplySampleMain.getQueryTimes())
                    .isDownload(orgApplySampleMain.getIsDownload()).isReport(orgApplySampleMain.getIsReport()).patientSubAge(orgApplySampleMain.getPatientSubage())
                    .patientSubAgeUnit(orgApplySampleMain.getPatientSubageUnit()).status(orgApplySampleMain.getStatus()).reportUrls(JSONObject.toJSONString(report.getReportUrls()))
                    .itemTestResultList(testItemResultList);
            resultDTOS.add(builder.build());
        }
        return resultDTOS;
    }

    private String calculateMd5(SampleDto sampleResult) {

        // 由于合并了结果集，ID每次不一定相同，并且ID对业务中台不需要，因此ID转为空
        if (CollectionUtils.isNotEmpty(sampleResult.getResults())) {
            for (SampleDto.Result result : sampleResult.getResults()) {
                result.setId(null);
            }
        }
        if (CollectionUtils.isNotEmpty(sampleResult.getTestItems())) {
            for (SampleDto.TestItem result : sampleResult.getTestItems()) {
                result.setId(null);
            }
        }
        String md5Hex = DigestUtils.md5Hex(JSONUtil.toJsonStr(sampleResult));
        log.info("barcode= {}, md5Hex = {}", sampleResult.getBarcode(), md5Hex);
        return md5Hex;
    }

    /**
     * 更新样本主表信息
     */
    private void refreshSampleProperties(TbOrgApplySampleMain tbSample, SampleDto sampleResult) {

        // 已报告
        tbSample.setIsReport(String.valueOf(ObjectUtils.defaultIfNull(sampleResult.getIsReport(), YesOrNoEnum.YES.getCode())));
        // 未下载
        tbSample.setIsDownload(IS_FALSE_INT);
        tbSample.setOutMainId(EMPTY);
        tbSample.setAuditorId(sampleResult.getAuditCode());
        tbSample.setAuditor(sampleResult.getCheckUserName());
        tbSample.setAuditDate(sampleResult.getAuditDate());
        tbSample.setInstrumentCode(sampleResult.getInstrumentCode());
        tbSample.setSampleNo(sampleResult.getSampleNo());
        tbSample.setTestTime(sampleResult.getTestDate());
        tbSample.setTestDate(sampleResult.getTestDateStr());
        tbSample.setTestItemSum(sampleResult.getTestItemSum());
        tbSample.setFirstAuditUserCode(sampleResult.getOneCheckUserCode());
        tbSample.setFirstAuditUserName(sampleResult.getOneCheckUserName());
        tbSample.setFirstAuditTime(sampleResult.getOneAuditDate());
        tbSample.setIsFirstAudit(sampleResult.getIsFirstAudit());
        tbSample.setSecAuditUserCode(sampleResult.getTwoCheckUserCode());
        tbSample.setSecAuditUserName(sampleResult.getTwoCheckUserName());
        tbSample.setSecAuditTime(sampleResult.getTwoAuditDate());
        tbSample.setIsSecAudit(sampleResult.getIsSecAudit());
        tbSample.setIsCheck(ObjectUtils.defaultIfNull(sampleResult.getIsChecked(), TRUE.getValue()));//sampleResult.getIsChecked() == null ? TRUE.getValue() : sampleResult.getIsChecked());
        tbSample.setTestUserCode(sampleResult.getTestUserCode());
        tbSample.setTestUserName(sampleResult.getTestUserName());
        tbSample.setReportUrls(JSONUtil.toJsonStr(ObjectUtils.defaultIfNull(sampleResult.getReportUrls(), Collections.EMPTY_LIST)));// sampleResult.getReportUrls() != null ? JSONUtil.toJsonStr(sampleResult.getReportUrls()) : JSONUtil.toJsonStr(Collections.EMPTY_LIST));
        tbSample.setImgUrls(JSONUtil.toJsonStr(ObjectUtils.defaultIfNull(sampleResult.getImgUrls(), Collections.EMPTY_LIST)));
        tbSample.setUpdateTime(new Date());
        tbSample.setReportMd5(calculateMd5(sampleResult));
        tbSample.setTubeType(sampleResult.getTubeName());
        tbSample.setResultRemark(sampleResult.getResultRemark());
        tbSample.setCheckIdCard(sampleResult.getCheckIdCard());
        tbSample.setOneCheckIdCard(sampleResult.getOneCheckIdCard());
        tbSample.setTwoCheckIdCard(sampleResult.getTwoCheckIdCard());
        tbSample.setAllReportsCount(sampleResult.getAllReportsCount());
        tbSample.setCurrentReportsCount(sampleResult.getCurrentReportsCount());
    }

    /**
     * 构建报告项目结果实体类
     */
    private TbOrgApplySampleMainItemResult buildTbSampleMainItemResult(TbOrgApplySampleMain tbSample, TbOrgApplySampleMainItem tbItem, SampleDto sampleResult, SampleDto.Result reportResult) {
        TbOrgApplySampleMainItemResult tempItemResult = new TbOrgApplySampleMainItemResult();
        tempItemResult.setResultId(IdEnum.TB_ORG_APPLY_SAMPLE_MAIN_ITEM_RESULT.getValue() + IdUtil.getSnowflake().nextId());
        tempItemResult.setMainId(tbSample.getMainId());
        tempItemResult.setHspOrgCode(tbSample.getHspOrgCode());
        tempItemResult.setHspOrgName(tbSample.getHspOrgName());
        tempItemResult.setBarcode(tbSample.getBarcode());
        tempItemResult.setItemId(tbItem == null ? EMPTY : tbItem.getMainItemId());
        tempItemResult.setOutTestItemCode(tbItem == null ? EMPTY : tbItem.getOutTestItemCode());
        tempItemResult.setOutTestItemName(tbItem == null ? EMPTY : tbItem.getOutTestItemName());
        tempItemResult.setSampleNo(sampleResult.getSampleNo());
        tempItemResult.setTestDate(sampleResult.getTestDate());
        Boolean isAdd = reportResult.getIsAdd();
        if (isAdd != null) {
            tempItemResult.setIsAdd(isAdd ? TRUE.getValue() : FALSE.getValue());
        } else {
            tempItemResult.setIsAdd(FALSE.getValue());
            log.warn("样本{}没有isAdd标识，设置默认值0!", sampleResult.getSampleNo());
        }
        // 是否复测
        tempItemResult.setIsRetest(FALSE.getValue());
        tempItemResult.setItemTestCode(reportResult.getTestItemCode());
        tempItemResult.setItemTestName(reportResult.getTestItemName());
        tempItemResult.setTestResult(reportResult.getResult());
        tempItemResult.setResultUnit(reportResult.getUnit());
        tempItemResult.setReferenceValue(reportResult.getReferenceRange());
        tempItemResult.setTestJudge(reportResult.getTestJudge());
        tempItemResult.setInstrumentCode(reportResult.getInstrumentCode());
        tempItemResult.setMachineResult(reportResult.getMachineResult());
        tempItemResult.setUpValue(reportResult.getUpValue());
        tempItemResult.setDownValue(reportResult.getDownValue());
        tempItemResult.setItemReportCode(reportResult.getCode());
        tempItemResult.setItemReportName(reportResult.getName());
        tempItemResult.setRecordDate(reportResult.getRecordDate());
        tempItemResult.setCreateBy(DefaultUserEnum.SYSTEMUSER.getUserId());
        tempItemResult.setUpdateBy(DefaultUserEnum.SYSTEMUSER.getUserId());
        Date now = new Date();
        tempItemResult.setCreateTime(now);
        tempItemResult.setUpdateTime(now);
        tempItemResult.setDeleteFlag(DeleteFlagEnum.NO_DELETE.getCode());
        tempItemResult.setPrintSort(reportResult.getPrintSort());
        tempItemResult.setTestMethod(reportResult.getTestMethod());
        tempItemResult.setResultType(reportResult.getResultType());

        return tempItemResult;
    }

    /**
     * 创建报告项目结果分组的key
     */
    private String makeItemResultGroupKey(TbOrgApplySampleMainItemResult itemResult) {
        if (itemResult == null) {
            return EMPTY;
        }
        return itemResult.getItemTestCode() + "-" + itemResult.getItemReportCode();
    }

    /**
     * 创建报告项目结果分组的key
     */
    private String makeItemResultGroupKey(SampleDto.Result reportResult) {
        if (reportResult == null) {
            return EMPTY;
        }
        return reportResult.getTestItemCode() + "-" + reportResult.getCode();
    }

    private String concatOrgStr(String orgCode, String orgName) {
        return "【" + orgCode + "-" + orgName + "】";
    }

    /**
     * 保存业务中台样本状态流转记录
     */
    private void recordOptFlowForReport(List<TbOrgApplySampleMain> mainList) {

        AddOptFlowInfoRequest flowInfoRequest = new AddOptFlowInfoRequest();
        flowInfoRequest.setFlowStatus(FlowStatusEnum.REPORT.getCode());

        List<AddOptFlowInfoRequest.AddOptFlow> addOptFlowList = mainList.stream().map(x ->
                AddOptFlowInfoRequest.AddOptFlow.builder()
                        .formId(x.getFormId()) //申请单id
                        .formCode(x.getFormCode()) //申请单编码
                        .barcode(x.getBarcode()) //样本条码号
                        .lwBarcode(x.getSignBarcode()) //实验室条码号
                        .hspOrgCode(x.getHspOrgCode()) //送检机构编码
                        .hspOrgName(x.getHspOrgName()) //送检机构名称
                        .patientName(x.getPatientName()) //病人姓名
                        .patientCode(x.getPatientCard()) //病人编号
                        .sex(PatientSexEnum.getSexCodeStr(x.getPatientSex())) //病人性别
                        .age(String.valueOf(ObjectUtils.defaultIfNull(x.getPatientAge(), StringUtils.EMPTY))) //病人年龄
                        .patientBed(x.getPatientBed()) //床号
                        .applyType(x.getApplyType()) //就诊类型
                        .dept(x.getDept()) //申请科室
                        .sendDoctor(x.getSendDoctor()) //送检医生
                        .urgent(UrgentEnum.getUrgentCodeStr(x.getUrgent())) //是否加急
                        .remark(YesOrNoEnum.YES.getStringCode().equals(x.getIsReport()) ? MessageConstants.BUSINESS_TEST_ITEM_FULL : MessageConstants.BUSINESS_TEST_ITEM_SOME) //备注
                        .optId(DefaultUserEnum.SYSTEMUSER.getUserId()) //操作人id
                        .optName(MessageConstants.BUSINESS_CENTER_JOB_NAME) //操作人名称
                        .optTime(new Date()) //操作时间
                        .build()).collect(Collectors.toList());
        flowInfoRequest.setFlowList(addOptFlowList);

        // 更新样本主表流转状态最终状态
        tbOrgApplySampleMainRepository.updateFlowStatus(flowInfoRequest.getFlowList(), flowInfoRequest.getFlowStatus());
        // 保存记录
        tbOrgApplySampleMainOptFlowService.addOptFlowInfo(flowInfoRequest);
        return;
    }

    private void logInfoAndSend(String content, Object... args) {
        log.info(content, args);
        notifyUtil.sendFormat(content, args);
    }

    private void logWarnAndSend(String content, Object... args) {
        log.warn(content, args);
        notifyUtil.sendFormat(content, args);
    }

    private void logErrorAndSend(String content, Object... args) {
        log.error(content, args);
        notifyUtil.sendFormat(content, args);
    }

    // 处理获取分血样本合并结果
    private SampleDto getSplitSampleResult(TbOrgApplySampleMain tbSample, SampleDto currentSampleResult) throws Exception {
        String signOrgCode = tbSample.getSignOrgCode();
        String hspOrgCode = tbSample.getHspOrgCode();
        String signBarcode = tbSample.getSignBarcode();
        String outBarcode = currentSampleResult.getOutBarcode();
        String splitBarcode = tbSample.getSplitBarcode();
        // 分血条条码
        List<String> splitBarcodes = new ArrayList<>();
        splitBarcodes.addAll(Arrays.asList(splitBarcode.split(",")));

        // 所有的实验室条码
        List<String> allLimsBarcodes = new ArrayList<>();
        allLimsBarcodes.addAll(splitBarcodes);
        allLimsBarcodes.add(signBarcode);
        if (CollectionUtils.isNotEmpty(currentSampleResult.getLimsSplitBarcodes())) {
            allLimsBarcodes.addAll(currentSampleResult.getLimsSplitBarcodes());
        }

        if (StringUtils.isBlank(splitBarcode)) {
            log.warn("样本【签收条码号：{}】,确实分血条码号，有可能是终止检验了，不进行报告单合并！！！", signBarcode);
            return currentSampleResult;
        }


        ReportPrintQueryDto queryDto = new ReportPrintQueryDto();
        queryDto.setOutBarcodes(Collections.singletonList(outBarcode));
        queryDto.setPage(1);
        queryDto.setSize(querySize);
        // 查询ES样本数据
        List<ReportInfoDto> splitReportInfoDtos = getReportInfoDtos(queryDto, Collections.singletonList(HspOrgCodeConvertEnum.getLJCodeByLimsCode(hspOrgCode)));

        // 获取ES样本数据 这里是根据送检机构查询的 所以只会有一条数据
        List<SampleDto> reports = splitReportInfoDtos.get(0).getReports();

        // 样本结果合并
        SampleDto sampleResult = mergeSampleResults(reports).get(0);

        // 报告样本的所有实验室条码号
        Set<String> allReportBarcodes = reports.stream().map(e -> e.getBarcode()).collect(Collectors.toSet());
        // 报告单pdf数量
        List<String> reportUrlCount = sampleResult.getReportUrls();

        // 报告标识 默认已报告 （如果报告平台结果包含了所有的实验室条码 则认为报告出全了）
        // 根据报告单数量判断是否已报告 报告单数量大于等于分条码数量的个数
        Integer isReport = YesOrNoEnum.NO.getCode();
        // 所有条码的集合（实验室的条码）
        Set<String> collect = allLimsBarcodes.stream().collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(reportUrlCount) && reportUrlCount.size() >= collect.size()) {
            isReport = YesOrNoEnum.YES.getCode();
        }

//        Integer isReport = allReportBarcodes.containsAll(splitBarcodes) ? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode();

        // 调用lims合并报告单
        List<String> reportUrls = sampleResult.getReportUrls();
        tbSample.setOriginalReportUrls(JSONUtil.toJsonStr(reportUrls));


        // 调用实验室合并报告单
        MergeReportRequest mergeReportRequest = new MergeReportRequest();
        mergeReportRequest.setOrgCode(signOrgCode);
        mergeReportRequest.setBarcodes(allLimsBarcodes);
        mergeReportRequest.setReportUrls(reportUrls);
        mergeReportRequest.setIsMergerReport(isReport);
        log.info("调用实验室合并报告单信息，送检机构样本条码号：{}", outBarcode);
        MergeReportDto mergeReportDto = limsHttpUtil.invokeLimsObject(signOrgCode, limsMergeReport, JSONObject.toJSONString(mergeReportRequest), MergeReportDto.class);
        log.info("送检机构样本条码号:{}，合并报告单结束，实验室响应信息：{}", outBarcode, JSONObject.toJSONString(mergeReportDto));

        // 填充合并后的样本报告地址
        sampleResult.setReportUrls(mergeSampleResults(mergeReportDto.getReportUrls(), reportUrls, tbSample.getBarcode()));
        sampleResult.setIsReport(isReport);

        return sampleResult;
    }

    // 多张报告单合并成一张报告单
    public List<String> mergeSampleResults(List<String> newPdfUrls, List<String> oldPdfUrls, String barcode) {

        // 新地址为空，不进行合并，使用原始pdf地址
        if (CollectionUtils.isEmpty(newPdfUrls)) {
            return oldPdfUrls;
        }

        // 判断新地址是否包含旧的地址，如果包含说明没有合并，则不进行url合并
        Optional<String> first = newPdfUrls.stream().filter(e -> oldPdfUrls.contains(e)).findFirst();
        if (first.isPresent()) {
            log.warn("原始PDF url地址包含合并后的pdf地址,说明没进行合并，不进行pdf地址合并！！！外部条码号：{}", barcode);
            return newPdfUrls;
        }

        File tempFile = FileUtil.createTempFile(CompareConstants.PDF_MERGER_DIR, null, null, true);

        // 合并新地址成一个pdf
        List<File> files = new ArrayList<>();
        final PDFMergerUtility merger = new PDFMergerUtility();
        try {
            for (String newPdfUrl : newPdfUrls) {
                merger.addSource(obsUtil.downloadFileByUrl(newPdfUrl));
            }
        } catch (Exception e) {
            log.error("pdf合并成一个url下载失败，返回多个pdf文件！外部条码号：{}", barcode, e);
            return newPdfUrls;
        }

        final PDDocumentInformation information = new PDDocumentInformation();
//        information.setKeywords("");
        merger.setDestinationFileName(tempFile.getAbsolutePath());
        merger.setDestinationDocumentInformation(information);

        try {
            merger.mergeDocuments(MemoryUsageSetting.setupTempFileOnly());
        } catch (Exception e) {
            log.error("pdf合并成一个url合成失败，返回多个pdf文件！外部条码号：{}", barcode, e);
            return newPdfUrls;
        } finally {
            files.forEach(FileUtils::deleteQuietly);
        }

        try {
            // 上传文件到obs
            String objectKey = FileSuffixConstants.UPLOAD_OSS_RESULT + LocalDateTime.now().format(DateTimeFormatter.ofPattern("/yyyy/MM/dd/")) + UUID.randomUUID() + "." + FileTypeEnum.PDF.getType();
            return Collections.singletonList(obsUtil.uploadFile(new FileInputStream(tempFile), objectKey));
        } catch (Exception e) {
            log.error("pdf合并成一个url上传失败，返回多个pdf文件！外部条码号：{}", barcode, e);
            return newPdfUrls;
        } finally {
            FileUtils.deleteQuietly(tempFile);
        }

    }


}