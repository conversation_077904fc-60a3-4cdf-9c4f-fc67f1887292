package com.labway.business.center.result.repository.result;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.business.center.result.constants.DeleteFlagEnum;
import com.labway.business.center.result.persistence.entity.result.SampleGermResult;
import com.labway.business.center.result.persistence.entity.result.SampleMedicineResult;
import com.labway.business.center.result.persistence.mapper.result.SampleMedicineResultMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 *
 *
 * <AUTHOR> on 2024/12/26.
 */
@Repository
public class SampleMedicineResultRepository {

	@Resource
	private SampleMedicineResultMapper sampleMedicineResultMapper;

	/**
	 * 根据样本id查询微生物结果药物
	 * @param sampleBackIds 回传样本
	 * @return 微生物结果药物列表
	 */
	public List<SampleMedicineResult> searchListBySampleBackIds(Collection<Long> sampleBackIds) {
		if (CollectionUtils.isEmpty(sampleBackIds)) {
			return Collections.emptyList();
		}
		return sampleMedicineResultMapper.selectList(Wrappers.lambdaQuery(SampleMedicineResult.class)
				.in(SampleMedicineResult::getSampleBackId, sampleBackIds));
	}

	public void insertSampleMedicineResultBatch(List<SampleMedicineResult> medicineResults) {
		if (CollectionUtils.isEmpty(medicineResults)) {
			return;
		}
		sampleMedicineResultMapper.insertBatchSomeColumn(medicineResults);
	}

	public void deleteSampleMedicineResultBatchBySampleBackIds(List<Long> sampleIds) {
		if (CollectionUtils.isEmpty(sampleIds)) {
			return;
		}
		sampleMedicineResultMapper.update(null, Wrappers.lambdaUpdate(SampleMedicineResult.class).in(SampleMedicineResult::getSampleBackId, sampleIds)
				.set(SampleMedicineResult::getIsDelete, DeleteFlagEnum.DELETED.getCode()));
	}
}
