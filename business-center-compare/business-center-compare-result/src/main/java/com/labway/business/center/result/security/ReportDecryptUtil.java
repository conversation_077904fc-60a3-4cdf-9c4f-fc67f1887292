package com.labway.business.center.result.security;

import cn.hutool.crypto.Mode;
import cn.hutool.crypto.Padding;
import cn.hutool.crypto.symmetric.AES;

/**
 * <AUTHOR>
 * @version 2023/05/09 10:57
 * @description
 **/
public class ReportDecryptUtil {

    public static final String TOKEN_KEY = "Authorization";
    public static final String AUTHORIZATION = "5cd5696287714a2a9fcad6fcd94e5d04";

    public static final AES aes = new AES(Mode.CBC, Padding.ZeroPadding, "AjT3RvPji6FVg5Mp".getBytes(), "8TjBCZKwXSA5LsVQ".getBytes());

    public static String decrypt(String data) {
//        final byte[] decrypt = aes.decrypt("I3oVhUXkxG3LygChfLd6WkATnhfbh08ESJ5plCuItQ43lfVQTMh2Pj/KiIgDQiN1AYfr+KH/gfKpz7UaouZ3llUtbbyTj0kk99t/2WJiF4v/di0Zvcww1sOH51DzIwYf2c3DjqFsyXqgm4vRP6DNj36ET9mHiX6qLPEsyaI1IqrJSJ62iceF5ffCh8NfwsatmotKVMwSY7d8j8lnrRWGUN+PIhyrcHK3yMj/9OAesk2TzSGeMifI3OJiMyIVtvBuEw8CBXe3H5IHdql0UCnd4ao2RiALytj9oT6pAAOqubJk+IVHWMV0Bmbe8ffsy8lO");
        final byte[] decrypt = aes.decrypt(data);
        return new String(decrypt);
    }

    public static String encrypt(String data) {
//        String data = "{\"contactNumber\":\"0\",\"customerCode\":\"216007\",\"customerContact\":\"王煚\",\"customerName\":\"宜兴杨巷镇卫生院\",\"memo\":\"\",\"orgId\":\"21\",\"orgName\":\"常州兰卫医学检验所\",\"outCustomerCode\":\"0\",\"type\":\"送检机构\"}";
        data = data.replaceAll("\\s*|\r|\n|\t", "").trim();
        final String en = aes.encryptBase64(data);
        return en;
    }

    public static void main(String[] args) {
        String encrypt = decrypt("I3oVhUXkxG3LygChfLd6WkATnhfbh08ESJ5plCuItQ49E0D2Bt0rBfOIetNn5w64+eFP9O3wrcvROPg87ekeJ6V+zaDr0NfErw1+CTHnxCqu4+2Et33og2l29zbt+jpT2xZspIUPxfNWPYnUmfvIHvtAyQA/6E4y3Hlp/iQx+x70QGBydNhxoFb46MVmIKoQsZaghVw54tv6cN0uqyhXQ/PXyVeIocfWLmIdESyN+Yl9KsFbDh7GDcdktwfnoK/r+hYjt2TsBUvQk/7CxnOJtWJLyRY7DHpccieW6/bPXJE2ev5UdEsy9zT10I88Lulf");
        System.out.println(encrypt);
    }

}