package com.labway.business.center.result.repository;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.business.center.core.enums.DeleteFlagEnum;
import com.labway.business.center.result.persistence.entity.TbOrgApplySampleMainItemResult;
import com.labway.business.center.result.persistence.mapper.TbOrgApplySampleMainItemResultMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2023/04/25 14:37
 * @description
 **/
@Slf4j
@Repository
@RefreshScope
public class TbOrgApplySampleMainItemResultRepository {

    @Resource
    private TbOrgApplySampleMainItemResultMapper tbOrgApplySampleMainItemResultMapper;


    /**
     * 根据样本id查询已经出报告结果的报告信息
     * @param sampleMainIds
     * @return
     */
    public List<TbOrgApplySampleMainItemResult> queryItemResultByMainIds(List<String> sampleMainIds) {
        if (CollectionUtils.isEmpty(sampleMainIds)){
            return Collections.emptyList();
        }

        return tbOrgApplySampleMainItemResultMapper.selectList(Wrappers.lambdaQuery(TbOrgApplySampleMainItemResult.class)
                .in(TbOrgApplySampleMainItemResult::getMainId,sampleMainIds)
                .eq(TbOrgApplySampleMainItemResult::getDeleteFlag,DeleteFlagEnum.NO_DELETE.getCode())
                .orderByDesc(TbOrgApplySampleMainItemResult::getCreateTime,TbOrgApplySampleMainItemResult::getResultId));
    }

    /**
     * 删除旧的报告结果信息
     * @param deleteMainResultList
     * @return
     */
    public int deleteApplySampleMainItemBatch(List<TbOrgApplySampleMainItemResult> deleteMainResultList) {
        if (CollectionUtils.isEmpty(deleteMainResultList)){
            return 0;
        }

        List<String> resultIds = deleteMainResultList.stream().map(TbOrgApplySampleMainItemResult::getResultId).collect(Collectors.toList());

        return tbOrgApplySampleMainItemResultMapper.delete(Wrappers.lambdaQuery(TbOrgApplySampleMainItemResult.class)
                .in(TbOrgApplySampleMainItemResult::getResultId,resultIds));
    }

    /**
     * 删除旧的报告结果信息
     */
    public int deleteApplySampleMainItemByMainIds(List<String> mainIds) {

        return tbOrgApplySampleMainItemResultMapper.delete(Wrappers.lambdaQuery(TbOrgApplySampleMainItemResult.class)
                .in(TbOrgApplySampleMainItemResult::getMainId, mainIds));
    }

    /**
     * 新增新的报告结果信息-批量
     * @param insertMainResultList
     * @return
     */
    public int insertApplySampleMainItemBatch(List<TbOrgApplySampleMainItemResult> insertMainResultList) {
        if (CollectionUtils.isEmpty(insertMainResultList)){
            return 0;
        }

       return tbOrgApplySampleMainItemResultMapper.insertBatchSomeColumn(insertMainResultList);
    }


    /**
     * 报告结果置空
     */
    public int resetTestResult(List<String> sampleMainIdList,String optUserName) {
        if (CollectionUtils.isEmpty(sampleMainIdList)){
            return 0;
        }

        return tbOrgApplySampleMainItemResultMapper.update(null, Wrappers.lambdaUpdate(TbOrgApplySampleMainItemResult.class)
                .set(TbOrgApplySampleMainItemResult::getTestResult, StringUtils.EMPTY)
                .set(TbOrgApplySampleMainItemResult::getUpdateTime, new Date())
                .set(StringUtils.isNotBlank(optUserName),TbOrgApplySampleMainItemResult::getUpdateBy, optUserName)
                .eq(TbOrgApplySampleMainItemResult::getDeleteFlag, DeleteFlagEnum.NO_DELETE.getCode())
                .in(TbOrgApplySampleMainItemResult::getMainId, sampleMainIdList));
    }

    /**
     * 删除样本预置结果信息
     * @param mainIds
     * @return
     */
    public Integer deleteForRollback(List<String> mainIds) {
        if (CollectionUtils.isEmpty(mainIds)){
            return 0;
        }

        return tbOrgApplySampleMainItemResultMapper.delete(Wrappers.lambdaQuery(TbOrgApplySampleMainItemResult.class)
                .in(TbOrgApplySampleMainItemResult::getMainId,mainIds));
    }


    /**
     * 批量更新样本预置结果信息
     * @param tbOrgApplySampleMainItemResults

     */
    public Integer updateSampleResultBatch(List<TbOrgApplySampleMainItemResult> tbOrgApplySampleMainItemResults) {
        if (CollectionUtils.isEmpty(tbOrgApplySampleMainItemResults)){
            return 0;
        }

        for (TbOrgApplySampleMainItemResult tbOrgApplySampleMainItemResult : tbOrgApplySampleMainItemResults) {
            tbOrgApplySampleMainItemResultMapper.updateById(tbOrgApplySampleMainItemResult);
        }

        return tbOrgApplySampleMainItemResults.size();
    }

}