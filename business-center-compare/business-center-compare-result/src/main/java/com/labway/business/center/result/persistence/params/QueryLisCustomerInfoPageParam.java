package com.labway.business.center.result.persistence.params;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 2023/06/30 20:43
 * @description
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class QueryLisCustomerInfoPageParam implements Serializable {

    // 业务单元编码
    private String orgCode;

    // 客商名称
    private String lisCustomerName;


    // 当前页
    private  int page;

    // 每页显示条数
    private int pageSize;

}