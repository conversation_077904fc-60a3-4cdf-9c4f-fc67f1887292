package com.labway.business.center.result.controller;

import com.labway.business.center.compare.dto.result.LisReportResultDto;
import com.labway.business.center.compare.dto.result.ReceiveLisReportResultDto;
import com.labway.business.center.compare.request.result.LisReportResultRequest;
import com.labway.business.center.compare.request.result.TestMergePdfRequest;
import com.labway.business.center.compare.service.SyncReportResultMiddleService;
import com.labway.business.center.compare.service.SyncReportResultService;
import com.labway.business.center.compare.service.result.SampleResultWriterService;
import com.labway.business.center.core.enums.Region;
import com.labway.business.center.result.service.impl.BaseSyncReportResultMiddleService;
import com.labway.business.center.result.service.impl.SyncReportResultServiceImpl;
import com.labway.report.common.dto.sample.ReportPrintQueryDto;
import com.swak.frame.dto.Response;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;

import static com.labway.business.center.result.constants.ResponseCode.*;
/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/result")
public class ResultTestController {

    @Resource
    private SyncReportResultService syncReportResultService;

	@Resource
	private SampleResultWriterService sampleResultWriterService;


    /**
     * 接口 接收样本报告结果 --- Lis推
     */
    @PostMapping("/receive")
    public Response<?> receiveResult(@RequestBody @Valid LisReportResultRequest reportResultRequest){
        // request body取参
        String orgCode = reportResultRequest.getOrgCode();
        String barcode = reportResultRequest.getBarcode();
        String hspOrgCode = reportResultRequest.getHspOrgCode();
        String auditDateStr = reportResultRequest.getAuditDate();
        Date auditTimeFromDate = null;

        if (StringUtils.isNotBlank(auditDateStr)){
            auditTimeFromDate = Date.from(LocalDateTime.parse(auditDateStr, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")).atZone(ZoneId.systemDefault()).toInstant());
        }

        Response check = reportResultRequest.check();
        if (!check.isSuccess()){
            return check;
        }

        //封装requestDTO
        ReceiveLisReportResultDto receiveLisReportResultDto = ReceiveLisReportResultDto.builder()
                        .hspOrgCode(hspOrgCode)
                        .hspOrgName(reportResultRequest.getHspOrgName())
                        .orgCode(orgCode)
                        .orgName(reportResultRequest.getOrgName())
                        .barcode(barcode)
                        .result(reportResultRequest.getResult())
                        .reportPdf(reportResultRequest.getReportPdf())
                        .reportImg(reportResultRequest.getReportImg())
                        .imgType(reportResultRequest.getImgType())
                        .resultRemark(reportResultRequest.getResultRemark())
                        .auditDate(ObjectUtils.defaultIfNull(auditTimeFromDate ,new Date()))
                        .isReport(reportResultRequest.getIsReport()).build();

        return syncReportResultService.syncReportResultFromLis(receiveLisReportResultDto);
    }

    /**
     * 手动同步业务中台样本的报告结果 --- 从报告平台获取
     */
    @PostMapping("/syncReportResult")
    public Response<?> syncReportResult(@RequestBody ReportPrintQueryDto queryDto){
        return syncReportResultService.syncReportResult(queryDto);
    }

    /**
     * 手动同步中间库的样本结果 --- 待完善。。。
     */
    @PostMapping("/syncReportResultMiddle")
    public Response<?> syncReportResultMiddle(@RequestParam(required = false) String region, @RequestBody List<String> barcodes){
        return BaseSyncReportResultMiddleService.get(Region.of(region)).pushReport2MiddleByBarcodes(barcodes);
    }

    /**
     * 测试合并pdf报告单
     */
    @PostMapping("/testConvertToPdf")
    public Response<?> testMergePdf(@RequestBody TestMergePdfRequest testMergePdfRequest){
        return Response.success(((SyncReportResultServiceImpl)syncReportResultService).mergeSampleResults(testMergePdfRequest.getNewUrls(), testMergePdfRequest.getOldUrls(), StringUtils.EMPTY));
    }

	/**
	 * 手动同步报告平台的结果 -- 新
	 */
	@PostMapping("/sync")
	public Response<String> sync(@RequestBody ReportPrintQueryDto queryDto) {
		// 老结果同步
		sampleResultWriterService.handlerOldSync(queryDto, queryDto.getBarcode());

		return sampleResultWriterService.syncReportResultNew(queryDto);
	}
}
