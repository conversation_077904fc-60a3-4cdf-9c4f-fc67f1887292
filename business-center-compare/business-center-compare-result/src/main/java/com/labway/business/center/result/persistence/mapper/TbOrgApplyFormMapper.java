package com.labway.business.center.result.persistence.mapper;

import com.labway.business.center.result.persistence.entity.TbOrgApplyForm;
import com.labway.business.center.core.injector.ExtBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 外送申请单信息(TbOrgApplyForm)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-05-05 10:13:39
 */
public interface TbOrgApplyFormMapper extends ExtBaseMapper<TbOrgApplyForm> {

/**
* 批量新增数据（MyBatis原生foreach方法）
*
* @param entities List<TbOrgApplyForm> 实例对象列表
* @return 影响行数
*/
int insertBatch(@Param("entities") List<TbOrgApplyForm> entities);

/**
* 批量新增或按主键更新数据（MyBatis原生foreach方法）
*
* @param entities List<TbOrgApplyForm> 实例对象列表
* @return 影响行数
* @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
*/
int insertOrUpdateBatch(@Param("entities") List<TbOrgApplyForm> entities);

}

