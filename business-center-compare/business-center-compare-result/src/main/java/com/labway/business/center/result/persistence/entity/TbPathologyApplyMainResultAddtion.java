package com.labway.business.center.result.persistence.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * (TbPathologyApplyMainResultAddtion)表实体类
 *
 * <AUTHOR>
 * @since 2023-07-13 09:11:55
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@SuppressWarnings("serial")
public class TbPathologyApplyMainResultAddtion extends Model<TbPathologyApplyMainResultAddtion> {
    //病理补充结果表 id
    @TableId(type = IdType.INPUT)
    private String addtionId;
    //检验结果表id
    private String resultId;
    //补充报告序号
    private Integer fBcBgxh;
    //补充报告医生
    private String fBcBgys;
    //补充审核医生
    private String fBcShys;
    //补充报告日期
    private String fBcBgrq;
    //补充诊断
    private String fBczd;
    //补充报告状态
    private String fBcBgzt;
    //补充报告复诊医生
    private String fBcFzys;
    //补充报告打印状态
    private String fBcDyzt;
    //补充报告签收本打印状态
    private String fBcQsbDyzt;
    //补充报告报告位置
    private String fBcBgwz;
    //补充报告报告位置确认时间
    private String fBcBgwzQrsj;
    //补充报告报告位置确认操作员
    private String fBcBgwzQrczy;
    //补充报告备注
    private String fBcBz;
    //补充报告审核时间
    private String fBcSpare5;
    //补充报告镜下所见
    private String fBcJxsj;
    //补充报告特殊检查
    private String fBcTsjc;
    //补充报告报告格式
    private String fBcBggs;
    //补充报告发布时间
    private String fBcFbsj;
    // 补充报告发布医生
    private String fBcFbys;

//    @TableLogic(value = "0",delval = "1")
    private Integer deleteFlag;

    // 创建时间
    private Date createTime;
    // 送检机构编码
    private String hspOrgCode;
    // 条码号
    private String barcode;
    // 病理号
    private String fBcBlh;

    // 更新时间
    private Date updateTime;

}

