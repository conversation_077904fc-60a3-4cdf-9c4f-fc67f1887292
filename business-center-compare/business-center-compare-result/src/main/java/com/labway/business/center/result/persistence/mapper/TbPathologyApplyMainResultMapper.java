package com.labway.business.center.result.persistence.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.labway.business.center.result.persistence.entity.TbPathologyApplyMainResult;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * (TbPathologyApplyMainResult)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-07-13 09:11:45
 */
public interface TbPathologyApplyMainResultMapper extends BaseMapper<TbPathologyApplyMainResult> {

/**
* 批量新增数据（MyBatis原生foreach方法）
*
* @param entities List<TbPathologyApplyMainResult> 实例对象列表
* @return 影响行数
*/
int insertBatch(@Param("entities") List<TbPathologyApplyMainResult> entities);

/**
* 批量新增或按主键更新数据（MyBatis原生foreach方法）
*
* @param entities List<TbPathologyApplyMainResult> 实例对象列表
* @return 影响行数
* @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
*/
int insertOrUpdateBatch(@Param("entities") List<TbPathologyApplyMainResult> entities);

}

