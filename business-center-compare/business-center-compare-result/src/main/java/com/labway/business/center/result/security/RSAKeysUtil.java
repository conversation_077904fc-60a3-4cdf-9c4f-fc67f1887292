 package com.labway.business.center.result.security;

 import java.security.Key;
 import java.security.KeyPair;
 import java.security.KeyPairGenerator;
 import java.security.interfaces.RSAPrivateKey;
 import java.security.interfaces.RSAPublicKey;
 import java.util.Base64;
 import java.util.HashMap;
 import java.util.Map;
/**
 * 算法工具类
 * <AUTHOR>
 * @date 2023/03/28
 */
 public class RSAKeysUtil {
  
     public static final String KEY_ALGORITHM = "RSA";
     public static final String SIGNATURE_ALGORITHM = "MD5withRSA";
     private static final String PUBLIC_KEY = "RSAPublicKey";
     private static final String PRIVATE_KEY = "RSAPrivateKey";
  
     public static void main(String[] args) {
         Map<String, Object> keyMap;
         try {
             keyMap = initKey();
             String publicKey = getPublicKey(keyMap);
             System.out.println(publicKey);
             String privateKey = getPrivateKey(keyMap);
             System.out.println(privateKey);
         } catch (Exception e) {
             e.printStackTrace();
         }
     }
  
     public static String getPublicKey(Map<String, Object> keyMap) throws Exception {
         Key key = (Key) keyMap.get(PUBLIC_KEY);
         byte[] publicKey = key.getEncoded();
         return encryptBASE64(key.getEncoded());
     }
  
     public static String getPrivateKey(Map<String, Object> keyMap) throws Exception {
         Key key = (Key) keyMap.get(PRIVATE_KEY);
         byte[] privateKey = key.getEncoded();
         return encryptBASE64(key.getEncoded());
     }
  
     public static byte[] decryptBASE64(String key) throws Exception {
         return Base64.getDecoder().decode(key);
     }
  
     public static String encryptBASE64(byte[] key) throws Exception {
         return Base64.getEncoder().encodeToString(key);
     }
  
     public static Map<String, Object> initKey() throws Exception {
         KeyPairGenerator keyPairGen = KeyPairGenerator.getInstance(KEY_ALGORITHM);
         keyPairGen.initialize(1024);
         KeyPair keyPair = keyPairGen.generateKeyPair();
         RSAPublicKey publicKey = (RSAPublicKey) keyPair.getPublic();
         RSAPrivateKey privateKey = (RSAPrivateKey) keyPair.getPrivate();
         Map<String, Object> keyMap = new HashMap<>(2);
         keyMap.put(PUBLIC_KEY, publicKey);
         keyMap.put(PRIVATE_KEY, privateKey);
         return keyMap;
     }
 }