package com.labway.business.center.result.config;


import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.shardingsphere.api.sharding.standard.PreciseShardingAlgorithm;
import org.apache.shardingsphere.api.sharding.standard.PreciseShardingValue;

import java.util.Collection;

/**
 * <AUTHOR>
 */
@Slf4j
public class TableShardingAlgorithm implements PreciseShardingAlgorithm<String> {

    @Override
    public String doSharding(Collection<String> collection, PreciseShardingValue<String> preciseShardingValue) {

        //取最后两位进行分库分表
        String num = StringUtils.substring(preciseShardingValue.getValue(), preciseShardingValue.getValue().length() - 2);

        // 如果不是数字
        if (!NumberUtils.isParsable(num)) {

            // 转成 ASCII 然后取数字的最后一位
            // 如果是 A 那就是 65 那就取 5
            // 如果是 h 那就是 104 那就取 4
            final int a = num.charAt(0);
            final int b = num.charAt(1);

            // 如果 a = A = 65 ， b = h = 104
            // 那么就是 num 就是 54
            num = StringUtils.right(String.valueOf(a), 1) + StringUtils.right(String.valueOf(b), 1);

        }

        final String table = preciseShardingValue.getLogicTableName() + "_" + num;

        if (collection.contains(table)) {
            return table;
        }

        throw new IllegalArgumentException("未知表名！");
    }
}
