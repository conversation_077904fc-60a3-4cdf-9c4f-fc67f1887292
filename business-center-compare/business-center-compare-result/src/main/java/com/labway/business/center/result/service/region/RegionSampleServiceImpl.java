package com.labway.business.center.result.service.region;

import cn.hutool.core.collection.CollectionUtil;
import com.labway.business.center.compare.dto.TbOrgApplySampleMainDTO;
import com.labway.business.center.core.util.BeanCopyUtils;
import com.labway.business.center.result.persistence.entity.TbOrgApplySampleMain;
import com.labway.business.center.result.repository.TbOrgApplySampleMainRepository;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class RegionSampleServiceImpl implements IRegionSampleService{

    @Resource
    private TbOrgApplySampleMainRepository sampleMainRepository;

    @Override
    public List<TbOrgApplySampleMainDTO> querySamplesStatus(String hspOrgCode, List<String> barcodes) {
        List<TbOrgApplySampleMain> sampleMains = sampleMainRepository.querySamplesStatus(hspOrgCode,barcodes);
        if (CollectionUtil.isNotEmpty(sampleMains)){
            return BeanCopyUtils.copyList(sampleMains,TbOrgApplySampleMainDTO.class);
        }
        return List.of();
    }
}
