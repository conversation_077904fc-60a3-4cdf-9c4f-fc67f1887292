package com.labway.business.center.result.persistence.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.util.Date;

/**
 * 机构（业务单元）和客商的关联关系表(TbOrgCustomerRelation)表实体类
 *
 * <AUTHOR>
 * @since 2023-04-19 17:21:15
 */
@SuppressWarnings("serial")
public class TbOrgCustomerRelation extends Model<TbOrgCustomerRelation> {
    //关系表主键
    @TableId(type = IdType.INPUT)
    private String relationId;
    //机构（业务单元）编码-关联业务单元表的org_id
    private String orgCode;
    //机构（业务单元）名称
    private String orgName;
    //客商编码
    private String customerCode;
    //客商名称
    private String customerName;
    //创建人id
    private String createBy;
    //创建时间
    private Date createTime;
    //更新人id
    private String updateBy;
    //跟新时间
    private Date updateTime;
    //删除标识 0删除 1未删除
    @TableLogic(value = "0",delval = "1")
    private Integer deleteFlag;


    public String getRelationId() {
        return relationId;
    }

    public void setRelationId(String relationId) {
        this.relationId = relationId;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getDeleteFlag() {
        return deleteFlag;
    }

    public void setDeleteFlag(Integer deleteFlag) {
        this.deleteFlag = deleteFlag;
    }


    }

