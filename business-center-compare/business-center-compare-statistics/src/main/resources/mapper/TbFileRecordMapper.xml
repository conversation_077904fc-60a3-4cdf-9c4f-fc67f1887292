<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.business.center.statistics.persistence.mapper.TbFileRecordMapper">

    <resultMap type="com.labway.business.center.statistics.persistence.entity.TbFileRecord" id="TbFileRecordMap">
        <result property="recordId" column="record_id" jdbcType="VARCHAR"/>
        <result property="recordType" column="record_type" jdbcType="INTEGER"/>
        <result property="recordStatus" column="record_status" jdbcType="INTEGER"/>
        <result property="fileUrls" column="record_urls" jdbcType="VARCHAR"/>
        <result property="recordTime" column="record_time" jdbcType="INTEGER"/>
        <result property="failReason" column="fail_reason" jdbcType="VARCHAR"/>
        <result property="orgCode" column="org_code" jdbcType="VARCHAR"/>
        <result property="orgName" column="org_name" jdbcType="VARCHAR"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="deleteFlag" column="delete_flag" jdbcType="INTEGER"/>
        <result property="recordKeyword" column="record_keyword" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="recordId" useGeneratedKeys="true">
        insert into business-statistics.tb_file_record(record_type, record_status, record_urls, record_time, fail_reason, org_code, org_name, create_by, create_time, update_by, update_time, delete_flag, record_keyword)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.recordType}, #{entity.recordStatus}, #{entity.recordUrls}, #{entity.recordTime}, #{entity.failReason}, #{entity.orgCode}, #{entity.orgName}, #{entity.createBy}, #{entity.createTime}, #{entity.updateBy}, #{entity.updateTime}, #{entity.deleteFlag}, #{entity.recordKeyword})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="recordId" useGeneratedKeys="true">
        insert into business-statistics.tb_file_record(record_type, record_status, record_urls, record_time, fail_reason, org_code, org_name, create_by, create_time, update_by, update_time, delete_flag, record_keyword)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.recordType}, #{entity.recordStatus}, #{entity.recordUrls}, #{entity.recordTime}, #{entity.failReason}, #{entity.orgCode}, #{entity.orgName}, #{entity.createBy}, #{entity.createTime}, #{entity.updateBy}, #{entity.updateTime}, #{entity.deleteFlag}, #{entity.recordKeyword})
        </foreach>
        on duplicate key update
         record_type = values(record_type) , record_status = values(record_status) , record_urls = values(record_urls) , record_time = values(record_time) , fail_reason = values(fail_reason) , org_code = values(org_code) , org_name = values(org_name) , create_by = values(create_by) , create_time = values(create_time) , update_by = values(update_by) , update_time = values(update_time) , delete_flag = values(delete_flag) , record_keyword = values(record_keyword)     </insert>

</mapper>

