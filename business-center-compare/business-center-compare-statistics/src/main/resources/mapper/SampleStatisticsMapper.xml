<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.business.center.statistics.persistence.mapper.SampleStatisticsMapper">

    <resultMap type="com.labway.business.center.statistics.persistence.entity.SampleStatistics" id="SampleStatisticsMap">
        <result property="statisticsId" column="statistics_id" jdbcType="VARCHAR"/>
        <result property="sampleCode" column="sample_code" jdbcType="VARCHAR"/>
        <result property="patientName" column="patient_name" jdbcType="VARCHAR"/>
        <result property="patientSex" column="patient_sex" jdbcType="INTEGER"/>
        <result property="patientAge" column="patient_age" jdbcType="INTEGER"/>
        <result property="ageUnit" column="age_unit" jdbcType="VARCHAR"/>
        <result property="patientBirthday" column="patient_birthday" jdbcType="TIMESTAMP"/>
        <result property="patientRegistrationNo" column="patient_registration_no" jdbcType="VARCHAR"/>
        <result property="treatmentCard" column="treatment_card" jdbcType="VARCHAR"/>
        <result property="treatmentType" column="treatment_type" jdbcType="VARCHAR"/>
        <result property="bedNo" column="bed_no" jdbcType="VARCHAR"/>
        <result property="dept" column="dept" jdbcType="VARCHAR"/>
        <result property="barcode" column="barcode" jdbcType="VARCHAR"/>
        <result property="outBarcode" column="out_barcode" jdbcType="VARCHAR"/>
        <result property="hisBarcode" column="his_barcode" jdbcType="VARCHAR"/>
        <result property="sampleCreateDate" column="sample_create_date" jdbcType="TIMESTAMP"/>
        <result property="sendTime" column="send_time" jdbcType="TIMESTAMP"/>
        <result property="sendDoctorCode" column="send_doctor_code" jdbcType="VARCHAR"/>
        <result property="sendDoctorName" column="send_doctor_name" jdbcType="VARCHAR"/>
        <result property="testTime" column="test_time" jdbcType="TIMESTAMP"/>
        <result property="testUserCode" column="test_user_code" jdbcType="VARCHAR"/>
        <result property="testUserName" column="test_user_name" jdbcType="VARCHAR"/>
        <result property="auditTime" column="audit_time" jdbcType="TIMESTAMP"/>
        <result property="auditUserCode" column="audit_user_code" jdbcType="VARCHAR"/>
        <result property="auditUserName" column="audit_user_name" jdbcType="VARCHAR"/>
        <result property="testItemCode" column="test_item_code" jdbcType="VARCHAR"/>
        <result property="testItemName" column="test_item_name" jdbcType="VARCHAR"/>
        <result property="outTestItemCode" column="out_test_item_code" jdbcType="VARCHAR"/>
        <result property="outTestItemName" column="out_test_item_name" jdbcType="VARCHAR"/>
        <result property="itemCount" column="item_count" jdbcType="INTEGER"/>
        <result property="standardPrice" column="standard_price" jdbcType="NUMERIC"/>
        <result property="totalAmount" column="total_amount" jdbcType="NUMERIC"/>
        <result property="settlementAmount" column="settlement_amount" jdbcType="NUMERIC"/>
        <result property="settlementTypeCode" column="settlement_type_code" jdbcType="VARCHAR"/>
        <result property="settlementTypeName" column="settlement_type_name" jdbcType="VARCHAR"/>
        <result property="settlementRemark" column="settlement_remark" jdbcType="VARCHAR"/>
        <result property="discount" column="discount" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="deleteFlag" column="delete_flag" jdbcType="INTEGER"/>
        <result property="orgCode" column="org_code" jdbcType="VARCHAR"/>
        <result property="orgName" column="org_name" jdbcType="VARCHAR"/>
        <result property="orgType" column="org_type" jdbcType="INTEGER"/>
        <result property="originalOrgCode" column="original_org_code" jdbcType="VARCHAR"/>
        <result property="originalOrgName" column="original_org_name" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="statisticsId" useGeneratedKeys="true">
        insert into sample_statistics(statistics_id,sample_code, patient_name, patient_sex, patient_age, age_unit, patient_birthday,
        patient_registration_no, treatment_card, treatment_type, bed_no, dept, barcode, out_barcode, his_barcode, sample_create_date,
        send_time, send_doctor_code, send_doctor_name, test_time, test_user_code, test_user_name, audit_time, audit_user_code, audit_user_name,
        test_item_code, test_item_name, out_test_item_code, out_test_item_name, item_count, standard_price, total_amount, settlement_amount,
        settlement_type_code, settlement_type_name, settlement_remark, discount, create_time, create_by, update_time, update_by, delete_flag,
        org_code, org_name, org_type, original_org_code, original_org_name, test_type_code, test_type_name, his_serial_no, his_apply_time)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.statisticsId}, #{entity.sampleCode}, #{entity.patientName}, #{entity.patientSex}, #{entity.patientAge}, #{entity.ageUnit}, #{entity.patientBirthday}, #{entity.patientRegistrationNo}, #{entity.treatmentCard}, #{entity.treatmentType}, #{entity.bedNo}, #{entity.dept}, #{entity.barcode}, #{entity.outBarcode}, #{entity.hisBarcode}, #{entity.sampleCreateDate}, #{entity.sendTime}, #{entity.sendDoctorCode}, #{entity.sendDoctorName}, #{entity.testTime}, #{entity.testUserCode}, #{entity.testUserName}, #{entity.auditTime}, #{entity.auditUserCode}, #{entity.auditUserName}, #{entity.testItemCode}, #{entity.testItemName}, #{entity.outTestItemCode}, #{entity.outTestItemName}, #{entity.itemCount}, #{entity.standardPrice}, #{entity.totalAmount},
            #{entity.settlementAmount}, #{entity.settlementTypeCode}, #{entity.settlementTypeName}, #{entity.settlementRemark},
            #{entity.discount}, #{entity.createTime}, #{entity.createBy}, #{entity.updateTime}, #{entity.updateBy},
            #{entity.deleteFlag}, #{entity.orgCode}, #{entity.orgName}, #{entity.orgType},
            #{entity.originalOrgCode}, #{entity.originalOrgName}, #{entity.testTypeCode}, #{entity.testTypeName}, #{entity.hisSerialNo}, #{entity.hisApplyTime})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="statisticsId" useGeneratedKeys="true">
        insert into business-statistics.sample_statistics_00(sample_code, patient_name, patient_sex, patient_age, age_unit, patient_birthday, patient_registration_no, treatment_card, treatment_type, bed_no, dept, barcode, out_barcode, his_barcode, sample_create_date, send_time, send_doctor_code, send_doctor_name, test_time, test_user_code, test_user_name, audit_time, audit_user_code, audit_user_name, test_item_code, test_item_name, out_test_item_code, out_test_item_name, item_count, standard_price, total_amount, settlement_amount, settlement_type_code, settlement_type_name, settlement_remark, discount, create_time, create_by, update_time, update_by, delete_flag)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.sampleCode}, #{entity.patientName}, #{entity.patientSex}, #{entity.patientAge}, #{entity.ageUnit}, #{entity.patientBirthday}, #{entity.patientRegistrationNo}, #{entity.treatmentCard}, #{entity.treatmentType}, #{entity.bedNo}, #{entity.dept}, #{entity.barcode}, #{entity.outBarcode}, #{entity.hisBarcode}, #{entity.sampleCreateDate}, #{entity.sendTime}, #{entity.sendDoctorCode}, #{entity.sendDoctorName}, #{entity.testTime}, #{entity.testUserCode}, #{entity.testUserName}, #{entity.auditTime}, #{entity.auditUserCode}, #{entity.auditUserName}, #{entity.testItemCode}, #{entity.testItemName}, #{entity.outTestItemCode}, #{entity.outTestItemName}, #{entity.itemCount}, #{entity.standardPrice}, #{entity.totalAmount}, #{entity.settlementAmount}, #{entity.settlementTypeCode}, #{entity.settlementTypeName}, #{entity.settlementRemark}, #{entity.discount}, #{entity.createTime}, #{entity.createBy}, #{entity.updateTime}, #{entity.updateBy}, #{entity.deleteFlag})
        </foreach>
        on duplicate key update
         sample_code = values(sample_code) , patient_name = values(patient_name) , patient_sex = values(patient_sex) , patient_age = values(patient_age) , age_unit = values(age_unit) , patient_birthday = values(patient_birthday) , patient_registration_no = values(patient_registration_no) , treatment_card = values(treatment_card) , treatment_type = values(treatment_type) , bed_no = values(bed_no) , dept = values(dept) , barcode = values(barcode) , out_barcode = values(out_barcode) , his_barcode = values(his_barcode) , sample_create_date = values(sample_create_date) , send_time = values(send_time) , send_doctor_code = values(send_doctor_code) , send_doctor_name = values(send_doctor_name) , test_time = values(test_time) , test_user_code = values(test_user_code) , test_user_name = values(test_user_name) , audit_time = values(audit_time) , audit_user_code = values(audit_user_code) , audit_user_name = values(audit_user_name) , test_item_code = values(test_item_code) , test_item_name = values(test_item_name) , out_test_item_code = values(out_test_item_code) , out_test_item_name = values(out_test_item_name) , item_count = values(item_count) , standard_price = values(standard_price) , total_amount = values(total_amount) , settlement_amount = values(settlement_amount) , settlement_type_code = values(settlement_type_code) , settlement_type_name = values(settlement_type_name) , settlement_remark = values(settlement_remark) , discount = values(discount) , create_time = values(create_time) , create_by = values(create_by) , update_time = values(update_time) , update_by = values(update_by) , delete_flag = values(delete_flag)     </insert>

</mapper>

