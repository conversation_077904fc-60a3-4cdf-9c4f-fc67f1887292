package com.labway.business.center.statistics.constant;

/**
 * <AUTHOR>
 */

public enum RecordKeywordEnum {
    SAMPLE_IMPORT("SAMPLE_IMPORT", "样本统计导入"),
    SAMPLE_EXPORT("SAMPLE_EXPORT", "样本统计导出");


    String value;
    String desc;

    RecordKeywordEnum(String code, String desc) {
        this.value = code;
        this.desc = desc;
    }
    public String getValue() {
        return value;
    }
    public String getDesc() {
        return desc;
    }
}
