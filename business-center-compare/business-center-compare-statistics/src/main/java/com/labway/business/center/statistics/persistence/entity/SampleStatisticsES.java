package com.labway.business.center.statistics.persistence.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.DateFormat;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 样本
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Document(indexName = "sample-statistics")
public class SampleStatisticsES implements Serializable {

    //样本统计表id-数据库id
    @Field(type = FieldType.Keyword)
    private String statisticsId;
    //样本编号-全局唯一
    @Id
    @Field(type = FieldType.Keyword)
    private String sampleCode;
    //病人姓名
    @Field(type = FieldType.Keyword)
    private String patientName;
    //病人性别 0未知 1男 2女
    @Field(type = FieldType.Integer)
    private Integer patientSex;
    //病人年龄
    @Field(type = FieldType.Integer)
    private Integer patientAge;
    //年龄单位
    @Field(type = FieldType.Keyword)
    private String ageUnit;
    //病人生日
    @Field(type = FieldType.Date, format = DateFormat.date_optional_time)
    private Date patientBirthday;
    //门诊/住院登记号
    @Field(type = FieldType.Keyword)
    private String patientRegistrationNo;
    //就诊卡号
    @Field(type = FieldType.Keyword)
    private String treatmentCard;
    //就诊类型 门诊/住院等
    @Field(type = FieldType.Keyword)
    private String treatmentType;
    //床号
    @Field(type = FieldType.Keyword)
    private String bedNo;
    //样本科室
    @Field(type = FieldType.Keyword)
    private String dept;
    //样本条码号（本系统条码号）
    @Field(type = FieldType.Keyword)
    private String barcode;
    //外部条码号（送检机构的条码号）
    @Field(type = FieldType.Keyword)
    private String outBarcode;
    //His条码号（一般情况下同out_barcode）
    @Field(type = FieldType.Keyword)
    private String hisBarcode;
    //样本创建时间（三方系统的创建时间）
    @Field(type = FieldType.Date, format = DateFormat.date_optional_time)
    private Date sampleCreateDate;
    //送检时间
    @Field(type = FieldType.Date, format = DateFormat.date_optional_time)
    private Date sendTime;
    //送检医生
    @Field(type = FieldType.Keyword)
    private String sendDoctorCode;
    //送检医生名称
    @Field(type = FieldType.Keyword)
    private String sendDoctorName;
    //检验时间
    @Field(type = FieldType.Date, format = DateFormat.date_optional_time)
    private Date testTime;
    //检验人编码
    @Field(type = FieldType.Keyword)
    private String testUserCode;
    //检验人名称
    @Field(type = FieldType.Keyword)
    private String testUserName;
    //审核时间
    @Field(type = FieldType.Date, format = DateFormat.date_optional_time)
    private Date auditTime;
    //审核人编码
    @Field(type = FieldType.Keyword)
    private String auditUserCode;
    //审核人名称
    @Field(type = FieldType.Keyword)
    private String auditUserName;
    //检验项目编码
    @Field(type = FieldType.Keyword)
    private String testItemCode;
    //检验项目名称
    @Field(type = FieldType.Keyword)
    private String testItemName;
    //外部项目编码
    @Field(type = FieldType.Keyword)
    private String outTestItemCode;
    //外部项目名称
    @Field(type = FieldType.Keyword)
    private String outTestItemName;
    //数量
    @Field(type = FieldType.Integer)
    private Integer itemCount;
    //标准单价
    @Field(type = FieldType.Keyword)
    private BigDecimal standardPrice;
    //合计金额
    @Field(type = FieldType.Keyword)
    private BigDecimal totalAmount;
    //结算金额
    @Field(type = FieldType.Keyword)
    private BigDecimal settlementAmount;
    //结算类型编码
    @Field(type = FieldType.Keyword)
    private String settlementTypeCode;
    //结算类型名称
    @Field(type = FieldType.Keyword)
    private String settlementTypeName;
    //结算备注
    @Field(type = FieldType.Text)
    private String settlementRemark;
    //折扣
    @Field(type = FieldType.Keyword)
    private String discount;
    //创建时间
    @Field(type = FieldType.Date, format = DateFormat.date_optional_time)
    private Date createTime;
    //创建人
    @Field(type = FieldType.Keyword)
    private String createBy;
    //更新时间
    @Field(type = FieldType.Date, format = DateFormat.date_optional_time)
    private Date updateTime;
    //更新人
    @Field(type = FieldType.Keyword)
    private String updateBy;
    //删除标识 0未删除 1删除
    @Field(type = FieldType.Integer)
    private Integer deleteFlag;

    // 机构编码（推送样本的机构）
    @Field(type = FieldType.Keyword)
    private String orgCode;
    // 机构名称（推送样本的机构）
    @Field(type = FieldType.Keyword)
    private String orgName;
    // 机构类型 1,lims 2,lis
    @Field(type = FieldType.Keyword)
    private Integer orgType;
    // 原始送检机构编码
    @Field(type = FieldType.Keyword)
    private String originalOrgCode;
    // 原始送检机构名称
    @Field(type = FieldType.Keyword)
    private String originalOrgName;
	// 检验类别编码
	@Field(type = FieldType.Keyword)
	private String testTypeCode;
	// 检验类别名称
	@Field(type = FieldType.Keyword)
	private String testTypeName;

    /**
     * his流水号
     */
    @Field(type = FieldType.Keyword)
    private String hisSerialNo;

    /**
     * his开单时间  yyyy-MM-dd HH:mm:ss
     */
    @Field(type = FieldType.Date, format = DateFormat.date_optional_time)
    private Date hisApplyTime;


}

