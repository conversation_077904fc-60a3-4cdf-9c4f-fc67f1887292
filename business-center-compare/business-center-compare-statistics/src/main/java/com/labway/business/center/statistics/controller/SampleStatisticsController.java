package com.labway.business.center.statistics.controller;


import com.labway.business.center.compare.dto.statistics.QueryStatisticsSampleESDto;
import com.labway.business.center.compare.dto.statistics.QueryStatisticsSampleTotalAmountDto;
import com.labway.business.center.compare.request.statistics.QueryStatisticsSampleESRequest;
import com.labway.business.center.compare.request.statistics.SampleStatisticsRequest;
import com.labway.business.center.compare.request.statistics.SaveStatisticsSampleRequest;
import com.labway.business.center.core.enums.OperationTypeEnum;
import com.labway.business.center.core.enums.SystemModuleEmun;
import com.labway.business.center.statistics.annotation.LogAnnotation;
import com.labway.business.center.statistics.service.SampleStatisticsService;
import com.swak.frame.dto.Response;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

/**
 * (SampleStatistics00)表控制层
 *
 * <AUTHOR>
 * @since 2024-04-24 10:10:55
 */
@RestController
@RequestMapping("/statistics")
public class SampleStatisticsController {
    /**
     * 服务对象
     */
    @Resource
    private SampleStatisticsService sampleStatisticsService;


    /**
     * 保存样本统计数据
     */

    @PostMapping("/saveStatisticsSample")
//    @LogAnnotation(value = "保存样本统计数据", OperationType = OperationTypeEnum.SAVE, SystemModule = SystemModuleEmun.STATISTICS_SERVER)
    public Response<?> saveStatisticsSample(@RequestBody @Valid SaveStatisticsSampleRequest statisticsRequest){
        return sampleStatisticsService.saveStatisticsSample(statisticsRequest);
    }

    /**
     * 查询统计样本数据
     */
    @PostMapping("/queryStatisticsSampleES")
//    @LogAnnotation(value = "查询统计样本数据", OperationType = OperationTypeEnum.QUERY, SystemModule = SystemModuleEmun.STATISTICS_SERVER)
    public Response<QueryStatisticsSampleESDto> queryStatisticsSampleES(@RequestBody @Valid QueryStatisticsSampleESRequest statisticsRequest){
        return sampleStatisticsService.queryStatisticsSampleES(statisticsRequest);
    }

    /**
     * 查询统计样本金额总数
     */
    @PostMapping("/queryStatisticsSampleTotalAmount")
//    @LogAnnotation(value = "查询统计样本金额总数", OperationType = OperationTypeEnum.QUERY, SystemModule = SystemModuleEmun.STATISTICS_SERVER)
    public Response<QueryStatisticsSampleTotalAmountDto> queryStatisticsSampleTotalAmount(@RequestBody @Valid QueryStatisticsSampleESRequest statisticsRequest){
        return sampleStatisticsService.queryStatisticsSampleTotalAmount(statisticsRequest);
    }

    /**
     * 导出统计样本数据
     */
    @PostMapping("/exportStatisticsSampleES")
    @LogAnnotation(value = "查询统计样本金额总数", OperationType = OperationTypeEnum.EXPORT, SystemModule = SystemModuleEmun.STATISTICS_SERVER)
    public Response<?> exportStatisticsSampleES(@RequestBody @Valid QueryStatisticsSampleESRequest statisticsRequest, HttpServletResponse response) throws IOException {
        return sampleStatisticsService.exportStatisticsSampleES(statisticsRequest,response);
    }

    ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

    /**
     * 测试查询
     */
    @PostMapping("/queryTest")
    public Response<?> queryTest(@RequestBody SampleStatisticsRequest statisticsRequest){
        return sampleStatisticsService.queryTest(statisticsRequest);
    }

    /**
     * 测试新增
     */
    @PostMapping("/savaTest")
    public Response<?> savaTest(@RequestBody SampleStatisticsRequest statisticsRequest){
        return sampleStatisticsService.savaTest(statisticsRequest);
    }

    /**
     * 造测试数据
     */
    @PostMapping("/savaTestData")
    public Response<?> savaTestData(@RequestParam String orgCode,
                                    @RequestParam String orgName,
                                    @RequestParam String barcode,
                                    @RequestParam String testItemCode,
                                    @RequestParam String testItemName,
                                    @RequestParam(required = false,defaultValue = "") String outBarcode,
                                    @RequestParam(required = false,defaultValue = "") String hisBarcode,
                                    @RequestParam(required = false,defaultValue = "") String outTestItemCode,
                                    @RequestParam(required = false,defaultValue = "") String outTestItemName,
                                    @RequestParam(required = false,defaultValue = "xiaozhensuo") String originalOrgCode,
                                    @RequestParam(required = false,defaultValue = "小诊所") String originalOrgName,
                                    @RequestParam(required = false,defaultValue = "张三") String patientName,
                                    @RequestParam(required = false,defaultValue = "1") Integer patientSex,
                                    @RequestParam(required = false,defaultValue = "18") Integer patientAge,
                                    @RequestParam(required = false,defaultValue = "岁") String ageUnit,
                                    @RequestParam(required = false,defaultValue = "1994-04-06 12:00:00") String patientBirthday,
                                    @RequestParam(required = false,defaultValue = "PRN1000001") String patientRegistrationNo,
                                    @RequestParam(required = false,defaultValue = "TC2000001") String treatmentCard,
                                    @RequestParam(required = false,defaultValue = "门诊") String treatmentType,
                                    @RequestParam(required = false,defaultValue = "BN3") String bedNo,
                                    @RequestParam(required = false,defaultValue = "检验科") String dept,
                                    @RequestParam(required = false,defaultValue = "2024-05-06 12:00:00") String sampleCreateDate,
                                    @RequestParam(required = false,defaultValue = "2024-05-06 13:00:00") String sendTime,
                                    @RequestParam(required = false,defaultValue = "zhangyisheng") String sendDoctorCode,
                                    @RequestParam(required = false,defaultValue = "张医生") String sendDoctorName,
                                    @RequestParam(required = false,defaultValue = "2024-05-06 14:00:00") String testTime,
                                    @RequestParam(required = false,defaultValue = "zhaoyisheng") String testUserCode,
                                    @RequestParam(required = false,defaultValue = "赵医生") String testUserName,
                                    @RequestParam(required = false,defaultValue = "2003-05-06 15:00:00") String auditTime,
                                    @RequestParam(required = false,defaultValue = "liyisheng") String auditUserCode,
                                    @RequestParam(required = false,defaultValue = "李医生") String auditUserName,
                                    @RequestParam(required = false,defaultValue = "1") Integer itemCount,
                                    @RequestParam(required = false,defaultValue = "12.34") String standardPrice,
                                    @RequestParam(required = false,defaultValue = "56.78") String totalAmount,
                                    @RequestParam(required = false,defaultValue = "12.34") String settlementAmount,
                                    @RequestParam(required = false,defaultValue = "一般结算") String settlementTypeCode,
                                    @RequestParam(required = false,defaultValue = "一般结算") String settlementTypeName,
                                    @RequestParam(required = false,defaultValue = "结算备注") String settlementRemark,
                                    @RequestParam(required = false,defaultValue = "10折") String discount,
                                    @RequestParam(required = false,defaultValue = "1") Integer orgType,
                                    @RequestParam(required = false,defaultValue = "1") Long count) throws ParseException {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        SaveStatisticsSampleRequest statisticsRequest = new SaveStatisticsSampleRequest();
        List<SampleStatisticsRequest> sampleStatisticsRequests = new ArrayList<>();
        statisticsRequest.setSampleStatisticsRequests(sampleStatisticsRequests);
        for (int i = 0; i < count; i++) {
            int random = (int)(Math.random()*100);
            SampleStatisticsRequest temp = new SampleStatisticsRequest();
            temp.setOrgCode(orgCode);
            temp.setOrgName(orgName);
            temp.setBarcode(barcode + i);
            temp.setOutBarcode("OB"+barcode+i);
            temp.setHisBarcode("HB"+barcode+i);
            temp.setTestItemCode(testItemCode+random);
            temp.setTestItemName(testItemName+random);
            temp.setOutTestItemCode(orgType==1?"OTC"+testItemCode + random:"");
            temp.setOutTestItemName(orgType==1?"OTN"+testItemName + random:"");
            temp.setOriginalOrgCode(orgType==1?originalOrgCode + random:"");
            temp.setOriginalOrgName(orgType==1?originalOrgName + random:"");
            temp.setPatientName(patientName + random);
            temp.setPatientSex(patientSex);
            temp.setPatientAge(patientAge);
            temp.setAgeUnit(ageUnit);
            temp.setPatientBirthday(simpleDateFormat.parse(patientBirthday));
            temp.setPatientRegistrationNo(patientRegistrationNo + random);
            temp.setTreatmentCard(treatmentCard + random);
            temp.setTreatmentType(treatmentType);
            temp.setBedNo(bedNo + random);
            temp.setDept(dept);
            temp.setSampleCreateDate(simpleDateFormat.parse(sampleCreateDate));
            temp.setSendTime(simpleDateFormat.parse(sendTime));
            temp.setSendDoctorCode(sendDoctorCode + random);
            temp.setSendDoctorName(sendDoctorName + random);
            temp.setTestTime(simpleDateFormat.parse(testTime));
            temp.setTestUserCode(testUserCode + random);
            temp.setTestUserName(testUserName + random);
            temp.setAuditTime(simpleDateFormat.parse(auditTime));
            temp.setAuditUserCode(auditUserCode + random);
            temp.setAuditUserName(auditUserName + random);
            temp.setItemCount(itemCount);
            temp.setStandardPrice(new BigDecimal(standardPrice));
            temp.setTotalAmount(new BigDecimal(totalAmount));
            temp.setSettlementAmount(new BigDecimal(settlementAmount));
            temp.setSettlementTypeCode(settlementTypeCode);
            temp.setSettlementTypeName(settlementTypeName);
            temp.setSettlementRemark(settlementRemark);
            temp.setDiscount(discount);
            temp.setOrgType(orgType);
            sampleStatisticsRequests.add(temp);
        }
        statisticsRequest.setSampleStatisticsRequests(sampleStatisticsRequests);
        return sampleStatisticsService.saveStatisticsSample(statisticsRequest);
    }


}

