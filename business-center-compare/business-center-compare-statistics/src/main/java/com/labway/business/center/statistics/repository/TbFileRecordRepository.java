package com.labway.business.center.statistics.repository;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.business.center.compare.request.statistics.QueryFileRecordRequest;
import com.labway.business.center.statistics.constant.DeleteEnum;
import com.labway.business.center.statistics.constant.RecordKeywordEnum;
import com.labway.business.center.statistics.constant.RecordTypeEnum;
import com.labway.business.center.statistics.persistence.entity.TbFileRecord;
import com.labway.business.center.statistics.persistence.mapper.TbFileRecordMapper;
import com.labway.sso.core.user.SsoUser;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public class TbFileRecordRepository {

    @Resource
    private TbFileRecordMapper tbFileRecordMapper;




    // 获取默认对象
    public TbFileRecord getDefaultInstance(RecordTypeEnum recordTypeEnum, RecordKeywordEnum keywordEnum, Date date , SsoUser loginUser) {
        TbFileRecord tbFileRecord = new TbFileRecord();
        tbFileRecord.setRecordId(String.valueOf(IdWorker.getId()));
        tbFileRecord.setRecordType(recordTypeEnum.getCode());
        tbFileRecord.setFileUrls("");
        tbFileRecord.setRecordTime(0);
        tbFileRecord.setRecordStatus(1);
        tbFileRecord.setRecordKeyword(keywordEnum.getValue());
        tbFileRecord.setOrgCode("");
        tbFileRecord.setOrgName("");
        tbFileRecord.setCreateBy(loginUser.getUserName());
        tbFileRecord.setCreateTime(date);
        tbFileRecord.setUpdateBy(loginUser.getUserName());
        tbFileRecord.setUpdateTime(date);
        tbFileRecord.setDeleteFlag(DeleteEnum.NOT_DELETE.getCode());
        tbFileRecord.setFailReason("");
        return tbFileRecord;

    }


    /**
     * 保存
     * @param defaultInstance
     * @return
     */
    public Integer save(TbFileRecord defaultInstance) {
        if (defaultInstance == null){
            return 0;
        }

        return tbFileRecordMapper.insert(defaultInstance);
    }

    public Integer update(TbFileRecord defaultInstance) {
        if (defaultInstance == null|| StringUtils.isBlank(defaultInstance.getRecordId())){
            return 0;
        }

        return tbFileRecordMapper.updateById(defaultInstance);
    }

    /**
     * 查询导入导出记录
     * @param recordRequest
     * @return
     */
    public List<TbFileRecord> queryFileRecord(QueryFileRecordRequest recordRequest) {
        return tbFileRecordMapper.selectList(Wrappers.lambdaQuery(TbFileRecord.class)
                .eq(TbFileRecord::getRecordKeyword,recordRequest.getRecordKeyword())
                .eq(TbFileRecord::getOrgCode,recordRequest.getOrgCode())
                .eq(TbFileRecord::getDeleteFlag,DeleteEnum.NOT_DELETE.getCode())
                .orderByDesc(TbFileRecord::getCreateTime));
    }

    /**
     * 根据记录id删除导入导出记录
     * @param recordId
     * @return
     */
    public Integer deleteFileRecord(String recordId) {
        return tbFileRecordMapper.deleteById(recordId);
    }

}
