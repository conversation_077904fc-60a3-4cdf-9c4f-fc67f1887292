package com.labway.business.center.statistics.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.labway.business.center.compare.request.statistics.QueryFileRecordRequest;
import com.labway.business.center.statistics.persistence.entity.TbFileRecord;
import com.swak.frame.dto.Response;

/**
 * (TbFileRecord)表服务接口
 *
 * <AUTHOR>
 * @since 2024-04-28 17:11:47
 */
public interface TbFileRecordService extends IService<TbFileRecord> {

    /**
     * 查询导入导出记录
     * @param recordRequest
     * @return
     */
    Response<?> queryFileRecord(QueryFileRecordRequest recordRequest);

    Response<Integer> deleteFileRecord(String recordId);
}

