package com.labway.business.center.compare.persistence.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.labway.business.center.compare.constants.ConditionTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;


/**
 * <p>
 * 字典表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-17
 */
@Data
@TableName("tb_org_dict")
@ApiModel(value = "TbOrgDict对象", description = "字典表")
public class TbOrgDict implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("字典ID")
    @TableId(type = IdType.INPUT)
    private String dictId;

    @ApiModelProperty("字典值编码")
    private String dictCode;

    @ApiModelProperty("字典值")
    private String dictValue;

    @ApiModelProperty("字典名称编码")
    private String dictName;

    /**
     * 字典类型
     */
    private String dictType;

    @ApiModelProperty("父字典编码")
    private String dictParentId;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("是否有效 0否 1是")
    private Integer enabled;

    @ApiModelProperty("创建人")
    private String createBy;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("更新人")
    private String updateBy;

    @ApiModelProperty("更新时间")
    private Date updateTime;

    @ApiModelProperty("逻辑删除标志 0未删除1已删除")
    @TableLogic(value = "0", delval = "1")
    private Integer deleteFlag;

    /**
     * 机构编码
     */
    private String orgCode;

    /**
     * 机构名称
     */
    private String orgName;

	/**
	 * 关联的字典id
	 * {@link #getDictId()}
	 */
	private String relationDictId;

	/**
	 * 匹配方式
	 * @see ConditionTypeEnum
	 */
	private String conditionType;

}
