package com.labway.business.center.compare.persistence.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.labway.business.center.compare.dto.ItemTestDTO;
import com.labway.business.center.compare.persistence.entity.TbOrgApplySampleMainItemMapping;
import com.labway.business.center.compare.persistence.entity.TbOrgItemTestRelation;
import com.labway.business.center.core.injector.ExtBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 机构下发的检验项目信息表(TbOrgItemTest)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-04-21 09:32:05
 */
@DS("account")
public interface TbOrgItemTestRelationMapper extends ExtBaseMapper<TbOrgItemTestRelation> {

/**
* 批量新增数据（MyBatis原生foreach方法）
*
* @param entities List<TbOrgItemTest> 实例对象列表
* @return 影响行数
*/
int insertBatch(@Param("entities") List<TbOrgItemTestRelation> entities);

/**
* 批量新增或按主键更新数据（MyBatis原生foreach方法）
*
* @param entities List<TbOrgItemTest> 实例对象列表
* @return 影响行数
* @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
*/
int insertOrUpdateBatch(@Param("entities") List<TbOrgItemTestRelation> entities);

    List<TbOrgItemTestRelation> queryByOrgCodeAndTestCode(@Param("list") List<TbOrgApplySampleMainItemMapping> sampleMainItemMappings);
    List<TbOrgItemTestRelation> queryByOrgCodeAndTestCode2(@Param("list") List<TbOrgApplySampleMainItemMapping> sampleMainItemMappings);

	void updateItemTestInfoByMdm(@Param("list") List<ItemTestDTO> itemTestList);
}

