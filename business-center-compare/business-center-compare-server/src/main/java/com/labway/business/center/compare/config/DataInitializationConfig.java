package com.labway.business.center.compare.config;

import com.labway.business.center.compare.persistence.entity.TbOrgApplyForm;
import com.labway.business.center.compare.persistence.entity.TbOrgApplySampleMain;
import com.labway.business.center.compare.repository.center.TbOrgApplyFormRepository;
import com.labway.business.center.compare.repository.center.TbOrgApplySampleMainRepository;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2023/06/26 13:34
 * @description 系统启动进行数据初始化缓存处理
 **/
@Component
public class DataInitializationConfig implements ApplicationRunner {

    // 缓存送检客商的外送申请单号
    public static final String COMPARE_SEND_APPLY_FORM_CODE = "COMPARE_SEND_APPLY_FORM_CODE:";
    // 缓存送检客商的样本条码号
    public static final String COMPARE_SEND_SAMPLE_BARCODE = "COMPARE_SEND_SAMPLE_BARCODE:";

    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private TbOrgApplyFormRepository tbOrgApplyFormRepository;
    @Resource
    private TbOrgApplySampleMainRepository tbOrgApplySampleMainRepository;


    // 系统启动完成执行
    @Override
    public void run(ApplicationArguments args) throws Exception {
        // 缓存客商申请单号
        queryFormCodeList();
        // 缓存客商样本条码号
        querySampleBarcodeList();
    }


    // 初始化送检机构的申请单号信息
    public void queryFormCodeList(){
        List<TbOrgApplyForm> tbOrgApplyForms = tbOrgApplyFormRepository.queryFormCodeList();
        if (CollectionUtils.isEmpty(tbOrgApplyForms)){
            return;
        }

        Map<String, List<TbOrgApplyForm>> hspOrgCodeGroup = tbOrgApplyForms.stream().collect(Collectors.groupingBy(e -> e.getHspOrgCode()));
        if (CollectionUtils.isEmpty(hspOrgCodeGroup)){
            return ;
        }

        for (Map.Entry<String, List<TbOrgApplyForm>> entry : hspOrgCodeGroup.entrySet()) {
            List<TbOrgApplyForm> tempFormValues = entry.getValue();
            if (CollectionUtils.isEmpty(tempFormValues)){
                continue;
            }

            Map<String, String> formCodesMap = tempFormValues.stream().collect(Collectors.toMap(e -> e.getFormCode(), p -> p.getFormCode(), (o1, o2) -> o2));
            stringRedisTemplate.opsForHash().putAll(COMPARE_SEND_APPLY_FORM_CODE +entry.getKey(),formCodesMap);
        }
    }


    // 初始化送检机构的样本条码号信息
    public void querySampleBarcodeList() {
        List<TbOrgApplySampleMain> tbOrgApplySampleMains = tbOrgApplySampleMainRepository.querySampleBarcodeList();
        if (CollectionUtils.isEmpty(tbOrgApplySampleMains)) {
            return;
        }

        Map<String, List<TbOrgApplySampleMain>> hspOrgCodeGroup = tbOrgApplySampleMains.stream().collect(Collectors.groupingBy(e -> e.getHspOrgCode()));
        if (CollectionUtils.isEmpty(hspOrgCodeGroup)) {
            return;
        }

        for (Map.Entry<String, List<TbOrgApplySampleMain>> entry : hspOrgCodeGroup.entrySet()) {
            List<TbOrgApplySampleMain> tempFormValues = entry.getValue();
            if (CollectionUtils.isEmpty(tempFormValues)) {
                continue;
            }

            Map<String, String> barcodesMap = tempFormValues.stream().collect(Collectors.toMap(e -> e.getBarcode(), p -> p.getBarcode(), (o1, o2) -> o2));
            stringRedisTemplate.opsForHash().putAll(COMPARE_SEND_SAMPLE_BARCODE + entry.getKey(), barcodesMap);
        }
    }


}