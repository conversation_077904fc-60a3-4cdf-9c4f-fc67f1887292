package com.labway.business.center.compare.controller;

import com.labway.business.center.compare.annotation.LogAnnotation;
import com.labway.business.center.compare.dto.OutApplyInfoDTO;
import com.labway.business.center.compare.request.CancelSignRequest;
import com.labway.business.center.compare.request.OuterLabOutApplyInfoRequest;
import com.labway.business.center.compare.request.SignOutApplyInfoRequest;
import com.labway.business.center.compare.service.OuterLabService;
import com.labway.business.center.core.enums.OperationTypeEnum;
import com.labway.business.center.core.enums.SystemModuleEmun;
import com.swak.frame.dto.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 外部实验室操作控制层
 */
@Slf4j
@RestController
@RequestMapping("/outer/lab")
public class ApiOuterLabController {

    @Resource
    private OuterLabService outerLabService;

    /**
     * 查询样本信息-外部实验室
     */
    @PostMapping("/queryOutApplyInfo")
    public Response<OutApplyInfoDTO> queryOutApplyInfo(@RequestBody @Valid OuterLabOutApplyInfoRequest request) {
        return outerLabService.queryOutApplyInfo(request);
    }

    /**
     * 签收样本信息-外部实验室
     */
    @LogAnnotation(value = "外部实验室签收样本信息", OperationType = OperationTypeEnum.UPDATE, SystemModule = SystemModuleEmun.BUSINESS_COMPARE)
    @PostMapping("/signOutApplyInfo")
    public Response<Boolean> signOutApplyInfo(@RequestBody @Valid SignOutApplyInfoRequest request) {
        return outerLabService.signOutApplyInfo(request);
    }

    /**
     * 取消签收样本信息-外部实验室
     */
    @LogAnnotation(value = "外部实验室取消签收样本信息", OperationType = OperationTypeEnum.UPDATE, SystemModule = SystemModuleEmun.BUSINESS_COMPARE)
    @PostMapping("/cancelSign")
    public Response<Boolean> cancelSign(@RequestBody @Valid CancelSignRequest request) {
        return outerLabService.cancelSign(request);
    }

}
