package com.labway.business.center.compare.persistence.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.util.Date;

/**
 * 外送申请单样本主表(TbOrgApplySampleMainDTO)表实体类
 *
 * <AUTHOR>
 * @since 2023-04-24 13:11:32
 */
@SuppressWarnings("serial")
public class TbOrgApplySampleMain extends Model<TbOrgApplySampleMain> {
    //申请单样本表id
    @TableId(type = IdType.INPUT)
    private String mainId;
    //外送到中台的条码号
    private String barcode;
    //送检机构编码（对应中台的客商编码）
    private String hspOrgCode;
    //送检机构名称（对应中台的客商名称）
    private String hspOrgName;
    //所属业务单元编码
    private String orgCode;
    //所属业务单元名称
    private String orgName;
    //申请单类型 门诊/住院
    private String applyType;
    //门诊/住院 号
    private String patientVisitCard;
    //是否加急 0否1是
    private Integer urgent;
    //样本类型
    private String sampleType;
    //样本形状
    private String sampleProperty;
    //申请科室
    private String dept;
    //病区
    private String inpatientArea;
    //患者名称
    private String patientName;
    //性别 1男2女
    private Integer patientSex;
    //年龄 xx岁
    private Integer patientAge;
    //子年龄 xxx天|xxx周|xxx月|
    private Integer patientSubage;
    //子年龄单位
    private String patientSubageUnit;
    //生日
    private Date patientBirthday;
    //床号
    private String patientBed;
    //临床诊断
    private String clinicalDiagnosis;
    //身份证
    private String patientCard;
    //证件类型
    private String patientCardType;
    //住址
    private String patientAsddress;
    //手机号
    private String patientMobile;
    //送检医生
    private String sendDoctor;
    //申请时间（送检时间）
    private Date applyDate;
    //采样时间
    private Date samplingDate;
    //备注
    private String remark;
    //更新人id
    private String updateBy;
    //创建时间
    private Date createTime;
    //创建人id
    private String createBy;
    //更新时间
    private Date updateTime;
    //删除标识 0未删除 1删除
    @TableLogic(value = "0",delval = "1")
    private Integer deleteFlag;
    /**
     * @see com.labway.business.center.core.enums.ApplySampleStatus
     */
    //1待对照 2待签收 3已签收 4取消外送  5退回
    private Integer status;

    // 签收人编码
    private String receiveUserCode;
    // 签收人名称
    private String receiveUserName;
    // 签收时间
    private Date receiveTime;
    // 签收机构编码
    private String signOrgCode;
    // 签收机构名称
    private String signOrgName;
    // 签收机构条码
    private String signBarcode;
    // 签收主条码（暂无用）
    private String signMainBarcode;

    /**
     * 外送样本来源标识（his,物流，社区等）
     * @see com.labway.business.center.core.enums.SampleSource
     */
    private String sampleSource;
    // 样本送往的目标机构编码(业务单元)
    private String targetOrgCode;
    // 样本送往的目标机构名称(业务单元)
    private String targetOrgName;

    /**
     * 外部主表id
     */
    private String outMainId;
    /**
     * 审核人id
     */
    private String auditorId;
    /**
     * 审核人
     */
    private String auditor;
    /**
     * 审核时间
     */
    private Date auditDate;

    private String instrumentCode;
    /**
     * 样本号
     */
    private String sampleNo;
    /**
     * 检验日期-字符串
     */
    private String testDate;
    /**
     * 检验时间
     */
    private Date testTime;
    /**
     * 检验项目数量
     */
    private Integer testItemSum;
    /**
     * 是否一审
     */
    private Integer isFirstAudit;
    /**
     * 一审人编码
     */
    private String firstAuditUserCode;
    /**
     * 一审人名称
     */
    private String firstAuditUserName;
    /**
     * 一审时间
     */
    private Date firstAuditTime;
    /**
     * 是否二审
     */
    private Integer isSecAudit;
    /**
     * 二审人编码
     */
    private String secAuditUserCode;
    /**
     * 二审人名称
     */
    private String secAuditUserName;
    /**
     * 二审时间
     */
    private Date secAuditTime;
    /**
     * 检验人编码
     */
    private String testUserCode;
    /**
     * 检验人名称
     */
    private String testUserName;

    private Integer isCheck;
    //外送申请单信息id
    private String formId;
    //申请单编码
    private String formCode;
    /**
     * 管型
     */
    private String tubeType;
    // 是否确认样本报告结果 0未确认 1确认
    private Integer isDownload;
    // 结果查询次数
    private Integer queryTimes;
    // 报告结果是否全部拉取完毕 0未拉取完 1报告结果已经拉取完毕
    private String isReport;
    // 是否是补录样本 0否 1是
    private Integer isAdditional;
    // 样本数量
    private Integer sampleNum;
    // 前处理是否交接 0未交接 1已交接 2取消交接
    private Integer isHandover;
    // 交接时间
    private Date handoverTime;
    // 样本流转状态 10已发送 20已取样 30已妥投 40前处理交接 50前处理签收 60已报告 70取消外送
    private Integer flowStatus;

    // 医保卡号
    private String visitCardNo;

    // 报告单urls
    private String reportUrls;

    // 退回原因
    private String returnReason;

    // 报告单hash值
    private String reportMd5;

    // lis客商编码
    private String lisCustomerCode;
    // lis客商名称
    private String lisCustomerName;

    private String microbiologyReportMd5;// 微生物报告单MD5值
    private String microbiologyReportUrls;// 微生物报告单url
    private String itemType;// 项目类型

    // 取消外送原因（0默认，1物流未取样）
    private Integer cancelReason;

    // 是否终止检验 0否 1是
    private Integer isTerminateTest;

    // 是否是手工单样本 0否1是
    private Integer isHandSample;

    // 是否外送迪安 0否1是
    private Integer isDian;

    // 是否分血 0否1是
    private Integer isSplit;
    // 分血条码号
    private String  splitBarcode;
    // 原始报告单地址（未合并）
    private String originalReportUrls;

    // 物流手动录入样本取样照片（多张英文逗号分割）
    private String handReceivePicCode;

    // 报告单urls
    private String imgUrls;
    // 结果备注，建议与解释等
    private String resultRemark;

    // 审核人身份证号
    private String checkIdCard;

    // 一审人身份证号
    private String oneCheckIdCard;

    // 二审人身份证号
    private String twoCheckIdCard;

    /**
     * 样本报告总数量
     */
    private Integer allReportsCount;

    /**
     * 当前样本报告数量
     */
    private Integer currentReportsCount;


    /**
     *     // 登记医生身份证号
     */
    private String inputDoctorCode;

    /**
     *     // 登记医生姓名
     */
    private String inputDoctor;

    /**
     *     // 登记日期时间--yyyy-mm-dd hh24:mi:ss
     */
    private Date inputDate;


    public String getMainId() {
        return mainId;
    }

    public void setMainId(String mainId) {
        this.mainId = mainId;
    }

    public String getBarcode() {
        return barcode;
    }

    public void setBarcode(String barcode) {
        this.barcode = barcode;
    }

    public String getHspOrgCode() {
        return hspOrgCode;
    }

    public void setHspOrgCode(String hspOrgCode) {
        this.hspOrgCode = hspOrgCode;
    }

    public String getHspOrgName() {
        return hspOrgName;
    }

    public void setHspOrgName(String hspOrgName) {
        this.hspOrgName = hspOrgName;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getApplyType() {
        return applyType;
    }

    public void setApplyType(String applyType) {
        this.applyType = applyType;
    }

    public String getPatientVisitCard() {
        return patientVisitCard;
    }

    public void setPatientVisitCard(String patientVisitCard) {
        this.patientVisitCard = patientVisitCard;
    }

    public Integer getUrgent() {
        return urgent;
    }

    public void setUrgent(Integer urgent) {
        this.urgent = urgent;
    }

    public String getSampleType() {
        return sampleType;
    }

    public void setSampleType(String sampleType) {
        this.sampleType = sampleType;
    }

    public String getSampleProperty() {
        return sampleProperty;
    }

    public void setSampleProperty(String sampleProperty) {
        this.sampleProperty = sampleProperty;
    }

    public String getDept() {
        return dept;
    }

    public void setDept(String dept) {
        this.dept = dept;
    }

    public String getInpatientArea() {
        return inpatientArea;
    }

    public void setInpatientArea(String inpatientArea) {
        this.inpatientArea = inpatientArea;
    }

    public String getPatientName() {
        return patientName;
    }

    public void setPatientName(String patientName) {
        this.patientName = patientName;
    }

    public Integer getPatientSex() {
        return patientSex;
    }

    public void setPatientSex(Integer patientSex) {
        this.patientSex = patientSex;
    }

    public Integer getPatientAge() {
        return patientAge;
    }

    public void setPatientAge(Integer patientAge) {
        this.patientAge = patientAge;
    }

    public Integer getPatientSubage() {
        return patientSubage;
    }

    public void setPatientSubage(Integer patientSubage) {
        this.patientSubage = patientSubage;
    }

    public String getPatientSubageUnit() {
        return patientSubageUnit;
    }

    public void setPatientSubageUnit(String patientSubageUnit) {
        this.patientSubageUnit = patientSubageUnit;
    }

    public Date getPatientBirthday() {
        return patientBirthday;
    }

    public void setPatientBirthday(Date patientBirthday) {
        this.patientBirthday = patientBirthday;
    }

    public String getPatientBed() {
        return patientBed;
    }

    public void setPatientBed(String patientBed) {
        this.patientBed = patientBed;
    }

    public String getClinicalDiagnosis() {
        return clinicalDiagnosis;
    }

    public void setClinicalDiagnosis(String clinicalDiagnosis) {
        this.clinicalDiagnosis = clinicalDiagnosis;
    }

    public String getPatientCard() {
        return patientCard;
    }

    public void setPatientCard(String patientCard) {
        this.patientCard = patientCard;
    }

    public String getPatientCardType() {
        return patientCardType;
    }

    public void setPatientCardType(String patientCardType) {
        this.patientCardType = patientCardType;
    }

    public String getPatientAsddress() {
        return patientAsddress;
    }

    public void setPatientAsddress(String patientAsddress) {
        this.patientAsddress = patientAsddress;
    }

    public String getPatientMobile() {
        return patientMobile;
    }

    public void setPatientMobile(String patientMobile) {
        this.patientMobile = patientMobile;
    }

    public String getSendDoctor() {
        return sendDoctor;
    }

    public void setSendDoctor(String sendDoctor) {
        this.sendDoctor = sendDoctor;
    }

    public Date getApplyDate() {
        return applyDate;
    }

    public void setApplyDate(Date applyDate) {
        this.applyDate = applyDate;
    }

    public Date getSamplingDate() {
        return samplingDate;
    }

    public void setSamplingDate(Date samplingDate) {
        this.samplingDate = samplingDate;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getDeleteFlag() {
        return deleteFlag;
    }

    public void setDeleteFlag(Integer deleteFlag) {
        this.deleteFlag = deleteFlag;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getReceiveUserCode() {
        return receiveUserCode;
    }

    public void setReceiveUserCode(String receiveUserCode) {
        this.receiveUserCode = receiveUserCode;
    }

    public String getReceiveUserName() {
        return receiveUserName;
    }

    public void setReceiveUserName(String receiveUserName) {
        this.receiveUserName = receiveUserName;
    }

    public Date getReceiveTime() {
        return receiveTime;
    }

    public void setReceiveTime(Date receiveTime) {
        this.receiveTime = receiveTime;
    }

    public String getSignOrgCode() {
        return signOrgCode;
    }

    public void setSignOrgCode(String signOrgCode) {
        this.signOrgCode = signOrgCode;
    }

    public String getSignOrgName() {
        return signOrgName;
    }

    public void setSignOrgName(String signOrgName) {
        this.signOrgName = signOrgName;
    }

    public String getSignBarcode() {
        return signBarcode;
    }

    public void setSignBarcode(String signBarcode) {
        this.signBarcode = signBarcode;
    }

    public String getSignMainBarcode() {
        return signMainBarcode;
    }

    public void setSignMainBarcode(String signMainBarcode) {
        this.signMainBarcode = signMainBarcode;
    }

    public String getSampleSource() {
        return sampleSource;
    }

    public void setSampleSource(String sampleSource) {
        this.sampleSource = sampleSource;
    }

    public String getTargetOrgCode() {
        return targetOrgCode;
    }

    public void setTargetOrgCode(String targetOrgCode) {
        this.targetOrgCode = targetOrgCode;
    }

    public String getTargetOrgName() {
        return targetOrgName;
    }

    public void setTargetOrgName(String targetOrgName) {
        this.targetOrgName = targetOrgName;
    }

    public String getOutMainId() {
        return outMainId;
    }

    public void setOutMainId(String outMainId) {
        this.outMainId = outMainId;
    }

    public String getAuditorId() {
        return auditorId;
    }

    public void setAuditorId(String auditorId) {
        this.auditorId = auditorId;
    }

    public String getAuditor() {
        return auditor;
    }

    public void setAuditor(String auditor) {
        this.auditor = auditor;
    }

    public Date getAuditDate() {
        return auditDate;
    }

    public void setAuditDate(Date auditDate) {
        this.auditDate = auditDate;
    }

    public String getInstrumentCode() {
        return instrumentCode;
    }

    public void setInstrumentCode(String instrumentCode) {
        this.instrumentCode = instrumentCode;
    }

    public String getSampleNo() {
        return sampleNo;
    }

    public void setSampleNo(String sampleNo) {
        this.sampleNo = sampleNo;
    }

    public String getTestDate() {
        return testDate;
    }

    public void setTestDate(String testDate) {
        this.testDate = testDate;
    }

    public Date getTestTime() {
        return testTime;
    }

    public void setTestTime(Date testTime) {
        this.testTime = testTime;
    }

    public Integer getTestItemSum() {
        return testItemSum;
    }

    public void setTestItemSum(Integer testItemSum) {
        this.testItemSum = testItemSum;
    }

    public Integer getIsFirstAudit() {
        return isFirstAudit;
    }

    public void setIsFirstAudit(Integer isFirstAudit) {
        this.isFirstAudit = isFirstAudit;
    }

    public String getFirstAuditUserCode() {
        return firstAuditUserCode;
    }

    public void setFirstAuditUserCode(String firstAuditUserCode) {
        this.firstAuditUserCode = firstAuditUserCode;
    }

    public String getFirstAuditUserName() {
        return firstAuditUserName;
    }

    public void setFirstAuditUserName(String firstAuditUserName) {
        this.firstAuditUserName = firstAuditUserName;
    }

    public Date getFirstAuditTime() {
        return firstAuditTime;
    }

    public void setFirstAuditTime(Date firstAuditTime) {
        this.firstAuditTime = firstAuditTime;
    }

    public Integer getIsSecAudit() {
        return isSecAudit;
    }

    public void setIsSecAudit(Integer isSecAudit) {
        this.isSecAudit = isSecAudit;
    }

    public String getSecAuditUserCode() {
        return secAuditUserCode;
    }

    public void setSecAuditUserCode(String secAuditUserCode) {
        this.secAuditUserCode = secAuditUserCode;
    }

    public String getSecAuditUserName() {
        return secAuditUserName;
    }

    public void setSecAuditUserName(String secAuditUserName) {
        this.secAuditUserName = secAuditUserName;
    }

    public Date getSecAuditTime() {
        return secAuditTime;
    }

    public void setSecAuditTime(Date secAuditTime) {
        this.secAuditTime = secAuditTime;
    }

    public String getTestUserCode() {
        return testUserCode;
    }

    public void setTestUserCode(String testUserCode) {
        this.testUserCode = testUserCode;
    }

    public String getTestUserName() {
        return testUserName;
    }

    public void setTestUserName(String testUserName) {
        this.testUserName = testUserName;
    }

    public Integer getIsCheck() {
        return isCheck;
    }

    public void setIsCheck(Integer isCheck) {
        this.isCheck = isCheck;
    }

    public String getFormId() {
        return formId;
    }

    public void setFormId(String formId) {
        this.formId = formId;
    }

    public String getFormCode() {
        return formCode;
    }

    public void setFormCode(String formCode) {
        this.formCode = formCode;
    }

    public String getTubeType() {
        return tubeType;
    }

    public void setTubeType(String tubeType) {
        this.tubeType = tubeType;
    }

    public Integer getIsDownload() {
        return isDownload;
    }

    public void setIsDownload(Integer isDownload) {
        this.isDownload = isDownload;
    }

    public Integer getQueryTimes() {
        return queryTimes;
    }

    public void setQueryTimes(Integer queryTimes) {
        this.queryTimes = queryTimes;
    }

    public String getIsReport() {
        return isReport;
    }

    public void setIsReport(String isReport) {
        this.isReport = isReport;
    }

    public Integer getIsAdditional() {
        return isAdditional;
    }

    public void setIsAdditional(Integer isAdditional) {
        this.isAdditional = isAdditional;
    }

    public Integer getSampleNum() {
        return sampleNum;
    }

    public void setSampleNum(Integer sampleNum) {
        this.sampleNum = sampleNum;
    }

    public Integer getIsHandover() {
        return isHandover;
    }

    public void setIsHandover(Integer isHandover) {
        this.isHandover = isHandover;
    }

    public Date getHandoverTime() {
        return handoverTime;
    }

    public void setHandoverTime(Date handoverTime) {
        this.handoverTime = handoverTime;
    }

    public Integer getFlowStatus() {
        return flowStatus;
    }

    public void setFlowStatus(Integer flowStatus) {
        this.flowStatus = flowStatus;
    }

    public String getReportUrls() {
        return reportUrls;
    }

    public void setReportUrls(String reportUrls) {
        this.reportUrls = reportUrls;
    }

    public String getVisitCardNo() {
        return visitCardNo;
    }

    public void setVisitCardNo(String visitCardNo) {
        this.visitCardNo = visitCardNo;
    }

    public String getReturnReason() {
        return returnReason;
    }

    public void setReturnReason(String returnReason) {
        this.returnReason = returnReason;
    }

    public String getReportMd5() {
        return reportMd5;
    }

    public void setReportMd5(String reportMd5) {
        this.reportMd5 = reportMd5;
    }

    public String getLisCustomerCode() {
        return lisCustomerCode;
    }

    public void setLisCustomerCode(String lisCustomerCode) {
        this.lisCustomerCode = lisCustomerCode;
    }

    public String getLisCustomerName() {
        return lisCustomerName;
    }

    public void setLisCustomerName(String lisCustomerName) {
        this.lisCustomerName = lisCustomerName;
    }

    public String getMicrobiologyReportMd5() {
        return microbiologyReportMd5;
    }

    public void setMicrobiologyReportMd5(String microbiologyReportMd5) {
        this.microbiologyReportMd5 = microbiologyReportMd5;
    }

    public String getMicrobiologyReportUrls() {
        return microbiologyReportUrls;
    }

    public void setMicrobiologyReportUrls(String microbiologyReportUrls) {
        this.microbiologyReportUrls = microbiologyReportUrls;
    }

    public String getItemType() {
        return itemType;
    }

    public void setItemType(String itemType) {
        this.itemType = itemType;
    }

    public Integer getCancelReason() {
        return cancelReason;
    }

    public void setCancelReason(Integer cancelReason) {
        this.cancelReason = cancelReason;
    }

    public Integer getIsTerminateTest() {
        return isTerminateTest;
    }

    public void setIsTerminateTest(Integer isTerminateTest) {
        this.isTerminateTest = isTerminateTest;
    }

    public Integer getIsHandSample() {
        return isHandSample;
    }

    public void setIsHandSample(Integer isHandSample) {
        this.isHandSample = isHandSample;
    }

    public Integer getIsDian() {
        return isDian;
    }

    public void setIsDian(Integer isDian) {
        this.isDian = isDian;
    }

    public Integer getIsSplit() {
        return isSplit;
    }

    public void setIsSplit(Integer isSplit) {
        this.isSplit = isSplit;
    }

    public String getSplitBarcode() {
        return splitBarcode;
    }

    public void setSplitBarcode(String splitBarcode) {
        this.splitBarcode = splitBarcode;
    }

    public String getOriginalReportUrls() {
        return originalReportUrls;
    }

    public void setOriginalReportUrls(String originalReportUrls) {
        this.originalReportUrls = originalReportUrls;
    }

    public String getHandReceivePicCode() {
        return handReceivePicCode;
    }

    public void setHandReceivePicCode(String handReceivePicCode) {
        this.handReceivePicCode = handReceivePicCode;
    }

    public String getImgUrls() {
        return imgUrls;
    }

    public void setImgUrls(String imgUrls) {
        this.imgUrls = imgUrls;
    }

    public String getResultRemark() {
        return resultRemark;
    }

    public void setResultRemark(String resultRemark) {
        this.resultRemark = resultRemark;
    }

    public void setCheckIdCard(String checkIdCard) {
        this.checkIdCard = checkIdCard;
    }

    public String getCheckIdCard() {
        return checkIdCard;
    }

    public void setOneCheckIdCard(String oneCheckIdCard) {
        this.oneCheckIdCard = oneCheckIdCard;
    }

    public String getOneCheckIdCard() {
        return oneCheckIdCard;
    }

    public void setTwoCheckIdCard(String twoCheckIdCard) {
        this.twoCheckIdCard = twoCheckIdCard;
    }

    public String getTwoCheckIdCard() {
        return twoCheckIdCard;
    }

    public Integer getAllReportsCount() {
        return allReportsCount;
    }

    public void setAllReportsCount(Integer allReportsCount) {
        this.allReportsCount = allReportsCount;
    }

    public Integer getCurrentReportsCount() {
        return currentReportsCount;
    }

    public void setCurrentReportsCount(Integer currentReportsCount) {
        this.currentReportsCount = currentReportsCount;
    }

    public String getInputDoctorCode() {
        return inputDoctorCode;
    }

    public void setInputDoctorCode(String inputDoctorCode) {
        this.inputDoctorCode = inputDoctorCode;
    }

    public String getInputDoctor() {
        return inputDoctor;
    }

    public void setInputDoctor(String inputDoctor) {
        this.inputDoctor = inputDoctor;
    }

    public Date getInputDate() {
        return inputDate;
    }

    public void setInputDate(Date inputDate) {
        this.inputDate = inputDate;
    }


    @Override
    public String toString() {
        return "TbOrgApplySampleMain{" +
                "mainId='" + mainId + '\'' +
                ", barcode='" + barcode + '\'' +
                ", hspOrgCode='" + hspOrgCode + '\'' +
                ", hspOrgName='" + hspOrgName + '\'' +
                ", orgCode='" + orgCode + '\'' +
                ", orgName='" + orgName + '\'' +
                ", applyType='" + applyType + '\'' +
                ", patientVisitCard='" + patientVisitCard + '\'' +
                ", urgent=" + urgent +
                ", sampleType='" + sampleType + '\'' +
                ", sampleProperty='" + sampleProperty + '\'' +
                ", dept='" + dept + '\'' +
                ", inpatientArea='" + inpatientArea + '\'' +
                ", patientName='" + patientName + '\'' +
                ", patientSex=" + patientSex +
                ", patientAge=" + patientAge +
                ", patientSubage=" + patientSubage +
                ", patientSubageUnit='" + patientSubageUnit + '\'' +
                ", patientBirthday=" + patientBirthday +
                ", patientBed='" + patientBed + '\'' +
                ", clinicalDiagnosis='" + clinicalDiagnosis + '\'' +
                ", patientCard='" + patientCard + '\'' +
                ", patientCardType='" + patientCardType + '\'' +
                ", patientAsddress='" + patientAsddress + '\'' +
                ", patientMobile='" + patientMobile + '\'' +
                ", sendDoctor='" + sendDoctor + '\'' +
                ", applyDate=" + applyDate +
                ", samplingDate=" + samplingDate +
                ", remark='" + remark + '\'' +
                ", updateBy='" + updateBy + '\'' +
                ", createTime=" + createTime +
                ", createBy='" + createBy + '\'' +
                ", updateTime=" + updateTime +
                ", deleteFlag=" + deleteFlag +
                ", status=" + status +
                ", receiveUserCode='" + receiveUserCode + '\'' +
                ", receiveUserName='" + receiveUserName + '\'' +
                ", receiveTime=" + receiveTime +
                ", signOrgCode='" + signOrgCode + '\'' +
                ", signOrgName='" + signOrgName + '\'' +
                ", signBarcode='" + signBarcode + '\'' +
                ", signMainBarcode='" + signMainBarcode + '\'' +
                ", sampleSource='" + sampleSource + '\'' +
                ", targetOrgCode='" + targetOrgCode + '\'' +
                ", targetOrgName='" + targetOrgName + '\'' +
                ", outMainId='" + outMainId + '\'' +
                ", auditorId='" + auditorId + '\'' +
                ", auditor='" + auditor + '\'' +
                ", auditDate=" + auditDate +
                ", instrumentCode='" + instrumentCode + '\'' +
                ", sampleNo='" + sampleNo + '\'' +
                ", testDate='" + testDate + '\'' +
                ", testTime=" + testTime +
                ", testItemSum=" + testItemSum +
                ", isFirstAudit=" + isFirstAudit +
                ", firstAuditUserCode='" + firstAuditUserCode + '\'' +
                ", firstAuditUserName='" + firstAuditUserName + '\'' +
                ", firstAuditTime=" + firstAuditTime +
                ", isSecAudit=" + isSecAudit +
                ", secAuditUserCode='" + secAuditUserCode + '\'' +
                ", secAuditUserName='" + secAuditUserName + '\'' +
                ", secAuditTime=" + secAuditTime +
                ", testUserCode='" + testUserCode + '\'' +
                ", testUserName='" + testUserName + '\'' +
                ", isCheck=" + isCheck +
                ", formId='" + formId + '\'' +
                ", formCode='" + formCode + '\'' +
                ", tubeType='" + tubeType + '\'' +
                ", isDownload=" + isDownload +
                ", queryTimes=" + queryTimes +
                ", isReport='" + isReport + '\'' +
                ", isAdditional=" + isAdditional +
                ", sampleNum=" + sampleNum +
                ", isHandover=" + isHandover +
                ", handoverTime=" + handoverTime +
                ", flowStatus=" + flowStatus +
                ", visitCardNo='" + visitCardNo + '\'' +
                ", reportUrls='" + reportUrls + '\'' +
                ", returnReason='" + returnReason + '\'' +
                ", reportMd5='" + reportMd5 + '\'' +
                ", lisCustomerCode='" + lisCustomerCode + '\'' +
                ", lisCustomerName='" + lisCustomerName + '\'' +
                ", microbiologyReportMd5='" + microbiologyReportMd5 + '\'' +
                ", microbiologyReportUrls='" + microbiologyReportUrls + '\'' +
                ", itemType='" + itemType + '\'' +
                '}';
    }
}

