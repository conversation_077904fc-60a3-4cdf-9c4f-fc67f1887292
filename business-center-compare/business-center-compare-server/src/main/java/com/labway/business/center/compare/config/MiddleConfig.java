package com.labway.business.center.compare.config;

import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.labway.business.center.core.enums.Region;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 2023/07/21 13:17
 **/
@Data
@RefreshScope
@Configuration
@ConfigurationProperties(prefix = "special")
public class MiddleConfig {

    /**
     * 配置那些签收机构 只签收已对照项目，忽略未对照项目
     */
    private Set<String> ignoreNoMappingSignOrgs = new HashSet<>();

    /**
     * 中间库结果回传，查询实验室请求path
     * @since 2024/10/21 广州需要配置特定机构报告延迟发出，调整为新的接口
     * @since 2023/07/21 默认是/statistics/es/selectSamples
     */
    private String selectSampleQueryPath = "/statistics/es/selectSamplesWithDelay";
    /**
     * 对接中间库服务的机构code
     */
    private Map<String, String> orgCodes;
    /**
     * 机构 -- 获取 LIMS URL 配置
     */
    private Map<String, String> orgCodesLimsPath = new HashMap<>();

    /**
     * 细菌备注 - 用于判断危急值
     */
    private List<String> germRemarkList;

    /**
     * 机构列表 - 需要处理 细菌备注 和 检验项目 判断是否危急
     */
    private List<String> germCriticalOrgCodes;

    /**
     * 检验项目列表 - 这些检验项目如果有细菌，则判断是危急
     */
    private List<String> germCriticalTestItemCodes;

    /**
     * 细菌备注 - 用于判断危急值
     */
    private List<String> germCriticalRemarks;

    /**
     * 判断方式 1:值包含配置，2:配置包含值，3:值包含配置 || 配置包含值
     */
    private Integer judgeType;

    /**
     * 匹配大小写
     */
    private Boolean matchCase;

    /**
     * range 换行分隔符
     */
    private String rangeSeparator = StringPool.PIPE;
    /**
     * 需要排除处理
     */
    private List<String> rangeExchangeExcludeOrgs = List.of();

    /**
     * 哪些地区按照 dubbo group 区分
     */
    private List<String> groupRegions = List.of(Region.dongguan.name());

    public String getOrgNameByCode(String orgCode) {
        return orgCodes.getOrDefault(orgCode, StringUtils.EMPTY);
    }

    /**
     * 判断是否是需要处理微生物危急值机构
     */
    public boolean isGermOrgCode(String hspOrgCode) {
        return CollectionUtils.isNotEmpty(germCriticalOrgCodes) && germCriticalOrgCodes.contains(hspOrgCode);
    }

    /**
     * 是否是危急值 检验项目
     */
    public boolean isGermTestItem(String testItemCode) {
        return CollectionUtils.isNotEmpty(germCriticalTestItemCodes) && germCriticalTestItemCodes.contains(testItemCode);
    }

    /**
     * 备注是否复核危急值判断
     */
    public boolean isGermCriticalRemark(String germRemark) {
        if (CollectionUtils.isEmpty(germCriticalRemarks) || StringUtils.isBlank(germRemark)) {
            return false;
        }

        // 匹配大小写
        String remarkMatchCase = matchCase ? germRemark : germRemark.toLowerCase();

        boolean isCritical;
        switch (JudgeType.getByCode(judgeType)) {
            case VALUE_CONTAIN:// 1:值包含配置
                isCritical = germCriticalRemarks
                        .stream()
                        .map(remark -> matchCase ? remark : remark.toLowerCase())
                        .anyMatch(remarkMatchCase::contains);
                break;
            case CONFIG_CONTAIN:// 2:配置包含值
                isCritical = germCriticalRemarks.stream()
                        .map(remark -> matchCase ? remark : remark.toLowerCase())
                        .anyMatch(remark -> remark.contains(remarkMatchCase));
                break;
            default:
                isCritical =
                        germCriticalRemarks.stream().map(remark -> matchCase ? remark : remark.toLowerCase()).anyMatch(remarkMatchCase::contains)
                        || germCriticalRemarks.stream().map(remark -> matchCase ? remark : remark.toLowerCase()).anyMatch(remark -> remark.contains(remarkMatchCase));
                break;
        }

        return isCritical;
    }

    /**
     * 判断是不是要排除掉range转换 的机构
     */
    public boolean isExcludeOrg(String hspOrgCode) {
        return CollectionUtils.isNotEmpty(rangeExchangeExcludeOrgs)
                && StringUtils.isNotBlank(hspOrgCode)
                && rangeExchangeExcludeOrgs.contains(hspOrgCode);
    }

    @Getter
    @AllArgsConstructor
    enum JudgeType {
        /**
         * 1:值包含配置
         */
        VALUE_CONTAIN(1),
        /**
         * 2:配置包含值
         */
        CONFIG_CONTAIN(2),
        /**
         * 3:两者仪器判断 值包含配置 || 配置包含值
         */
        BOTH_CONTAIN(3),

        ;

        private final Integer code;

        public static JudgeType getByCode(Integer code) {
            for (JudgeType judgeType : values()) {
                if (Objects.equals(code, judgeType.getCode())) {
                    return judgeType;
                }
            }
            return JudgeType.BOTH_CONTAIN;
        }
    }

}