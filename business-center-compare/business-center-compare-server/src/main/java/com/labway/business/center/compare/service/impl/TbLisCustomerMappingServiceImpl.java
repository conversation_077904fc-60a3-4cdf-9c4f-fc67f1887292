package com.labway.business.center.compare.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.labway.business.center.compare.persistence.mapper.TbLisCustomerMappingMapper;
import com.labway.business.center.compare.persistence.entity.TbLisCustomerMapping;
import com.labway.business.center.compare.repository.center.TbLisCustomerMappingRepository;
import com.labway.business.center.compare.service.TbLisCustomerMappingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * lis客商映射关系表(TbLisCustomerMapping)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-06-30 20:09:02
 */
@Slf4j
@Service("tbLisCustomerMappingService")
public class TbLisCustomerMappingServiceImpl extends ServiceImpl<TbLisCustomerMappingMapper, TbLisCustomerMapping> implements TbLisCustomerMappingService {

    @Resource
    private TbLisCustomerMappingRepository tbLisCustomerMappingRepository;




}

