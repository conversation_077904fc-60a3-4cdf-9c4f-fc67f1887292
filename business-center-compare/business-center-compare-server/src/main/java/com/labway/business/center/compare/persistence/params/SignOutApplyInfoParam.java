package com.labway.business.center.compare.persistence.params;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 2023/04/24 17:25
 * @description
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SignOutApplyInfoParam implements Serializable {

    //申请单样本表id
    private String mainId;
    // 外送申请单条码
    private String barcode;
    // 外送机构编码
    private String hspOrgCode;
    // 签收人编码
    private String receiveUserCode;
    // 签收人名称
    private String receiveUserName;
    // 签收时间
    private Date receiveTime;
    // 签收机构编码
    private String signOrgCode;
    // 签收机构名称
    private String signOrgName;
    // 签收机构条码
    private String signBarcode;
    // 签收主条码（暂无用）
    private String signMainBarcode;
    /**
     * 样本流转状态
     */
    private Integer flowStatus;

}