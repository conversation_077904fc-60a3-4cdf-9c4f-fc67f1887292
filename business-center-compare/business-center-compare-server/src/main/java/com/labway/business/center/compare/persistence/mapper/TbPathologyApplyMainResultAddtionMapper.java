package com.labway.business.center.compare.persistence.mapper;

import java.util.List;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import com.labway.business.center.compare.persistence.entity.TbPathologyApplyMainResultAddtion;

/**
 * (TbPathologyApplyMainResultAddtion)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-07-13 09:11:55
 */
@DS("account")
public interface TbPathologyApplyMainResultAddtionMapper extends BaseMapper<TbPathologyApplyMainResultAddtion> {

/**
* 批量新增数据（MyBatis原生foreach方法）
*
* @param entities List<TbPathologyApplyMainResultAddtion> 实例对象列表
* @return 影响行数
*/
int insertBatch(@Param("entities") List<TbPathologyApplyMainResultAddtion> entities);

/**
* 批量新增或按主键更新数据（MyBatis原生foreach方法）
*
* @param entities List<TbPathologyApplyMainResultAddtion> 实例对象列表
* @return 影响行数
* @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
*/
int insertOrUpdateBatch(@Param("entities") List<TbPathologyApplyMainResultAddtion> entities);

}

