package com.labway.business.center.compare.service.impl.middle;

import com.labway.business.center.compare.persistence.entity.TbOrgApplySampleMain;
import com.labway.business.center.compare.repository.center.TbOrgApplySampleMainRepository;
import com.labway.business.center.compare.service.la.LaDubboService;
import com.labway.business.center.core.enums.Region;
import com.labway.busniess.center.middlebase.jianyan.feign.ResultClient;
import com.labway.busniess.center.middlebase.jianyan.feign.config.JianyanFeignClientFactory;
import com.labway.busniess.center.middlebase.jianyan.reqeust.LisTestMainRequest;
import com.swak.frame.dto.Response;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.List;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.stream.Collectors;

/**
 * 中间库程序 报告
 * 丹阳 结果回传处理
 *
 * <AUTHOR> Tianhao
 * @version 2023/07/19 09:34
 **/
@Service
@DubboService
@RefreshScope
public class ChangzhouMiddleReportServiceImpl extends BaseMiddleReportService {

    @Value("${special.ignoreReportItemMapping.changzhou:}")
    private String ignoreReportItemMapping;

    @DubboReference
    private LaDubboService laDubboService;

    @Autowired
    @Resource
    private TbOrgApplySampleMainRepository tbOrgApplySampleMainRepository;


    public ChangzhouMiddleReportServiceImpl(@Value("${lab.orgCode.changzhou}") String orgCode) {
        super(Region.changzhou, orgCode);
    }

    @Override
    public boolean isOtherMappingType(String hspOrgCode) {
        return true;
    }

    @Override
    public String resultRangeExchange(String hspOrgCode, String range) {
        return range;
    }

    @Override
    protected void notifyByDinger(String content) {
        // TODO: 2023/11/2 丹阳中间库告警机器人
        super.notifyByDinger(content);
    }

    @Override
    protected boolean isSkipReportItemMapping(String hspOrgCode) {
        return ignoreReportItemMapping.contains(hspOrgCode);
    }


    // 根据条码号推送结果
    @Override
    public Response<?> pushReport2MiddleByBarcodes(List<String> barcodes) {

        // 查询样本信息
        List<TbOrgApplySampleMain> tbOrgApplySampleMains = tbOrgApplySampleMainRepository.querySampleBySignBarcodes(orgCode, barcodes);
        if (CollectionUtils.isEmpty(tbOrgApplySampleMains)){
            return Response.success();
        }

        // 跟新样本下载状态
        tbOrgApplySampleMainRepository.updateDownloadStatusByMainIds(tbOrgApplySampleMains.stream().map(e->e.getMainId()).collect(Collectors.toList()), 0);

        // 调用中间程序重新拉取结果
        JianyanFeignClientFactory.getClient(super.region.name(), ResultClient.class).receiveLimsResult(convertSampleInfo(tbOrgApplySampleMains));

        return Response.success();
    }


    public Response<?> dylaPushSampleResultInfo(List<String> sampleCodes) {
        return Response.success();
    }



    //==================================================================================================================

    // 对象转换
    private List<LisTestMainRequest> convertSampleInfo(List<TbOrgApplySampleMain> tbOrgApplySampleMains) {

        List<LisTestMainRequest> lisTestMainRequests = new ArrayList<>();
        for (TbOrgApplySampleMain tbOrgApplySampleMain : tbOrgApplySampleMains) {
            LisTestMainRequest temp = new LisTestMainRequest();
            temp.setHspOrgCode(tbOrgApplySampleMain.getHspOrgCode());
            temp.setBarcode(tbOrgApplySampleMain.getBarcode());
            lisTestMainRequests.add(temp);
        }

        return lisTestMainRequests;
    }



    @Override
    public Response<?> dylaPushSampleResultInfo(Region region, List<String> sampleCodes) {
        return laDubboService.laPushSampleResultInfo(region.name(), sampleCodes);
    }

}