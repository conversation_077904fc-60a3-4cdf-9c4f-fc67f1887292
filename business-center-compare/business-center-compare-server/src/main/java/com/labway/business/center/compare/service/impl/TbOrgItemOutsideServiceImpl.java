package com.labway.business.center.compare.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.labway.business.center.compare.converter.TbOrgItemOutsideConverter;
import com.labway.business.center.compare.dto.QueryItemOutsidePageDTO;
import com.labway.business.center.compare.dto.QueryItemTestByOrgCodeDTO;
import com.labway.business.center.compare.excel.TbOrgItemMappingRelationImportDTO;
import com.labway.business.center.compare.excel.TbOrgItemOutsideImportDTO;
import com.labway.business.center.compare.persistence.entity.*;
import com.labway.business.center.compare.persistence.mapper.TbOrgItemOutsideMapper;
import com.labway.business.center.compare.persistence.params.QueryItemOutsidePageParam;
import com.labway.business.center.compare.persistence.params.QueryItemTestByOrgCodeParam;
import com.labway.business.center.compare.repository.center.*;
import com.labway.business.center.compare.request.DeleteItemOutsideBatchRequest;
import com.labway.business.center.compare.request.ImportItemAndMappingRequest;
import com.labway.business.center.compare.request.QueryItemOutsidePageRequest;
import com.labway.business.center.compare.request.QueryItemTestByOrgCodeRequest;
import com.labway.business.center.compare.request.SaveItemOutsideRequest;
import com.labway.business.center.compare.request.UpdateItemOutsideRequest;
import com.labway.business.center.compare.service.TbOrgItemOutsideService;
import com.labway.business.center.core.config.Pager;
import com.labway.business.center.core.enums.DeleteFlagEnum;
import com.labway.business.center.core.enums.IdEnum;
import com.labway.business.center.core.enums.IsMappingFlagEnum;
import com.labway.business.center.core.enums.ResultCode;
import com.labway.business.center.core.enums.StatusEnableFlagEnum;
import com.labway.business.center.core.user.LoginUserInfoUtil;
import com.labway.business.center.core.util.ExtraCollectionUtil;
import com.labway.business.center.core.util.FileTypeUtil;
import com.labway.business.center.core.util.NotifyUtil;
import com.labway.business.center.core.util.ObsUtil;
import com.labway.sso.core.user.SsoUser;
import com.swak.frame.dto.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 外部检验项目表(TbOrgItemOutside)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-04-23 10:33:24
 */
@Slf4j
@Service("tbOrgItemOutsideService")
public class TbOrgItemOutsideServiceImpl extends ServiceImpl<TbOrgItemOutsideMapper, TbOrgItemOutside> implements TbOrgItemOutsideService {

    @Resource
    private NotifyUtil notifyUtil;
    @Resource
    private TbOrgItemOutsideRepository tbOrgItemOutsideRepository;
    @Resource
    private TbOrgItemTestRelationRepository tbOrgItemTestRelationRepository;
    @Resource
    private TbOrgItemMappingRelationRepository tbOrgItemMappingRelationRepository;
    @Resource
    private TbOrgItemOutsideConverter tbOrgItemOutsideConverter;
    @Resource
    private ObsUtil obsUtil;
    @Resource
    private TbOrgApplySampleMainRepository tbOrgApplySampleMainRepository;
    @Resource
    private TbOrgApplySampleMainItemRepository tbOrgApplySampleMainItemRepository;


    /**
     * 分页查询外部检验项目信息
     *
     * @param queryRequest
     * @return
     */
    @Override
    public Response<Pager<List<QueryItemOutsidePageDTO>>> queryItemOutsidePage(QueryItemOutsidePageRequest queryRequest) {

        IPage queryPage = new Page(queryRequest.getPage(), queryRequest.getPageSize());
        QueryItemOutsidePageParam param = new QueryItemOutsidePageParam();
        BeanUtils.copyProperties(queryRequest, param);

        IPage<TbOrgItemOutside> itemOutsideIPage = tbOrgItemOutsideRepository.queryItemOutsidePage(queryPage, param);

        Pager<List<QueryItemOutsidePageDTO>> resultPager = new Pager<>();

        if (CollectionUtils.isEmpty(itemOutsideIPage.getRecords())) {
            resultPager.setItem(Collections.emptyList());
        } else {
            resultPager.setItem(itemOutsideIPage.getRecords().stream().map(e -> tbOrgItemOutsideConverter.convertQueryItemOutsidePageDTO(e)).collect(Collectors.toList()));
        }
        resultPager.setPage(queryRequest.getPage());
        resultPager.setPageSize(queryRequest.getPageSize());
        resultPager.setTotalPage((int) itemOutsideIPage.getPages());
        resultPager.setTotal(itemOutsideIPage.getTotal());

        return Response.success(resultPager);
    }

    /**
     * 查询业务单元分配的检验项目信息
     *
     * @param queryRequest
     * @return
     */
    @Override
    public Response<List<QueryItemTestByOrgCodeDTO>> queryItemTestByOrgCode(QueryItemTestByOrgCodeRequest queryRequest) {

        QueryItemTestByOrgCodeParam param = new QueryItemTestByOrgCodeParam();
        BeanUtils.copyProperties(queryRequest, param);

        List<TbOrgItemTestRelation> items = tbOrgItemTestRelationRepository.queryItemTestByOrgCode(param);

        if (CollectionUtils.isEmpty(items)) {
            Response.success(Collections.emptyList());
        }

        return Response.success(items.stream().map(e -> tbOrgItemOutsideConverter.convertQueryItemTestByOrgCodeDTO(e)).collect(Collectors.toList()));
    }

    /**
     * 新增外部项目-编码唯一不重复，名称可以重复
     *
     * @param saveRequest
     * @return
     */
    @Override
    public Response<?> saveItemOutside(SaveItemOutsideRequest saveRequest) {

        // 校验是否存在相同的编码
        List<TbOrgItemOutside> tbOrgItemOutsides = tbOrgItemOutsideRepository.queryItemOutsideByCodeAndName(saveRequest.getOrgCode(),
                saveRequest.getCustomerCode(), saveRequest.getItemOutsideCode(), saveRequest.getItemOutsideName());

        if (CollectionUtils.isNotEmpty(tbOrgItemOutsides)) {
            log.info("新增外部项目的编码已经存在！新增项目信息：【{}】", JSON.toJSONString(saveRequest));
            return Response.fail(ResultCode.OUTSIDE_ITEM_HAS_EXIT);
        }

        Date now = new Date();
        List<TbOrgItemOutside> orgItemOutsides = new ArrayList<>();
        TbOrgItemOutside tbOrgItemOutside = new TbOrgItemOutside();
        tbOrgItemOutside.setOutsideId(IdEnum.TB_ORG_ITEM_OUTSIDE.getValue() + IdWorker.getId());
        tbOrgItemOutside.setItemOutsideCode(saveRequest.getItemOutsideCode());
        tbOrgItemOutside.setItemOutsdieName(saveRequest.getItemOutsideName());
        tbOrgItemOutside.setCustomerCode(saveRequest.getCustomerCode());
        tbOrgItemOutside.setCustomerName(saveRequest.getCustomerName());
        tbOrgItemOutside.setOrgCode(saveRequest.getOrgCode());
        tbOrgItemOutside.setOrgName(saveRequest.getOrgName());
        tbOrgItemOutside.setIsMapping(IsMappingFlagEnum.NO_MAPPING.getCode());
        tbOrgItemOutside.setStatus(saveRequest.getStatus());
        tbOrgItemOutside.setCreateBy(LoginUserInfoUtil.getUserLoginName());
        tbOrgItemOutside.setCreateTime(now);
        tbOrgItemOutside.setUpdateBy(LoginUserInfoUtil.getUserLoginName());
        tbOrgItemOutside.setUpdateTime(now);
        tbOrgItemOutside.setDeleteFlag(DeleteFlagEnum.NO_DELETE.getCode());

        orgItemOutsides.add(tbOrgItemOutside);
        tbOrgItemOutsideRepository.saveItemOutsideBatch(orgItemOutsides);

        return Response.success();
    }

    /**
     * 修改外部项目-编码和名称唯一
     *
     * @param updateRequest
     * @return
     */
    @Override
    public Response<?> updateItemOutside(UpdateItemOutsideRequest updateRequest) {

        // 不需要判断了，产品说编码不能改，名字可以重复
//        // 判断项目名称是否存在（因为只有项目名称能修改）
//        List<TbOrgItemOutside> tbOrgItemOutsides = tbOrgItemOutsideRepository.queryItemOutsideExcludeId(updateRequest.getOrgCode(),
//                updateRequest.getCustomerCode(), updateRequest.getItemOutsideName(), updateRequest.getOutsideId());
//        if (CollectionUtils.isNotEmpty(tbOrgItemOutsides)) {
//            if (tbOrgItemOutsides.size() == 1) {
//                return Response.fail(ResultCode.OUTSIDE_ITEM_NAME_HAS_EXIT);
//            } else {
//                TbOrgItemOutside tbOrgItemOutside = tbOrgItemOutsides.get(0);
//                return Response.fail(ResultCode.OUTSIDE_ITEM_NAME_HAS_MORE.getCode(),
//                        String.format(ResultCode.OUTSIDE_ITEM_NAME_HAS_MORE.getMsg(), tbOrgItemOutside.getItemOutsdieName(), tbOrgItemOutside.getOrgName(), tbOrgItemOutside.getCustomerName()));
//            }
//        }

        Date now = new Date();
        TbOrgItemOutside tbOrgItemOutside = new TbOrgItemOutside();
        tbOrgItemOutside.setOutsideId(updateRequest.getOutsideId());
//        tbOrgItemOutside.setItemOutsideCode(updateRequest.getItemOutsideCode());
        tbOrgItemOutside.setItemOutsdieName(updateRequest.getItemOutsideName());
        tbOrgItemOutside.setCustomerCode(updateRequest.getCustomerCode());
        tbOrgItemOutside.setOrgCode(updateRequest.getOrgCode());
        tbOrgItemOutside.setStatus(updateRequest.getStatus());
        tbOrgItemOutside.setUpdateBy(LoginUserInfoUtil.getUserLoginName());
        tbOrgItemOutside.setUpdateTime(now);

        // 更新外部项目信息
        tbOrgItemOutsideRepository.updateItemOutsideById(tbOrgItemOutside);

        return Response.success();
    }

    /**
     * 批量删除外部项目-同步删除对照关系
     *
     * @param deleteRequest
     * @return
     */
    @DS("account")
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Response<?> deleteItemOutsideBatch(DeleteItemOutsideBatchRequest deleteRequest) {

        SsoUser loginUser = null;
        try {
            loginUser = LoginUserInfoUtil.getLoginUser();
        } catch (Exception e) {
            log.error("批量删除外部项目--获取登录用户信息异常！！！！");
        }

        // 查询未签收的样本 判断是否存在有未签收的外送申请单，有则不允许删除
        List<TbOrgApplySampleMain> sampleMainList = tbOrgApplySampleMainRepository.queryApplySampleMainWithUnSign(deleteRequest.getCustomerCode(),deleteRequest.getOrgCode());
        // 根据未签收的样本查询送检项目信息
        if(CollectionUtils.isNotEmpty(sampleMainList)){
            // 未签收的样本ids
            List<String> mainIds = sampleMainList.stream().map(e -> e.getMainId()).collect(Collectors.toList());
            List<TbOrgApplySampleMainItem> tbOrgApplySampleMainItems = tbOrgApplySampleMainItemRepository.queryItemInfoByMainIds(mainIds);
            if (CollectionUtils.isNotEmpty(tbOrgApplySampleMainItems)){
                Optional<TbOrgApplySampleMainItem> first = tbOrgApplySampleMainItems.stream().filter(e -> deleteRequest.getItemOutsideCodes().contains(e.getOutTestItemCode())).findFirst();
                if(first.isPresent()){
                    TbOrgApplySampleMainItem tbOrgApplySampleMainItem = first.get();
                    notifyUtil.send("外部项目【编码："+tbOrgApplySampleMainItem.getOutTestItemCode()+"，名称:"+tbOrgApplySampleMainItem.getOutTestItemName()+"】存在未签收的样本，样本条码号："+tbOrgApplySampleMainItem.getBarcode());
                    return Response.fail(ResultCode.OUTSIDE_ITEM_DELETE_ERROR.getCode()
                            ,String.format(ResultCode.OUTSIDE_ITEM_DELETE_ERROR.getMsg(),tbOrgApplySampleMainItem.getOutTestItemCode(),tbOrgApplySampleMainItem.getOutTestItemName(),tbOrgApplySampleMainItem.getBarcode()));
                }
            }

        }

        // 删除外部项目信息
        tbOrgItemOutsideRepository.deleteItemOutsideBatch(deleteRequest.getOrgCode(), deleteRequest.getCustomerCode(), deleteRequest.getItemOutsideCodes(),loginUser);

        // 删除外部项目的对照关系
        tbOrgItemMappingRelationRepository.deleteItemMappingRelationBatch(deleteRequest.getOrgCode(), deleteRequest.getCustomerCode(), deleteRequest.getItemOutsideCodes(),loginUser);

        return Response.success();
    }

    /**
     * 导入外部项目和对照关系
     *
     * @return
     */
    @DS("account")
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Response<?> importItemAndMapping(ImportItemAndMappingRequest importRequest) throws IOException {

        String url = importRequest.getUrl();
        // 下载文件
        File file = obsUtil.downloadFileByUrl(url);

        // 文件不能为空
        if (!file.exists()) {
            return Response.fail(ResultCode.IMPORT_ITEM_MAPPING_FILE_EMPTY);
        }

        // 文件后缀名必须是xls或xlsx，且根据文件头信息解析到的文件类型必须是excel
        String fileName = file.getName();
        if ((fileName.indexOf(".xls") == -1 && fileName.indexOf(".xlsx") == -1) || FileTypeUtil.isNotExcel(file)) {
            return Response.fail(ResultCode.IMPORT_ITEM_MAPPING_FILE_NOT_EXCEL);
        }
        // 读取第一个sheet页，解析为外部检验项目
        List<TbOrgItemOutsideImportDTO> itemList = EasyExcel.read(file)
                .head(TbOrgItemOutsideImportDTO.class)
                .sheet(0)
                .doReadSync();

        // 读取第二个sheet页，解析为项目映射关系
        List<TbOrgItemMappingRelationImportDTO> mappingList = EasyExcel.read(file)
                .head(TbOrgItemMappingRelationImportDTO.class)
                .sheet(1)
                .doReadSync();

        // 删除本地文件
        if (file.exists()) {
            file.delete();
        }

        // 校验导入数据
        // 检查外部检验项目导入数据
        Map<Integer, List<String>> itemErrorsMap = Collections.emptyMap();
        if (CollectionUtils.isNotEmpty(itemList)) {
            itemErrorsMap = checkItemOutside(itemList, importRequest);
        }

        // 检查项目映射关系导入数据
        Map<Integer, List<String>> mappingErrorsMap = Collections.emptyMap();
        if (CollectionUtils.isNotEmpty(mappingList)) {
            mappingErrorsMap = checkItemMapping(mappingList, itemList, importRequest);
        }

        // 错误信息为空，才进行持久化操作
        if (MapUtils.isEmpty(itemErrorsMap) && MapUtils.isEmpty(mappingErrorsMap)) {
            // 保存数据
            saveItemOutside(importRequest, itemList);
            saveItemMapping(importRequest, mappingList);
        } else {
            // 返回错误信息
            String errorMsg = MapUtils.isNotEmpty(itemErrorsMap) ? "Sheet1中，" + parseErrorsMap(itemErrorsMap) : "";
            errorMsg = MapUtils.isNotEmpty(mappingErrorsMap) ? errorMsg + "Sheet2中，" + parseErrorsMap(mappingErrorsMap) : errorMsg;
            return Response.fail(ResultCode.PARAMS_NOT_VALIDATE.getCode(), errorMsg);
        }

        return Response.success();
    }


    /**
     * 检查外部检验项目导入数据
     *
     * @return
     */
    private Map<Integer, List<String>> checkItemOutside(List<TbOrgItemOutsideImportDTO> itemList, ImportItemAndMappingRequest importRequest) {

        if(CollectionUtils.isEmpty(itemList)) {
            return Collections.emptyMap();
        }

        // 业务单元编码
        String orgCode = importRequest.getOrgCode();
        // 客商编码
        String customerCode = importRequest.getCustomerCode();

        // 校验不通过的行及错误提示
        Map<Integer, List<String>> errorsMap = new TreeMap<>();

        // 补充行号
        for (int num = 0; num < itemList.size(); num++) {
            // 所在行行号
            // 序号从0开始，且excel首行为表头，所以加2
            itemList.get(num).setRowNum(num + 2);
        }

        // ------ 字段非空校验不通过 ------
        List<String> notEmptyErrors = new ArrayList<>();
        for (TbOrgItemOutsideImportDTO item : itemList) {

            notEmptyErrors.clear();
            int rowNum = item.getRowNum();

            // 检查字段非空
            if (StringUtils.isEmpty(item.getItemOutsideCode())) {
                notEmptyErrors.add("外部项目编码为空");
            }
            if (StringUtils.isEmpty(item.getItemOutsideName())) {
                notEmptyErrors.add("外部项目名称为空");
            }

            // 收集错误提示
            if (CollectionUtils.isNotEmpty(notEmptyErrors)) {
                List<String> errorList = ExtraCollectionUtil.getOrDefaultAndPut(errorsMap, rowNum, () -> new ArrayList<>());
                errorList.addAll(notEmptyErrors);
            }
        }

        // ------ 字段长度校验 ------
        List<String> dataLengthErrors = new ArrayList<>();
        for (TbOrgItemOutsideImportDTO itemDto : itemList) {

            dataLengthErrors.clear();
            int rowNum = itemDto.getRowNum();

            // 检查字段长度
            if (StringUtils.isNotBlank(itemDto.getItemOutsideCode()) && itemDto.getItemOutsideCode().length() > 50) {
                dataLengthErrors.add("外部项目编码长度超过50个字符");
            }
            if (StringUtils.isNotBlank(itemDto.getItemOutsideName()) && itemDto.getItemOutsideName().length() > 50) {
                dataLengthErrors.add("外部项目名称长度超过50个字符");
            }

            // 收集错误提示
            if (!org.springframework.util.CollectionUtils.isEmpty(dataLengthErrors)) {
                List<String> errorList = ExtraCollectionUtil.getOrDefaultAndPut(errorsMap, rowNum, () -> new ArrayList<>());
                errorList.addAll(dataLengthErrors);
                errorsMap.put(rowNum, errorList);
            }
        }

        // ------ 检查外部项目编码在数据库中或者Excel中是否重复 ------
        // 导入数据中的外部项目编码
        List<String> codeList = itemList.stream().map(TbOrgItemOutsideImportDTO::getItemOutsideCode).filter(StringUtils::isNotEmpty).collect(Collectors.toList());

        // 查询数据库，查找已存在的外部项目编码
        List<TbOrgItemOutside> codeRepeatItemList = tbOrgItemOutsideRepository.queryItemOutsideListByCodes(orgCode, customerCode, codeList);

        // 如果查到了，则说明存在重复的编码，需要找出其所在行号
        if (CollectionUtils.isNotEmpty(codeRepeatItemList)) {

            // 重复的外部项目编码
            HashSet<String> codeRepeatItemSet = new HashSet<>(codeRepeatItemList.stream().map(TbOrgItemOutside::getItemOutsideCode).collect(Collectors.toList()));

            // 找出其所在Excel行数并记录
            for (TbOrgItemOutsideImportDTO item : itemList) {
                if (codeRepeatItemSet.contains(item.getItemOutsideCode())) {
                    // 行号
                    int rowNum = item.getRowNum();
                    List<String> errorList = ExtraCollectionUtil.getOrDefaultAndPut(errorsMap, rowNum, () -> new ArrayList<>());
                    errorList.add("外部项目编码已存在");
                }
            }
        }

        // 检查外部项目编码导入数据中是否存在重复
        Map<String, List<TbOrgItemOutsideImportDTO>> groupByCodeItems = itemList.stream().filter(i -> StringUtils.isNotEmpty(i.getItemOutsideCode()))
                .collect(Collectors.groupingBy(TbOrgItemOutsideImportDTO::getItemOutsideCode));
        for (Map.Entry<String, List<TbOrgItemOutsideImportDTO>> entry : groupByCodeItems.entrySet()) {

            List<TbOrgItemOutsideImportDTO> tempList = entry.getValue();
            // 数量大于1，则存在重复
            if (CollectionUtils.isNotEmpty(tempList) && tempList.size() > 1) {
                List<Integer> rowNumList = tempList.stream().map(TbOrgItemOutsideImportDTO::getRowNum).collect(Collectors.toList());
                String errorInfo = "外部项目编码在导入文件中重复";
                for (int rowNum : rowNumList) {
                    List<String> errorList = ExtraCollectionUtil.getOrDefaultAndPut(errorsMap, rowNum, () -> new ArrayList<>());
                    errorList.add(errorInfo);
                }
            }
        }


        return errorsMap;
    }

    /**
     * 检查项目映射关系导入数据
     *
     * @return
     */
    private Map<Integer, List<String>> checkItemMapping(List<TbOrgItemMappingRelationImportDTO> mappingList, List<TbOrgItemOutsideImportDTO> itemList, ImportItemAndMappingRequest importRequest) {

        if(CollectionUtils.isEmpty(mappingList)) {
            return Collections.emptyMap();
        }

        // 业务单元编码
        String orgCode = importRequest.getOrgCode();
        // 客商编码
        String customerCode = importRequest.getCustomerCode();

        // 校验不通过的行及错误提示
        Map<Integer, List<String>> errorsMap = new TreeMap<>();

        // 补充行号
        for (int num = 0; num < mappingList.size(); num++) {
            // 所在行行号
            // 序号从0开始，且excel首行为表头，所以加2
            mappingList.get(num).setRowNum(num + 2);
        }

        // 字段非空校验不通过
        List<String> notEmptyErrors = new ArrayList<>();
        for (TbOrgItemMappingRelationImportDTO mapping : mappingList) {

            notEmptyErrors.clear();
            int rowNum = mapping.getRowNum();

            // 检查字段非空
            if (StringUtils.isEmpty(mapping.getItemTestCode())) {
                notEmptyErrors.add("检验项目编码为空");
            }
            if (StringUtils.isEmpty(mapping.getItemTestName())) {
                notEmptyErrors.add("检验项目名称为空");
            }
            if (StringUtils.isEmpty(mapping.getItemOutsideCode())) {
                notEmptyErrors.add("外部项目编码为空");
            }
            if (StringUtils.isEmpty(mapping.getItemOutsideName())) {
                notEmptyErrors.add("外部项目名称为空");
            }

            // 收集错误提示
            if (CollectionUtils.isNotEmpty(notEmptyErrors)) {
                List<String> errorList = ExtraCollectionUtil.getOrDefaultAndPut(errorsMap, rowNum, () -> new ArrayList<>());
                errorList.addAll(notEmptyErrors);
            }
        }

        // ------ 字段长度校验 ------
        List<String> dataLengthErrors = new ArrayList<>();
        for (TbOrgItemMappingRelationImportDTO mapping : mappingList) {

            dataLengthErrors.clear();
            int rowNum = mapping.getRowNum();

            // 检查字段长度
            if (StringUtils.isNotBlank(mapping.getItemTestCode()) && mapping.getItemTestCode().length() > 50) {
                dataLengthErrors.add("检验项目编码长度超过50个字符");
            }
            if (StringUtils.isNotBlank(mapping.getItemTestName()) && mapping.getItemTestName().length() > 50) {
                dataLengthErrors.add("检验项目名称长度超过50个字符");
            }
            if (StringUtils.isNotBlank(mapping.getItemOutsideCode()) && mapping.getItemOutsideCode().length() > 50) {
                dataLengthErrors.add("外部项目编码长度超过50个字符");
            }
            if (StringUtils.isNotBlank(mapping.getItemOutsideName()) && mapping.getItemOutsideName().length() > 50) {
                dataLengthErrors.add("外部项目名称长度超过50个字符");
            }

            // 收集错误提示
            if (CollectionUtils.isNotEmpty(dataLengthErrors)) {
                List<String> errorList = ExtraCollectionUtil.getOrDefaultAndPut(errorsMap, rowNum, () -> new ArrayList<>());
                errorList.addAll(dataLengthErrors);
                errorsMap.put(rowNum, errorList);
            }
        }

        // ------ 校验检验项目编码是否存在（应在数据库中存在） ------
        List<String> itemCodes = mappingList.stream().map(TbOrgItemMappingRelationImportDTO::getItemTestCode).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        // 根据业务单元编码、项目编码、项目名称查询
        List<TbOrgItemTestRelation> existsItemTests = tbOrgItemTestRelationRepository.queryOrgItemTestListByOrgCodeAndItemCodes(orgCode, itemCodes);
        Set<String> existsItemTestSet = existsItemTests.stream().map(TbOrgItemTestRelation::getItemTestCode).collect(Collectors.toSet());
        List<TbOrgItemMappingRelationImportDTO> notExistsList = mappingList.stream().filter(m -> StringUtils.isNotEmpty(m.getItemTestCode())).filter(x -> !existsItemTestSet.contains(x.getItemTestCode())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(notExistsList)) {
            // 记录不存在的检验项目
            for (TbOrgItemMappingRelationImportDTO dto : notExistsList) {
                int rowNum = dto.getRowNum();
                List<String> errors = ExtraCollectionUtil.getOrDefaultAndPut(errorsMap, rowNum, () -> new ArrayList<>());
                errors.add("检验项目编码不存在");
            }
        }

        // ------ 校验外部项目编码是否存在（应在数据库中或导入数据中存在） ------
        List<String> itemOutsideCodes = mappingList.stream().map(TbOrgItemMappingRelationImportDTO::getItemOutsideCode).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        // 先在导入数据中查找
        Set<String> importOutsideItems = itemList.stream().map(TbOrgItemOutsideImportDTO::getItemOutsideCode).collect(Collectors.toSet());
        List<TbOrgItemMappingRelationImportDTO> notExistsInExcel = mappingList.stream().filter(m -> StringUtils.isNotEmpty(m.getItemOutsideCode())).filter(x -> !importOutsideItems.contains(x.getItemOutsideCode())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(notExistsInExcel)) {
            List<TbOrgItemOutside> queryResult = tbOrgItemOutsideRepository.queryItemOutsideListByCodes(orgCode, customerCode, itemOutsideCodes);
            Set<String> entityOutsideItems = queryResult.stream().map(TbOrgItemOutside::getItemOutsideCode).collect(Collectors.toSet());
            List<TbOrgItemMappingRelationImportDTO> notExistsItems = notExistsInExcel.stream().filter(x -> !entityOutsideItems.contains(x.getItemOutsideCode())).collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(notExistsItems)) {
                // 记录不存在的外部项目
                for (TbOrgItemMappingRelationImportDTO dto : notExistsItems) {
                    int rowNum = dto.getRowNum();
                    List<String> errors = ExtraCollectionUtil.getOrDefaultAndPut(errorsMap, rowNum, () -> new ArrayList<>());
                    errors.add("外部项目编码不存在");
                }
            }
        }

        // ------ 校验映射关系是否重复（应在数据库中和导入数据中检查） ------

        // 编码非空的映射关系
        List<TbOrgItemMappingRelationImportDTO> notEmptyMappings = mappingList.stream().filter(x -> StringUtils.isNotEmpty(x.getItemTestCode()) && StringUtils.isNotEmpty(x.getItemOutsideCode())).collect(Collectors.toList());

        // 检查映射关系在数据库中是否重复
        List<TbOrgItemMappingRelation> entities = notEmptyMappings.stream().map(x -> {
                                                        TbOrgItemMappingRelation entity = new TbOrgItemMappingRelation();
                                                        entity.setItemTestCode(x.getItemTestCode());
                                                        entity.setItemOutsideCode(x.getItemOutsideCode());
                                                        return entity;
                                                    }).collect(Collectors.toList());
        List<TbOrgItemMappingRelation> existsMappings = tbOrgItemMappingRelationRepository.queryMappingByOutsideAndTestCodes(orgCode, customerCode, entities);
        if (CollectionUtils.isNotEmpty(existsMappings)) {

            // 编码对照与行号的对应关系
            Map<String, Integer> rowNumMap = new HashMap<>();
            notEmptyMappings.forEach(m -> rowNumMap.put(m.getItemTestCode() + m.getItemOutsideCode(), m.getRowNum()));

            // 记录重复的对照关系
            for (TbOrgItemMappingRelation mapping : existsMappings) {
                // 找到行号
                int rowNum = rowNumMap.get(mapping.getItemTestCode() + mapping.getItemOutsideCode());
                List<String> errors = ExtraCollectionUtil.getOrDefaultAndPut(errorsMap, rowNum, () -> new ArrayList<>());
                errors.add("对照关系已存在");
            }
        }

        // 检查映射关系在导入数据中是否重复
        Map<String, List<TbOrgItemMappingRelationImportDTO>> groupMappings = notEmptyMappings.stream().collect(Collectors.groupingBy(m -> m.getItemTestCode() + m.getItemOutsideCode()));
        for (Map.Entry<String, List<TbOrgItemMappingRelationImportDTO>> entry : groupMappings.entrySet()) {
            List<TbOrgItemMappingRelationImportDTO> mappings = entry.getValue();

            // 组内数量大于1，说明存在重复
            if (mappings.size() > 1) {

                String errorMsg = "对照关系在导入文件中重复";
                for (TbOrgItemMappingRelationImportDTO dto : mappings) {
                    int rowNum = dto.getRowNum();
                    List<String> errors = ExtraCollectionUtil.getOrDefaultAndPut(errorsMap, rowNum, () -> new ArrayList<>());
                    errors.add(errorMsg);
                }
            }
        }

        return errorsMap;
    }

    /**
     * 保存外部检验项目
     */
    private void saveItemOutside(ImportItemAndMappingRequest importRequest, List<TbOrgItemOutsideImportDTO> itemList) {

        if (CollectionUtils.isEmpty(itemList)) {
            return;
        }

        Date now = new Date();
        List<TbOrgItemOutside> orgItemOutsides = new ArrayList<>();

        // 转换为实体类
        for (TbOrgItemOutsideImportDTO item : itemList) {
            TbOrgItemOutside itemEntity = new TbOrgItemOutside();
            itemEntity.setOutsideId(IdEnum.TB_ORG_ITEM_OUTSIDE.getValue() + IdWorker.getId());
            // 数据来源：导入文件
            itemEntity.setItemOutsideCode(item.getItemOutsideCode());
            itemEntity.setItemOutsdieName(item.getItemOutsideName());
            // 数据来源：前端表单
            itemEntity.setCustomerCode(importRequest.getCustomerCode());
            itemEntity.setCustomerName(importRequest.getCustomerName());
            itemEntity.setOrgCode(importRequest.getOrgCode());
            itemEntity.setOrgName(importRequest.getOrgName());
            // 补充其它字段
            itemEntity.setIsMapping(IsMappingFlagEnum.NO_MAPPING.getCode());
            itemEntity.setStatus(StatusEnableFlagEnum.ENABLED.getCode());
            itemEntity.setCreateBy(LoginUserInfoUtil.getUserLoginName());
            itemEntity.setCreateTime(now);
            itemEntity.setUpdateBy(LoginUserInfoUtil.getUserLoginName());
            itemEntity.setUpdateTime(now);
            itemEntity.setDeleteFlag(DeleteFlagEnum.NO_DELETE.getCode());
            orgItemOutsides.add(itemEntity);
        }

        // 批量保存
        tbOrgItemOutsideRepository.saveItemOutsideBatch(orgItemOutsides);
    }

    /**
     * 保存项目映射关系
     */
    private void saveItemMapping(ImportItemAndMappingRequest importRequest, List<TbOrgItemMappingRelationImportDTO> mappingList) {

        if (CollectionUtils.isEmpty(mappingList)) {
            return;
        }

        // 更新是否有对照关系
        List<TbOrgItemOutside> entities = mappingList.stream().map(m -> {
            TbOrgItemOutside orgItemOutside = new TbOrgItemOutside();
            orgItemOutside.setOrgCode(importRequest.getOrgCode());
            orgItemOutside.setCustomerCode(importRequest.getCustomerCode());
            orgItemOutside.setItemOutsideCode(m.getItemOutsideCode());
            return orgItemOutside;
        }).collect(Collectors.toList());
        tbOrgItemOutsideRepository.updateStatusBatch(entities, IsMappingFlagEnum.MAPPING.getCode());

        // 添加新的对照关系
        List<TbOrgItemMappingRelation> mappingEntityList = new ArrayList<>();
        Date now = new Date();
        for (TbOrgItemMappingRelationImportDTO mapping : mappingList) {
            TbOrgItemMappingRelation entity = new TbOrgItemMappingRelation();
            entity.setMappingId(IdEnum.TB_ORG_ITEM_MAPPING_RELATION.getValue() + IdWorker.getId());
            // 数据来源：导入文件
            entity.setItemTestCode(mapping.getItemTestCode());
            entity.setItemTestName(mapping.getItemTestName());
            entity.setItemOutsideCode(mapping.getItemOutsideCode());
            entity.setItemOutsideName(mapping.getItemOutsideName());
            // 数据来源：前端表单
            entity.setCustomerCode(importRequest.getCustomerCode());
            entity.setCustomerName(importRequest.getCustomerName());
            entity.setOrgCode(importRequest.getOrgCode());
            entity.setOrgName(importRequest.getOrgName());
            // 补充其它字段
            entity.setCreateBy(LoginUserInfoUtil.getUserLoginName());
            entity.setCreateTime(now);
            entity.setUpdateBy(LoginUserInfoUtil.getUserLoginName());
            entity.setUpdateTime(now);
            entity.setDeleteFlag(DeleteFlagEnum.NO_DELETE.getCode());
            mappingEntityList.add(entity);
        }
        tbOrgItemMappingRelationRepository.saveOrgItemMappingRelation(mappingEntityList);
    }

    private String parseErrorsMap(Map<Integer, List<String>> errorsMap) {

        String errorInfo = "";
        for (Map.Entry<Integer, List<String>> entry : errorsMap.entrySet()) {
            int rowNum = entry.getKey();
            List<String> errors = entry.getValue();
            errorInfo += ("第" + rowNum + "行，");
            for (int i = 0; i < errors.size(); i++) {
                errorInfo += errors.get(i);
                if (i < errors.size() - 1) {
                    errorInfo += "，";
                }
            }
            errorInfo += "<br/>";
        }
        return errorInfo;
    }

}

