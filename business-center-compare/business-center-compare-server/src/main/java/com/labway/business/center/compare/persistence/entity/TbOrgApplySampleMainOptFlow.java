package com.labway.business.center.compare.persistence.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.io.Serializable;
import java.util.Date;

/**
 * 外送样本流转记录表(TbOrgApplySampleMainOptFlow)表实体类
 *
 * <AUTHOR>
 * @since 2023-06-16 09:30:38
 */
@SuppressWarnings("serial")
public class TbOrgApplySampleMainOptFlow extends Model<TbOrgApplySampleMainOptFlow> {
    //样本流转记录表主键id
    @TableId(type = IdType.INPUT)
    private String optFlowId;
    //申请单id
    private String formId;
    //申请单编码
    private String formCode;
    //样本条码号
    private String barcode;
    //实验室条码号
    private String lwBarcode;
    //样本流转状态
    private Integer flowStatus;
    //送检机构编码
    private String hspOrgCode;
    //送检机构名称
    private String hspOrgName;
    //病人姓名
    private String patientName;
    //病人编号
    private String patientCode;
    //病人性别
    private String sex;
    //病人年龄
    private String age;
    //床号
    private String patientBed;
    //就诊类型
    private String applyType;
    //申请科室
    private String dept;
    //送检医生
    private String sendDoctor;
    //是否加急
    private String urgent;
    //备注
    private String remark;
    //创建时间
    private Date createTime;
    //创建人id
    private String createBy;
    //更新时间
    private Date updateTime;
    //更新人id
    private String updateBy;
    //删除标识 0未删除 1删除
    @TableLogic(value = "0", delval = "1")
    private Integer deleteFalg;
    //操作人id
    private String optId;
    //操作人名称
    private String optName;
    //操作时间
    private Date optTime;


    public String getOptFlowId() {
        return optFlowId;
    }

    public void setOptFlowId(String optFlowId) {
        this.optFlowId = optFlowId;
    }

    public String getFormId() {
        return formId;
    }

    public void setFormId(String formId) {
        this.formId = formId;
    }

    public String getFormCode() {
        return formCode;
    }

    public void setFormCode(String formCode) {
        this.formCode = formCode;
    }

    public String getBarcode() {
        return barcode;
    }

    public void setBarcode(String barcode) {
        this.barcode = barcode;
    }

    public String getLwBarcode() {
        return lwBarcode;
    }

    public void setLwBarcode(String lwBarcode) {
        this.lwBarcode = lwBarcode;
    }

    public Integer getFlowStatus() {
        return flowStatus;
    }

    public void setFlowStatus(Integer flowStatus) {
        this.flowStatus = flowStatus;
    }

    public String getHspOrgCode() {
        return hspOrgCode;
    }

    public void setHspOrgCode(String hspOrgCode) {
        this.hspOrgCode = hspOrgCode;
    }

    public String getHspOrgName() {
        return hspOrgName;
    }

    public void setHspOrgName(String hspOrgName) {
        this.hspOrgName = hspOrgName;
    }

    public String getPatientName() {
        return patientName;
    }

    public void setPatientName(String patientName) {
        this.patientName = patientName;
    }

    public String getPatientCode() {
        return patientCode;
    }

    public void setPatientCode(String patientCode) {
        this.patientCode = patientCode;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getAge() {
        return age;
    }

    public void setAge(String age) {
        this.age = age;
    }

    public String getPatientBed() {
        return patientBed;
    }

    public void setPatientBed(String patientBed) {
        this.patientBed = patientBed;
    }

    public String getApplyType() {
        return applyType;
    }

    public void setApplyType(String applyType) {
        this.applyType = applyType;
    }

    public String getDept() {
        return dept;
    }

    public void setDept(String dept) {
        this.dept = dept;
    }

    public String getSendDoctor() {
        return sendDoctor;
    }

    public void setSendDoctor(String sendDoctor) {
        this.sendDoctor = sendDoctor;
    }

    public String getUrgent() {
        return urgent;
    }

    public void setUrgent(String urgent) {
        this.urgent = urgent;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public Integer getDeleteFalg() {
        return deleteFalg;
    }

    public void setDeleteFalg(Integer deleteFalg) {
        this.deleteFalg = deleteFalg;
    }

    public String getOptId() {
        return optId;
    }

    public void setOptId(String optId) {
        this.optId = optId;
    }

    public String getOptName() {
        return optName;
    }

    public void setOptName(String optName) {
        this.optName = optName;
    }

    public Date getOptTime() {
        return optTime;
    }

    public void setOptTime(Date optTime) {
        this.optTime = optTime;
    }


}

