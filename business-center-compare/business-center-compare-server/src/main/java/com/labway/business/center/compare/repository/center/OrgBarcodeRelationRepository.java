package com.labway.business.center.compare.repository.center;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.business.center.compare.persistence.entity.TbOrgBarcodeRelation;
import com.labway.business.center.compare.persistence.mapper.TbOrgBarcodeRelationMapper;
import com.labway.business.center.compare.request.OrgBarcodeRelationReqeust;
import com.labway.business.center.core.enums.DeleteFlagEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2023/04/19 17:46
 * @description
 **/
@Slf4j
@Repository
@RefreshScope
public class OrgBarcodeRelationRepository {

    @Resource
    private TbOrgBarcodeRelationMapper orgBarcodeRelationMapper;

    /**
     * 根据入参进行条件查询
     * @param relationReqeust
     * @return
     */
    public List<TbOrgBarcodeRelation> queryBarcodeList(OrgBarcodeRelationReqeust relationReqeust){
        if (relationReqeust != null){
            LambdaQueryWrapper<TbOrgBarcodeRelation> queryWrapper = Wrappers.lambdaQuery();
            if (!StringUtils.isEmpty(relationReqeust.getHspOrgCode())){
                queryWrapper.eq(TbOrgBarcodeRelation::getHspOrgCode,relationReqeust.getHspOrgCode());
            }
            if (!StringUtils.isEmpty(relationReqeust.getOrgCode())){
                queryWrapper.eq(TbOrgBarcodeRelation::getOrgCode,relationReqeust.getOrgCode());
            }
            if (!StringUtils.isEmpty(relationReqeust.getBarcode())){
                queryWrapper.eq(TbOrgBarcodeRelation::getBarcode,relationReqeust.getBarcode());
            }
            if (!StringUtils.isEmpty(relationReqeust.getSignBarcode())){
                queryWrapper.eq(TbOrgBarcodeRelation::getSignBarcode,relationReqeust.getSignBarcode());
            }
            if (!StringUtils.isEmpty(relationReqeust.getSplitBarcode())){
                queryWrapper.eq(TbOrgBarcodeRelation::getSplitBarcode,relationReqeust.getSplitBarcode());
            }

            queryWrapper.eq(TbOrgBarcodeRelation::getDeleteFlag,DeleteFlagEnum.NO_DELETE.getCode());

            return orgBarcodeRelationMapper.selectList(queryWrapper);
        }

        return null;
    }

    /**
     * 批量删除条码
     * @param barcodes
     * @param hspOrgCode
     */
    public void deleteRelationByBarcode(List<String> barcodes, String hspOrgCode) {
        LambdaUpdateWrapper<TbOrgBarcodeRelation> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(TbOrgBarcodeRelation::getDeleteFlag,DeleteFlagEnum.DELETED.getCode());
        updateWrapper.eq(TbOrgBarcodeRelation::getHspOrgCode,hspOrgCode);
        updateWrapper.in(TbOrgBarcodeRelation::getBarcode,barcodes);

        orgBarcodeRelationMapper.update(null,updateWrapper);
    }

}