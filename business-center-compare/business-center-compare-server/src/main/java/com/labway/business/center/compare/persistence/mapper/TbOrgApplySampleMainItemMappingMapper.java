package com.labway.business.center.compare.persistence.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.labway.business.center.compare.persistence.entity.TbOrgApplySampleMainItemMapping;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 送检项目对照的检验项目信息表(TbOrgApplySampleMainItemMapping)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-04-24 13:28:22
 */
@DS("account")
public interface TbOrgApplySampleMainItemMappingMapper extends BaseMapper<TbOrgApplySampleMainItemMapping> {

/**
* 批量新增数据（MyBatis原生foreach方法）
*
* @param entities List<TbOrgApplySampleMainItemMapping> 实例对象列表
* @return 影响行数
*/
int insertBatch(@Param("entities") List<TbOrgApplySampleMainItemMapping> entities);

/**
* 批量新增或按主键更新数据（MyBatis原生foreach方法）
*
* @param entities List<TbOrgApplySampleMainItemMapping> 实例对象列表
* @return 影响行数
* @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
*/
int insertOrUpdateBatch(@Param("entities") List<TbOrgApplySampleMainItemMapping> entities);

}

