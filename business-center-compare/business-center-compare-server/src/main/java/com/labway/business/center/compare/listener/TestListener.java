package com.labway.business.center.compare.listener;

import com.alibaba.fastjson.JSONObject;
import com.labway.business.center.compare.config.RabbitmqConfig;
import com.labway.business.center.compare.dto.RemoveOrgCustomerRelationNotifyDTO;
import com.labway.business.center.compare.request.DeleteOrgItemTestRequest;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.List;
import java.util.stream.Collector;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2023/05/04 14:18
 * @description
 **/
@Slf4j
@Component
public class TestListener {

    /**
     * lims监听模拟（删除检验项目信息）
     * @param msg
     */
//    @RabbitListener(queues = {RabbitmqConfig.LABWAY_BUSINESS_TEST_ITEM_QUEUE})
    public void testListenTestItem(String msg, Message message, Channel channel) throws IOException {
        log.info("监听到消息：{}", msg);
        long deliveryTag = message.getMessageProperties().getDeliveryTag();
        DeleteOrgItemTestRequest deleteOrgItemTestRequest = JSONObject.parseObject(msg, DeleteOrgItemTestRequest.class);
        log.info("删除项目的业务单元：【{}】，删除项目编码：【{}】", deleteOrgItemTestRequest.getOrgCode(), String.join(",", deleteOrgItemTestRequest.getItemTestCodes()));
    }

    /**
     * lims监听模拟（删除业务单元关联的客商信息）
     */
//    @RabbitListener(queues = {RabbitmqConfig.LABWAY_BUSINESS_CUSTOMER_QUEUE})
    public void testListenCustomerRelation(String msg, Message message, Channel channel){
        log.info("监听到消息：{}", msg);
        List<RemoveOrgCustomerRelationNotifyDTO> notifyDTOS = JSONObject.parseArray(msg, RemoveOrgCustomerRelationNotifyDTO.class);
        String orgNameStr = notifyDTOS.stream().map(e -> e.getOrgName()).collect(Collectors.joining(","));
        String customerNameStr = notifyDTOS.stream().map(e -> e.getCustomerName()).collect(Collectors.joining(","));
        log.info("删除客商的业务单元：【{}】，删除的客商编码：【{}】",orgNameStr , customerNameStr);
    }

    /**
     * lims监听模拟（业务中台通知lims补录样本信息）
     * 外送样本时根据样本录入标识选择是否需要通知lims进行样本补录操作
     */

    /**
     * lims监听模拟 (业务中台通知lis样本取样)
     */

    /**
     * lims监听模拟 (业务中台通知lis样本妥投)
     */

    /**
     * lims监听模拟 (业务中台通知lis样本签收)
     */


    /**
     * 监听物流样本妥投信息
     */





}