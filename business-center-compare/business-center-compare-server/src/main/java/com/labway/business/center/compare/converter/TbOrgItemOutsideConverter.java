package com.labway.business.center.compare.converter;

import com.labway.business.center.compare.dto.QueryItemOutsidePageDTO;
import com.labway.business.center.compare.dto.QueryItemTestByOrgCodeDTO;
import com.labway.business.center.compare.dto.QueryOrgItemMappingRelationDTO;
import com.labway.business.center.compare.persistence.entity.TbOrgItemMappingRelation;
import com.labway.business.center.compare.persistence.entity.TbOrgItemOutside;
import com.labway.business.center.compare.persistence.entity.TbOrgItemTestRelation;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface TbOrgItemOutsideConverter {

    QueryItemOutsidePageDTO convertQueryItemOutsidePageDTO(TbOrgItemOutside orgItemOutside);

    QueryOrgItemMappingRelationDTO convertQueryOrgItemMappingRelationDTO(TbOrgItemMappingRelation tbOrgItemMappingRelation);

    QueryItemTestByOrgCodeDTO convertQueryItemTestByOrgCodeDTO(TbOrgItemTestRelation tbOrgItemTestRelation);

}
