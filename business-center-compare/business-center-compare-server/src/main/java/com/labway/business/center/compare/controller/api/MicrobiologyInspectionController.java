package com.labway.business.center.compare.controller.api;

import com.labway.business.center.compare.dto.lims.MicrobiologyInspectionDto;
import com.swak.frame.dto.Response;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Set;

/**
 * 微生物检验
 *
 * <AUTHOR> on 2024/12/9.
 */
@RestController
@RequestMapping("/api/microbiology")
public class MicrobiologyInspectionController {

	/**
	 * 根据外部条码号查询对应的微生物结果信息
	 * @param outBarcodes 外部条码号
	 * @return {@link MicrobiologyInspectionDto 微生物结果}
	 */
	@PostMapping("/microbiology-result")
	public Response<List<MicrobiologyInspectionDto>> getMicrobiologyResult(@RequestBody Set<String> outBarcodes) {
		return null;
	}
}
