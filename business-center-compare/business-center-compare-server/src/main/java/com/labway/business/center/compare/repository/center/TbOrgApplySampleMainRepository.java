package com.labway.business.center.compare.repository.center;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.labway.business.center.compare.dto.OrgApplyResultSamplesDTO;
import com.labway.business.center.compare.dto.SampleInfoDto;
import com.labway.business.center.compare.dto.monitoring.SampleReportMonitoringDto;
import com.labway.business.center.compare.persistence.entity.TbOrgApplySampleMain;
import com.labway.business.center.compare.persistence.mapper.TbOrgApplySampleMainMapper;
import com.labway.business.center.compare.persistence.params.HandoverSampleMainInfoParam;
import com.labway.business.center.compare.persistence.params.SignOutApplyInfoParam;
import com.labway.business.center.compare.request.AddOptFlowInfoRequest;
import com.labway.business.center.compare.request.LimsCancelHandSampleRequest;
import com.labway.business.center.compare.request.OrgApplySampleSendRequest;
import com.labway.business.center.compare.request.QueryLimsOutSourceSampleInfoRequest;
import com.labway.business.center.compare.request.compare.QueryPDASampleInfoRequest;
import com.labway.business.center.core.enums.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @version 2023/04/24 14:16
 * @description
 **/
@Slf4j
@Repository
@RefreshScope
public class TbOrgApplySampleMainRepository {

//    @Value("${desensitization.fields}")
//    private List<String> fieldNames = new ArrayList<>();
    @Resource
    private TbOrgApplySampleMainMapper tbOrgApplySampleMainMapper;

    /**
     * 根据客商编码和送检条码查询申领单信息
     * @param barcode 条码号
     * @param customerCode 客商编码
     * @return
     */
    public TbOrgApplySampleMain queryApplySampleMainInfoForSign(String barcode, String customerCode) {

        TbOrgApplySampleMain tbOrgApplySampleMain = tbOrgApplySampleMainMapper.selectOne(Wrappers.lambdaQuery(TbOrgApplySampleMain.class)
                .eq(TbOrgApplySampleMain::getBarcode, barcode)
                .eq(TbOrgApplySampleMain::getHspOrgCode, customerCode)
                .ne(TbOrgApplySampleMain::getStatus, 4)  // 这里过滤掉取消外送的是为了避免二次外送时使用相同的条码号
                .eq(TbOrgApplySampleMain::getDeleteFlag, 0));
        // 脱敏字段解密
//        FieldDesensitizationUtil.decryptFields(tbOrgApplySampleMain,fieldNames);
        return tbOrgApplySampleMain;
    }


    /**
     * 根据申请单id更新申请单对照状态
     * @param mainId
     * @param mappingStatus 1待对照 2待签收 3已签收 4取消外送
     * @return
     */
    public int updateApplySampleMainStatusById(String mainId, Integer mappingStatus,Integer flowStatus,Integer handover,String returnReason,String optUser) {
        return tbOrgApplySampleMainMapper.update(null,Wrappers.lambdaUpdate(TbOrgApplySampleMain.class)
                .set(TbOrgApplySampleMain::getStatus,mappingStatus)
                .set(flowStatus!=null,TbOrgApplySampleMain::getFlowStatus,flowStatus)
                .set(handover!=null,TbOrgApplySampleMain::getIsHandover,handover)
                .set(StringUtils.isNotBlank(returnReason),TbOrgApplySampleMain::getReturnReason,returnReason)
                        .set(TbOrgApplySampleMain::getUpdateTime,new Date())
                        .set(StringUtils.isNotBlank(optUser),TbOrgApplySampleMain::getUpdateBy,optUser)
                .eq(TbOrgApplySampleMain::getMainId,mainId));
    }

    /**
     * 根据申请单id更新申请单对照状态-批量
     * @param mainIds
     * @param mappingStatus 1待对照 2待签收 3已签收 4取消外送
     * @return
     */
    public int updateApplySampleMainStatusByIds(List<String> mainIds, Integer mappingStatus,String optUserName){
        return tbOrgApplySampleMainMapper.update(null,Wrappers.lambdaUpdate(TbOrgApplySampleMain.class)
                .set(TbOrgApplySampleMain::getStatus,mappingStatus)
                .set(TbOrgApplySampleMain::getUpdateTime,new Date())
                .set(StringUtils.isNotBlank(optUserName),TbOrgApplySampleMain::getUpdateBy,optUserName)
                .in(TbOrgApplySampleMain::getMainId,mainIds));
    }

    /**
     * 签收外送申领单专用
     * @param param
     * @return
     */
    public int updateApplySampleMainForSign(SignOutApplyInfoParam param) {
        return tbOrgApplySampleMainMapper.update(null,Wrappers.lambdaUpdate(TbOrgApplySampleMain.class)
                .set(TbOrgApplySampleMain::getReceiveUserCode,param.getReceiveUserCode())
                .set(TbOrgApplySampleMain::getReceiveUserName,param.getReceiveUserName())
                .set(TbOrgApplySampleMain::getReceiveTime, param.getReceiveTime())
                .set(TbOrgApplySampleMain::getSignOrgCode,param.getSignOrgCode())
                .set(TbOrgApplySampleMain::getSignOrgName,param.getSignOrgName())
                .set(TbOrgApplySampleMain::getSignBarcode,param.getSignBarcode())
                .set(TbOrgApplySampleMain::getSignMainBarcode,param.getSignMainBarcode())
                .set(TbOrgApplySampleMain::getStatus,3)
                .set(TbOrgApplySampleMain::getUpdateTime,param.getReceiveTime())
                .set(TbOrgApplySampleMain::getUpdateBy,param.getReceiveUserName())
                .set(TbOrgApplySampleMain::getFlowStatus,param.getFlowStatus())
                .eq(TbOrgApplySampleMain::getMainId,param.getMainId()));
    }


    public int saveOrgApplySampleMainBatch(List<TbOrgApplySampleMain> mainList ) {
        if (CollectionUtils.isEmpty(mainList)){
            return 0;
        }

        // 敏感字段脱敏处理
//        FieldDesensitizationUtil.encryptList(mainList,fieldNames);

        mainList.stream().forEach(main -> {
            if (main.getFlowStatus() == null) {
                main.setFlowStatus(0);
            }
        });

        return tbOrgApplySampleMainMapper.insertBatch(mainList);
    }

    /**
     * 查询业务中台的外送样本信息-根据客商编码和样本号(已签收的)
     * @param sendOrgCode
     * @param reportBarcodes
     * @return
     */
    public List<TbOrgApplySampleMain> querySampleMainListByCustomerAndBarcode(String sendOrgCode, List<String> reportBarcodes) {
        if (StringUtils.isEmpty(sendOrgCode)||CollectionUtils.isEmpty(reportBarcodes)){
            return Collections.emptyList();
        }

        List<TbOrgApplySampleMain> tbOrgApplySampleMains = tbOrgApplySampleMainMapper.selectList(Wrappers.lambdaQuery(TbOrgApplySampleMain.class)
                .eq(TbOrgApplySampleMain::getHspOrgCode, sendOrgCode)
                .in(TbOrgApplySampleMain::getBarcode, reportBarcodes)
                .ne(TbOrgApplySampleMain::getIsAdditional, 1)
                .eq(TbOrgApplySampleMain::getStatus, ApplySampleStatus.SIGN.getValue())
                .eq(TbOrgApplySampleMain::getDeleteFlag, 0)
                .orderByDesc(TbOrgApplySampleMain::getCreateTime, TbOrgApplySampleMain::getMainId));
        // 脱敏字段解密
//        FieldDesensitizationUtil.decryptList(tbOrgApplySampleMains,fieldNames);
        return tbOrgApplySampleMains;
    }

    /**
     * 查询业务中台的外送样本信息-根据客商编码和样本号(已签收的)
     * @return
     */
    public List<TbOrgApplySampleMain> querySampleMainListByCustomersAndBarcode(Collection<String> sendOrgCodes, Collection<String> outBarcodes) {
        if (CollectionUtils.isEmpty(sendOrgCodes)||CollectionUtils.isEmpty(outBarcodes)){
            return Collections.emptyList();
        }

        List<TbOrgApplySampleMain> tbOrgApplySampleMains = tbOrgApplySampleMainMapper.selectList(Wrappers.lambdaQuery(TbOrgApplySampleMain.class)
                .in(TbOrgApplySampleMain::getHspOrgCode, sendOrgCodes)
                .in(TbOrgApplySampleMain::getBarcode, outBarcodes)
                .ne(TbOrgApplySampleMain::getIsAdditional, 1)
                .eq(TbOrgApplySampleMain::getStatus, ApplySampleStatus.SIGN.getValue())
                .eq(TbOrgApplySampleMain::getDeleteFlag, 0)
                .orderByDesc(TbOrgApplySampleMain::getCreateTime, TbOrgApplySampleMain::getMainId));
        // 脱敏字段解密
//        FieldDesensitizationUtil.decryptList(tbOrgApplySampleMains,fieldNames);
        return tbOrgApplySampleMains;
    }

    /**
     * 批量更新样本信息-拉取报告结果
     * @param updateMainList
     * @return
     */
    public int updateApplySampleMainBatch(List<TbOrgApplySampleMain> updateMainList) {
        if (CollectionUtils.isEmpty(updateMainList)){
            return 0;
        }
        // 脱敏字段加密
//        FieldDesensitizationUtil.encryptList(updateMainList,fieldNames);
        return tbOrgApplySampleMainMapper.updateApplySampleMainBatch(updateMainList);
    }
    
    
    public List<TbOrgApplySampleMain> getLogisticsSample(String sendApplyNo) {
        if (StringUtils.isBlank(sendApplyNo)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<TbOrgApplySampleMain> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TbOrgApplySampleMain::getFormCode, sendApplyNo)
                .ne(TbOrgApplySampleMain::getStatus, ApplySampleStatus.CANCEL_SEND.getValue())
                .eq(TbOrgApplySampleMain::getDeleteFlag,DeleteFlagEnum.NO_DELETE.getCode())
                .orderByDesc(TbOrgApplySampleMain::getMainId);
        List<TbOrgApplySampleMain> tbOrgApplySampleMains = tbOrgApplySampleMainMapper.selectList(queryWrapper);
        // 脱敏字段解密
//        FieldDesensitizationUtil.decryptList(tbOrgApplySampleMains,fieldNames);
        return tbOrgApplySampleMains;
    }
    
    /**
     * 根据 条码号 和 机构编码查询对应的样本
     * @param barcode
     * @param hspOrgCode
     * @return
     */
    public TbOrgApplySampleMain getApplySample(String barcode, String hspOrgCode) {
        LambdaQueryWrapper<TbOrgApplySampleMain> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TbOrgApplySampleMain::getBarcode,barcode)
                .ne(TbOrgApplySampleMain::getStatus,ApplySampleStatus.CANCEL_SEND.getValue())
                .eq(TbOrgApplySampleMain::getHspOrgCode,hspOrgCode)
                .orderByDesc(TbOrgApplySampleMain::getCreateTime).last(" limit 1");
        TbOrgApplySampleMain tbOrgApplySampleMain = tbOrgApplySampleMainMapper.selectOne(queryWrapper);
        // 脱敏字段解密
//        FieldDesensitizationUtil.decryptFields(tbOrgApplySampleMain,fieldNames);
        return tbOrgApplySampleMain;
    }
    
    /**
     * 查找样本补录信息
     * @param hspOrgCode
     * @param barCodes
     * @return
     */
    public List<TbOrgApplySampleMain> searchRecord(String hspOrgCode, List<String> barCodes) {
        LambdaQueryWrapper<TbOrgApplySampleMain> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TbOrgApplySampleMain::getHspOrgCode,hspOrgCode).in(TbOrgApplySampleMain::getBarcode,barCodes);
        List<TbOrgApplySampleMain> tbOrgApplySampleMains = tbOrgApplySampleMainMapper.selectList(queryWrapper);
        // 脱敏字段解密
//        FieldDesensitizationUtil.decryptFields(tbOrgApplySampleMains,fieldNames);
        return tbOrgApplySampleMains;
    }

    /**
     * 根据样本外送的送检机构、检验机构、条码号等信息查询外送样本
     * @param request
     * @return
     */
    public IPage<TbOrgApplySampleMain> queryApplySampleBySendInfo(OrgApplySampleSendRequest request) {

        LambdaQueryWrapper<TbOrgApplySampleMain> queryWrapper = Wrappers.lambdaQuery(TbOrgApplySampleMain.class)
                .eq(StringUtils.isNotBlank(request.getHspOrgCode()), TbOrgApplySampleMain::getHspOrgCode, request.getHspOrgCode())
                .eq(StringUtils.isNotBlank(request.getTargetOrgCode()), TbOrgApplySampleMain::getTargetOrgCode, request.getTargetOrgCode())
                .like(StringUtils.isNotBlank(request.getPatientName()), TbOrgApplySampleMain::getPatientName, request.getPatientName())
                .eq(StringUtils.isNotBlank(request.getBarcode()), TbOrgApplySampleMain::getBarcode, request.getBarcode())
                .eq(request.getStatus() != null, TbOrgApplySampleMain::getStatus, request.getStatus())
                .eq(request.getFlowStatus() != null, TbOrgApplySampleMain::getFlowStatus, request.getFlowStatus())
                .between(request.getApplyDateFrom() != null && request.getApplyDateTo() != null, TbOrgApplySampleMain::getApplyDate, request.getApplyDateFrom(), request.getApplyDateTo())
                .orderByAsc(TbOrgApplySampleMain::getFormId, TbOrgApplySampleMain::getApplyDate, TbOrgApplySampleMain::getMainId);

        IPage<TbOrgApplySampleMain> page = Page.of(request.getPage(), request.getPageSize());
        IPage<TbOrgApplySampleMain> pageResult = tbOrgApplySampleMainMapper.selectPage(page, queryWrapper);

        if (CollectionUtils.isEmpty(pageResult.getRecords())){
            return pageResult;
        }

        // 脱敏字段解密
        for (TbOrgApplySampleMain record : pageResult.getRecords()) {
//            FieldDesensitizationUtil.decryptFields(record,fieldNames);
        }

        return pageResult;
    }

    /**
     * 根据条码号和客商编码查询有效的外送样本信息
     * @param sendBarcode
     * @param hspOrgCode
     * @return
     */
    public TbOrgApplySampleMain querySampleMainInfoByBarcode(String sendBarcode, String hspOrgCode,String mainId) {
        if (StringUtils.isBlank(sendBarcode)|| StringUtils.isBlank(hspOrgCode) || StringUtils.isBlank(mainId)){
            return null;
        }

        TbOrgApplySampleMain tbOrgApplySampleMain = tbOrgApplySampleMainMapper.selectOne(Wrappers.lambdaQuery(TbOrgApplySampleMain.class)
                .eq(TbOrgApplySampleMain::getHspOrgCode, hspOrgCode)
                .eq(TbOrgApplySampleMain::getBarcode, sendBarcode)
                .eq(TbOrgApplySampleMain::getMainId, mainId)
                .eq(TbOrgApplySampleMain::getDeleteFlag, DeleteFlagEnum.NO_DELETE.getCode()));
        // 脱敏字段解密
//        FieldDesensitizationUtil.decryptFields(tbOrgApplySampleMain,fieldNames);
        return tbOrgApplySampleMain;
    }


    /**
     * 根据条码号和客商编码查询有效的外送样本信息
     *
     * @param hspOrgCode
     * @param sendBarcodes
     * @return
     */
    public List<TbOrgApplySampleMain> queryAllSampleMainByBarcodes(String hspOrgCode, List<String> sendBarcodes,int size,String formCode,Integer maxQueryTimes) {
        if (StringUtils.isEmpty(hspOrgCode)){
            return Collections.emptyList();
        }

        List<TbOrgApplySampleMain> tbOrgApplySampleMains = tbOrgApplySampleMainMapper.selectList(Wrappers.lambdaQuery(TbOrgApplySampleMain.class)
                .in(!CollectionUtils.isEmpty(sendBarcodes), TbOrgApplySampleMain::getBarcode, sendBarcodes)
                .eq(TbOrgApplySampleMain::getHspOrgCode, hspOrgCode)
                .eq(TbOrgApplySampleMain::getIsReport, 1)
                .eq(TbOrgApplySampleMain::getIsDownload, 0)
                .eq(StringUtils.isNotBlank(formCode), TbOrgApplySampleMain::getFormCode, formCode)
                .eq(TbOrgApplySampleMain::getDeleteFlag, DeleteFlagEnum.NO_DELETE.getCode())
                .le(maxQueryTimes != null && maxQueryTimes > 0,TbOrgApplySampleMain::getQueryTimes, maxQueryTimes)
                .orderByDesc(TbOrgApplySampleMain::getCreateTime, TbOrgApplySampleMain::getMainId)
                .last(" limit " + size));
        // 脱敏字段解密
//        FieldDesensitizationUtil.decryptFields(tbOrgApplySampleMains,fieldNames);
        return tbOrgApplySampleMains;
    }

    /**
     * 查询申请单的样本信息--根据
     * @param formId
     * @return
     */
    public List<TbOrgApplySampleMain> querySampleMainListByFormId(String formId) {
        if (StringUtils.isBlank(formId)){
            return Collections.emptyList();
        }

        List<TbOrgApplySampleMain> tbOrgApplySampleMains = tbOrgApplySampleMainMapper.selectList(Wrappers.lambdaQuery(TbOrgApplySampleMain.class)
                .select(TbOrgApplySampleMain::getMainId,
                        TbOrgApplySampleMain::getBarcode,
                        TbOrgApplySampleMain::getHspOrgCode,
                        TbOrgApplySampleMain::getHspOrgName,
                        TbOrgApplySampleMain::getOrgCode,
                        TbOrgApplySampleMain::getOrgName,
                        TbOrgApplySampleMain::getTargetOrgCode,
                        TbOrgApplySampleMain::getTargetOrgName,
                        TbOrgApplySampleMain::getSampleSource,
                        TbOrgApplySampleMain::getApplyType,
                        TbOrgApplySampleMain::getPatientVisitCard,
                        TbOrgApplySampleMain::getUrgent,
                        TbOrgApplySampleMain::getSampleType,
                        TbOrgApplySampleMain::getDept,
                        TbOrgApplySampleMain::getInpatientArea,
                        TbOrgApplySampleMain::getPatientName,
                        TbOrgApplySampleMain::getPatientSex,
                        TbOrgApplySampleMain::getPatientAge,
                        TbOrgApplySampleMain::getPatientSubage,
                        TbOrgApplySampleMain::getPatientSubageUnit,
                        TbOrgApplySampleMain::getPatientCard,
                        TbOrgApplySampleMain::getSendDoctor,
                        TbOrgApplySampleMain::getPatientBed,
                        TbOrgApplySampleMain::getSignBarcode,
                        TbOrgApplySampleMain::getSignMainBarcode,
                        TbOrgApplySampleMain::getSignOrgCode,
                        TbOrgApplySampleMain::getSignOrgName,
                        TbOrgApplySampleMain::getFormId,
                        TbOrgApplySampleMain::getFormCode,
                        TbOrgApplySampleMain::getIsAdditional,
                        TbOrgApplySampleMain::getIsHandover,
                        TbOrgApplySampleMain::getHandoverTime,
                        TbOrgApplySampleMain::getStatus,
                        TbOrgApplySampleMain::getFlowStatus)
                .eq(TbOrgApplySampleMain::getFormId, formId)
                .eq(TbOrgApplySampleMain::getDeleteFlag, DeleteFlagEnum.NO_DELETE.getCode()));
        // 脱敏字段解密
//        FieldDesensitizationUtil.decryptFields(tbOrgApplySampleMains,fieldNames);
        return tbOrgApplySampleMains;
    }


    /**
     * 取消外送样本
     * @param formId
     * @return
     */
    public int cancelApplySampleMainByFormId(String formId,String optUser) {
        if (StringUtils.isBlank(formId)){
            return 0;
        }

        return tbOrgApplySampleMainMapper.update(null,Wrappers.lambdaUpdate(TbOrgApplySampleMain.class)
                .set(TbOrgApplySampleMain::getStatus,ApplySampleStatus.CANCEL_SEND.getValue())
                .set(TbOrgApplySampleMain::getUpdateTime,new Date())
                .set(StringUtils.isNotBlank(optUser),TbOrgApplySampleMain::getUpdateBy,optUser)
                .eq(TbOrgApplySampleMain::getFormId,formId));
    }


    /**
     *
     * @param formId
     * @param barcode
     * @return
     */
    public int cancelApplySampleMainByFormIdAndBarcode(String formId, String barcode) {
        if (StringUtils.isBlank(formId) || StringUtils.isBlank(barcode)){
            return 0;
        }

        return tbOrgApplySampleMainMapper.update(null,Wrappers.lambdaUpdate(TbOrgApplySampleMain.class)
                .set(TbOrgApplySampleMain::getStatus,ApplySampleStatus.CANCEL_SEND.getValue())
                .set(TbOrgApplySampleMain::getUpdateTime,new Date())
                .eq(TbOrgApplySampleMain::getFormId,formId)
                .eq(TbOrgApplySampleMain::getBarcode,barcode));
    }



    /**
     * 查询外送样本信息--根据条码和送检机构编码
     * @param barcode
     * @param hspOrgCode
     * @return
     */
    public TbOrgApplySampleMain queryApplySampleMainByBarcode(String barcode, String hspOrgCode) {
        if (StringUtils.isBlank(barcode)|| StringUtils.isBlank(hspOrgCode)){
            return null;
        }

        List<TbOrgApplySampleMain> sampleMainList = tbOrgApplySampleMainMapper.selectList(Wrappers.lambdaQuery(TbOrgApplySampleMain.class)
                .eq(TbOrgApplySampleMain::getHspOrgCode, hspOrgCode)
                .select(TbOrgApplySampleMain::getMainId,
                        TbOrgApplySampleMain::getBarcode,
                        TbOrgApplySampleMain::getHspOrgCode,
                        TbOrgApplySampleMain::getHspOrgName,
                        TbOrgApplySampleMain::getOrgCode,
                        TbOrgApplySampleMain::getOrgName,
                        TbOrgApplySampleMain::getTargetOrgCode,
                        TbOrgApplySampleMain::getTargetOrgName,
                        TbOrgApplySampleMain::getSampleSource,
                        TbOrgApplySampleMain::getApplyType,
                        TbOrgApplySampleMain::getPatientVisitCard,
                        TbOrgApplySampleMain::getUrgent,
                        TbOrgApplySampleMain::getSampleType,
                        TbOrgApplySampleMain::getDept,
                        TbOrgApplySampleMain::getInpatientArea,
                        TbOrgApplySampleMain::getPatientName,
                        TbOrgApplySampleMain::getPatientSex,
                        TbOrgApplySampleMain::getPatientAge,
                        TbOrgApplySampleMain::getPatientSubage,
                        TbOrgApplySampleMain::getPatientSubageUnit,
                        TbOrgApplySampleMain::getPatientCard,
                        TbOrgApplySampleMain::getSendDoctor,
                        TbOrgApplySampleMain::getPatientBed,
                        TbOrgApplySampleMain::getSignBarcode,
                        TbOrgApplySampleMain::getSignMainBarcode,
                        TbOrgApplySampleMain::getSignOrgCode,
                        TbOrgApplySampleMain::getSignOrgName,
                        TbOrgApplySampleMain::getFormId,
                        TbOrgApplySampleMain::getFormCode,
                        TbOrgApplySampleMain::getIsAdditional,
                        TbOrgApplySampleMain::getIsHandover,
                        TbOrgApplySampleMain::getHandoverTime,
                        TbOrgApplySampleMain::getStatus,
                        TbOrgApplySampleMain::getFlowStatus)
                .eq(TbOrgApplySampleMain::getBarcode, barcode)
                .eq(TbOrgApplySampleMain::getDeleteFlag, DeleteFlagEnum.NO_DELETE.getCode())
                .orderByDesc(TbOrgApplySampleMain::getCreateTime,TbOrgApplySampleMain::getMainId));
        if (CollectionUtils.isEmpty(sampleMainList)){
            return null;
        }

        TbOrgApplySampleMain tbOrgApplySampleMain = sampleMainList.get(0);
        // 脱敏字段解密
//        FieldDesensitizationUtil.decryptFields(tbOrgApplySampleMain,fieldNames);
        return tbOrgApplySampleMain;
    }


    /**
     * 样本报告结果确认接口
     * @param barcodes
     * @param hspOrgCode
     * @return
     */
    public int confirmSampleReportByBarcodes(List<String> barcodes, String hspOrgCode) {
        if (CollectionUtils.isEmpty(barcodes)|| StringUtils.isBlank(hspOrgCode)){
            return 0;
        }

        return tbOrgApplySampleMainMapper.update(null,Wrappers.lambdaUpdate(TbOrgApplySampleMain.class)
                .set(TbOrgApplySampleMain::getIsDownload,1)
                .eq(TbOrgApplySampleMain::getHspOrgCode,hspOrgCode)
                .in(TbOrgApplySampleMain::getBarcode,barcodes)
                .ne(TbOrgApplySampleMain::getStatus,ApplySampleStatus.CANCEL_SEND.getValue())
                .eq(TbOrgApplySampleMain::getDeleteFlag,DeleteFlagEnum.NO_DELETE.getCode()));
    }


    /**
     * 查询未签收的样本
     * @return
     */
    public List<TbOrgApplySampleMain> queryApplySampleMainWithUnSign(String customerCode,String orgCode) {
        List<TbOrgApplySampleMain> tbOrgApplySampleMains = tbOrgApplySampleMainMapper.selectList(Wrappers.lambdaQuery(TbOrgApplySampleMain.class)
                .select(TbOrgApplySampleMain::getMainId,
                        TbOrgApplySampleMain::getBarcode,
                        TbOrgApplySampleMain::getHspOrgCode,
                        TbOrgApplySampleMain::getHspOrgName,
                        TbOrgApplySampleMain::getOrgCode,
                        TbOrgApplySampleMain::getOrgName,
                        TbOrgApplySampleMain::getTargetOrgCode,
                        TbOrgApplySampleMain::getTargetOrgName,
                        TbOrgApplySampleMain::getSignBarcode,
                        TbOrgApplySampleMain::getSignMainBarcode,
                        TbOrgApplySampleMain::getSignOrgCode,
                        TbOrgApplySampleMain::getSignOrgName,
                        TbOrgApplySampleMain::getFormId,
                        TbOrgApplySampleMain::getFormCode,
                        TbOrgApplySampleMain::getStatus,
                        TbOrgApplySampleMain::getFlowStatus)
                .in(TbOrgApplySampleMain::getStatus, Arrays.asList(ApplySampleStatus.UN_MAPPING.getValue(), ApplySampleStatus.UN_SIGN.getValue()))
                .eq(StringUtils.isNotBlank(customerCode), TbOrgApplySampleMain::getHspOrgCode, customerCode)
                .eq(StringUtils.isNotBlank(orgCode), TbOrgApplySampleMain::getTargetOrgCode, orgCode)
                .eq(TbOrgApplySampleMain::getDeleteFlag, DeleteFlagEnum.NO_DELETE.getCode()));
        // 脱敏字段解密
//        FieldDesensitizationUtil.decryptList(tbOrgApplySampleMains,fieldNames);
        return tbOrgApplySampleMains;
    }


    /**
     * 查询外送样本信息 -- for取消签收（谨慎复用）
     * @param barcodes
     * @param sendOrgCode
     */
    public List<TbOrgApplySampleMain> queryApplySampleInfoByBarcodes(List<String> barcodes, String sendOrgCode) {
        if (CollectionUtils.isEmpty(barcodes)|| StringUtils.isBlank(sendOrgCode)){
            return Collections.emptyList();
        }

        List<TbOrgApplySampleMain> sampleMainList = tbOrgApplySampleMainMapper.selectList(Wrappers.lambdaQuery(TbOrgApplySampleMain.class)
                .in(TbOrgApplySampleMain::getBarcode, barcodes)
                .eq(TbOrgApplySampleMain::getHspOrgCode, sendOrgCode)
                .ne(TbOrgApplySampleMain::getStatus,ApplySampleStatus.CANCEL_SEND.getValue())
                .eq(TbOrgApplySampleMain::getDeleteFlag, DeleteFlagEnum.NO_DELETE.getCode())
                .orderByDesc(TbOrgApplySampleMain::getCreateTime,TbOrgApplySampleMain::getMainId));

        if (CollectionUtils.isEmpty(sampleMainList)){
            return Collections.emptyList();
        }

        // 脱敏字段解密
//        FieldDesensitizationUtil.decryptList(sampleMainList,fieldNames);
        return sampleMainList;
    }


    /**
     * 更新样本结果查询次数
     *
     * @param hspOrgCode
     * @param barcodeList
     */
    public int updateSampleResultQueryTimes(String hspOrgCode, List<String> barcodeList) {
        if (StringUtils.isBlank(hspOrgCode) || CollectionUtils.isEmpty(barcodeList)) {
            return 0;
        }

        return tbOrgApplySampleMainMapper.update(null, Wrappers.lambdaUpdate(TbOrgApplySampleMain.class)
                // 查询次数加一
                .setSql("query_times = query_times + 1")
                .eq(TbOrgApplySampleMain::getHspOrgCode, hspOrgCode)
                .in(TbOrgApplySampleMain::getBarcode, barcodeList));
    }

    /**
     * 判断条码号是否存在
     * @param barcode
     * @return
     */
    public long countByBarcode(String barcode,String customerCode) {
        LambdaQueryWrapper<TbOrgApplySampleMain> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TbOrgApplySampleMain::getBarcode,barcode).eq(TbOrgApplySampleMain::getHspOrgCode,customerCode);
        return tbOrgApplySampleMainMapper.selectCount(queryWrapper);
    }

    /**
     * 根据送检机构编码和外部条码号查询样本主表
     */
    public List<TbOrgApplySampleMain> listByOrgCodeAndBarcodes(String hspOrgCode, String orgId, List<String> barcodeList) {
        if (StringUtils.isBlank(hspOrgCode) || CollectionUtils.isEmpty(barcodeList)) {
            return Collections.EMPTY_LIST;
        }

        List<TbOrgApplySampleMain> tbOrgApplySampleMains = tbOrgApplySampleMainMapper.selectList(Wrappers.lambdaQuery(TbOrgApplySampleMain.class)
                .eq(TbOrgApplySampleMain::getDeleteFlag, DeleteFlagEnum.NO_DELETE.getCode())
                .eq(TbOrgApplySampleMain::getOrgCode, orgId)
                .eq(TbOrgApplySampleMain::getHspOrgCode, hspOrgCode)
                .eq(TbOrgApplySampleMain::getFlowStatus, FlowStatusEnum.REPORT.getCode()) // 已报告
                .in(TbOrgApplySampleMain::getSignBarcode, barcodeList));
        // 脱敏字段解密
//        FieldDesensitizationUtil.decryptList(tbOrgApplySampleMains,fieldNames);
        return tbOrgApplySampleMains;
    }

    /**
     * 取消报告结果审核状态
     */
    public int cancelTestResult(String hspOrgCode, String orgId, List<String> barcodeList,String optUserName) {

        if (StringUtils.isBlank(hspOrgCode) || CollectionUtils.isEmpty(barcodeList)) {
            return 0;
        }

        // 已出报告状态和已确认下载状态置否
        return tbOrgApplySampleMainMapper.update(null, Wrappers.lambdaUpdate(TbOrgApplySampleMain.class)
                .set(TbOrgApplySampleMain::getIsReport, Constants.IS_FALSE)
                .set(TbOrgApplySampleMain::getIsDownload, Constants.IS_FALSE)
                .set(StringUtils.isNotBlank(optUserName),TbOrgApplySampleMain::getUpdateBy, optUserName)
                .set(TbOrgApplySampleMain::getUpdateTime, new Date())
                .set(TbOrgApplySampleMain::getReportMd5, StringUtils.EMPTY)
                .eq(TbOrgApplySampleMain::getDeleteFlag, DeleteFlagEnum.NO_DELETE.getCode())
                .eq(TbOrgApplySampleMain::getOrgCode, orgId)
                .eq(TbOrgApplySampleMain::getHspOrgCode, hspOrgCode)
                .eq(TbOrgApplySampleMain::getFlowStatus, FlowStatusEnum.REPORT.getCode()) // 已报告
                .in(TbOrgApplySampleMain::getSignBarcode, barcodeList));

    }

    /**
     * 更新样本主表最终状态
     */
    public int updateFlowStatus(List<AddOptFlowInfoRequest.AddOptFlow> flowList, Integer flowStatus) {

        return tbOrgApplySampleMainMapper.updateFlowStatus(flowList, flowStatus);
    }

    /**
     * 根据送检机构编码和申请单编码查询
     */
    public List<TbOrgApplySampleMain> listByOrgCodeAndFormCode(String hspOrgCode, List<String> formCode) {
        if (StringUtils.isBlank(hspOrgCode) || CollectionUtils.isEmpty(formCode)) {
            return Collections.EMPTY_LIST;
        }

        List<TbOrgApplySampleMain> tbOrgApplySampleMains = tbOrgApplySampleMainMapper.selectList(Wrappers.lambdaQuery(TbOrgApplySampleMain.class)
                .eq(TbOrgApplySampleMain::getHspOrgCode, hspOrgCode)
                .in(TbOrgApplySampleMain::getFormCode, formCode)
                .ne(TbOrgApplySampleMain::getFlowStatus, FlowStatusEnum.CANCEL.getCode())
                .eq(TbOrgApplySampleMain::getDeleteFlag, DeleteFlagEnum.NO_DELETE.getCode()));
        // 脱敏字段解密
//        FieldDesensitizationUtil.decryptList(tbOrgApplySampleMains,fieldNames);
        return tbOrgApplySampleMains;
    }

    /**
     * 分页查询未交接的样本信息
     * @param hspOrgCode
     * @return
     */
    public List<TbOrgApplySampleMain> queryUnHandoverSample(String orgId,String hspOrgCode,Date begin,Date end) {
        List<TbOrgApplySampleMain> tbOrgApplySampleMains = tbOrgApplySampleMainMapper.selectList(Wrappers.lambdaQuery(TbOrgApplySampleMain.class)
                .eq(TbOrgApplySampleMain::getTargetOrgCode, orgId)
                .eq(StringUtils.isNotBlank(hspOrgCode),TbOrgApplySampleMain::getHspOrgCode, hspOrgCode)
                .eq(TbOrgApplySampleMain::getIsHandover, 0)
                .ne(TbOrgApplySampleMain::getStatus, ApplySampleStatus.CANCEL_SEND.getValue())
                .eq(TbOrgApplySampleMain::getDeleteFlag, DeleteFlagEnum.NO_DELETE.getCode())
                .ge(begin != null, TbOrgApplySampleMain::getCreateTime, begin)
                .lt(end != null, TbOrgApplySampleMain::getCreateTime, end)
                .orderByDesc(TbOrgApplySampleMain::getCreateTime, TbOrgApplySampleMain::getMainId));
        // 脱敏字段解密
//        FieldDesensitizationUtil.decryptList(tbOrgApplySampleMains,fieldNames);
        return tbOrgApplySampleMains;
    }

    /**
     * 查询未交接的
     * @param hspOrgCode
     * @param barCodes
     * @return
     */
    public List<TbOrgApplySampleMain> querySampleInfoForHandover(String orgId,String hspOrgCode, List<String> barCodes) {
        List<TbOrgApplySampleMain> tbOrgApplySampleMains = tbOrgApplySampleMainMapper.selectList(Wrappers.lambdaQuery(TbOrgApplySampleMain.class)
                .eq(TbOrgApplySampleMain::getTargetOrgCode, orgId)
                .eq(TbOrgApplySampleMain::getHspOrgCode, hspOrgCode)
                .eq(TbOrgApplySampleMain::getIsHandover, 0)
                .in(TbOrgApplySampleMain::getBarcode, barCodes)
                .ne(TbOrgApplySampleMain::getStatus, ApplySampleStatus.CANCEL_SEND.getValue())
                .eq(TbOrgApplySampleMain::getDeleteFlag, DeleteFlagEnum.NO_DELETE.getCode())
                .orderByDesc(TbOrgApplySampleMain::getCreateTime, TbOrgApplySampleMain::getMainId));
        // 脱敏字段解密
//        FieldDesensitizationUtil.decryptList(tbOrgApplySampleMains,fieldNames);
        return tbOrgApplySampleMains;
    }

    /**
     * 批量交接外送样本
     * @param handoverSampleMainInfoParams
     * @return
     */
    public int handoverSampleMainInfo(List<HandoverSampleMainInfoParam> handoverSampleMainInfoParams) {
        if (CollectionUtils.isEmpty(handoverSampleMainInfoParams)){
            return 0;
        }

        return tbOrgApplySampleMainMapper.handoverSampleMainInfo(handoverSampleMainInfoParams);
    }


    public List<TbOrgApplySampleMain> querySampleForTms(List<SampleInfoDto> sampleList) {
        if (CollectionUtils.isEmpty(sampleList)){
            return Collections.EMPTY_LIST;
        }

        List<TbOrgApplySampleMain> tbOrgApplySampleMains = tbOrgApplySampleMainMapper.querySampleForTms(sampleList);
        // 脱敏字段解密
//        FieldDesensitizationUtil.decryptList(tbOrgApplySampleMains,fieldNames);
        return tbOrgApplySampleMains;
    }
    /**
     * 根据送检机构 申请单和条码号查询样本信息
     * @param handoverSampleMainInfoParams
     * @return
     */
    public List<TbOrgApplySampleMain> queryApplySampleMainInfoForHandover(List<HandoverSampleMainInfoParam> handoverSampleMainInfoParams) {
        if (CollectionUtils.isEmpty(handoverSampleMainInfoParams)){
            return Collections.emptyList();
        }

        List<TbOrgApplySampleMain> tbOrgApplySampleMains = tbOrgApplySampleMainMapper.queryApplySampleMainInfoForHandover(handoverSampleMainInfoParams);
        // 脱敏字段解密
//        FieldDesensitizationUtil.decryptList(tbOrgApplySampleMains,fieldNames);
        return tbOrgApplySampleMains;
    }


    /**
     * 根据条码查询样本信息，过滤掉取消外送的
     * @param barcodes
     * @param hspOrgCode
     * @return
     */
    public List<TbOrgApplySampleMain> queryApplySampleInfoUnSignByBarcodes(List<String> barcodes, String hspOrgCode) {
        if (StringUtils.isEmpty(hspOrgCode) || CollectionUtils.isEmpty(barcodes)){
            return Collections.emptyList();
        }

        List<TbOrgApplySampleMain> tbOrgApplySampleMains = tbOrgApplySampleMainMapper.selectList(Wrappers.lambdaQuery(TbOrgApplySampleMain.class)
                .in(TbOrgApplySampleMain::getBarcode, barcodes)
                .eq(TbOrgApplySampleMain::getHspOrgCode, hspOrgCode)
                .ne(TbOrgApplySampleMain::getStatus, ApplySampleStatus.CANCEL_SEND.getValue())
                .eq(TbOrgApplySampleMain::getDeleteFlag, DeleteFlagEnum.NO_DELETE.getCode()));

        // 脱敏字段解密
//        FieldDesensitizationUtil.decryptList(tbOrgApplySampleMains,fieldNames);
        return tbOrgApplySampleMains;
    }

    public Long countByFormId(String formId) {
        return tbOrgApplySampleMainMapper.selectCount(Wrappers.lambdaQuery(TbOrgApplySampleMain.class)
                .eq(TbOrgApplySampleMain::getFormId, formId)
                .ne(TbOrgApplySampleMain::getFlowStatus, FlowStatusEnum.CANCEL.getCode())
                .eq(TbOrgApplySampleMain::getDeleteFlag, DeleteFlagEnum.NO_DELETE.getCode()));
    }

    /**
     * 查询送检客商的样本条码号--用于数据缓存初始化
     * @return
     */
    public List<TbOrgApplySampleMain> querySampleBarcodeList() {
        List<TbOrgApplySampleMain> tbOrgApplySampleMains = tbOrgApplySampleMainMapper.selectList(Wrappers.lambdaQuery(TbOrgApplySampleMain.class)
                .select(TbOrgApplySampleMain::getHspOrgCode, TbOrgApplySampleMain::getBarcode)
                .ne(TbOrgApplySampleMain::getStatus, ApplySampleStatus.CANCEL_SEND.getValue())
                .eq(TbOrgApplySampleMain::getDeleteFlag, 0));
        // 脱敏字段解密
//        FieldDesensitizationUtil.decryptList(tbOrgApplySampleMains,fieldNames);
        return tbOrgApplySampleMains;
    }

    /**
     * 根据送检机构和条码号查询申领信息
     * @param barcodes
     * @param sendOrgCode
     * @return
     */
    public List<TbOrgApplySampleMain> querySignApplySampleMainBySendOrgCodeAndBarcodes(List<String> barcodes, String sendOrgCode) {
        LambdaQueryWrapper<TbOrgApplySampleMain> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TbOrgApplySampleMain::getHspOrgCode,sendOrgCode).eq(TbOrgApplySampleMain::getStatus,ApplySampleStatus.SIGN.getValue())
                .in(TbOrgApplySampleMain::getBarcode,barcodes);
        List<TbOrgApplySampleMain> tbOrgApplySampleMains = tbOrgApplySampleMainMapper.selectList(queryWrapper);

        // 脱敏字段解密
//        FieldDesensitizationUtil.decryptList(tbOrgApplySampleMains,fieldNames);
        return tbOrgApplySampleMains;
    }

    public List<TbOrgApplySampleMain> queryOrgApplySampleMainList(String customerCode, Date startDate, Date endDate) {
        // 查询签收数据
        List<TbOrgApplySampleMain> tbOrgApplySampleMainList = tbOrgApplySampleMainMapper.selectList(Wrappers.lambdaQuery(TbOrgApplySampleMain.class)
                .eq(TbOrgApplySampleMain::getHspOrgCode, customerCode)
                .eq(TbOrgApplySampleMain::getStatus, ApplySampleStatus.SIGN.getValue())
                .eq(TbOrgApplySampleMain::getDeleteFlag, 0)
                .gt(TbOrgApplySampleMain::getReceiveTime, startDate)
                .lt(TbOrgApplySampleMain::getReceiveTime, endDate));

        return tbOrgApplySampleMainList;

    }

    /**
     * 根据签收编码和签收机构查询样本信息
     * @param signBarcode
     * @param signOrgCode
     * @return
     */
    public TbOrgApplySampleMain queryByLwSignBarcodeAndOrgCode(String signBarcode, String signOrgCode) {
        TbOrgApplySampleMain tbOrgApplySampleMain = tbOrgApplySampleMainMapper.selectOne(Wrappers.lambdaQuery(TbOrgApplySampleMain.class)
//                .select(TbOrgApplySampleMain::getHspOrgCode, TbOrgApplySampleMain::getBarcode,TbOrgApplySampleMain::getSignBarcode,TbOrgApplySampleMain::getSignOrgCode)
                .eq(TbOrgApplySampleMain::getSignBarcode, signBarcode)
                .eq(TbOrgApplySampleMain::getSignOrgCode, signOrgCode)
                .eq(TbOrgApplySampleMain::getStatus, ApplySampleStatus.SIGN.getValue())
                .eq(TbOrgApplySampleMain::getDeleteFlag, DeleteFlagEnum.NO_DELETE.getCode()));
        // 脱敏字段解密
//        FieldDesensitizationUtil.decryptFields(tbOrgApplySampleMain,fieldNames);
        return tbOrgApplySampleMain;
    }

    /**
     * 根据送检客商编码和条码号 查询已存在的样本
     * @param hspOrgCode
     * @param signBarcodes
     * @return
     */
    public List<TbOrgApplySampleMain> querySampleMainByCustomerCodeOrSignBarcodes(String hspOrgCode,String orgCode, List<String> signBarcodes) {
        if (StringUtils.isBlank(hspOrgCode)|| CollectionUtils.isEmpty(signBarcodes)){
            return Collections.emptyList();
        }

        return tbOrgApplySampleMainMapper.selectList(Wrappers.lambdaQuery(TbOrgApplySampleMain.class)
                .and(e -> e.or(f->f.eq(TbOrgApplySampleMain::getOrgCode,orgCode)
                                .in(TbOrgApplySampleMain::getSignBarcode, signBarcodes))
                        .or(p->p.eq(TbOrgApplySampleMain::getHspOrgCode, hspOrgCode)
                                .in(TbOrgApplySampleMain::getBarcode, signBarcodes)))
                .ne(TbOrgApplySampleMain::getStatus, ApplySampleStatus.CANCEL_SEND.getValue())
                .eq(TbOrgApplySampleMain::getDeleteFlag, DeleteFlagEnum.NO_DELETE.getCode())
        );
    }

    /**
     * 根据签收条码查询手工单样本
     * @param orgCode
     * @param signBarcodes
     * @return
     */
    public List<TbOrgApplySampleMain> queryHandSampleBySignBarcodes(String orgCode, List<String> signBarcodes) {
        if (StringUtils.isBlank(orgCode) || CollectionUtils.isEmpty(signBarcodes)){
            return Collections.emptyList();
        }

        return tbOrgApplySampleMainMapper.selectList(Wrappers.lambdaQuery(TbOrgApplySampleMain.class)
                .eq(TbOrgApplySampleMain::getSignOrgCode,orgCode)
                .in(TbOrgApplySampleMain::getSignBarcode,signBarcodes)
                .eq(TbOrgApplySampleMain::getIsHandSample,1)
                .eq(TbOrgApplySampleMain::getDeleteFlag,DeleteFlagEnum.NO_DELETE.getCode()));
    }

    /**
     * 根据签收条码查询样本
     * @param orgCode
     * @param signBarcodes
     * @return
     */
    public List<TbOrgApplySampleMain> querySampleBySignBarcodes(String orgCode, List<String> signBarcodes) {
        if (StringUtils.isBlank(orgCode) || CollectionUtils.isEmpty(signBarcodes)){
            return Collections.emptyList();
        }

        return tbOrgApplySampleMainMapper.selectList(Wrappers.lambdaQuery(TbOrgApplySampleMain.class)
                .eq(TbOrgApplySampleMain::getSignOrgCode,orgCode)
                .in(TbOrgApplySampleMain::getSignBarcode,signBarcodes)
                .eq(TbOrgApplySampleMain::getDeleteFlag,DeleteFlagEnum.NO_DELETE.getCode()));
    }


    /**
     * 根据样本id删除样本信息
     * @param mainIds
     * @return
     */
    public Integer deleteByIds(List<String> mainIds) {
        if (CollectionUtils.isEmpty(mainIds)){
            return 0;
        }

        return tbOrgApplySampleMainMapper.deleteBatchIds(mainIds);
    }

    /**
     * 根据样本id删除样本信息
     * @param mainIds
     * @return
     */
    public Integer deleteByMainIds(List<String> mainIds, LimsCancelHandSampleRequest limsCancelHandSampleRequest) {
        if (CollectionUtils.isEmpty(mainIds)){
            return 0;
        }

        return tbOrgApplySampleMainMapper.update(null,Wrappers.lambdaUpdate(TbOrgApplySampleMain.class)
                .set(TbOrgApplySampleMain::getDeleteFlag,1)
                        .set(TbOrgApplySampleMain::getUpdateTime,new Date())
                .set(StringUtils.isNotBlank(limsCancelHandSampleRequest.getOptUserName()),TbOrgApplySampleMain::getUpdateBy,limsCancelHandSampleRequest.getOptUserName())
                .in(TbOrgApplySampleMain::getMainId,mainIds));
    }


    /**
     * 根据申请单编码查询样本信息
     * @param formCodes
     * @return
     */
    public List<TbOrgApplySampleMain> queryApplySampleMainByFormCodes(List<String> formCodes) {
        if (CollectionUtils.isEmpty(formCodes)){
            return Collections.emptyList();
        }

        return tbOrgApplySampleMainMapper.selectList(Wrappers.lambdaQuery(TbOrgApplySampleMain.class)
                .in(TbOrgApplySampleMain::getFormCode,formCodes)
                .eq(TbOrgApplySampleMain::getDeleteFlag,DeleteFlagEnum.NO_DELETE.getCode()));
    }


    /**
     * 查询补录样本西信息
     * @param barcodes
     * @param hspOrgCode
     * @return
     */
    public List<TbOrgApplySampleMain> queryAdditionalSampleByBarcodes(List<String> barcodes, String hspOrgCode) {
        if (CollectionUtils.isEmpty(barcodes)|| StringUtils.isBlank(hspOrgCode)){
            return Collections.emptyList();
        }

        List<TbOrgApplySampleMain> sampleMainList = tbOrgApplySampleMainMapper.selectList(Wrappers.lambdaQuery(TbOrgApplySampleMain.class)
                .in(TbOrgApplySampleMain::getBarcode, barcodes)
                .eq(TbOrgApplySampleMain::getHspOrgCode, hspOrgCode)
                .eq(TbOrgApplySampleMain::getStatus,ApplySampleStatus.SIGN.getValue())
                .eq(TbOrgApplySampleMain::getIsAdditional,1)
                .eq(TbOrgApplySampleMain::getDeleteFlag, DeleteFlagEnum.NO_DELETE.getCode()));

        if (CollectionUtils.isEmpty(sampleMainList)){
            return Collections.emptyList();
        }

        // 脱敏字段解密
//        FieldDesensitizationUtil.decryptList(sampleMainList,fieldNames);
        return sampleMainList;
    }


    /**
     * 批量跟新补录样本信息
     * @param tbOrgApplySampleMains
     * @return
     */
    public Integer updateApplySampleMainBatchForLimsPush(List<TbOrgApplySampleMain> tbOrgApplySampleMains) {
        if (CollectionUtils.isEmpty(tbOrgApplySampleMains)){
            return 0;
        }
        // 脱敏字段加密
//        FieldDesensitizationUtil.encryptList(tbOrgApplySampleMains,fieldNames);
        return tbOrgApplySampleMainMapper.updateApplySampleMainBatchForLimsPush(tbOrgApplySampleMains);
    }


    /**
     * 根据签收条码和检验机构查询样本信息
     * @param signBarcode
     * @param signOrgCode
     * @return
     */
    public TbOrgApplySampleMain queryBySignBarcodeAndOrgCodeForBingli(String signBarcode, String signOrgCode) {
        TbOrgApplySampleMain tbOrgApplySampleMain = tbOrgApplySampleMainMapper.selectOne(Wrappers.lambdaQuery(TbOrgApplySampleMain.class)
                .eq(TbOrgApplySampleMain::getSignBarcode, signBarcode)
                .eq(TbOrgApplySampleMain::getSignOrgCode, signOrgCode)
                .eq(TbOrgApplySampleMain::getStatus, ApplySampleStatus.SIGN.getValue())
//                .eq(TbOrgApplySampleMain::getIsHandSample,1) // 这里只查询手工单样本-后面需要查询社区样本需要放开此条件查询
                .eq(TbOrgApplySampleMain::getDeleteFlag, DeleteFlagEnum.NO_DELETE.getCode()));
        // 脱敏字段解密
//        FieldDesensitizationUtil.decryptFields(tbOrgApplySampleMain,fieldNames);
        return tbOrgApplySampleMain;
    }

    /**
     * 查询样本信息
     * @param signBarcodes
     * @return
     */
    public List<TbOrgApplySampleMain> queryListBySignBarcodes(List<String> signBarcodes) {
        if (CollectionUtils.isEmpty(signBarcodes)){
            return Collections.emptyList();
        }

       return tbOrgApplySampleMainMapper.selectList(Wrappers.lambdaQuery(TbOrgApplySampleMain.class)
                .in(TbOrgApplySampleMain::getSignBarcode,signBarcodes)
                .eq(TbOrgApplySampleMain::getDeleteFlag,DeleteFlagEnum.NO_DELETE.getCode()));
    }


    /**
     * 根据送检机构编码 申请单编码 条码号 查询样本信息
     * @param hspOrgCode
     * @param formCode
     * @param barcodes
     * @return
     */
    public List<TbOrgApplySampleMain> querySampleByFormCodeAndBarcodes(String hspOrgCode, String formCode, List<String> barcodes) {
        if (StringUtils.isBlank(hspOrgCode) || StringUtils.isBlank(formCode) || CollectionUtils.isEmpty(barcodes)){
            return Collections.emptyList();
        }

        return tbOrgApplySampleMainMapper.selectList(Wrappers.lambdaQuery(TbOrgApplySampleMain.class)
                .eq(TbOrgApplySampleMain::getHspOrgCode,hspOrgCode)
                .eq(TbOrgApplySampleMain::getFormCode,formCode)
                .in(TbOrgApplySampleMain::getBarcode,barcodes)
                .eq(TbOrgApplySampleMain::getDeleteFlag,DeleteFlagEnum.NO_DELETE.getCode()));
    }

    /**
     * 根据送检机构编码 申请单编码 条码号 查询样本信息(过滤掉取消外送的样本)
     * @param hspOrgCode
     * @param formCode
     * @param barcodes
     * @return
     */
    public List<TbOrgApplySampleMain> queryHasSendSampleByFormCodeAndBarcodes(String hspOrgCode, String formCode, List<String> barcodes) {
        if (StringUtils.isBlank(hspOrgCode) || StringUtils.isBlank(formCode) || CollectionUtils.isEmpty(barcodes)){
            return Collections.emptyList();
        }

        return tbOrgApplySampleMainMapper.selectList(Wrappers.lambdaQuery(TbOrgApplySampleMain.class)
                .eq(TbOrgApplySampleMain::getHspOrgCode,hspOrgCode)
                .eq(TbOrgApplySampleMain::getFormCode,formCode)
                .ne(TbOrgApplySampleMain::getStatus,ApplySampleStatus.CANCEL_SEND.getValue())
                .in(TbOrgApplySampleMain::getBarcode,barcodes)
                .eq(TbOrgApplySampleMain::getDeleteFlag,DeleteFlagEnum.NO_DELETE.getCode()));
    }


    /**
     * 删除样本信息
     * @param mainIds
     * @return
     */
    public Integer deleteForRollback(List<String> mainIds) {
        if (CollectionUtils.isEmpty(mainIds)){
            return 0;
        }
        Map<String,Object> map = new HashMap<>();
        Objects.isNull(map.get("noreason"));
        return tbOrgApplySampleMainMapper.deleteBatchIds(mainIds);
    }


    public List<OrgApplyResultSamplesDTO> queryResultSamples(QueryLimsOutSourceSampleInfoRequest request) {
    return tbOrgApplySampleMainMapper.queryResultSamples(request);
    }

    public List<OrgApplyResultSamplesDTO> queryResultSamples2(QueryLimsOutSourceSampleInfoRequest request) {
        return tbOrgApplySampleMainMapper.queryResultSamples2(request);
    }


    /**
     *
     * @param hspOrgCode
     * @param formCode
     * @return
     */
    public List<TbOrgApplySampleMain> queryByFormCodeForXiaozhensuo(String hspOrgCode, String formCode) {
        if (StringUtils.isAllBlank(hspOrgCode,formCode)){
            return Collections.emptyList();
        }

        return tbOrgApplySampleMainMapper.selectList(Wrappers.lambdaQuery(TbOrgApplySampleMain.class)
                .eq(TbOrgApplySampleMain::getHspOrgCode,hspOrgCode)
                .eq(TbOrgApplySampleMain::getFormCode,formCode)
                .ne(TbOrgApplySampleMain::getStatus,ApplySampleStatus.CANCEL_SEND.getValue())
                .eq(TbOrgApplySampleMain::getDeleteFlag,DeleteFlagEnum.NO_DELETE.getCode()));
    }

    /**
     * 根据申请单id更新申请单外送状态（小诊所）
     * @param mainIds
     * @param mappingStatus 1待对照 2待签收 3已签收 4取消外送
     * @return
     */
    public int unSignApplySampleMainForXiaozhensuo(List<String> mainIds, Integer mappingStatus,Integer flowStatus,String optUserName){
        return tbOrgApplySampleMainMapper.update(null,Wrappers.lambdaUpdate(TbOrgApplySampleMain.class)
                .set(TbOrgApplySampleMain::getStatus,mappingStatus)
                .set(TbOrgApplySampleMain::getUpdateTime,new Date())
                .set(TbOrgApplySampleMain::getFlowStatus,flowStatus)
                .set(StringUtils.isNotBlank(optUserName),TbOrgApplySampleMain::getUpdateBy,optUserName)
                .in(TbOrgApplySampleMain::getMainId,mainIds));
    }

    /**
     * 查询样本信息根据送检机机构编码和条码号
     * @param hspOrgCode
     * @param barcodes
     * @return
     */
    public List<TbOrgApplySampleMain> querySampleForFlowStatus(String hspOrgCode, List<String> barcodes) {
        if (StringUtils.isBlank(hspOrgCode) || CollectionUtils.isEmpty(barcodes)){
            return Collections.emptyList();
        }

        return tbOrgApplySampleMainMapper.selectList(Wrappers.lambdaQuery(TbOrgApplySampleMain.class)
                .eq(TbOrgApplySampleMain::getHspOrgCode,hspOrgCode)
                .in(TbOrgApplySampleMain::getBarcode,barcodes)
                .eq(TbOrgApplySampleMain::getDeleteFlag,DeleteFlagEnum.NO_DELETE.getCode()));
    }


    /**
     * 根据签收编码和签收机构查询样本信息
     * @param signBarcode
     * @param hspOrgCode
     * @return
     */
    public TbOrgApplySampleMain queryBySignBarcodeAndHspOrgCode(String signBarcode, String hspOrgCode) {
        List<TbOrgApplySampleMain> tbOrgApplySampleMains = tbOrgApplySampleMainMapper.selectList(Wrappers.lambdaQuery(TbOrgApplySampleMain.class)
                .eq(TbOrgApplySampleMain::getSignBarcode, signBarcode)
                .eq(TbOrgApplySampleMain::getHspOrgCode, hspOrgCode)
                .eq(TbOrgApplySampleMain::getStatus, ApplySampleStatus.SIGN.getValue())
                .eq(TbOrgApplySampleMain::getDeleteFlag, DeleteFlagEnum.NO_DELETE.getCode()));
        if (CollectionUtils.isEmpty(tbOrgApplySampleMains)){
            return null;
        }
        TbOrgApplySampleMain tbOrgApplySampleMain = tbOrgApplySampleMains.get(0);
        // 脱敏字段解密
//        FieldDesensitizationUtil.decryptFields(tbOrgApplySampleMain,fieldNames);
        return tbOrgApplySampleMain;
    }

    /**
     * 更新样本的报告状态（用于重新同步报告结果）
     * @param mainId
     * @param reportStatus 报告状态 0未报告 1已报告
     * @param reportMd5 报告单md5值
     * @return
     */
    public Integer updateReportStatusByMainId(String mainId, Integer reportStatus, String reportMd5) {
        return tbOrgApplySampleMainMapper.update(null,Wrappers.lambdaUpdate(TbOrgApplySampleMain.class)
                .set(TbOrgApplySampleMain::getIsReport,reportStatus)
                .set(TbOrgApplySampleMain::getReportMd5,reportMd5)
                .eq(TbOrgApplySampleMain::getMainId,mainId));
    }


    /**
     * 根据送检机构编码和条码号查询样本
     * @param barcode
     * @param hspOrgCode
     * @return
     */
    public TbOrgApplySampleMain queryByBarcodeAndHspOrgCode(String barcode, String hspOrgCode) {
        if (StringUtils.isAnyBlank(barcode,hspOrgCode)){
            return null;
        }

        List<TbOrgApplySampleMain> tbOrgApplySampleMains = tbOrgApplySampleMainMapper.selectList(Wrappers.lambdaQuery(TbOrgApplySampleMain.class)
                .eq(TbOrgApplySampleMain::getHspOrgCode, hspOrgCode)
                .eq(TbOrgApplySampleMain::getBarcode, barcode)
                .eq(TbOrgApplySampleMain::getStatus, ApplySampleStatus.SIGN.getValue())
                .eq(TbOrgApplySampleMain::getDeleteFlag, DeleteFlagEnum.NO_DELETE.getCode())
                .last(" limit 1")
        );
        if (CollectionUtils.isEmpty(tbOrgApplySampleMains)){
            return null;
        }

        return tbOrgApplySampleMains.get(0);
    }

    /**
     * 查询未报告的样本
     * @param params
     * @return
     */
    public List<TbOrgApplySampleMain> queryUnReportSample(List<String> params) {
        if (CollectionUtils.isEmpty(params)){
            return Collections.emptyList();
        }

        return tbOrgApplySampleMainMapper.selectList(Wrappers.lambdaQuery(TbOrgApplySampleMain.class)
                .in(TbOrgApplySampleMain::getBarcode,params)
                .eq(TbOrgApplySampleMain::getIsReport,0)
                .ne(TbOrgApplySampleMain::getStatus,ApplySampleStatus.CANCEL_SEND.getValue())
                .eq(TbOrgApplySampleMain::getDeleteFlag,DeleteFlagEnum.NO_DELETE.getCode()));
    }


    /**
     * 查询待补尝的样本
     * @param createDateStart
     * @param createDateEnd
     * @return
     */
    public List<TbOrgApplySampleMain> queryUnReportSampleForCompensation(Date createDateStart, Date createDateEnd) {
        return tbOrgApplySampleMainMapper.selectList(Wrappers.lambdaQuery(TbOrgApplySampleMain.class)
                .ge(TbOrgApplySampleMain::getCreateTime, createDateStart)
                .le(TbOrgApplySampleMain::getCreateTime, createDateEnd)
                .eq(TbOrgApplySampleMain::getIsReport, 0)
                .eq(TbOrgApplySampleMain::getStatus, ApplySampleStatus.SIGN.getValue())
                .eq(TbOrgApplySampleMain::getDeleteFlag, DeleteFlagEnum.NO_DELETE.getCode()));
    }

    /**
     * 根据 条码号和机构编码查询 签收的样本
     * @param barcode
     * @param hspOrgCode
     * @return
     */
    public TbOrgApplySampleMain getSignedApplySample(String barcode, String hspOrgCode,String orgCode) {
        LambdaQueryWrapper<TbOrgApplySampleMain> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TbOrgApplySampleMain::getHspOrgCode,hspOrgCode)
                .eq(TbOrgApplySampleMain::getBarcode,barcode)
                .eq(TbOrgApplySampleMain::getOrgCode,orgCode)
                .eq(TbOrgApplySampleMain::getStatus,ApplySampleStatus.SIGN.getValue())
                .orderByDesc(TbOrgApplySampleMain::getCreateTime).last(" limit 1");
        TbOrgApplySampleMain tbOrgApplySampleMain = tbOrgApplySampleMainMapper.selectOne(queryWrapper);
        // 脱敏字段解密
//        FieldDesensitizationUtil.decryptFields(tbOrgApplySampleMain,fieldNames);
        return tbOrgApplySampleMain;
    }


    /**
     *
     * @param mainIds
     * @param status
     * @return
     */
    public Integer updateDownloadStatusByMainIds(List<String> mainIds, int status) {
        return tbOrgApplySampleMainMapper.update(null,Wrappers.lambdaUpdate(TbOrgApplySampleMain.class)
                .set(TbOrgApplySampleMain::getIsDownload,status)
                .in(TbOrgApplySampleMain::getMainId,mainIds));
    }

    /**
     * 查询监控统计的样本数据
     * @param sampleReportMonitoringDto
     * @return
     */
    public List<TbOrgApplySampleMain> querySampleReportMonitoring(SampleReportMonitoringDto sampleReportMonitoringDto) {
        return tbOrgApplySampleMainMapper.selectList(Wrappers.lambdaQuery(TbOrgApplySampleMain.class)
                .eq(TbOrgApplySampleMain::getOrgCode,sampleReportMonitoringDto.getOrgCode())
                .eq(TbOrgApplySampleMain::getStatus,ApplySampleStatus.SIGN.getValue())
                .eq(TbOrgApplySampleMain::getIsReport, YesOrNoEnum.NO.getCode())
                .between(TbOrgApplySampleMain::getCreateTime,sampleReportMonitoringDto.getBeginTIme(),sampleReportMonitoringDto.getEndTime())
                .eq(TbOrgApplySampleMain::getDeleteFlag,DeleteFlagEnum.NO_DELETE.getCode())
        );
    }

    /**
     * 查询拉取pda样本信息
     * @param queryPDASampleInfoRequest
     * @return
     */
    public List<TbOrgApplySampleMain> queryPDASampleInfo(QueryPDASampleInfoRequest queryPDASampleInfoRequest) {

        return tbOrgApplySampleMainMapper.selectList(Wrappers.lambdaQuery(TbOrgApplySampleMain.class)
                .eq(StringUtils.isNotBlank(queryPDASampleInfoRequest.getOrgCode()), TbOrgApplySampleMain::getOrgCode, queryPDASampleInfoRequest.getOrgCode())
                .in(!CollectionUtils.isEmpty(queryPDASampleInfoRequest.getHspOrgCodes()), TbOrgApplySampleMain::getHspOrgCode, queryPDASampleInfoRequest.getHspOrgCodes())
                .in(!CollectionUtils.isEmpty(queryPDASampleInfoRequest.getBarcodes()), TbOrgApplySampleMain::getBarcode, queryPDASampleInfoRequest.getBarcodes())
                .between(ObjectUtils.allNotNull(queryPDASampleInfoRequest.getCreateTimeStart(), queryPDASampleInfoRequest.getCreateTimeEnd()), TbOrgApplySampleMain::getCreateTime, queryPDASampleInfoRequest.getCreateTimeStart(), queryPDASampleInfoRequest.getCreateTimeEnd())
                .lt(TbOrgApplySampleMain::getStatus, ApplySampleStatus.SIGN.getValue())
                .eq(TbOrgApplySampleMain::getIsAdditional, YesOrNoEnum.YES.getCode())
                .eq(TbOrgApplySampleMain::getDeleteFlag, DeleteFlagEnum.NO_DELETE.getCode())
                .last(queryPDASampleInfoRequest.getSize() != null, " limit " + queryPDASampleInfoRequest.getSize())
        );
    }

    /**
     * 批量更新PDA样本信息
     * @param updateMainList
     * @return
     */
    public int updateApplyPDASampleMainBatch(List<TbOrgApplySampleMain> updateMainList) {
        if (CollectionUtils.isEmpty(updateMainList)){
            return 0;
        }
        // 脱敏字段加密
//        FieldDesensitizationUtil.encryptList(updateMainList,fieldNames);
        return tbOrgApplySampleMainMapper.updateApplyPDASampleMainBatch(updateMainList);
    }

    /**
     * 查询pda样本信息
     * @param orgCode
     * @param limsBarcodes
     * @return
     */
    public List<TbOrgApplySampleMain> queryPDASamplesValid(String orgCode, List<String> limsBarcodes) {
        return tbOrgApplySampleMainMapper.selectList(Wrappers.lambdaQuery(TbOrgApplySampleMain.class)
                .eq(TbOrgApplySampleMain::getOrgCode, orgCode)
                .in(TbOrgApplySampleMain::getBarcode,limsBarcodes)
//                .ne(TbOrgApplySampleMain::getStatus, ApplySampleStatus.CANCEL_SEND.getValue())
                .eq(TbOrgApplySampleMain::getIsAdditional, YesOrNoEnum.YES.getCode())
                .eq(TbOrgApplySampleMain::getDeleteFlag, DeleteFlagEnum.NO_DELETE.getCode())
        );
    }

    /**
     * 根据签收条码号 查询签收的样本信息
     * @param signBarcode
     * @param signOrgCode
     * @return
     */
    public TbOrgApplySampleMain querySignSampleMainBySignBarcode(String signBarcode, String signOrgCode) {
        if (StringUtils.isBlank(signBarcode) || StringUtils.isBlank(signOrgCode)){
            return null;
        }

        List<TbOrgApplySampleMain> tbOrgApplySampleMains = tbOrgApplySampleMainMapper.selectList(Wrappers.lambdaQuery(TbOrgApplySampleMain.class)
                .eq(TbOrgApplySampleMain::getSignBarcode, signBarcode)
                .eq(TbOrgApplySampleMain::getSignOrgCode, signOrgCode)
                .eq(TbOrgApplySampleMain::getStatus, ApplySampleStatus.SIGN.getValue())
                .eq(TbOrgApplySampleMain::getDeleteFlag, DeleteFlagEnum.NO_DELETE.getCode()));
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(tbOrgApplySampleMains)){
            return null;
        }

        return tbOrgApplySampleMains.get(NumberUtils.INTEGER_ZERO);
    }

    /**
     * 更新样本信息
     * @param tbOrgApplySampleMain
     * @return
     */
    public Integer updateApplySampleMainById(TbOrgApplySampleMain tbOrgApplySampleMain) {
        if (Objects.isNull(tbOrgApplySampleMain) || StringUtils.isBlank(tbOrgApplySampleMain.getMainId())) {
            return NumberUtils.INTEGER_ZERO;
        }

        return tbOrgApplySampleMainMapper.updateById(tbOrgApplySampleMain);
    }

}