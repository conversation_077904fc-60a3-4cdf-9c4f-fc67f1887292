package com.labway.business.center.compare.controller;

import com.labway.business.center.compare.annotation.LogAnnotation;
import com.labway.business.center.core.enums.SystemModuleEmun;
import com.labway.business.center.compare.dto.QueryLisCustomerMappingDto;
import com.labway.business.center.compare.dto.QueryOrgCustomerRelationListDto;
import com.labway.business.center.compare.dto.TbLisCustomerInfoDto;
import com.labway.business.center.compare.request.*;
import com.labway.business.center.compare.service.TbLisCustomerInfoService;
import com.labway.business.center.core.config.Pager;
import com.labway.business.center.core.enums.OperationTypeEnum;
import com.swak.frame.dto.Response;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * lis客商信息表(TbLisCustomerInfo)表控制层
 *
 * <AUTHOR>
 * @since 2023-06-30 20:08:02
 */
@RestController
@RequestMapping("/tbLisCustomerInfo")
public class TbLisCustomerInfoController {
    /**
     * 服务对象
     */
    @Resource
    private TbLisCustomerInfoService tbLisCustomerInfoService;


    /**
     * 分页查询lis客商信息
     */
//    @LogAnnotation(value = "分页查询lis客商信息",OperationType = OperationTypeEnum.QUERY,SystemModule = SystemModuleEmun.BUSINESS_COMPARE)
    @PostMapping("/queryLisCustomerInfoPage")
    public Response<Pager<List<TbLisCustomerInfoDto>>> queryLisCustomerInfoPage(@RequestBody @Valid QueryLisCustomerInfoPageRequest pageRequest){
        return tbLisCustomerInfoService.queryLisCustomerInfoPage(pageRequest);
    }

    /**
     * 查询lis客商对照的ncc客商信息
     */
//    @LogAnnotation(value = "查询lis客商对照的ncc客商信息",OperationType = OperationTypeEnum.QUERY,SystemModule = SystemModuleEmun.BUSINESS_COMPARE)
    @PostMapping("/queryLisCustomerMapping")
    public Response<QueryLisCustomerMappingDto> queryLisCustomerMapping(@RequestBody @Valid QueryLisCustomerMappingRequest queryLisCustomerMappingRequest){
        return tbLisCustomerInfoService.queryLisCustomerMapping(queryLisCustomerMappingRequest);
    }

    /**
     * 保存lis客商和ncc客商的对照关系
     */
    @LogAnnotation(value = "保存lis客商和ncc客商的对照关系",OperationType = OperationTypeEnum.SAVE,SystemModule = SystemModuleEmun.BUSINESS_COMPARE)
    @PostMapping("/saveLisCustomerMapping")
    public Response<?> saveLisCustomerMapping(@RequestBody @Valid SaveLisCustomerMappingRequest saveLisCustomerMappingRequest){
        return tbLisCustomerInfoService.saveLisCustomerMapping(saveLisCustomerMappingRequest);
    }

    /**
     * 删除lis客商和ncc客商的映射关系
     */
    @LogAnnotation(value = "删除lis客商和ncc客商的映射关系",OperationType = OperationTypeEnum.DELETE,SystemModule = SystemModuleEmun.BUSINESS_COMPARE)
    @PostMapping("/deleteLisCustomerMappingById")
    public Response<?> deleteLisCustomerMappingById(@RequestBody @Valid DeleteLisCustomerMappingByIdRquest deleteLisCustomerMappingByIdRquest){
        return tbLisCustomerInfoService.deleteLisCustomerMappingById(deleteLisCustomerMappingByIdRquest);
    }


    //==================================================================================================================

    /**
     * 根据业务单元编码查询下发的客商信息列表 -- 不传查所有
     */
//    @LogAnnotation(value = "根据业务单元编码查询下发的客商信息列表",OperationType = OperationTypeEnum.QUERY,SystemModule = SystemModuleEmun.BUSINESS_COMPARE)
    @PostMapping("/queryCustomerByOrgCode")
    public Response<List<QueryOrgCustomerRelationListDto>> queryOrgCustomerRelationList(@RequestBody @Valid QueryOrgCustomerRelationListRequest queryOrgCustomerRelationListRequest){
        return tbLisCustomerInfoService.queryOrgCustomerRelationList(queryOrgCustomerRelationListRequest);
    }

    /**
     * 导入lis客商
     */
    @LogAnnotation(value = "导入lis客商",OperationType = OperationTypeEnum.IMPORT,SystemModule = SystemModuleEmun.BUSINESS_COMPARE)
    @PostMapping("/importLisCustomerInfo")
    public Response<?> importLisCustomerInfo(@RequestBody @Valid ImportLisCustomerInfoRequest importLisCustomerInfoRequest){
        return tbLisCustomerInfoService.importLisCustomerInfo(importLisCustomerInfoRequest);
    }

    /**
     * 删除lis客商信息-以及对照关系
     */
    @LogAnnotation(value = "删除lis客商信息-以及对照关系",OperationType = OperationTypeEnum.DELETE,SystemModule = SystemModuleEmun.BUSINESS_COMPARE)
    @PostMapping("/deleteLisCustomerAndMapping")
    public Response<?> deleteLisCustomerAndMapping(@RequestBody @Valid DeleteLisCustomerAndMappingRequest deleteLisCustomerAndMappingRequest){
        return tbLisCustomerInfoService.deleteLisCustomerAndMapping(deleteLisCustomerAndMappingRequest);
    }


}

