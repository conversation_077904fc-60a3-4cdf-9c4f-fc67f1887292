package com.labway.business.center.compare.converter;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.property.ExcelContentProperty;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class AgeConverter implements Converter<Integer> {
    @Override
    public Class supportJavaTypeKey() {
        return null;
    }
 
    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return null;
    }
 
    @Override
    public Integer convertToJavaData(CellData cellData, ExcelContentProperty excelContentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        return getAge(cellData.getStringValue());
    }
 
    @Override
    public CellData convertToExcelData(Integer integer, ExcelContentProperty excelContentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        return new CellData(setAge(integer));
    }

    public Integer getAge(String age) {
        Pattern pattern = Pattern.compile("\\d+");
        Matcher matcher = pattern.matcher(age);
        if (matcher.find()) {
            String userAge = matcher.group();
            return Integer.parseInt(userAge);
        }
        return -1;
    }
    public String setAge(Integer age) {
        return age + "岁";
    }

}