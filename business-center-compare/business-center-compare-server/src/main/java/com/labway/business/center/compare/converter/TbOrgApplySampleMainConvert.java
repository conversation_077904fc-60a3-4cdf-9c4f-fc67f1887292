package com.labway.business.center.compare.converter;


import com.labway.business.center.compare.dto.LisMiddleSampleInfoDto;
import com.labway.business.center.compare.dto.OrgApplySampleSendDTO;
import com.labway.business.center.compare.dto.OrgApplySampleSendDetailDTO.OrgApplySampleSendBarcodeInfo;
import com.labway.business.center.compare.dto.OrgApplySampleSendDetailDTO.OrgApplySampleSendPatientInfo;
import com.labway.business.center.compare.dto.OutApplyInfoDTO;
import com.labway.business.center.compare.dto.TbOrgApplySampleMainImportResultDTO;
import com.labway.business.center.compare.dto.middlebase.OrgApplySampleMainDTO;
import com.labway.business.center.compare.dto.middlebase.OrgApplySampleMainItemDTO;
import com.labway.business.center.compare.excel.TbOrgApplySampleMainImportDTO;
import com.labway.business.center.compare.persistence.entity.TbOrgApplySampleMain;
import com.labway.business.center.compare.persistence.entity.TbOrgApplySampleMainItem;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

@Mapper(componentModel = "spring")
public interface TbOrgApplySampleMainConvert {

    @Mapping(target = "patientSexCode", source = "patientSex")
    @Mapping(target = "statusCode", source = "status")
    @Mapping(target = "patientSex", ignore = true)
    @Mapping(target = "status", ignore = true)
    OrgApplySampleSendDTO convertOrgApplySampleSendDTO(TbOrgApplySampleMain tbOrgApplySampleMain);

    OrgApplySampleSendBarcodeInfo convertOrgApplySampleSendBarcodeInfo(TbOrgApplySampleMain tbOrgApplySampleMain);

    @Mapping(target = "patientSexCode", source = "patientSex")
    @Mapping(target = "urgentCode", source = "urgent")
    OrgApplySampleSendPatientInfo convertOrgApplySampleSendPatientInfo(TbOrgApplySampleMain tbOrgApplySampleMain);

    @Mapping(target = "patientAge", ignore = true)
    TbOrgApplySampleMain convertImport2Main(TbOrgApplySampleMainImportDTO importDTO);
    TbOrgApplySampleMainImportResultDTO convertImport2ResultDTO(TbOrgApplySampleMainImportDTO importDTO);

    @Mapping(target = "patientAsddress", source = "patientAddress")
    TbOrgApplySampleMain convertMiddleOrgApplySampleMain2TbOrgApplySampleMain(OrgApplySampleMainDTO mainDTO);

}
