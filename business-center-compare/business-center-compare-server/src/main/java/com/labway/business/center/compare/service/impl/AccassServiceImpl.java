 package com.labway.business.center.compare.service.impl;

 import com.labway.business.center.compare.converter.AccassConverter;
 import com.labway.business.center.compare.persistence.entity.Accass;
 import com.labway.business.center.compare.repository.center.AccassRepository;
 import com.labway.business.center.finance.dto.AccassDTO;
 import com.labway.business.center.finance.request.AccassListRequest;
 import com.labway.business.center.finance.service.AccassService;
 import com.swak.frame.dto.Response;
 import lombok.extern.slf4j.Slf4j;
 import org.apache.dubbo.config.annotation.DubboService;

 import javax.annotation.Resource;
 import java.util.List;
 import java.util.stream.Collectors;

/**
 * 辅助核算服务类
 * <AUTHOR>
 * @date 2023/03/21
 */
@DubboService(group = "center-compare")
@Slf4j
public class AccassServiceImpl implements AccassService {

   

    @Resource
    private AccassRepository accassRepository;
    
    @Resource
    private AccassConverter accassConverter;

    @Override
    public Response<?> washAccassToCenterDatabase() {
      return null;
    }
    
    /**
     * 通过科目id查询对应的辅助核算
     *
     * @param accassListRequest 科目id
     * @return
     */
    @Override
    public Response<List<AccassDTO>> getAccassByAccasoaId(AccassListRequest accassListRequest) {
        List<Accass> accassList = accassRepository.getAccassByAccasoaId(accassListRequest.getAccasoaId());
        return Response.success(accassList.stream().map(item -> accassConverter.convertAccass2DTO(item)).collect(Collectors.toList()));
    }
    
   

}
