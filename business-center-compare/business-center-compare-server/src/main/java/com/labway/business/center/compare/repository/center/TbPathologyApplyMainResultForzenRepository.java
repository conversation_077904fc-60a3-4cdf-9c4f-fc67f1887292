package com.labway.business.center.compare.repository.center;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.business.center.compare.persistence.entity.TbPathologyApplyMainResult;
import com.labway.business.center.compare.persistence.entity.TbPathologyApplyMainResultAddtion;
import com.labway.business.center.compare.persistence.entity.TbPathologyApplyMainResultForzen;
import com.labway.business.center.compare.persistence.mapper.TbPathologyApplyMainResultForzenMapper;
import com.labway.business.center.core.enums.DeleteFlagEnum;
import com.labway.business.center.core.util.NotifyUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;

@Slf4j
@Repository
public class TbPathologyApplyMainResultForzenRepository {

    @Resource
    private NotifyUtil notifyUtil;
    @Resource
    private TbPathologyApplyMainResultForzenMapper tbPathologyApplyMainResultForzenMapper;


    /**
     * 新增冰冻结果信息
     * @param mainResultForzen
     * @return
     */
    public int saveOne(TbPathologyApplyMainResultForzen mainResultForzen) {
        if (mainResultForzen == null){
            return 0;
        }

        return tbPathologyApplyMainResultForzenMapper.insert(mainResultForzen);
    }

    /**
     * 批量添加冰冻结果信息
     * @param mainResultForzens
     * @return
     */
    public int saveBatch(List<TbPathologyApplyMainResultForzen> mainResultForzens) {
        if (CollectionUtils.isEmpty(mainResultForzens)){
            return 0;
        }

        return tbPathologyApplyMainResultForzenMapper.insertBatch(mainResultForzens);
    }



    /**
     * 更新冰冻报告结果信息
     * @param tbPathologyApplyMainResultForzen
     * @return
     */
    public int updateById(TbPathologyApplyMainResultForzen tbPathologyApplyMainResultForzen) {
        if (tbPathologyApplyMainResultForzen == null) {
            return 0;
        }

        return tbPathologyApplyMainResultForzenMapper.updateById(tbPathologyApplyMainResultForzen);
    }


    /**
     * 根据病理号和序号查询冰冻报告结果
     * @param hspOrgCode
     * @param barcode
     * @param fBlh
     * @param fBgxh
     * @return
     */
    public TbPathologyApplyMainResultForzen queryByBlhAndXh(String hspOrgCode, String barcode, String fBlh, Integer fBgxh) {
        List<TbPathologyApplyMainResultForzen> mainResultForzens = tbPathologyApplyMainResultForzenMapper.selectList(Wrappers.lambdaQuery(TbPathologyApplyMainResultForzen.class)
                .eq(TbPathologyApplyMainResultForzen::getHspOrgCode, hspOrgCode)
                .eq(TbPathologyApplyMainResultForzen::getBarcode, barcode)
                .eq(TbPathologyApplyMainResultForzen::getfBdBlh, fBlh)
                .eq(TbPathologyApplyMainResultForzen::getfBdBgxh, fBgxh)
                .eq(TbPathologyApplyMainResultForzen::getDeleteFlag, DeleteFlagEnum.NO_DELETE.getCode()));
        if (CollectionUtils.isEmpty(mainResultForzens)){
            return null;
        }

        if (mainResultForzens.size()>1){
            log.warn("冰冻结果表(送检机构:{},条码号:{},病理号:{},序号:{})出现多个报告如果，请及时报告结果信息！",hspOrgCode,barcode,fBlh,fBgxh);
        }

        return mainResultForzens.get(0);
    }


    /**
     *
     * @param hspOrgCode
     * @param barcode
     * @param fBlh
     * @param mainResultId
     * @return
     */
    public int setResultIdByBlh(String hspOrgCode, String barcode, String fBlh, String mainResultId) {
        return tbPathologyApplyMainResultForzenMapper.update(null,Wrappers.lambdaUpdate(TbPathologyApplyMainResultForzen.class)
                .set(TbPathologyApplyMainResultForzen::getResultId,mainResultId)
                .eq(TbPathologyApplyMainResultForzen::getHspOrgCode,hspOrgCode)
                .eq(TbPathologyApplyMainResultForzen::getBarcode,barcode)
                .eq(TbPathologyApplyMainResultForzen::getfBdBlh,fBlh)
                .eq(TbPathologyApplyMainResultForzen::getDeleteFlag,DeleteFlagEnum.NO_DELETE.getCode()));
    }

    /**
     * 置空冰冻报告resultId
     * @param resultId
     * @return
     */
    public int resetResultId(String resultId) {
        return tbPathologyApplyMainResultForzenMapper.update(null,Wrappers.lambdaUpdate(TbPathologyApplyMainResultForzen.class)
                .set(TbPathologyApplyMainResultForzen::getResultId,"")
                .eq(TbPathologyApplyMainResultForzen::getResultId,resultId)
                .eq(TbPathologyApplyMainResultForzen::getDeleteFlag,DeleteFlagEnum.NO_DELETE.getCode()));
    }

    /**
     * 删除冰冻报告
     * @param resultId
     * @return
     */
    public int deleteById(String resultId) {
        return tbPathologyApplyMainResultForzenMapper.deleteById(resultId);
    }

    /**
     * 查询冰冻报告信息
     * @param hspOrgCode
     * @param barcode
     * @param fBlh
     * @param fBgxh
     * @param resultUniqueCode
     * @return
     */
    public TbPathologyApplyMainResultForzen queryOneByFBlh(String hspOrgCode, String barcode, String fBlh, String fBgxh, String resultUniqueCode) {

        List<TbPathologyApplyMainResultForzen> mainResults = tbPathologyApplyMainResultForzenMapper.selectList(Wrappers.lambdaQuery(TbPathologyApplyMainResultForzen.class)
                .eq(TbPathologyApplyMainResultForzen::getHspOrgCode, hspOrgCode)
                .eq(TbPathologyApplyMainResultForzen::getBarcode, barcode)
                .eq(TbPathologyApplyMainResultForzen::getfBdBlh, fBlh)
                .eq(TbPathologyApplyMainResultForzen::getfBdBgxh, fBgxh)
                .eq(TbPathologyApplyMainResultForzen::getDeleteFlag, DeleteFlagEnum.NO_DELETE.getCode())
                .orderByDesc(TbPathologyApplyMainResultForzen::getHspOrgCode, TbPathologyApplyMainResultForzen::getResultId));
        if (CollectionUtils.isEmpty(mainResults)){
            return null;
        }

        if (mainResults.size()>1){
            log.warn("送检机构：{},条码号：{}，病理号：{}，查询到多条冰冻报告，请核实病理冰冻报告结果是否异常！！！",hspOrgCode,barcode,fBlh);
            notifyUtil.send(String.format("送检机构：%s,条码号：%s，病理号：%s，查询到多条冰冻报告，请核实病理冰冻报告结果是否异常！！！",hspOrgCode,barcode,fBlh));
        }

        return mainResults.get(0);
    }

    /**
     * 根据更新时间区间查询病理结果信息
     * @param beginTime
     * @param endTime
     * @return
     */
    public List<TbPathologyApplyMainResultForzen> queryByTimeInterval(Date beginTime, Date endTime) {
        return tbPathologyApplyMainResultForzenMapper.selectList(Wrappers.lambdaQuery(TbPathologyApplyMainResultForzen.class)
                .ge(TbPathologyApplyMainResultForzen::getUpdateTime, beginTime)
                .lt(TbPathologyApplyMainResultForzen::getUpdateTime, endTime));
    }


    /**
     * 根据resultIds查询冰冻病理报告信息
     * @param resultIds
     * @return
     */
    public List<TbPathologyApplyMainResultForzen> queryListByResultIds(List<String> resultIds) {
        if (CollectionUtils.isEmpty(resultIds)) {
            return Collections.emptyList();
        }

        return tbPathologyApplyMainResultForzenMapper.selectList(Wrappers.lambdaQuery(TbPathologyApplyMainResultForzen.class).in(TbPathologyApplyMainResultForzen::getResultId, resultIds));
    }

    /**
     * 删除冰冻报告
     * @param forzenId
     * @return
     */
    public Integer updateDeleteById(String forzenId) {
        return tbPathologyApplyMainResultForzenMapper.update(null, Wrappers.lambdaUpdate(TbPathologyApplyMainResultForzen.class)
                .eq(TbPathologyApplyMainResultForzen::getForzenId, forzenId)
                        .set(TbPathologyApplyMainResultForzen::getUpdateTime,new Date())
                .set(TbPathologyApplyMainResultForzen::getDeleteFlag, 1));
    }


}
