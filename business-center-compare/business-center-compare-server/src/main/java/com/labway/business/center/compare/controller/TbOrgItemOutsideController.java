package com.labway.business.center.compare.controller;


import com.alibaba.fastjson.JSONObject;
import com.labway.business.center.compare.annotation.LogAnnotation;
import com.labway.business.center.compare.dto.QueryItemOutsidePageDTO;
import com.labway.business.center.compare.dto.QueryItemTestByOrgCodeDTO;
import com.labway.business.center.compare.dto.QueryOrgItemMappingRelationDTO;
import com.labway.business.center.compare.request.*;
import com.labway.business.center.compare.service.TbOrgItemMappingRelationService;
import com.labway.business.center.compare.service.TbOrgItemOutsideService;
import com.labway.business.center.core.config.Pager;
import com.labway.business.center.core.enums.OperationTypeEnum;
import com.labway.business.center.core.enums.SystemModuleEmun;
import com.swak.frame.dto.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.List;

/**
 * 外部检验项目表(TbOrgItemOutside)表控制层
 *
 * <AUTHOR>
 * @since 2023-04-23 10:33:24
 */
@Slf4j
@RestController
@RequestMapping("/tbOrgItemOutside")
public class TbOrgItemOutsideController {
    /**
     * 服务对象
     */
    @Resource
    private TbOrgItemOutsideService tbOrgItemOutsideService;
    @Resource
    private TbOrgItemMappingRelationService tbOrgItemMappingRelationService;

    /**
     * 分页查询外部检验项目信息
     */
//    @LogAnnotation(value = "分页查询外部检验项目信息",OperationType = OperationTypeEnum.QUERY,SystemModule = SystemModuleEmun.BUSINESS_COMPARE)
    @PostMapping("/queryItemOutsidePage")
    public Response<Pager<List<QueryItemOutsidePageDTO>>> queryItemOutsidePage(@RequestBody @Valid QueryItemOutsidePageRequest queryRequest){
        return tbOrgItemOutsideService.queryItemOutsidePage(queryRequest);
    }

    /**
     * 查询外部项目对照的检验项目信息
     */
//    @LogAnnotation(value = "查询外部项目对照的检验项目信息",OperationType = OperationTypeEnum.QUERY,SystemModule = SystemModuleEmun.BUSINESS_COMPARE)
    @PostMapping("/queryOrgItemMappingRelation")
    public Response<List<QueryOrgItemMappingRelationDTO>> queryOrgItemMappingRelation(@RequestBody @Valid QueryOrgItemMappingRelationRequest queryRequest){
        return tbOrgItemMappingRelationService.queryOrgItemMappingRelation(queryRequest);
    }

    /**
     * 查询业务单元分配的检验项目信息
     */
//    @LogAnnotation(value = "查询业务单元分配的检验项目信息",OperationType = OperationTypeEnum.QUERY,SystemModule = SystemModuleEmun.BUSINESS_COMPARE)
    @PostMapping("/queryItemTestByOrgCode")
    public Response<List<QueryItemTestByOrgCodeDTO>> queryItemTestByOrgCode(@RequestBody @Valid QueryItemTestByOrgCodeRequest queryRequest){
        return tbOrgItemOutsideService.queryItemTestByOrgCode(queryRequest);
    }

    /**
     * 保存外部项目和检验项目的对照关系
     */
    @LogAnnotation(value = "保存外部项目和检验项目的对照关系",OperationType = OperationTypeEnum.SAVE,SystemModule = SystemModuleEmun.BUSINESS_COMPARE)
    @PostMapping("/saveOrgItemMappingRelation")
    public Response<Integer> saveOrgItemMappingRelation(@RequestBody @Valid SaveOrgItemMappingRelationRequest saveRequest){
        return tbOrgItemMappingRelationService.saveOrgItemMappingRelation(saveRequest);
    }

    /**
     * 删除外部项目对照的项目信息-校验更新外部项目的对照状态
     */
    @LogAnnotation(value = "删除外部项目对照的项目信息-校验更新外部项目的对照状态",OperationType = OperationTypeEnum.DELETE,SystemModule = SystemModuleEmun.BUSINESS_COMPARE)
    @PostMapping("/deleteOrgItemMappingRelation")
    public Response<Integer> deleteOrgItemMappingRelation(@RequestBody @Valid DeleteOrgItemMappingRelationRequest deleteRequest){
        log.info("业务中台--deleteOrgItemMappingRelation入参信息：{}", JSONObject.toJSONString(deleteRequest));
        return tbOrgItemMappingRelationService.deleteOrgItemMappingRelation(deleteRequest);
    }

    /**
     * 新增外部项目-编码唯一不重复，名称可以重复
     */
    @LogAnnotation(value = "新增外部项目",OperationType = OperationTypeEnum.SAVE,SystemModule = SystemModuleEmun.BUSINESS_COMPARE)
    @PostMapping("/saveItemOutside")
    public Response<?> saveItemOutside(@RequestBody @Valid SaveItemOutsideRequest saveRequest){
        return tbOrgItemOutsideService.saveItemOutside(saveRequest);
    }

    /**
     * 修改外部项目-编码不允许修改，名称可以重复
     */
    @LogAnnotation(value = "修改外部项目",OperationType = OperationTypeEnum.UPDATE,SystemModule = SystemModuleEmun.BUSINESS_COMPARE)
    @PostMapping("/updateItemOutside")
    public Response<?> updateItemOutside(@RequestBody @Valid UpdateItemOutsideRequest updateRequest){
        return tbOrgItemOutsideService.updateItemOutside(updateRequest);
    }

    /**
     * 批量删除外部项目-同步删除对照关系
     */
    @LogAnnotation(value = "批量删除外部项目",OperationType = OperationTypeEnum.DELETE,SystemModule = SystemModuleEmun.BUSINESS_COMPARE)
    @PostMapping("/deleteItemOutsideBatch")
    public Response<?> deleteItemOutsideBatch(@RequestBody @Valid DeleteItemOutsideBatchRequest deleteRequest){
        log.info("删除外部项目的对照关系，调用入参：{}",JSONObject.toJSONString(deleteRequest));
        return tbOrgItemOutsideService.deleteItemOutsideBatch(deleteRequest);
    }

    /**
     * 导入外部项目和对照关系
     */
    @LogAnnotation(value = "导入外部项目和对照关系",OperationType = OperationTypeEnum.IMPORT,SystemModule = SystemModuleEmun.BUSINESS_COMPARE)
    @PostMapping("/importItemAndMapping")
    public Response<?> importItemAndMapping(@RequestBody @Valid ImportItemAndMappingRequest importRequest) throws IOException {
        return tbOrgItemOutsideService.importItemAndMapping(importRequest);
    }

    /**
     * 下载外部项目和对照关系导入的模板文件
     */
    @LogAnnotation(value = "下载外部项目和对照关系导入的模板文件",OperationType = OperationTypeEnum.EXPORT,SystemModule = SystemModuleEmun.BUSINESS_COMPARE)
    @GetMapping("/downloadItemExcelTemplate")
    public void downloadItemExcelTemplate(HttpServletResponse response) throws IOException {

        // 下载文件的路径
        String path = "file/ItemAndMappingTemplate.xlsx";
        ClassPathResource classPathResource = new ClassPathResource(path);
        File file = classPathResource.getFile();

        // 将文件写入输入流
        InputStream fis = new FileInputStream(classPathResource.getFile());
        byte[] buffer = new byte[fis.available()];
        fis.read(buffer);
        fis.close();

        // 清空response
        response.reset();
        // 设置response的Header
        response.setCharacterEncoding("UTF-8");
        // Content-Disposition的作用：告知浏览器以何种方式显示响应返回的文件，用浏览器打开还是以附件的形式下载到本地保存
        // attachment表示以附件方式下载 inline表示在线打开 "Content-Disposition: inline; filename=文件名.mp3"
        // filename表示文件的默认名称，因为网络传输只支持URL编码的相关支付，因此需要将文件名URL编码后进行传输,前端收到后需要反编码才能获取到真正的名称
        response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(file.getName(), "UTF-8"));
        // 告知浏览器文件的大小
        response.addHeader("Content-Length", "" + file.length());
        OutputStream outputStream = new BufferedOutputStream(response.getOutputStream());
        response.setContentType("application/octet-stream");
        outputStream.write(buffer);
        outputStream.flush();
    }

    /**
     * 同步复制外部项目的对照关系
     */
    @LogAnnotation(value = "同步复制外部项目的对照关系",OperationType = OperationTypeEnum.SAVE,SystemModule = SystemModuleEmun.BUSINESS_COMPARE)
    @PostMapping("/syncCopyOrgItemMappingRelation")
    public Response<Integer> syncCopyOrgItemMappingRelation(@RequestBody @Valid SyncCopyOrgItemMappingRelationDto syncRequest){
        return tbOrgItemMappingRelationService.syncCopyOrgItemMappingRelation(syncRequest);
    }

    /**
     * 导出送检机构的外部检验项目对照关系
     */
    @LogAnnotation(value = "导出送检机构的外部检验项目对照关系",OperationType = OperationTypeEnum.EXPORT,SystemModule = SystemModuleEmun.BUSINESS_COMPARE)
    @GetMapping("/exportOutTestItemMapping/{orgCode}")
    public void exportOutTestItemMapping(@PathVariable String orgCode, HttpServletResponse response) {
        ExportOutTestItemMappingRequest exportRequest = new ExportOutTestItemMappingRequest();
        exportRequest.setOrgCode(orgCode);
        tbOrgItemMappingRelationService.exportOutTestItemMapping(exportRequest,response);
    }



}

