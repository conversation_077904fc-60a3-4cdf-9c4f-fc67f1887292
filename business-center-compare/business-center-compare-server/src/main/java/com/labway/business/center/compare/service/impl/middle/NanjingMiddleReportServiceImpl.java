package com.labway.business.center.compare.service.impl.middle;

import com.labway.business.center.core.enums.Region;
import com.labway.business.center.core.util.NotifyUtil;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <pre>
 * NanjingMiddleReportServiceImpl
 * 南京 中间库程序 结果回传
 * </pre>
 *
 * <AUTHOR>
 * @since 2024/10/11 16:30
 */
@Service
@DubboService
@RefreshScope
public class NanjingMiddleReportServiceImpl extends BaseMiddleReportService {

    @Resource
    private NotifyUtil notifyUtil;

    @Value("${special.ignoreReportItemMapping.nanjing:}")
    private String ignoreReportItemMapping;

    public NanjingMiddleReportServiceImpl(@Value("${lab.orgCode.nanjing}") String orgCode) {
        super(Region.nanjing, orgCode);
    }

    @Override
    public Region getRegion() {
        return Region.nanjing;
    }

    @Override
    public boolean isOtherMappingType(String hspOrgCode) {
        return true;
    }

    @Override
    public String resultRangeExchange(String hspOrgCode, String range) {
        return range;
    }

    @Override
    protected void notifyByDinger(String content) {
        // TODO 南京中间库告警机器人
        notifyUtil.notifyByRegion(Region.nanjing.name(), content);
    }

    @Override
    protected boolean isSkipReportItemMapping(String hspOrgCode) {
        return ignoreReportItemMapping.contains(hspOrgCode);
    }

}
