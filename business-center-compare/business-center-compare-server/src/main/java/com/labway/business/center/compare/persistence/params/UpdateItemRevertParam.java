package com.labway.business.center.compare.persistence.params;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UpdateItemRevertParam implements Serializable {

    // 回传项目id
    private String revertId;

    // 回传项目名称
    private String revertItemName;

    // 是否启用 0否1是
    private Integer enable;

}
