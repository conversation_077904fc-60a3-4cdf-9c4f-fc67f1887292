package com.labway.business.center.compare.persistence.params;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 2023/04/23 11:31
 * @description
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class QueryItemOutsidePageParam implements Serializable {

    // 业务单元编码
    private String orgCode;

    // 客商编码
    private String customerCode;

    // 外部项目名称
    private String itemOutsideName;

    // 对照状态 0为对照 1已对照
    private Integer isMapping;

}