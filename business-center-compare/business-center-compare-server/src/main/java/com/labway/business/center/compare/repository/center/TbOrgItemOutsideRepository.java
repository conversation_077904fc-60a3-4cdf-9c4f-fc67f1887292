package com.labway.business.center.compare.repository.center;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.labway.business.center.compare.constants.CompareConstants;
import com.labway.business.center.core.enums.YesOrNoEnum;
import com.labway.business.center.compare.persistence.entity.TbOrgItemOutside;
import com.labway.business.center.compare.persistence.mapper.TbOrgItemOutsideMapper;
import com.labway.business.center.compare.persistence.params.QueryItemOutsidePageParam;
import com.labway.business.center.core.enums.DeleteFlagEnum;
import com.labway.sso.core.user.SsoUser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2023/04/23 10:38
 * @description
 **/
@Slf4j
@Repository
@RefreshScope
public class TbOrgItemOutsideRepository {

    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private TbOrgItemOutsideMapper tbOrgItemOutsideMapper;


    /**
     * 分页查询外部检验项目信息
     * @param queryPage
     * @param param
     * @return
     */
    public IPage<TbOrgItemOutside> queryItemOutsidePage(IPage<TbOrgItemOutside> queryPage, QueryItemOutsidePageParam param) {
        IPage<TbOrgItemOutside> resultPage = tbOrgItemOutsideMapper.selectPage(queryPage, Wrappers.lambdaQuery(TbOrgItemOutside.class)
                .eq(TbOrgItemOutside::getOrgCode, param.getOrgCode())
                .eq(TbOrgItemOutside::getCustomerCode, param.getCustomerCode())
                .and(StringUtils.hasText(param.getItemOutsideName()), 
                    wp -> wp.like(TbOrgItemOutside::getItemOutsdieName, param.getItemOutsideName())
                            .or().like(TbOrgItemOutside::getItemOutsideCode, param.getItemOutsideName()))
                .eq(param.getIsMapping() != null, TbOrgItemOutside::getIsMapping, param.getIsMapping())
                .eq(TbOrgItemOutside::getDeleteFlag, 0));
        return resultPage;
    }


    /**
     * 修改外部项目的对照状态
     * @param orgCode
     * @param customerCode
     * @param itemOutsideCode
     * @param mapping 对照关系 0未对照 1已对照
     * @return
     */
    public int updateItemMappingInfo(String orgCode, String customerCode, String itemOutsideCode, int mapping,String optUser) {

        return tbOrgItemOutsideMapper.update(null,Wrappers.lambdaUpdate(TbOrgItemOutside.class)
                .set(TbOrgItemOutside::getIsMapping,mapping)
                        .set(TbOrgItemOutside::getUpdateTime,new Date())
                        .set(StringUtils.hasText(optUser),TbOrgItemOutside::getUpdateBy,optUser)
                .eq(TbOrgItemOutside::getOrgCode,orgCode)
                .eq(TbOrgItemOutside::getCustomerCode,customerCode)
                .eq(TbOrgItemOutside::getItemOutsideCode,itemOutsideCode));
    }


    /**
     * 批量新增外部项目信息
     * @param orgItemOutsides
     * @return
     */
    public int saveItemOutsideBatch(List<TbOrgItemOutside> orgItemOutsides) {
        // 清空缓存中的送检项目
        stringRedisTemplate.delete(CompareConstants.CUSTOMER_ORG_SEND_ITEM + orgItemOutsides.get(0).getCustomerCode()+ ":" + orgItemOutsides.get(0).getOrgCode());
        return tbOrgItemOutsideMapper.insertBatch(orgItemOutsides);
    }

    /**
     * 根据外部项目编码查询项目信息
     * @param orgCode
     * @param customerCode
     * @param itemOutsideCode
     * @param itemOutsideName
     * @return
     */
    public List<TbOrgItemOutside> queryItemOutsideByCodeAndName(String orgCode, String customerCode, String itemOutsideCode, String itemOutsideName) {
        List<TbOrgItemOutside> tbOrgItemOutsides = tbOrgItemOutsideMapper.selectList(Wrappers.lambdaQuery(TbOrgItemOutside.class)
                .eq(TbOrgItemOutside::getOrgCode, orgCode)
                .eq(TbOrgItemOutside::getCustomerCode, customerCode)
                .eq(TbOrgItemOutside::getItemOutsideCode,itemOutsideCode)
//                .and(e->e.eq(TbOrgItemOutside::getItemOutsideCode, itemOutsideCode)
//                        .or()
//                        .eq(TbOrgItemOutside::getItemOutsdieName, itemOutsideName))
                .eq(TbOrgItemOutside::getDeleteFlag, 0));
        if (CollectionUtils.isEmpty(tbOrgItemOutsides)){
            return Collections.emptyList();
        }

        if (tbOrgItemOutsides.size()>2){
            log.info("外部项目存在重复编码,请及时核实！项目机构编码：{},客商编码：{},项目编码：{},项目名称：{}",orgCode,customerCode,itemOutsideCode,itemOutsideName);
        }

        return tbOrgItemOutsides;
    }

//    /**
//     * 根据名称查询客商的项目信息（排除指定id）-用于名称判重
//     * @param orgCode 业务单元编码
//     * @param customerCode 客商编码
//     * @param itemOutsideName 项目名称
//     * @param excludeId 排除的项目id
//     * @return
//     */
//    public List<TbOrgItemOutside> queryItemOutsideExcludeId(String orgCode, String customerCode, String itemOutsideName, String excludeId) {
//        List<TbOrgItemOutside> tbOrgItemOutsides = tbOrgItemOutsideMapper.selectList(Wrappers.lambdaQuery(TbOrgItemOutside.class)
//                .eq(TbOrgItemOutside::getOrgCode, orgCode)
//                .eq(TbOrgItemOutside::getCustomerCode, customerCode)
//                .eq(TbOrgItemOutside::getItemOutsdieName, itemOutsideName)
//                .eq(TbOrgItemOutside::getDeleteFlag, 0)
//                .ne(TbOrgItemOutside::getOutsideId, excludeId)
//                .orderByDesc(TbOrgItemOutside::getCreateTime,TbOrgItemOutside::getOutsideId));
//        return tbOrgItemOutsides;
//    }

    /**
     * 更新外部项目信息
     * @param tbOrgItemOutside
     * @return
     */
    public int updateItemOutsideById(TbOrgItemOutside tbOrgItemOutside) {
        return tbOrgItemOutsideMapper.updateById(tbOrgItemOutside);
    }

    /**
     *
     * @param orgCode
     * @param customerCode
     * @param itemOutsideCodes
     * @return
     */
    public int deleteItemOutsideBatch(String orgCode, String customerCode, List<String> itemOutsideCodes, SsoUser loginUser) {

        if (!StringUtils.hasText(orgCode) || !StringUtils.hasText(customerCode) || CollectionUtils.isEmpty(itemOutsideCodes)){
            return 0;
        }

        // 清空缓存中的送检项目
        stringRedisTemplate.delete(CompareConstants.CUSTOMER_ORG_SEND_ITEM + customerCode+ ":" + orgCode);

        return tbOrgItemOutsideMapper.update(null,Wrappers.lambdaUpdate(TbOrgItemOutside.class)
                        .set(TbOrgItemOutside::getDeleteFlag,1)
                        .set(TbOrgItemOutside::getUpdateTime,new Date())
                        .set(loginUser!=null, TbOrgItemOutside::getUpdateBy,loginUser.getUserName())
                .eq(TbOrgItemOutside::getOrgCode,orgCode).eq(TbOrgItemOutside::getCustomerCode,customerCode)
                .in(TbOrgItemOutside::getItemOutsideCode,itemOutsideCodes));
    }

    /**
     * 根据项目编码查询客商的送检项目信息
     * @param outsideItemCodes
     * @param hspOrgCode
     * @return
     */
    public List<TbOrgItemOutside> queryItemOutsideListByCodes(List<String> outsideItemCodes, String hspOrgCode,String orgCode) {
        if ( org.apache.commons.lang3.StringUtils.isEmpty(hspOrgCode)){
            return Collections.emptyList();
        }

        return tbOrgItemOutsideMapper.selectList(Wrappers.lambdaQuery(TbOrgItemOutside.class)
                .eq(TbOrgItemOutside::getCustomerCode,hspOrgCode)
                .in(!CollectionUtils.isEmpty(outsideItemCodes),TbOrgItemOutside::getItemOutsideCode,outsideItemCodes)
                .eq(TbOrgItemOutside::getOrgCode,orgCode)
                .eq(TbOrgItemOutside::getDeleteFlag,0));
    }


    /**
     * 根据外部项目编码查询项目信息
     */
    public List<TbOrgItemOutside> queryItemOutsideListByCodes(String orgCode, String customerCode, List<String> outsideItemCodes) {
        if (CollectionUtils.isEmpty(outsideItemCodes)) {
            return Collections.emptyList();
        }

        return tbOrgItemOutsideMapper.selectList(Wrappers.lambdaQuery(TbOrgItemOutside.class)
                .in(TbOrgItemOutside::getItemOutsideCode, outsideItemCodes)
                .eq(TbOrgItemOutside::getOrgCode, orgCode)
                .eq(TbOrgItemOutside::getCustomerCode, customerCode)
                .eq(TbOrgItemOutside::getDeleteFlag,0)
                .orderByDesc(TbOrgItemOutside::getCreateTime,TbOrgItemOutside::getOutsideId));
    }

    /**
     * 根据外部项目名称查询项目信息
     */
    public List<TbOrgItemOutside> queryItemOutsideListByNames(String orgCode, String customerCode, List<String> outsideItemNames) {
        if (CollectionUtils.isEmpty(outsideItemNames)) {
            return Collections.emptyList();
        }

        return tbOrgItemOutsideMapper.selectList(Wrappers.lambdaQuery(TbOrgItemOutside.class)
                .in(TbOrgItemOutside::getItemOutsdieName, outsideItemNames)
                .eq(TbOrgItemOutside::getOrgCode, orgCode)
                .eq(TbOrgItemOutside::getCustomerCode, customerCode)
                .eq(TbOrgItemOutside::getDeleteFlag,0)
                .orderByDesc(TbOrgItemOutside::getCreateTime,TbOrgItemOutside::getOutsideId));
    }

    /**
     * 根据客商编码、业务单元编码和外部项目编码，批量更新外部项目的对照状态
     *
     * @param entities
     * @param isMapping
     * @return
     */
    public int updateStatusBatch(List<TbOrgItemOutside> entities, Integer isMapping) {
        if (CollectionUtils.isEmpty(entities)) {
            return 0;
        }

        // 防止in参数过多报错，超过500条时分批次更新
        int maxNum = 500;
        if (entities.size() <= maxNum) {
            return tbOrgItemOutsideMapper.updateStatusBatch(entities, isMapping);
        } else {
            int updateRow = 0;
            for (int i = 0; i < entities.size(); i += maxNum) {
                int toIndex = i + maxNum;
                toIndex = toIndex <= entities.size() ? toIndex : entities.size();
                updateRow += tbOrgItemOutsideMapper.updateStatusBatch(entities.subList(i, toIndex), isMapping);
            }
            return updateRow;
        }
    }

    public List<TbOrgItemOutside> getHasApplySampleItemCodes(List<String> itemCodeList,String orgCode, String customerCode) {
        LambdaQueryWrapper<TbOrgItemOutside> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(TbOrgItemOutside::getItemOutsideCode,itemCodeList)
                .eq(TbOrgItemOutside::getOrgCode,orgCode).eq(TbOrgItemOutside::getCustomerCode,customerCode)
                .select(TbOrgItemOutside::getItemOutsideCode);
        return tbOrgItemOutsideMapper.selectList(queryWrapper);
    }

    public Long countByOutItemCodeAndCustomerCodeAndOrgCode(String outTestItemCode, String hspOrgCode, String signOrgCode) {
        LambdaQueryWrapper<TbOrgItemOutside> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TbOrgItemOutside::getItemOutsideCode,outTestItemCode).eq(TbOrgItemOutside::getCustomerCode,hspOrgCode).eq(TbOrgItemOutside::getOrgCode,signOrgCode)
                .eq(TbOrgItemOutside::getStatus, YesOrNoEnum.YES.getCode()).eq(TbOrgItemOutside::getDeleteFlag, DeleteFlagEnum.NO_DELETE.getCode());
        return tbOrgItemOutsideMapper.selectCount(queryWrapper);
    }

    /**
     * 根据客商编码和项目编码查询客商项目信息
     * @param orgCode
     * @param customerCodes
     * @return
     */
    public List<TbOrgItemOutside> queryItemOutsideListByOrgAndCustomer(String orgCode, List<String> customerCodes) {
        return tbOrgItemOutsideMapper.selectList(Wrappers.lambdaQuery(TbOrgItemOutside.class)
                .eq(TbOrgItemOutside::getOrgCode,orgCode)
                .in(TbOrgItemOutside::getCustomerCode,customerCodes)
                .eq(TbOrgItemOutside::getDeleteFlag,YesOrNoEnum.NO.getCode()));
    }

}