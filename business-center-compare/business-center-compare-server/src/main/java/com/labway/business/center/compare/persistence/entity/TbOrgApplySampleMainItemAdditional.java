package com.labway.business.center.compare.persistence.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import java.util.Date;

/**
 * 外送生清单-送检项目信息表(TbOrgApplySampleMainItemAdditional)表实体类
 *
 * <AUTHOR>
 * @since 2023-05-12 18:16:27
 */
@SuppressWarnings("serial")
public class TbOrgApplySampleMainItemAdditional extends Model<TbOrgApplySampleMainItemAdditional> {
    //送检项目表id
    @TableId(type = IdType.INPUT)
    private String mainItemId;
    //申请单id
    private String mainId;
    //送检样本条码号
    private String barcode;
    //送检机构编码（对应中台客商编码）
    private String hspOrgCode;
    //送检机构名称（对应中台客商名称）
    private String hspOrgName;
    //检验项目编码
    private String testItemCode;
    //检验项目名称
    private String testItemName;
    //创建人id
    private String createBy;
    //创建时间
    private Date createTime;
    //更新人id
    private String updateBy;
    //更新时间
    private Date updateTime;
    //删除标识 0未删除 1删除
    @TableLogic(value = "0",delval = "1")
    private Integer deleteFlag;
    //申请单id
    private String formId;
    //申请单编码
    private String formCode;


    public String getMainItemId() {
        return mainItemId;
    }

    public void setMainItemId(String mainItemId) {
        this.mainItemId = mainItemId;
    }

    public String getMainId() {
        return mainId;
    }

    public void setMainId(String mainId) {
        this.mainId = mainId;
    }

    public String getBarcode() {
        return barcode;
    }

    public void setBarcode(String barcode) {
        this.barcode = barcode;
    }

    public String getHspOrgCode() {
        return hspOrgCode;
    }

    public void setHspOrgCode(String hspOrgCode) {
        this.hspOrgCode = hspOrgCode;
    }

    public String getHspOrgName() {
        return hspOrgName;
    }

    public void setHspOrgName(String hspOrgName) {
        this.hspOrgName = hspOrgName;
    }

    public String getTestItemCode() {
        return testItemCode;
    }

    public void setTestItemCode(String testItemCode) {
        this.testItemCode = testItemCode;
    }

    public String getTestItemName() {
        return testItemName;
    }

    public void setTestItemName(String testItemName) {
        this.testItemName = testItemName;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getDeleteFlag() {
        return deleteFlag;
    }

    public void setDeleteFlag(Integer deleteFlag) {
        this.deleteFlag = deleteFlag;
    }

    public String getFormId() {
        return formId;
    }

    public void setFormId(String formId) {
        this.formId = formId;
    }

    public String getFormCode() {
        return formCode;
    }

    public void setFormCode(String formCode) {
        this.formCode = formCode;
    }

    }

