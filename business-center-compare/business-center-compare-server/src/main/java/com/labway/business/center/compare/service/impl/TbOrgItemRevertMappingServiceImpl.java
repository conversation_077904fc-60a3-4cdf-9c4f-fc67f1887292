package com.labway.business.center.compare.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.labway.business.center.compare.converter.TbOrgItemRevertMappingConverter;
import com.labway.business.center.compare.dto.QueryItemRevertMappingByCustomerDto;
import com.labway.business.center.compare.dto.QueryItemRevertMappingForMiddleDto;
import com.labway.business.center.compare.persistence.mapper.TbOrgItemRevertMappingMapper;
import com.labway.business.center.compare.persistence.entity.TbOrgItemRevertMapping;
import com.labway.business.center.compare.persistence.params.DeleteItemRevertMappingParam;
import com.labway.business.center.compare.repository.center.TbOrgItemRevertMappingRepository;
import com.labway.business.center.compare.repository.center.TbOrgItemRevertRepository;
import com.labway.business.center.compare.request.*;
import com.labway.business.center.compare.service.TbOrgItemRevertMappingService;
import com.labway.business.center.core.enums.DeleteFlagEnum;
import com.labway.business.center.core.enums.IdEnum;
import com.labway.business.center.core.enums.ResultCode;
import com.labway.business.center.core.user.LoginUserInfoUtil;
import com.labway.sso.core.user.SsoUser;
import com.swak.frame.dto.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * (TbOrgItemRevertMapping)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-07-19 20:03:12
 * @since 2023-10-24 15:23:13
 */
@Slf4j
@DubboService
public class TbOrgItemRevertMappingServiceImpl extends ServiceImpl<TbOrgItemRevertMappingMapper, TbOrgItemRevertMapping> implements TbOrgItemRevertMappingService {

    @Resource
    private TbOrgItemRevertMappingConverter tbOrgItemRevertMappingConverter;
    @Resource
    private TbOrgItemRevertMappingRepository tbOrgItemRevertMappingRepository;
    @Resource
    private TbOrgItemRevertRepository tbOrgItemRevertRepository;


    /**
     * 查询回传对照关系 -- 根据送检客商回传项目编码
     *
     * @param queryItemRevertMappingByCustomerRequest
     * @return
     */
    @Override
    public Response<List<QueryItemRevertMappingByCustomerDto>> queryItemRevertMappingByCustomer(QueryItemRevertMappingByCustomerRequest queryItemRevertMappingByCustomerRequest) {
        List<TbOrgItemRevertMapping> tbOrgItemRevertMappings = tbOrgItemRevertMappingRepository.queryItemRevertMappingByCustomer(queryItemRevertMappingByCustomerRequest.getCustomerCode(),
                queryItemRevertMappingByCustomerRequest.getItemRevertCode(),
                queryItemRevertMappingByCustomerRequest.getOrgCode(), queryItemRevertMappingByCustomerRequest.getItemRevertType());
        if (CollectionUtils.isEmpty(tbOrgItemRevertMappings)) {
            return Response.success(Collections.EMPTY_LIST);
        }

        List<QueryItemRevertMappingByCustomerDto> resultMapping = tbOrgItemRevertMappings.stream().map(mapping -> tbOrgItemRevertMappingConverter.converterQueryItemRevertMappingByCustomerDto(mapping)).collect(Collectors.toList());
        return Response.success(resultMapping);
    }

    @Override
    public Response<List<QueryItemRevertMappingByCustomerDto>> queryItemRevertMappingByCustomerCode(String customerCode) {
        List<TbOrgItemRevertMapping> tbOrgItemRevertMappings = tbOrgItemRevertMappingRepository.queryItemRevertMappingByCustomerCode(customerCode, NumberUtils.INTEGER_ONE);

        if (CollectionUtils.isEmpty(tbOrgItemRevertMappings)) {
            return Response.success(Collections.EMPTY_LIST);
        }
        List<QueryItemRevertMappingByCustomerDto> resultMapping = tbOrgItemRevertMappings.stream().map(mapping -> tbOrgItemRevertMappingConverter.converterQueryItemRevertMappingByCustomerDto(mapping)).collect(Collectors.toList());
        return Response.success(resultMapping);
    }


    /**
     * 保存回传对照关系
     * @param saveItemRevertMappingRequest
     * @return
     */
    @DS("account")
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Response<?> saveItemRevertMapping(SaveItemRevertMappingRequest saveItemRevertMappingRequest) {
        String customerCode = saveItemRevertMappingRequest.getCustomerCode();
        String orgCode = saveItemRevertMappingRequest.getOrgCode();
        String customerItemCode = saveItemRevertMappingRequest.getCustomerItemCode();
        Integer mappingType = saveItemRevertMappingRequest.getMappingType();
        List<String> orgItemRevertCodes = saveItemRevertMappingRequest.getOrgItemRevertRequests().stream().map(e -> e.getOrgItemCode()).collect(Collectors.toList());

        SsoUser loginUser = LoginUserInfoUtil.getLoginUser();

        // 回传对照改成了多对多的关系
//        // 校验机构回传项目在该客商下是否有已经对照过的项目
//        List<TbOrgItemRevertMapping> tbOrgItemRevertMappings = tbOrgItemRevertMappingRepository.queryByOrgItemCodes(customerCode,orgCode,orgItemRevertCodes);
//        if (CollectionUtils.isNotEmpty(tbOrgItemRevertMappings)){
//            String itemStr = tbOrgItemRevertMappings.stream().map(e -> e.getOrgItemCode() + ":" + e.getOrgItemName()).collect(Collectors.joining(","));
//            return Response.fail(ResultCode.ITEM_REVERT_MAPPING_EXIST_ERROR.getCode(),String.format(ResultCode.ITEM_REVERT_MAPPING_EXIST_ERROR.getMsg(),itemStr));
//        }

        List<TbOrgItemRevertMapping> saveItemRevertMapping = getSaveItemRevertMapping(saveItemRevertMappingRequest);
        // 校验是否存在重复对照关系
        List<TbOrgItemRevertMapping> existMapping = tbOrgItemRevertMappingRepository.queryExistSameMapping(saveItemRevertMapping);
        if (CollectionUtils.isNotEmpty(existMapping)) {
            return Response.fail(ResultCode.ITEM_REVERT_MAPPING_SAME_ERROR);
        }

        // 保存对照关系
        tbOrgItemRevertMappingRepository.saveItemRevertMapping(saveItemRevertMapping);

        // 更新客商回传项目的对照标识
        tbOrgItemRevertRepository.setCustomerItemRevertIsMapping(customerCode, customerItemCode, 1, mappingType, orgCode, loginUser == null ? saveItemRevertMappingRequest.getOptUserName() : loginUser.getLoginName());

        return Response.success();
    }

    /**
     * 删除回传对照关系
     *
     * @param deleteItemRevertMappingRequest
     * @return
     */
    @DS("account")
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Response<?> deleteItemRevertMapping(DeleteItemRevertMappingRequest deleteItemRevertMappingRequest) {
        String customerCode = deleteItemRevertMappingRequest.getCustomerCode();
        String customerItemCode = deleteItemRevertMappingRequest.getCustomerItemCode();
        Integer mappingType = deleteItemRevertMappingRequest.getMappingType();
        String orgCode = deleteItemRevertMappingRequest.getOrgCode();

        SsoUser loginUser = null;
        try {
            loginUser = LoginUserInfoUtil.getLoginUser();
        } catch (Exception e) {
            log.error("删除回传对照关系时，获取用户信息异常！！！");
        }

        // 删除对照关系
        DeleteItemRevertMappingParam param = new DeleteItemRevertMappingParam();
        BeanUtils.copyProperties(deleteItemRevertMappingRequest, param);
        tbOrgItemRevertMappingRepository.deleteItemRevertMapping(param, loginUser);

        // 查询对照关系
        List<TbOrgItemRevertMapping> tbOrgItemRevertMappings = tbOrgItemRevertMappingRepository.queryByCustomerItemCode(customerCode, customerItemCode);
        if (CollectionUtils.isNotEmpty(tbOrgItemRevertMappings)) {
            return Response.success();
        }

        // 更新客商回传项目的对照标识
        tbOrgItemRevertRepository.setCustomerItemRevertIsMapping(customerCode, customerItemCode, 0, mappingType, orgCode, loginUser == null ? null : loginUser.getUserName());

        return Response.success();
    }

    /**
     * 查询回传项目对照关系--中间库结果推送专用
     *
     * @param queryItemRevertMappingForMiddleRequest
     * @return
     */
    @Override
    public Response<List<QueryItemRevertMappingForMiddleDto>> queryItemRevertMappingForMiddle(QueryItemRevertMappingForMiddleRequest queryItemRevertMappingForMiddleRequest) {

        List<TbOrgItemRevertMapping> tbOrgItemRevertMappings = tbOrgItemRevertMappingRepository.queryItemRevertMappingForMiddle(queryItemRevertMappingForMiddleRequest);
        if (CollectionUtils.isEmpty(tbOrgItemRevertMappings)) {
            Response.success(Collections.EMPTY_LIST);
        }

        Map<String, List<TbOrgItemRevertMapping>> customerCodeGroup = tbOrgItemRevertMappings.stream().collect(Collectors.groupingBy(TbOrgItemRevertMapping::getCustomerCode));
        List<QueryItemRevertMappingForMiddleDto> resultList = new ArrayList<>();

        for (Map.Entry<String, List<TbOrgItemRevertMapping>> stringListEntry : customerCodeGroup.entrySet()) {
            QueryItemRevertMappingForMiddleDto temp = new QueryItemRevertMappingForMiddleDto();
            temp.setCustomerCode(stringListEntry.getKey());
            List<QueryItemRevertMappingForMiddleDto.QueryItemRevertMappingForMiddle> collect = stringListEntry.getValue().stream().map(e -> tbOrgItemRevertMappingConverter.converterQueryItemRevertMappingForMiddle(e)).collect(Collectors.toList());
            temp.setQueryItemRevertMappingForMiddleList(collect);
            resultList.add(temp);
        }

        return Response.success(resultList);
    }


    //==================================================================================================================

    private List<TbOrgItemRevertMapping> getSaveItemRevertMapping(SaveItemRevertMappingRequest saveItemRevertMappingRequest) {

        Date now = new Date();
        SsoUser loginUser = LoginUserInfoUtil.getLoginUser();

        List<TbOrgItemRevertMapping> mappingList = new ArrayList<>();

        for (SaveItemRevertMappingRequest.OrgItemRevertRequest orgItemRevertRequest : saveItemRevertMappingRequest.getOrgItemRevertRequests()) {
            TbOrgItemRevertMapping temp = new TbOrgItemRevertMapping();
            BeanUtils.copyProperties(saveItemRevertMappingRequest, temp);
            temp.setMappingId(IdEnum.TB_ORG_ITEM_REVERT_MAPPING.getValue() + IdWorker.getId());
            temp.setOrgItemCode(orgItemRevertRequest.getOrgItemCode());
            temp.setOrgItemName(orgItemRevertRequest.getOrgItemName());
            temp.setCreateTime(now);
            temp.setCreateBy(loginUser == null ? saveItemRevertMappingRequest.getOptUserName() : loginUser.getLoginName());
            temp.setUpdateBy(loginUser == null ? saveItemRevertMappingRequest.getOptUserName() : loginUser.getLoginName());
            temp.setUpdateTime(now);
            temp.setDeleteFlag(DeleteFlagEnum.NO_DELETE.getCode());
            mappingList.add(temp);
        }

        return mappingList;
    }


}


