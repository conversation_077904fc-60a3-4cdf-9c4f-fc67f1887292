package com.labway.business.center.compare.persistence.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.util.Date;

/**
 * 机构关联条码信息表 用来记录签收后拆分条码的关联情况
 *
 * <AUTHOR>
 * @since 2024-07-12 10:21:15
 */
@SuppressWarnings("serial")
public class TbOrgBarcodeRelation extends Model<TbOrgBarcodeRelation> {
    //关系表主键
    @TableId(type = IdType.INPUT)
    private String relationId;
    /**
     * 机构（业务单元）编码-关联业务单元表的org_code
     */
    private String orgCode;
    /**机构（业务单元）名称*/
    private String orgName;
    /**送检机构编码*/
    private String hspOrgCode;
    /**送检机构名称*/
    private String hspOrgName;
    /**条码*/
    private String barcode;
    /**签收条码*/
    private String signBarcode;
    /**拆分条码(关联条码)*/
    private String splitBarcode;
    //创建时间
    private Date createTime;
    //跟新时间
    private Date updateTime;
    //删除标识 0删除 1未删除
    @TableLogic(value = "0",delval = "1")
    private Integer deleteFlag;


    public String getRelationId() {
        return relationId;
    }

    public void setRelationId(String relationId) {
        this.relationId = relationId;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }


    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getDeleteFlag() {
        return deleteFlag;
    }

    public void setDeleteFlag(Integer deleteFlag) {
        this.deleteFlag = deleteFlag;
    }

    public String getHspOrgCode() {
        return hspOrgCode;
    }

    public void setHspOrgCode(String hspOrgCode) {
        this.hspOrgCode = hspOrgCode;
    }

    public String getHspOrgName() {
        return hspOrgName;
    }

    public void setHspOrgName(String hspOrgName) {
        this.hspOrgName = hspOrgName;
    }

    public String getBarcode() {
        return barcode;
    }

    public void setBarcode(String barcode) {
        this.barcode = barcode;
    }

    public String getSignBarcode() {
        return signBarcode;
    }

    public void setSignBarcode(String signBarcode) {
        this.signBarcode = signBarcode;
    }

    public String getSplitBarcode() {
        return splitBarcode;
    }

    public void setSplitBarcode(String splitBarcode) {
        this.splitBarcode = splitBarcode;
    }
}

