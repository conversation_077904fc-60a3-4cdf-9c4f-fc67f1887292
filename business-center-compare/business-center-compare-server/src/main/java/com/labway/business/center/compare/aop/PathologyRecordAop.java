package com.labway.business.center.compare.aop;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.labway.business.center.compare.annotation.PathologyRecordAnnotation;
import com.labway.business.center.compare.persistence.entity.TbPathologyRecord;
import com.labway.business.center.compare.repository.center.TbPathologyRecordRepository;
import com.swak.frame.enums.BasicErrCode;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Date;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Slf4j
@Aspect
@Component
public class PathologyRecordAop {
    @Resource
    private TbPathologyRecordRepository tbPathologyRecordRepository;


    @Pointcut("@annotation(com.labway.business.center.compare.annotation.PathologyRecordAnnotation)")
    public void annotationPointCut() {
    }


    // 执行切点前
    @Around("annotationPointCut()")
    public Object cutAround(ProceedingJoinPoint point) throws Throwable {
        // 类型装换
        MethodSignature methodSignature = (MethodSignature) point.getSignature();

        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = requestAttributes.getRequest();
        String url = request.getRequestURL().toString();

        // 方法对象
        Method method = methodSignature.getMethod();
        log.info("执行方法{}。。。", method.getName());

        // 方法参数
        Object[] args = point.getArgs();

        // 操作记录注解
        PathologyRecordAnnotation optAnnotation = method.getAnnotation(PathologyRecordAnnotation.class);

        // 当期登录用户
//        UserLoginVo userinfo = getUserinfo(method, args);

        // 封装操作记录信息
        TbPathologyRecord record = getTbSysOptRecord(method, args, url);

        // 执行结果
        Object proceed = point.proceed(args);

        // 存储操作记录信息
        JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(proceed));
        record.setResponseResult(JSONObject.toJSONString(proceed));
        // 执行结果
        String code = jsonObject.getString("code");
        if (StringUtils.hasText(code) && BasicErrCode.SUCCESS.getCode().equals(code)) {
            record.setIsSuccess(1);
        } else {
            record.setIsSuccess(0);
        }

        tbPathologyRecordRepository.saveSync(record);

        return proceed;
    }

    private TbPathologyRecord getTbSysOptRecord(Method method, Object[] args,String url) {
        TbPathologyRecord record = new TbPathologyRecord();
        record.setRecordId(IdWorker.getIdStr());
        record.setRequestUrl(url);
        record.setRequestMethod(method.getName());
        Optional<Object> first = Arrays.stream(args).filter(e -> e instanceof MultipartFile).findFirst();
        if (!first.isPresent()){
            record.setRequestParam(JSONObject.toJSONString(args));
        }
        record.setOptTime(new Date());
        record.setOptUserId("朗迦调用");
        record.setOptUserName("朗迦调用");
        return record;
    }



}
