package com.labway.business.center.compare.persistence.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.labway.business.center.compare.dto.OrgApplyResultSamplesDTO;
import com.labway.business.center.compare.persistence.entity.TbOrgApplySampleMainItem;
import com.labway.business.center.compare.request.QueryLimsOutSourceSampleInfoRequest;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 外送生清单-送检项目信息表(TbOrgApplySampleMainItemDTO)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-04-24 13:20:25
 */
@DS("account")
public interface TbOrgApplySampleMainItemMapper extends BaseMapper<TbOrgApplySampleMainItem> {

/**
* 批量新增数据（MyBatis原生foreach方法）
*
* @param entities List<TbOrgApplySampleMainItemDTO> 实例对象列表
* @return 影响行数
*/
int insertBatch(@Param("entities") List<TbOrgApplySampleMainItem> entities);

/**
* 批量新增或按主键更新数据（MyBatis原生foreach方法）
*
* @param entities List<TbOrgApplySampleMainItemDTO> 实例对象列表
* @return 影响行数
* @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
*/
int insertOrUpdateBatch(@Param("entities") List<TbOrgApplySampleMainItem> entities);

}

