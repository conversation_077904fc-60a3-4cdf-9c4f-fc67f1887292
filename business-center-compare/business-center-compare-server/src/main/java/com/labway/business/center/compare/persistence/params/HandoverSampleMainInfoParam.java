package com.labway.business.center.compare.persistence.params;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 2023/06/16 16:09
 * @description
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class HandoverSampleMainInfoParam implements Serializable {

    // 送检机构编码
    private String hspOrgCode;

    // 申请单编码
    private String formCode;

    // 条码号
    private String barCode;

}