<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.business.center.compare.persistence.mapper.TbOrgApplySampleMainAdditionalMapper">

    <resultMap type="com.labway.business.center.compare.persistence.entity.TbOrgApplySampleMainAdditional" id="TbOrgApplySampleMainAdditionalMap">
        <result property="mainId" column="main_id" jdbcType="VARCHAR"/>
        <result property="barcode" column="barcode" jdbcType="VARCHAR"/>
        <result property="limsBarcode" column="lims_barcode" jdbcType="VARCHAR"/>
        <result property="formCode" column="form_code" jdbcType="VARCHAR"/>
        <result property="applyType" column="apply_type" jdbcType="VARCHAR"/>
        <result property="sampleType" column="sample_type" jdbcType="VARCHAR"/>
        <result property="sampleProperty" column="sample_property" jdbcType="VARCHAR"/>
        <result property="tubeType" column="tube_type" jdbcType="VARCHAR"/>
        <result property="dept" column="dept" jdbcType="VARCHAR"/>
        <result property="applyDate" column="apply_date" jdbcType="TIMESTAMP"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="picUrls" column="pic_urls" jdbcType="VARCHAR"/>
        <result property="itemInfo" column="item_info" jdbcType="VARCHAR"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="sampleSource" column="sample_source" jdbcType="VARCHAR"/>
        <result property="lisCustomerCode" column="lis_customer_code" jdbcType="VARCHAR"/>
        <result property="lisCustomerName" column="lis_customer_name" jdbcType="VARCHAR"/>
        <result property="hspOrgCode" column="hsp_org_code" jdbcType="VARCHAR"/>
        <result property="hspOrgName" column="hsp_org_name" jdbcType="VARCHAR"/>
        <result property="orgCode" column="org_code" jdbcType="VARCHAR"/>
        <result property="orgName" column="org_name" jdbcType="VARCHAR"/>
        <result property="confirmUserCode" column="confirm_user_code" jdbcType="VARCHAR"/>
        <result property="confirmUserName" column="confirm_user_name" jdbcType="VARCHAR"/>
        <result property="confirmTime" column="confirm_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="deleteFlag" column="delete_flag" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="mainId" useGeneratedKeys="true">
        insert into business-account.tb_org_apply_sample_main_additional(barcode, lims_barcode, form_code, apply_type, sample_type, sample_property, tube_type, dept, apply_date, status, pic_urls, item_info, remark, sample_source, lis_customer_code, lis_customer_name, hsp_org_code, hsp_org_name, org_code, org_name, confirm_user_code, confirm_user_name, confirm_time, update_by, create_time, create_by, update_time, delete_flag)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.barcode}, #{entity.limsBarcode}, #{entity.formCode}, #{entity.applyType}, #{entity.sampleType}, #{entity.sampleProperty}, #{entity.tubeType}, #{entity.dept}, #{entity.applyDate}, #{entity.status}, #{entity.picUrls}, #{entity.itemInfo}, #{entity.remark}, #{entity.sampleSource}, #{entity.lisCustomerCode}, #{entity.lisCustomerName}, #{entity.hspOrgCode}, #{entity.hspOrgName}, #{entity.orgCode}, #{entity.orgName}, #{entity.confirmUserCode}, #{entity.confirmUserName}, #{entity.confirmTime}, #{entity.updateBy}, #{entity.createTime}, #{entity.createBy}, #{entity.updateTime}, #{entity.deleteFlag})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="mainId" useGeneratedKeys="true">
        insert into business-account.tb_org_apply_sample_main_additional(barcode, lims_barcode, form_code, apply_type, sample_type, sample_property, tube_type, dept, apply_date, status, pic_urls, item_info, remark, sample_source, lis_customer_code, lis_customer_name, hsp_org_code, hsp_org_name, org_code, org_name, confirm_user_code, confirm_user_name, confirm_time, update_by, create_time, create_by, update_time, delete_flag)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.barcode}, #{entity.limsBarcode}, #{entity.formCode}, #{entity.applyType}, #{entity.sampleType}, #{entity.sampleProperty}, #{entity.tubeType}, #{entity.dept}, #{entity.applyDate}, #{entity.status}, #{entity.picUrls}, #{entity.itemInfo}, #{entity.remark}, #{entity.sampleSource}, #{entity.lisCustomerCode}, #{entity.lisCustomerName}, #{entity.hspOrgCode}, #{entity.hspOrgName}, #{entity.orgCode}, #{entity.orgName}, #{entity.confirmUserCode}, #{entity.confirmUserName}, #{entity.confirmTime}, #{entity.updateBy}, #{entity.createTime}, #{entity.createBy}, #{entity.updateTime}, #{entity.deleteFlag})
        </foreach>
        on duplicate key update
         barcode = values(barcode) , lims_barcode = values(lims_barcode) , form_code = values(form_code) , apply_type = values(apply_type) , sample_type = values(sample_type) , sample_property = values(sample_property) , tube_type = values(tube_type) , dept = values(dept) , apply_date = values(apply_date) , status = values(status) , pic_urls = values(pic_urls) , item_info = values(item_info) , remark = values(remark) , sample_source = values(sample_source) , lis_customer_code = values(lis_customer_code) , lis_customer_name = values(lis_customer_name) , hsp_org_code = values(hsp_org_code) , hsp_org_name = values(hsp_org_name) , org_code = values(org_code) , org_name = values(org_name) , confirm_user_code = values(confirm_user_code) , confirm_user_name = values(confirm_user_name) , confirm_time = values(confirm_time) , update_by = values(update_by) , create_time = values(create_time) , create_by = values(create_by) , update_time = values(update_time) , delete_flag = values(delete_flag)     </insert>

</mapper>

