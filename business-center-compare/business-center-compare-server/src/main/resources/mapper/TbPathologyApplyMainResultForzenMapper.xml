<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.business.center.compare.persistence.mapper.TbPathologyApplyMainResultForzenMapper">

    <resultMap type="com.labway.business.center.compare.persistence.entity.TbPathologyApplyMainResultForzen" id="TbPathologyApplyMainResultForzenMap">
        <result property="forzenId" column="forzen_id" jdbcType="VARCHAR"/>
        <result property="resultId" column="result_id" jdbcType="VARCHAR"/>
        <result property="fBdBgxh" column="f_bd_bgxh" jdbcType="INTEGER"/>
        <result property="fBdBgys" column="f_bd_bgys" jdbcType="VARCHAR"/>
        <result property="fBdShys" column="f_bd_shys" jdbcType="VARCHAR"/>
        <result property="fBdBgrq" column="f_bd_bgrq" jdbcType="VARCHAR"/>
        <result property="fBdBgzt" column="f_bd_bgzt" jdbcType="VARCHAR"/>
        <result property="fBdzd" column="f_bdzd" jdbcType="VARCHAR"/>
        <result property="fBdZpr" column="f_bd_zpr" jdbcType="VARCHAR"/>
        <result property="fBdSdrq" column="f_bd_sdrq" jdbcType="VARCHAR"/>
        <result property="fBdQcys" column="f_bd_qcys" jdbcType="VARCHAR"/>
        <result property="fBdCfyy" column="f_bd_cfyy" jdbcType="VARCHAR"/>
        <result property="fBdZps" column="f_bd_zps" jdbcType="VARCHAR"/>
        <result property="fBdBz" column="f_bd_bz" jdbcType="VARCHAR"/>
        <result property="fBdFzys" column="f_bd_fzys" jdbcType="VARCHAR"/>
        <result property="fBdDyzt" column="f_bd_dyzt" jdbcType="VARCHAR"/>
        <result property="fBdQsbDyzt" column="f_bd_qsb_dyzt" jdbcType="VARCHAR"/>
        <result property="fBdBgwz" column="f_bd_bgwz" jdbcType="VARCHAR"/>
        <result property="fBdBgwzQrsj" column="f_bd_bgwz_qrsj" jdbcType="VARCHAR"/>
        <result property="fBdBgwzQrczy" column="f_bd_bgwz_qrczy" jdbcType="VARCHAR"/>
        <result property="fZpsj" column="f_zpsj" jdbcType="VARCHAR"/>
        <result property="fLtsj" column="f_ltsj" jdbcType="VARCHAR"/>
        <result property="fZpkssj" column="f_zpkssj" jdbcType="VARCHAR"/>
        <result property="fBdFbsj" column="f_bd_fbsj" jdbcType="VARCHAR"/>
        <result property="fBdFbys" column="f_bd_fbys" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="forzenId" useGeneratedKeys="true">
        insert into tb_pathology_apply_main_result_forzen(forzen_id,result_id, f_bd_bgxh, f_bd_bgys, f_bd_shys, f_bd_bgrq, f_bd_bgzt, f_bdzd, f_bd_zpr, f_bd_sdrq, f_bd_qcys, f_bd_cfyy, f_bd_zps, f_bd_bz, f_bd_fzys, f_bd_dyzt, f_bd_qsb_dyzt, f_bd_bgwz, f_bd_bgwz_qrsj, f_bd_bgwz_qrczy, f_zpsj, f_ltsj, f_zpkssj, f_bd_fbsj, f_bd_fbys,delete_flag,create_time)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.forzenId}, #{entity.resultId}, #{entity.fBdBgxh}, #{entity.fBdBgys}, #{entity.fBdShys}, #{entity.fBdBgrq}, #{entity.fBdBgzt}, #{entity.fBdzd}, #{entity.fBdZpr}, #{entity.fBdSdrq}, #{entity.fBdQcys}, #{entity.fBdCfyy}, #{entity.fBdZps}, #{entity.fBdBz}, #{entity.fBdFzys}, #{entity.fBdDyzt}, #{entity.fBdQsbDyzt}, #{entity.fBdBgwz}, #{entity.fBdBgwzQrsj}, #{entity.fBdBgwzQrczy}, #{entity.fZpsj}, #{entity.fLtsj}, #{entity.fZpkssj}, #{entity.fBdFbsj}, #{entity.fBdFbys}, #{entity.deleteFlag}, #{entity.createTime})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="forzenId" useGeneratedKeys="true">
        insert into business-account.tb_pathology_apply_main_result_forzen(result_id, f_bd_bgxh, f_bd_bgys, f_bd_shys, f_bd_bgrq, f_bd_bgzt, f_bdzd, f_bd_zpr, f_bd_sdrq, f_bd_qcys, f_bd_cfyy, f_bd_zps, f_bd_bz, f_bd_fzys, f_bd_dyzt, f_bd_qsb_dyzt, f_bd_bgwz, f_bd_bgwz_qrsj, f_bd_bgwz_qrczy, f_zpsj, f_ltsj, f_zpkssj, f_bd_fbsj, f_bd_fbys)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.resultId}, #{entity.fBdBgxh}, #{entity.fBdBgys}, #{entity.fBdShys}, #{entity.fBdBgrq}, #{entity.fBdBgzt}, #{entity.fBdzd}, #{entity.fBdZpr}, #{entity.fBdSdrq}, #{entity.fBdQcys}, #{entity.fBdCfyy}, #{entity.fBdZps}, #{entity.fBdBz}, #{entity.fBdFzys}, #{entity.fBdDyzt}, #{entity.fBdQsbDyzt}, #{entity.fBdBgwz}, #{entity.fBdBgwzQrsj}, #{entity.fBdBgwzQrczy}, #{entity.fZpsj}, #{entity.fLtsj}, #{entity.fZpkssj}, #{entity.fBdFbsj}, #{entity.fBdFbys})
        </foreach>
        on duplicate key update
         result_id = values(result_id) , f_bd_bgxh = values(f_bd_bgxh) , f_bd_bgys = values(f_bd_bgys) , f_bd_shys = values(f_bd_shys) , f_bd_bgrq = values(f_bd_bgrq) , f_bd_bgzt = values(f_bd_bgzt) , f_bdzd = values(f_bdzd) , f_bd_zpr = values(f_bd_zpr) , f_bd_sdrq = values(f_bd_sdrq) , f_bd_qcys = values(f_bd_qcys) , f_bd_cfyy = values(f_bd_cfyy) , f_bd_zps = values(f_bd_zps) , f_bd_bz = values(f_bd_bz) , f_bd_fzys = values(f_bd_fzys) , f_bd_dyzt = values(f_bd_dyzt) , f_bd_qsb_dyzt = values(f_bd_qsb_dyzt) , f_bd_bgwz = values(f_bd_bgwz) , f_bd_bgwz_qrsj = values(f_bd_bgwz_qrsj) , f_bd_bgwz_qrczy = values(f_bd_bgwz_qrczy) , f_zpsj = values(f_zpsj) , f_ltsj = values(f_ltsj) , f_zpkssj = values(f_zpkssj) , f_bd_fbsj = values(f_bd_fbsj) , f_bd_fbys = values(f_bd_fbys)     </insert>

</mapper>

