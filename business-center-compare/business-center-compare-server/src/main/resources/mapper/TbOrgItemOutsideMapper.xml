<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.business.center.compare.persistence.mapper.TbOrgItemOutsideMapper">

    <resultMap type="com.labway.business.center.compare.persistence.entity.TbOrgItemOutside" id="TbOrgItemOutsideMap">
        <result property="outsideId" column="outside_id" jdbcType="VARCHAR"/>
        <result property="itemOutsideCode" column="item_outside_code" jdbcType="VARCHAR"/>
        <result property="itemOutsdieName" column="item_outsdie_name" jdbcType="VARCHAR"/>
        <result property="customerCode" column="customer_code" jdbcType="VARCHAR"/>
        <result property="customerName" column="customer_name" jdbcType="VARCHAR"/>
        <result property="orgCode" column="org_code" jdbcType="VARCHAR"/>
        <result property="orgName" column="org_name" jdbcType="VARCHAR"/>
        <result property="isMapping" column="is_mapping" jdbcType="INTEGER"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="deleteFlag" column="delete_flag" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="table_columns">outside_id, item_outside_code, item_outsdie_name, customer_code, customer_name, org_code, org_name, is_mapping, status, create_by, create_time, update_by, update_time, delete_flag</sql>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="outsideId" useGeneratedKeys="true">
        insert into tb_org_item_outside(outside_id, item_outside_code, item_outsdie_name, customer_code, customer_name, org_code, org_name, is_mapping, status, create_by, create_time, update_by, update_time, delete_flag)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.outsideId}, #{entity.itemOutsideCode}, #{entity.itemOutsdieName}, #{entity.customerCode}, #{entity.customerName}, #{entity.orgCode}, #{entity.orgName},#{entity.isMapping}, #{entity.status}, #{entity.createBy}, #{entity.createTime}, #{entity.updateBy}, #{entity.updateTime}, #{entity.deleteFlag})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="outsideId" useGeneratedKeys="true">
        insert into business-account.tb_org_item_outside(item_outside_code, item_outsdie_name, customer_code, customer_name, org_code, org_name, status, create_by, create_time, update_by, update_time, delete_flag)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.itemOutsideCode}, #{entity.itemOutsdieName}, #{entity.customerCode}, #{entity.customerName}, #{entity.orgCode}, #{entity.orgName}, #{entity.status}, #{entity.createBy}, #{entity.createTime}, #{entity.updateBy}, #{entity.updateTime}, #{entity.deleteFlag})
        </foreach>
        on duplicate key update
         item_outside_code = values(item_outside_code) , item_outsdie_name = values(item_outsdie_name) , customer_code = values(customer_code) , customer_name = values(customer_name) , org_code = values(org_code) , org_name = values(org_name) , status = values(status) , create_by = values(create_by) , create_time = values(create_time) , update_by = values(update_by) , update_time = values(update_time) , delete_flag = values(delete_flag)     </insert>

    <update id="updateStatusBatch" >
        update tb_org_item_outside
        set is_mapping = #{isMapping}
        where (org_code, customer_code, item_outside_code) in (
            <foreach collection="entities" item="entity" separator=",">
                (#{entity.orgCode}, #{entity.customerCode}, #{entity.itemOutsideCode})
            </foreach>
        )
        and delete_flag = 0
    </update>

</mapper>

