<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.business.center.compare.persistence.mapper.TbOrgBarcodeRelationMapper">

    <resultMap type="com.labway.business.center.compare.persistence.entity.TbOrgBarcodeRelation" id="TbOrgBarcodeRelationMap">
        <result property="relationId" column="relation_id" jdbcType="VARCHAR"/>
        <result property="orgCode" column="org_code" jdbcType="VARCHAR"/>
        <result property="orgName" column="org_name" jdbcType="VARCHAR"/>
        <result property="hspOrgCode" column="hsp_org_code" jdbcType="VARCHAR"/>
        <result property="hspOrgName" column="hsp_org_name" jdbcType="VARCHAR"/>
        <result property="barcode" column="barcode" jdbcType="VARCHAR"/>
        <result property="signBarcode" column="sign_barcode" jdbcType="VARCHAR"/>
        <result property="splitBarcode" column="split_barcode" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="deleteFlag" column="delete_flag" jdbcType="INTEGER"/>
    </resultMap>

</mapper>

