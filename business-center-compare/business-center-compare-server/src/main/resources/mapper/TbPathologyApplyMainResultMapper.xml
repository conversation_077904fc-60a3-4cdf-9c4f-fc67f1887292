<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.business.center.compare.persistence.mapper.TbPathologyApplyMainResultMapper">

    <resultMap type="com.labway.business.center.compare.persistence.entity.TbPathologyApplyMainResult" id="TbPathologyApplyMainResultMap">
        <result property="resultId" column="result_id" jdbcType="VARCHAR"/>
        <result property="testOrgName" column="test_org_name" jdbcType="VARCHAR"/>
        <result property="testOrgId" column="test_org_id" jdbcType="VARCHAR"/>
        <result property="appOrgName" column="app_org_name" jdbcType="VARCHAR"/>
        <result property="appOrgId" column="app_org_id" jdbcType="VARCHAR"/>
        <result property="fBlk" column="f_blk" jdbcType="VARCHAR"/>
        <result property="fBlh" column="f_blh" jdbcType="VARCHAR"/>
        <result property="fBrbh" column="f_brbh" jdbcType="VARCHAR"/>
        <result property="fSqxh" column="f_sqxh" jdbcType="VARCHAR"/>
        <result property="fYzid" column="f_yzid" jdbcType="VARCHAR"/>
        <result property="fYzxm" column="f_yzxm" jdbcType="VARCHAR"/>
        <result property="fStudyUid" column="f_study_uid" jdbcType="VARCHAR"/>
        <result property="fXm" column="f_xm" jdbcType="VARCHAR"/>
        <result property="fXb" column="f_xb" jdbcType="VARCHAR"/>
        <result property="fNl" column="f_nl" jdbcType="VARCHAR"/>
        <result property="fAge" column="f_age" jdbcType="INTEGER"/>
        <result property="fHy" column="f_hy" jdbcType="VARCHAR"/>
        <result property="fMz" column="f_mz" jdbcType="VARCHAR"/>
        <result property="fZy" column="f_zy" jdbcType="VARCHAR"/>
        <result property="fSfzh" column="f_sfzh" jdbcType="VARCHAR"/>
        <result property="fLxxx" column="f_lxxx" jdbcType="VARCHAR"/>
        <result property="fBrlb" column="f_brlb" jdbcType="VARCHAR"/>
        <result property="fFb" column="f_fb" jdbcType="VARCHAR"/>
        <result property="fZyh" column="f_zyh" jdbcType="VARCHAR"/>
        <result property="fMzh" column="f_mzh" jdbcType="VARCHAR"/>
        <result property="fBq" column="f_bq" jdbcType="VARCHAR"/>
        <result property="fSjks" column="f_sjks" jdbcType="VARCHAR"/>
        <result property="fCh" column="f_ch" jdbcType="VARCHAR"/>
        <result property="fSjdw" column="f_sjdw" jdbcType="VARCHAR"/>
        <result property="fSjys" column="f_sjys" jdbcType="VARCHAR"/>
        <result property="fSdrq" column="f_sdrq" jdbcType="VARCHAR"/>
        <result property="fJsy" column="f_jsy" jdbcType="VARCHAR"/>
        <result property="fBblx" column="f_bblx" jdbcType="VARCHAR"/>
        <result property="fBbqk" column="f_bbqk" jdbcType="VARCHAR"/>
        <result property="fJsyy" column="f_jsyy" jdbcType="VARCHAR"/>
        <result property="fSf" column="f_sf" jdbcType="INTEGER"/>
        <result property="fBbmc" column="f_bbmc" jdbcType="VARCHAR"/>
        <result property="fLczd" column="f_lczd" jdbcType="VARCHAR"/>
        <result property="fLczl" column="f_lczl" jdbcType="VARCHAR"/>
        <result property="fQcys" column="f_qcys" jdbcType="VARCHAR"/>
        <result property="fQcrq" column="f_qcrq" jdbcType="VARCHAR"/>
        <result property="fJly" column="f_jly" jdbcType="VARCHAR"/>
        <result property="fLkzs" column="f_lkzs" jdbcType="INTEGER"/>
        <result property="fCkzs" column="f_ckzs" jdbcType="INTEGER"/>
        <result property="fFy" column="f_fy" jdbcType="VARCHAR"/>
        <result property="fJxsj" column="f_jxsj" jdbcType="VARCHAR"/>
        <result property="fBlzd" column="f_blzd" jdbcType="VARCHAR"/>
        <result property="fTsjc" column="f_tsjc" jdbcType="VARCHAR"/>
        <result property="fBgys" column="f_bgys" jdbcType="VARCHAR"/>
        <result property="fShys" column="f_shys" jdbcType="VARCHAR"/>
        <result property="fBgrq" column="f_bgrq" jdbcType="VARCHAR"/>
        <result property="fCzyj" column="f_czyj" jdbcType="VARCHAR"/>
        <result property="fXgyj" column="f_xgyj" jdbcType="VARCHAR"/>
        <result property="fZdgjc" column="f_zdgjc" jdbcType="VARCHAR"/>
        <result property="fYyx" column="f_yyx" jdbcType="VARCHAR"/>
        <result property="fWfbgyy" column="f_wfbgyy" jdbcType="VARCHAR"/>
        <result property="fBz" column="f_bz" jdbcType="VARCHAR"/>
        <result property="fBdSffh" column="f_bd_sffh" jdbcType="VARCHAR"/>
        <result property="fBgzt" column="f_bgzt" jdbcType="VARCHAR"/>
        <result property="fSfct" column="f_sfct" jdbcType="VARCHAR"/>
        <result property="fSfdy" column="f_sfdy" jdbcType="VARCHAR"/>
        <result property="fBggs" column="f_bggs" jdbcType="VARCHAR"/>
        <result property="fGdzt" column="f_gdzt" jdbcType="VARCHAR"/>
        <result property="fKnhz" column="f_knhz" jdbcType="INTEGER"/>
        <result property="fZjyj" column="f_zjyj" jdbcType="VARCHAR"/>
        <result property="fWyyj" column="f_wyyj" jdbcType="VARCHAR"/>
        <result property="fSfzt" column="f_sfzt" jdbcType="INTEGER"/>
        <result property="fSfjg" column="f_sfjg" jdbcType="VARCHAR"/>
        <result property="fJbbmCn" column="f_jbbm_cn" jdbcType="VARCHAR"/>
        <result property="fJbbmEng" column="f_jbbm_eng" jdbcType="VARCHAR"/>
        <result property="fJbmc" column="f_jbmc" jdbcType="VARCHAR"/>
        <result property="fYblh" column="f_yblh" jdbcType="VARCHAR"/>
        <result property="fSjcl" column="f_sjcl" jdbcType="VARCHAR"/>
        <result property="fYblzd" column="f_yblzd" jdbcType="VARCHAR"/>
        <result property="fBgfsfs" column="f_bgfsfs" jdbcType="VARCHAR"/>
        <result property="fScys" column="f_scys" jdbcType="VARCHAR"/>
        <result property="fSffh" column="f_sffh" jdbcType="VARCHAR"/>
        <result property="fSpare1" column="f_spare1" jdbcType="VARCHAR"/>
        <result property="fSpare2" column="f_spare2" jdbcType="VARCHAR"/>
        <result property="fSpare3" column="f_spare3" jdbcType="VARCHAR"/>
        <result property="fSpare4" column="f_spare4" jdbcType="VARCHAR"/>
        <result property="fSpare5" column="f_spare5" jdbcType="VARCHAR"/>
        <result property="fSpare6" column="f_spare6" jdbcType="VARCHAR"/>
        <result property="fSpare7" column="f_spare7" jdbcType="VARCHAR"/>
        <result property="fSpare8" column="f_spare8" jdbcType="VARCHAR"/>
        <result property="fSpare9" column="f_spare9" jdbcType="VARCHAR"/>
        <result property="fSpare10" column="f_spare10" jdbcType="VARCHAR"/>
        <result property="fBy1" column="f_by1" jdbcType="VARCHAR"/>
        <result property="fBy2" column="f_by2" jdbcType="VARCHAR"/>
        <result property="fTxml" column="f_txml" jdbcType="VARCHAR"/>
        <result property="fZpzt" column="f_zpzt" jdbcType="VARCHAR"/>
        <result property="fMcyj" column="f_mcyj" jdbcType="VARCHAR"/>
        <result property="fSfjj" column="f_sfjj" jdbcType="VARCHAR"/>
        <result property="fTbsid" column="f_tbsid" jdbcType="INTEGER"/>
        <result property="fTbsmc" column="f_tbsmc" jdbcType="VARCHAR"/>
        <result property="fQsbDyzt" column="f_qsb_dyzt" jdbcType="VARCHAR"/>
        <result property="fBgwz" column="f_bgwz" jdbcType="VARCHAR"/>
        <result property="fBgwzQrsj" column="f_bgwz_qrsj" jdbcType="VARCHAR"/>
        <result property="fBgwzQrczy" column="f_bgwz_qrczy" jdbcType="VARCHAR"/>
        <result property="fBbwz" column="f_bbwz" jdbcType="VARCHAR"/>
        <result property="fLkwz" column="f_lkwz" jdbcType="VARCHAR"/>
        <result property="fQpwz" column="f_qpwz" jdbcType="VARCHAR"/>
        <result property="fGdczy" column="f_gdczy" jdbcType="VARCHAR"/>
        <result property="fGdsj" column="f_gdsj" jdbcType="VARCHAR"/>
        <result property="fGdbz" column="f_gdbz" jdbcType="VARCHAR"/>
        <result property="fBglry" column="f_bglry" jdbcType="VARCHAR"/>
        <result property="fFzys" column="f_fzys" jdbcType="VARCHAR"/>
        <result property="fYl1" column="f_yl1" jdbcType="VARCHAR"/>
        <result property="fYl2" column="f_yl2" jdbcType="VARCHAR"/>
        <result property="fYl3" column="f_yl3" jdbcType="VARCHAR"/>
        <result property="fYl4" column="f_yl4" jdbcType="VARCHAR"/>
        <result property="fYl5" column="f_yl5" jdbcType="VARCHAR"/>
        <result property="fYl6" column="f_yl6" jdbcType="VARCHAR"/>
        <result property="fYl7" column="f_yl7" jdbcType="VARCHAR"/>
        <result property="fYl8" column="f_yl8" jdbcType="VARCHAR"/>
        <result property="fYl9" column="f_yl9" jdbcType="VARCHAR"/>
        <result property="fYl10" column="f_yl10" jdbcType="VARCHAR"/>
        <result property="fIcd10Bm1" column="f_icd10_bm1" jdbcType="VARCHAR"/>
        <result property="fIcd10Mc1" column="f_icd10_mc1" jdbcType="VARCHAR"/>
        <result property="fBl1" column="f_bl1" jdbcType="VARCHAR"/>
        <result property="fBl2" column="f_bl2" jdbcType="VARCHAR"/>
        <result property="fBl3" column="f_bl3" jdbcType="VARCHAR"/>
        <result property="fBl4" column="f_bl4" jdbcType="VARCHAR"/>
        <result property="fBl5" column="f_bl5" jdbcType="VARCHAR"/>
        <result property="fBl6" column="f_bl6" jdbcType="VARCHAR"/>
        <result property="fBl7" column="f_bl7" jdbcType="VARCHAR"/>
        <result property="fBl8" column="f_bl8" jdbcType="VARCHAR"/>
        <result property="fBl9" column="f_bl9" jdbcType="VARCHAR"/>
        <result property="fBl10" column="f_bl10" jdbcType="VARCHAR"/>
        <result property="fIcd10Bm2" column="f_icd10_bm2" jdbcType="VARCHAR"/>
        <result property="fIcd10Mc2" column="f_icd10_mc2" jdbcType="VARCHAR"/>
        <result property="fFbsj" column="f_fbsj" jdbcType="VARCHAR"/>
        <result property="fFbys" column="f_fbys" jdbcType="VARCHAR"/>
        <result property="fRysj" column="f_rysj" jdbcType="VARCHAR"/>
        <result property="createDate" column="create_date" jdbcType="TIMESTAMP"/>
        <result property="downloadStatus" column="download_status" jdbcType="VARCHAR"/>
        <result property="ghdownloadStatus" column="ghdownload_status" jdbcType="VARCHAR"/>
        <result property="uploadFlag" column="upload_flag" jdbcType="VARCHAR"/>
        <result property="uploadDate" column="upload_date" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="resultId" useGeneratedKeys="true">
        insert into tb_pathology_apply_main_result(result_id, test_org_name, test_org_id, app_org_name, app_org_id, f_blk, f_blh, f_brbh, f_sqxh, f_yzid, f_yzxm, f_study_uid, f_xm, f_xb, f_nl, f_age, f_hy, f_mz, f_zy, f_sfzh, f_lxxx, f_brlb, f_fb, f_zyh, f_mzh, f_bq, f_sjks, f_ch, f_sjdw, f_sjys, f_sdrq, f_jsy, f_bblx, f_bbqk, f_jsyy, f_sf, f_bbmc, f_lczd, f_lczl, f_qcys, f_qcrq, f_jly, f_lkzs, f_ckzs, f_fy, f_jxsj, f_blzd, f_tsjc, f_bgys, f_shys, f_bgrq, f_czyj, f_xgyj, f_zdgjc, f_yyx, f_wfbgyy, f_bz, f_bd_sffh, f_bgzt, f_sfct, f_sfdy, f_bggs, f_gdzt, f_knhz, f_zjyj, f_wyyj, f_sfzt, f_sfjg, f_jbbm_cn, f_jbbm_eng, f_jbmc, f_yblh, f_sjcl, f_yblzd, f_bgfsfs, f_scys, f_sffh, f_spare1, f_spare2, f_spare3, f_spare4, f_spare5, f_spare6, f_spare7, f_spare8, f_spare9, f_spare10, f_by1, f_by2, f_txml, f_zpzt, f_mcyj, f_sfjj, f_tbsid, f_tbsmc, f_qsb_dyzt, f_bgwz, f_bgwz_qrsj, f_bgwz_qrczy, f_bbwz, f_lkwz, f_qpwz, f_gdczy, f_gdsj, f_gdbz, f_bglry, f_fzys, f_yl1, f_yl2, f_yl3, f_yl4, f_yl5, f_yl6, f_yl7, f_yl8, f_yl9, f_yl10, f_icd10_bm1, f_icd10_mc1, f_bl1, f_bl2, f_bl3, f_bl4, f_bl5, f_bl6, f_bl7, f_bl8, f_bl9, f_bl10, f_icd10_bm2, f_icd10_mc2, f_fbsj, f_fbys, f_rysj, create_date, download_status, ghdownload_status,upload_flag,upload_date,delete_flag,result_unique_code)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.resultId}, #{entity.testOrgName}, #{entity.testOrgId}, #{entity.appOrgName}, #{entity.appOrgId}, #{entity.fBlk}, #{entity.fBlh}, #{entity.fBrbh}, #{entity.fSqxh}, #{entity.fYzid}, #{entity.fYzxm}, #{entity.fStudyUid}, #{entity.fXm}, #{entity.fXb}, #{entity.fNl}, #{entity.fAge}, #{entity.fHy}, #{entity.fMz}, #{entity.fZy}, #{entity.fSfzh}, #{entity.fLxxx}, #{entity.fBrlb}, #{entity.fFb}, #{entity.fZyh}, #{entity.fMzh}, #{entity.fBq}, #{entity.fSjks}, #{entity.fCh}, #{entity.fSjdw}, #{entity.fSjys}, #{entity.fSdrq}, #{entity.fJsy}, #{entity.fBblx}, #{entity.fBbqk}, #{entity.fJsyy}, #{entity.fSf}, #{entity.fBbmc}, #{entity.fLczd}, #{entity.fLczl}, #{entity.fQcys}, #{entity.fQcrq}, #{entity.fJly}, #{entity.fLkzs}, #{entity.fCkzs}, #{entity.fFy}, #{entity.fJxsj}, #{entity.fBlzd}, #{entity.fTsjc}, #{entity.fBgys}, #{entity.fShys}, #{entity.fBgrq}, #{entity.fCzyj}, #{entity.fXgyj}, #{entity.fZdgjc}, #{entity.fYyx}, #{entity.fWfbgyy}, #{entity.fBz}, #{entity.fBdSffh}, #{entity.fBgzt}, #{entity.fSfct}, #{entity.fSfdy}, #{entity.fBggs}, #{entity.fGdzt}, #{entity.fKnhz}, #{entity.fZjyj}, #{entity.fWyyj}, #{entity.fSfzt}, #{entity.fSfjg}, #{entity.fJbbmCn}, #{entity.fJbbmEng}, #{entity.fJbmc}, #{entity.fYblh}, #{entity.fSjcl}, #{entity.fYblzd}, #{entity.fBgfsfs}, #{entity.fScys}, #{entity.fSffh}, #{entity.fSpare1}, #{entity.fSpare2}, #{entity.fSpare3}, #{entity.fSpare4}, #{entity.fSpare5}, #{entity.fSpare6}, #{entity.fSpare7}, #{entity.fSpare8}, #{entity.fSpare9}, #{entity.fSpare10}, #{entity.fBy1}, #{entity.fBy2}, #{entity.fTxml}, #{entity.fZpzt}, #{entity.fMcyj}, #{entity.fSfjj}, #{entity.fTbsid}, #{entity.fTbsmc}, #{entity.fQsbDyzt}, #{entity.fBgwz}, #{entity.fBgwzQrsj}, #{entity.fBgwzQrczy}, #{entity.fBbwz}, #{entity.fLkwz}, #{entity.fQpwz}, #{entity.fGdczy}, #{entity.fGdsj}, #{entity.fGdbz}, #{entity.fBglry}, #{entity.fFzys}, #{entity.fYl1}, #{entity.fYl2}, #{entity.fYl3}, #{entity.fYl4}, #{entity.fYl5}, #{entity.fYl6}, #{entity.fYl7}, #{entity.fYl8}, #{entity.fYl9}, #{entity.fYl10}, #{entity.fIcd10Bm1}, #{entity.fIcd10Mc1}, #{entity.fBl1}, #{entity.fBl2}, #{entity.fBl3}, #{entity.fBl4}, #{entity.fBl5}, #{entity.fBl6}, #{entity.fBl7}, #{entity.fBl8}, #{entity.fBl9}, #{entity.fBl10}, #{entity.fIcd10Bm2}, #{entity.fIcd10Mc2}, #{entity.fFbsj}, #{entity.fFbys}, #{entity.fRysj}, #{entity.createDate}, #{entity.downloadStatus}, #{entity.ghdownloadStatus},#{entity.uploadFlag},#{entity.uploadDate},#{entity.deleteFlag},#{entity.resultUniqueCode})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="resultId" useGeneratedKeys="true">
        insert into business-account.tb_pathology_apply_main_result(test_org_name, test_org_id, app_org_name, app_org_id, f_blk, f_blh, f_brbh, f_sqxh, f_yzid, f_yzxm, f_study_uid, f_xm, f_xb, f_nl, f_age, f_hy, f_mz, f_zy, f_sfzh, f_lxxx, f_brlb, f_fb, f_zyh, f_mzh, f_bq, f_sjks, f_ch, f_sjdw, f_sjys, f_sdrq, f_jsy, f_bblx, f_bbqk, f_jsyy, f_sf, f_bbmc, f_lczd, f_lczl, f_qcys, f_qcrq, f_jly, f_lkzs, f_ckzs, f_fy, f_jxsj, f_blzd, f_tsjc, f_bgys, f_shys, f_bgrq, f_czyj, f_xgyj, f_zdgjc, f_yyx, f_wfbgyy, f_bz, f_bd_sffh, f_bgzt, f_sfct, f_sfdy, f_bggs, f_gdzt, f_knhz, f_zjyj, f_wyyj, f_sfzt, f_sfjg, f_jbbm_cn, f_jbbm_eng, f_jbmc, f_yblh, f_sjcl, f_yblzd, f_bgfsfs, f_scys, f_sffh, f_spare1, f_spare2, f_spare3, f_spare4, f_spare5, f_spare6, f_spare7, f_spare8, f_spare9, f_spare10, f_by1, f_by2, f_txml, f_zpzt, f_mcyj, f_sfjj, f_tbsid, f_tbsmc, f_qsb_dyzt, f_bgwz, f_bgwz_qrsj, f_bgwz_qrczy, f_bbwz, f_lkwz, f_qpwz, f_gdczy, f_gdsj, f_gdbz, f_bglry, f_fzys, f_yl1, f_yl2, f_yl3, f_yl4, f_yl5, f_yl6, f_yl7, f_yl8, f_yl9, f_yl10, f_icd10_bm1, f_icd10_mc1, f_bl1, f_bl2, f_bl3, f_bl4, f_bl5, f_bl6, f_bl7, f_bl8, f_bl9, f_bl10, f_icd10_bm2, f_icd10_mc2, f_fbsj, f_fbys, f_rysj, create_date, download_status, ghdownload_status)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.testOrgName}, #{entity.testOrgId}, #{entity.appOrgName}, #{entity.appOrgId}, #{entity.fBlk}, #{entity.fBlh}, #{entity.fBrbh}, #{entity.fSqxh}, #{entity.fYzid}, #{entity.fYzxm}, #{entity.fStudyUid}, #{entity.fXm}, #{entity.fXb}, #{entity.fNl}, #{entity.fAge}, #{entity.fHy}, #{entity.fMz}, #{entity.fZy}, #{entity.fSfzh}, #{entity.fLxxx}, #{entity.fBrlb}, #{entity.fFb}, #{entity.fZyh}, #{entity.fMzh}, #{entity.fBq}, #{entity.fSjks}, #{entity.fCh}, #{entity.fSjdw}, #{entity.fSjys}, #{entity.fSdrq}, #{entity.fJsy}, #{entity.fBblx}, #{entity.fBbqk}, #{entity.fJsyy}, #{entity.fSf}, #{entity.fBbmc}, #{entity.fLczd}, #{entity.fLczl}, #{entity.fQcys}, #{entity.fQcrq}, #{entity.fJly}, #{entity.fLkzs}, #{entity.fCkzs}, #{entity.fFy}, #{entity.fJxsj}, #{entity.fBlzd}, #{entity.fTsjc}, #{entity.fBgys}, #{entity.fShys}, #{entity.fBgrq}, #{entity.fCzyj}, #{entity.fXgyj}, #{entity.fZdgjc}, #{entity.fYyx}, #{entity.fWfbgyy}, #{entity.fBz}, #{entity.fBdSffh}, #{entity.fBgzt}, #{entity.fSfct}, #{entity.fSfdy}, #{entity.fBggs}, #{entity.fGdzt}, #{entity.fKnhz}, #{entity.fZjyj}, #{entity.fWyyj}, #{entity.fSfzt}, #{entity.fSfjg}, #{entity.fJbbmCn}, #{entity.fJbbmEng}, #{entity.fJbmc}, #{entity.fYblh}, #{entity.fSjcl}, #{entity.fYblzd}, #{entity.fBgfsfs}, #{entity.fScys}, #{entity.fSffh}, #{entity.fSpare1}, #{entity.fSpare2}, #{entity.fSpare3}, #{entity.fSpare4}, #{entity.fSpare5}, #{entity.fSpare6}, #{entity.fSpare7}, #{entity.fSpare8}, #{entity.fSpare9}, #{entity.fSpare10}, #{entity.fBy1}, #{entity.fBy2}, #{entity.fTxml}, #{entity.fZpzt}, #{entity.fMcyj}, #{entity.fSfjj}, #{entity.fTbsid}, #{entity.fTbsmc}, #{entity.fQsbDyzt}, #{entity.fBgwz}, #{entity.fBgwzQrsj}, #{entity.fBgwzQrczy}, #{entity.fBbwz}, #{entity.fLkwz}, #{entity.fQpwz}, #{entity.fGdczy}, #{entity.fGdsj}, #{entity.fGdbz}, #{entity.fBglry}, #{entity.fFzys}, #{entity.fYl1}, #{entity.fYl2}, #{entity.fYl3}, #{entity.fYl4}, #{entity.fYl5}, #{entity.fYl6}, #{entity.fYl7}, #{entity.fYl8}, #{entity.fYl9}, #{entity.fYl10}, #{entity.fIcd10Bm1}, #{entity.fIcd10Mc1}, #{entity.fBl1}, #{entity.fBl2}, #{entity.fBl3}, #{entity.fBl4}, #{entity.fBl5}, #{entity.fBl6}, #{entity.fBl7}, #{entity.fBl8}, #{entity.fBl9}, #{entity.fBl10}, #{entity.fIcd10Bm2}, #{entity.fIcd10Mc2}, #{entity.fFbsj}, #{entity.fFbys}, #{entity.fRysj}, #{entity.createDate}, #{entity.downloadStatus}, #{entity.ghdownloadStatus})
        </foreach>
        on duplicate key update
         test_org_name = values(test_org_name) , test_org_id = values(test_org_id) , app_org_name = values(app_org_name) , app_org_id = values(app_org_id) , f_blk = values(f_blk) , f_blh = values(f_blh) , f_brbh = values(f_brbh) , f_sqxh = values(f_sqxh) , f_yzid = values(f_yzid) , f_yzxm = values(f_yzxm) , f_study_uid = values(f_study_uid) , f_xm = values(f_xm) , f_xb = values(f_xb) , f_nl = values(f_nl) , f_age = values(f_age) , f_hy = values(f_hy) , f_mz = values(f_mz) , f_zy = values(f_zy) , f_sfzh = values(f_sfzh) , f_lxxx = values(f_lxxx) , f_brlb = values(f_brlb) , f_fb = values(f_fb) , f_zyh = values(f_zyh) , f_mzh = values(f_mzh) , f_bq = values(f_bq) , f_sjks = values(f_sjks) , f_ch = values(f_ch) , f_sjdw = values(f_sjdw) , f_sjys = values(f_sjys) , f_sdrq = values(f_sdrq) , f_jsy = values(f_jsy) , f_bblx = values(f_bblx) , f_bbqk = values(f_bbqk) , f_jsyy = values(f_jsyy) , f_sf = values(f_sf) , f_bbmc = values(f_bbmc) , f_lczd = values(f_lczd) , f_lczl = values(f_lczl) , f_qcys = values(f_qcys) , f_qcrq = values(f_qcrq) , f_jly = values(f_jly) , f_lkzs = values(f_lkzs) , f_ckzs = values(f_ckzs) , f_fy = values(f_fy) , f_jxsj = values(f_jxsj) , f_blzd = values(f_blzd) , f_tsjc = values(f_tsjc) , f_bgys = values(f_bgys) , f_shys = values(f_shys) , f_bgrq = values(f_bgrq) , f_czyj = values(f_czyj) , f_xgyj = values(f_xgyj) , f_zdgjc = values(f_zdgjc) , f_yyx = values(f_yyx) , f_wfbgyy = values(f_wfbgyy) , f_bz = values(f_bz) , f_bd_sffh = values(f_bd_sffh) , f_bgzt = values(f_bgzt) , f_sfct = values(f_sfct) , f_sfdy = values(f_sfdy) , f_bggs = values(f_bggs) , f_gdzt = values(f_gdzt) , f_knhz = values(f_knhz) , f_zjyj = values(f_zjyj) , f_wyyj = values(f_wyyj) , f_sfzt = values(f_sfzt) , f_sfjg = values(f_sfjg) , f_jbbm_cn = values(f_jbbm_cn) , f_jbbm_eng = values(f_jbbm_eng) , f_jbmc = values(f_jbmc) , f_yblh = values(f_yblh) , f_sjcl = values(f_sjcl) , f_yblzd = values(f_yblzd) , f_bgfsfs = values(f_bgfsfs) , f_scys = values(f_scys) , f_sffh = values(f_sffh) , f_spare1 = values(f_spare1) , f_spare2 = values(f_spare2) , f_spare3 = values(f_spare3) , f_spare4 = values(f_spare4) , f_spare5 = values(f_spare5) , f_spare6 = values(f_spare6) , f_spare7 = values(f_spare7) , f_spare8 = values(f_spare8) , f_spare9 = values(f_spare9) , f_spare10 = values(f_spare10) , f_by1 = values(f_by1) , f_by2 = values(f_by2) , f_txml = values(f_txml) , f_zpzt = values(f_zpzt) , f_mcyj = values(f_mcyj) , f_sfjj = values(f_sfjj) , f_tbsid = values(f_tbsid) , f_tbsmc = values(f_tbsmc) , f_qsb_dyzt = values(f_qsb_dyzt) , f_bgwz = values(f_bgwz) , f_bgwz_qrsj = values(f_bgwz_qrsj) , f_bgwz_qrczy = values(f_bgwz_qrczy) , f_bbwz = values(f_bbwz) , f_lkwz = values(f_lkwz) , f_qpwz = values(f_qpwz) , f_gdczy = values(f_gdczy) , f_gdsj = values(f_gdsj) , f_gdbz = values(f_gdbz) , f_bglry = values(f_bglry) , f_fzys = values(f_fzys) , f_yl1 = values(f_yl1) , f_yl2 = values(f_yl2) , f_yl3 = values(f_yl3) , f_yl4 = values(f_yl4) , f_yl5 = values(f_yl5) , f_yl6 = values(f_yl6) , f_yl7 = values(f_yl7) , f_yl8 = values(f_yl8) , f_yl9 = values(f_yl9) , f_yl10 = values(f_yl10) , f_icd10_bm1 = values(f_icd10_bm1) , f_icd10_mc1 = values(f_icd10_mc1) , f_bl1 = values(f_bl1) , f_bl2 = values(f_bl2) , f_bl3 = values(f_bl3) , f_bl4 = values(f_bl4) , f_bl5 = values(f_bl5) , f_bl6 = values(f_bl6) , f_bl7 = values(f_bl7) , f_bl8 = values(f_bl8) , f_bl9 = values(f_bl9) , f_bl10 = values(f_bl10) , f_icd10_bm2 = values(f_icd10_bm2) , f_icd10_mc2 = values(f_icd10_mc2) , f_fbsj = values(f_fbsj) , f_fbys = values(f_fbys) , f_rysj = values(f_rysj) , create_date = values(create_date) , download_status = values(download_status) , ghdownload_status = values(ghdownload_status)     </insert>

</mapper>

