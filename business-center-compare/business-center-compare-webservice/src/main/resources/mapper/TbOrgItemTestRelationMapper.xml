<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.business.center.compare.webservice.persistence.mapper.TbOrgItemTestRelationMapper">

    <resultMap type="com.labway.business.center.compare.webservice.persistence.entity.TbOrgItemTestRelation" id="TbOrgItemTestRelationMap">
        <result property="itemId" column="item_id" jdbcType="VARCHAR"/>
        <result property="itemTestId" column="item_test_id" jdbcType="VARCHAR"/>
        <result property="itemTestCode" column="item_test_code" jdbcType="VARCHAR"/>
        <result property="itemTestName" column="item_test_name" jdbcType="VARCHAR"/>
        <result property="englishName" column="english_name" jdbcType="VARCHAR"/>
        <result property="sampleTypeCode" column="sample_type_code" jdbcType="VARCHAR"/>
        <result property="sampleTypeName" column="sample_type_name" jdbcType="VARCHAR"/>
        <result property="saveDescription" column="save_description" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="deleteFlag" column="delete_flag" jdbcType="INTEGER"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="orgCode" column="org_code" jdbcType="VARCHAR"/>
        <result property="orgName" column="org_name" jdbcType="VARCHAR"/>
        <result property="testMethod" column="test_method" jdbcType="VARCHAR"/>
        <result property="testMethodName" column="test_method_name" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="itemId" useGeneratedKeys="true">
        insert into business-account.tb_org_item_test_relation(item_test_id, item_test_code, item_test_name, english_name, sample_type_code, sample_type_name, save_description, status, delete_flag, create_by, update_by, create_time, update_time, org_code, org_name, test_method, test_method_name)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.itemTestId}, #{entity.itemTestCode}, #{entity.itemTestName}, #{entity.englishName}, #{entity.sampleTypeCode}, #{entity.sampleTypeName}, #{entity.saveDescription}, #{entity.status}, #{entity.deleteFlag}, #{entity.createBy}, #{entity.updateBy}, #{entity.createTime}, #{entity.updateTime}, #{entity.orgCode}, #{entity.orgName}, #{entity.testMethod}, #{entity.testMethodName})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="itemId" useGeneratedKeys="true">
        insert into business-account.tb_org_item_test_relation(item_test_id, item_test_code, item_test_name, english_name, sample_type_code, sample_type_name, save_description, status, delete_flag, create_by, update_by, create_time, update_time, org_code, org_name, test_method, test_method_name)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.itemTestId}, #{entity.itemTestCode}, #{entity.itemTestName}, #{entity.englishName}, #{entity.sampleTypeCode}, #{entity.sampleTypeName}, #{entity.saveDescription}, #{entity.status}, #{entity.deleteFlag}, #{entity.createBy}, #{entity.updateBy}, #{entity.createTime}, #{entity.updateTime}, #{entity.orgCode}, #{entity.orgName}, #{entity.testMethod}, #{entity.testMethodName})
        </foreach>
        on duplicate key update
         item_test_id = values(item_test_id) , item_test_code = values(item_test_code) , item_test_name = values(item_test_name) , english_name = values(english_name) , sample_type_code = values(sample_type_code) , sample_type_name = values(sample_type_name) , save_description = values(save_description) , status = values(status) , delete_flag = values(delete_flag) , create_by = values(create_by) , update_by = values(update_by) , create_time = values(create_time) , update_time = values(update_time) , org_code = values(org_code) , org_name = values(org_name) , test_method = values(test_method) , test_method_name = values(test_method_name)     </insert>

</mapper>

