<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.business.center.compare.webservice.persistence.mapper.TbOrgApplySampleMainMapper">

    <resultMap type="com.labway.business.center.compare.webservice.persistence.entity.TbOrgApplySampleMain" id="TbOrgApplySampleMainMap">
        <result property="mainId" column="main_id" jdbcType="VARCHAR"/>
        <result property="barcode" column="barcode" jdbcType="VARCHAR"/>
        <result property="hspOrgCode" column="hsp_org_code" jdbcType="VARCHAR"/>
        <result property="hspOrgName" column="hsp_org_name" jdbcType="VARCHAR"/>
        <result property="orgCode" column="org_code" jdbcType="VARCHAR"/>
        <result property="orgName" column="org_name" jdbcType="VARCHAR"/>
        <result property="applyType" column="apply_type" jdbcType="VARCHAR"/>
        <result property="patientVisitCard" column="patient_visit_card" jdbcType="VARCHAR"/>
        <result property="urgent" column="urgent" jdbcType="INTEGER"/>
        <result property="sampleType" column="sample_type" jdbcType="VARCHAR"/>
        <result property="sampleProperty" column="sample_property" jdbcType="VARCHAR"/>
        <result property="dept" column="dept" jdbcType="VARCHAR"/>
        <result property="inpatientArea" column="inpatient_area" jdbcType="VARCHAR"/>
        <result property="patientName" column="patient_name" jdbcType="VARCHAR"/>
        <result property="patientSex" column="patient_sex" jdbcType="INTEGER"/>
        <result property="patientAge" column="patient_age" jdbcType="INTEGER"/>
        <result property="patientSubage" column="patient_subage" jdbcType="INTEGER"/>
        <result property="patientSubageUnit" column="patient_subage_unit" jdbcType="VARCHAR"/>
        <result property="patientBirthday" column="patient_birthday" jdbcType="TIMESTAMP"/>
        <result property="patientBed" column="patient_bed" jdbcType="VARCHAR"/>
        <result property="clinicalDiagnosis" column="clinical_diagnosis" jdbcType="VARCHAR"/>
        <result property="patientCard" column="patient_card" jdbcType="VARCHAR"/>
        <result property="patientCardType" column="patient_card_type" jdbcType="VARCHAR"/>
        <result property="patientAsddress" column="patient_asddress" jdbcType="VARCHAR"/>
        <result property="patientMobile" column="patient_mobile" jdbcType="VARCHAR"/>
        <result property="sendDoctor" column="send_doctor" jdbcType="VARCHAR"/>
        <result property="applyDate" column="apply_date" jdbcType="TIMESTAMP"/>
        <result property="samplingDate" column="sampling_date" jdbcType="TIMESTAMP"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="deleteFlag" column="delete_flag" jdbcType="INTEGER"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="receiveUserCode" column="receive_user_code" jdbcType="VARCHAR"/>
        <result property="receiveUserName" column="receive_user_name" jdbcType="VARCHAR"/>
        <result property="receiveTime" column="receive_time" jdbcType="TIMESTAMP"/>
        <result property="signOrgCode" column="sign_org_code" jdbcType="VARCHAR"/>
        <result property="signOrgName" column="sign_org_name" jdbcType="VARCHAR"/>
        <result property="signBarcode" column="sign_barcode" jdbcType="VARCHAR"/>
        <result property="signMainBarcode" column="sign_main_barcode" jdbcType="VARCHAR"/>
        <result property="sampleSource" column="sample_source" jdbcType="VARCHAR"/>
        <result property="targetOrgCode" column="target_org_code" jdbcType="VARCHAR"/>
        <result property="targetOrgName" column="target_org_name" jdbcType="VARCHAR"/>
        <result property="outMainId" column="out_main_id" jdbcType="VARCHAR"/>
        <result property="auditorId" column="auditor_id" jdbcType="VARCHAR"/>
        <result property="auditor" column="auditor" jdbcType="VARCHAR"/>
        <result property="auditDate" column="audit_date" jdbcType="TIMESTAMP"/>
        <result property="instrumentCode" column="instrument_code" jdbcType="VARCHAR"/>
        <result property="sampleNo" column="sample_no" jdbcType="VARCHAR"/>
        <result property="testDate" column="test_date" jdbcType="VARCHAR"/>
        <result property="testTime" column="test_time" jdbcType="TIMESTAMP"/>
        <result property="testItemSum" column="test_item_sum" jdbcType="INTEGER"/>
        <result property="isFirstAudit" column="is_first_audit" jdbcType="INTEGER"/>
        <result property="firstAuditUserCode" column="first_audit_user_code" jdbcType="VARCHAR"/>
        <result property="firstAuditUserName" column="first_audit_user_name" jdbcType="VARCHAR"/>
        <result property="firstAuditTime" column="first_audit_time" jdbcType="TIMESTAMP"/>
        <result property="isSecAudit" column="is_sec_audit" jdbcType="INTEGER"/>
        <result property="secAuditUserCode" column="sec_audit_user_code" jdbcType="VARCHAR"/>
        <result property="secAuditUserName" column="sec_audit_user_name" jdbcType="VARCHAR"/>
        <result property="secAuditTime" column="sec_audit_time" jdbcType="TIMESTAMP"/>
        <result property="testUserCode" column="test_user_code" jdbcType="VARCHAR"/>
        <result property="testUserName" column="test_user_name" jdbcType="VARCHAR"/>
        <result property="isCheck" column="is_check" jdbcType="INTEGER"/>
        <result property="formId" column="form_id" jdbcType="VARCHAR"/>
        <result property="formCode" column="form_code" jdbcType="VARCHAR"/>
        <result property="tubeType" column="tube_type" jdbcType="VARCHAR"/>
        <result property="isDownload" column="is_download" jdbcType="INTEGER"/>
        <result property="queryTimes" column="query_times" jdbcType="INTEGER"/>
        <result property="isReport" column="is_report" jdbcType="INTEGER"/>
        <result property="isAdditional" column="is_additional" jdbcType="INTEGER"/>
        <result property="failReason" column="fail_reason" jdbcType="VARCHAR"/>
        <result property="sampleNum" column="sample_num" jdbcType="INTEGER"/>
        <result property="isReceive" column="is_receive" jdbcType="INTEGER"/>
        <result property="isHandover" column="is_handover" jdbcType="INTEGER"/>
        <result property="handoverTime" column="handover_time" jdbcType="TIMESTAMP"/>
        <result property="flowStatus" column="flow_status" jdbcType="INTEGER"/>
        <result property="reportUrls" column="report_urls" jdbcType="VARCHAR"/>
        <result property="visitCardNo" column="visit_card_no" jdbcType="VARCHAR"/>
        <result property="returnReason" column="return_reason" jdbcType="VARCHAR"/>
        <result property="reportMd5" column="report_md5" jdbcType="VARCHAR"/>
        <result property="lisCustomerCode" column="lis_customer_code" jdbcType="VARCHAR"/>
        <result property="lisCustomerName" column="lis_customer_name" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="mainId" useGeneratedKeys="true">
        insert into business-account.tb_org_apply_sample_main(main_id, barcode, hsp_org_code, hsp_org_name, org_code, org_name, apply_type, patient_visit_card, urgent, sample_type, sample_property, dept, inpatient_area, patient_name, patient_sex, patient_age, patient_subage, patient_subage_unit, patient_birthday, patient_bed, clinical_diagnosis, patient_card, patient_card_type, patient_asddress, patient_mobile, send_doctor, apply_date, sampling_date, remark, update_by, create_time, create_by, update_time, delete_flag, status, receive_user_code, receive_user_name, receive_time, sign_org_code, sign_org_name, sign_barcode, sign_main_barcode, sample_source, target_org_code, target_org_name, out_main_id, auditor_id, auditor, audit_date, instrument_code, sample_no, test_date, test_time, test_item_sum, is_first_audit, first_audit_user_code, first_audit_user_name, first_audit_time, is_sec_audit, sec_audit_user_code, sec_audit_user_name, sec_audit_time, test_user_code, test_user_name, is_check, form_id, form_code, tube_type, is_download, query_times, is_report, is_additional, fail_reason, sample_num, is_receive, is_handover, handover_time, flow_status, report_urls, visit_card_no, return_reason, report_md5, lis_customer_code, lis_customer_name)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.mainId}, #{entity.barcode}, #{entity.hspOrgCode}, #{entity.hspOrgName}, #{entity.orgCode}, #{entity.orgName}, #{entity.applyType}, #{entity.patientVisitCard}, #{entity.urgent}, #{entity.sampleType}, #{entity.sampleProperty}, #{entity.dept}, #{entity.inpatientArea}, #{entity.patientName}, #{entity.patientSex}, #{entity.patientAge}, #{entity.patientSubage}, #{entity.patientSubageUnit}, #{entity.patientBirthday}, #{entity.patientBed}, #{entity.clinicalDiagnosis}, #{entity.patientCard}, #{entity.patientCardType}, #{entity.patientAsddress}, #{entity.patientMobile}, #{entity.sendDoctor}, #{entity.applyDate}, #{entity.samplingDate}, #{entity.remark}, #{entity.updateBy}, #{entity.createTime}, #{entity.createBy}, #{entity.updateTime}, #{entity.deleteFlag}, #{entity.status}, #{entity.receiveUserCode}, #{entity.receiveUserName}, #{entity.receiveTime}, #{entity.signOrgCode}, #{entity.signOrgName}, #{entity.signBarcode}, #{entity.signMainBarcode}, #{entity.sampleSource}, #{entity.targetOrgCode}, #{entity.targetOrgName}, #{entity.outMainId}, #{entity.auditorId}, #{entity.auditor}, #{entity.auditDate}, #{entity.instrumentCode}, #{entity.sampleNo}, #{entity.testDate}, #{entity.testTime}, #{entity.testItemSum}, #{entity.isFirstAudit}, #{entity.firstAuditUserCode}, #{entity.firstAuditUserName}, #{entity.firstAuditTime}, #{entity.isSecAudit}, #{entity.secAuditUserCode}, #{entity.secAuditUserName}, #{entity.secAuditTime}, #{entity.testUserCode}, #{entity.testUserName}, #{entity.isCheck}, #{entity.formId}, #{entity.formCode}, #{entity.tubeType}, #{entity.isDownload}, #{entity.queryTimes}, #{entity.isReport}, #{entity.isAdditional}, #{entity.failReason}, #{entity.sampleNum}, #{entity.isReceive}, #{entity.isHandover}, #{entity.handoverTime}, #{entity.flowStatus}, #{entity.reportUrls}, #{entity.visitCardNo}, #{entity.returnReason}, #{entity.reportMd5}, #{entity.lisCustomerCode}, #{entity.lisCustomerName})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="mainId" useGeneratedKeys="true">
        insert into business-account.tb_org_apply_sample_main(barcode, hsp_org_code, hsp_org_name, org_code, org_name, apply_type, patient_visit_card, urgent, sample_type, sample_property, dept, inpatient_area, patient_name, patient_sex, patient_age, patient_subage, patient_subage_unit, patient_birthday, patient_bed, clinical_diagnosis, patient_card, patient_card_type, patient_asddress, patient_mobile, send_doctor, apply_date, sampling_date, remark, update_by, create_time, create_by, update_time, delete_flag, status, receive_user_code, receive_user_name, receive_time, sign_org_code, sign_org_name, sign_barcode, sign_main_barcode, sample_source, target_org_code, target_org_name, out_main_id, auditor_id, auditor, audit_date, instrument_code, sample_no, test_date, test_time, test_item_sum, is_first_audit, first_audit_user_code, first_audit_user_name, first_audit_time, is_sec_audit, sec_audit_user_code, sec_audit_user_name, sec_audit_time, test_user_code, test_user_name, is_check, form_id, form_code, tube_type, is_download, query_times, is_report, is_additional, fail_reason, sample_num, is_receive, is_handover, handover_time, flow_status, report_urls, visit_card_no, return_reason, report_md5, lis_customer_code, lis_customer_name)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.barcode}, #{entity.hspOrgCode}, #{entity.hspOrgName}, #{entity.orgCode}, #{entity.orgName}, #{entity.applyType}, #{entity.patientVisitCard}, #{entity.urgent}, #{entity.sampleType}, #{entity.sampleProperty}, #{entity.dept}, #{entity.inpatientArea}, #{entity.patientName}, #{entity.patientSex}, #{entity.patientAge}, #{entity.patientSubage}, #{entity.patientSubageUnit}, #{entity.patientBirthday}, #{entity.patientBed}, #{entity.clinicalDiagnosis}, #{entity.patientCard}, #{entity.patientCardType}, #{entity.patientAsddress}, #{entity.patientMobile}, #{entity.sendDoctor}, #{entity.applyDate}, #{entity.samplingDate}, #{entity.remark}, #{entity.updateBy}, #{entity.createTime}, #{entity.createBy}, #{entity.updateTime}, #{entity.deleteFlag}, #{entity.status}, #{entity.receiveUserCode}, #{entity.receiveUserName}, #{entity.receiveTime}, #{entity.signOrgCode}, #{entity.signOrgName}, #{entity.signBarcode}, #{entity.signMainBarcode}, #{entity.sampleSource}, #{entity.targetOrgCode}, #{entity.targetOrgName}, #{entity.outMainId}, #{entity.auditorId}, #{entity.auditor}, #{entity.auditDate}, #{entity.instrumentCode}, #{entity.sampleNo}, #{entity.testDate}, #{entity.testTime}, #{entity.testItemSum}, #{entity.isFirstAudit}, #{entity.firstAuditUserCode}, #{entity.firstAuditUserName}, #{entity.firstAuditTime}, #{entity.isSecAudit}, #{entity.secAuditUserCode}, #{entity.secAuditUserName}, #{entity.secAuditTime}, #{entity.testUserCode}, #{entity.testUserName}, #{entity.isCheck}, #{entity.formId}, #{entity.formCode}, #{entity.tubeType}, #{entity.isDownload}, #{entity.queryTimes}, #{entity.isReport}, #{entity.isAdditional}, #{entity.failReason}, #{entity.sampleNum}, #{entity.isReceive}, #{entity.isHandover}, #{entity.handoverTime}, #{entity.flowStatus}, #{entity.reportUrls}, #{entity.visitCardNo}, #{entity.returnReason}, #{entity.reportMd5}, #{entity.lisCustomerCode}, #{entity.lisCustomerName})
        </foreach>
        on duplicate key update
         barcode = values(barcode) , hsp_org_code = values(hsp_org_code) , hsp_org_name = values(hsp_org_name) , org_code = values(org_code) , org_name = values(org_name) , apply_type = values(apply_type) , patient_visit_card = values(patient_visit_card) , urgent = values(urgent) , sample_type = values(sample_type) , sample_property = values(sample_property) , dept = values(dept) , inpatient_area = values(inpatient_area) , patient_name = values(patient_name) , patient_sex = values(patient_sex) , patient_age = values(patient_age) , patient_subage = values(patient_subage) , patient_subage_unit = values(patient_subage_unit) , patient_birthday = values(patient_birthday) , patient_bed = values(patient_bed) , clinical_diagnosis = values(clinical_diagnosis) , patient_card = values(patient_card) , patient_card_type = values(patient_card_type) , patient_asddress = values(patient_asddress) , patient_mobile = values(patient_mobile) , send_doctor = values(send_doctor) , apply_date = values(apply_date) , sampling_date = values(sampling_date) , remark = values(remark) , update_by = values(update_by) , create_time = values(create_time) , create_by = values(create_by) , update_time = values(update_time) , delete_flag = values(delete_flag) , status = values(status) , receive_user_code = values(receive_user_code) , receive_user_name = values(receive_user_name) , receive_time = values(receive_time) , sign_org_code = values(sign_org_code) , sign_org_name = values(sign_org_name) , sign_barcode = values(sign_barcode) , sign_main_barcode = values(sign_main_barcode) , sample_source = values(sample_source) , target_org_code = values(target_org_code) , target_org_name = values(target_org_name) , out_main_id = values(out_main_id) , auditor_id = values(auditor_id) , auditor = values(auditor) , audit_date = values(audit_date) , instrument_code = values(instrument_code) , sample_no = values(sample_no) , test_date = values(test_date) , test_time = values(test_time) , test_item_sum = values(test_item_sum) , is_first_audit = values(is_first_audit) , first_audit_user_code = values(first_audit_user_code) , first_audit_user_name = values(first_audit_user_name) , first_audit_time = values(first_audit_time) , is_sec_audit = values(is_sec_audit) , sec_audit_user_code = values(sec_audit_user_code) , sec_audit_user_name = values(sec_audit_user_name) , sec_audit_time = values(sec_audit_time) , test_user_code = values(test_user_code) , test_user_name = values(test_user_name) , is_check = values(is_check) , form_id = values(form_id) , form_code = values(form_code) , tube_type = values(tube_type) , is_download = values(is_download) , query_times = values(query_times) , is_report = values(is_report) , is_additional = values(is_additional) , fail_reason = values(fail_reason) , sample_num = values(sample_num) , is_receive = values(is_receive) , is_handover = values(is_handover) , handover_time = values(handover_time) , flow_status = values(flow_status) , report_urls = values(report_urls) , visit_card_no = values(visit_card_no) , return_reason = values(return_reason) , report_md5 = values(report_md5) , lis_customer_code = values(lis_customer_code) , lis_customer_name = values(lis_customer_name)     </insert>

</mapper>

