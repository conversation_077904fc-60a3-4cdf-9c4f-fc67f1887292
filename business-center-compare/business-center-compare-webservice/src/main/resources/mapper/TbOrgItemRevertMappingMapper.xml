<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.labway.business.center.compare.webservice.persistence.mapper.TbOrgItemRevertMappingMapper">

    <resultMap type="com.labway.business.center.compare.webservice.persistence.entity.TbOrgItemRevertMapping" id="TbOrgItemRevertMappingMap">
        <result property="mappingId" column="mapping_id" jdbcType="VARCHAR"/>
        <result property="customerCode" column="customer_code" jdbcType="VARCHAR"/>
        <result property="customerName" column="customer_name" jdbcType="VARCHAR"/>
        <result property="customerItemCode" column="customer_item_code" jdbcType="VARCHAR"/>
        <result property="customerItemName" column="customer_item_name" jdbcType="VARCHAR"/>
        <result property="orgCode" column="org_code" jdbcType="VARCHAR"/>
        <result property="orgName" column="org_name" jdbcType="VARCHAR"/>
        <result property="orgItemCode" column="org_item_code" jdbcType="VARCHAR"/>
        <result property="orgItemName" column="org_item_name" jdbcType="VARCHAR"/>
        <result property="mappingType" column="mapping_type" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="deleteFlag" column="delete_flag" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="mappingId" useGeneratedKeys="true">
        insert into business-account.tb_org_item_revert_mapping(customer_code, customer_name, customer_item_code, customer_item_name, org_code, org_name, org_item_code, org_item_name, mapping_type, create_time, create_by, update_time, update_by, delete_flag)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.customerCode}, #{entity.customerName}, #{entity.customerItemCode}, #{entity.customerItemName}, #{entity.orgCode}, #{entity.orgName}, #{entity.orgItemCode}, #{entity.orgItemName}, #{entity.mappingType}, #{entity.createTime}, #{entity.createBy}, #{entity.updateTime}, #{entity.updateBy}, #{entity.deleteFlag})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="mappingId" useGeneratedKeys="true">
        insert into business-account.tb_org_item_revert_mapping(customer_code, customer_name, customer_item_code, customer_item_name, org_code, org_name, org_item_code, org_item_name, mapping_type, create_time, create_by, update_time, update_by, delete_flag)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.customerCode}, #{entity.customerName}, #{entity.customerItemCode}, #{entity.customerItemName}, #{entity.orgCode}, #{entity.orgName}, #{entity.orgItemCode}, #{entity.orgItemName}, #{entity.mappingType}, #{entity.createTime}, #{entity.createBy}, #{entity.updateTime}, #{entity.updateBy}, #{entity.deleteFlag})
        </foreach>
        on duplicate key update
         customer_code = values(customer_code) , customer_name = values(customer_name) , customer_item_code = values(customer_item_code) , customer_item_name = values(customer_item_name) , org_code = values(org_code) , org_name = values(org_name) , org_item_code = values(org_item_code) , org_item_name = values(org_item_name) , mapping_type = values(mapping_type) , create_time = values(create_time) , create_by = values(create_by) , update_time = values(update_time) , update_by = values(update_by) , delete_flag = values(delete_flag)     </insert>

    <select id="queryItemMappingRelation" resultType="com.labway.business.center.compare.dto.ItemMappingRelationsDto">
        select * from tb_org_item_mapping_relation
        where customer_code = #{hspOrgCode}
        and org_code = #{targetOrgCode}
        and delete_flag = 0
        and item_outside_code in
            <foreach collection="testItemCodes" item="itemCode" separator="," open="(" close=")">
                #{itemCode}
            </foreach>
    </select>

</mapper>

