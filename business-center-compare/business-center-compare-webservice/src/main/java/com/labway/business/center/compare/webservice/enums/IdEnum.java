package com.labway.business.center.compare.webservice.enums;

/**
 * <AUTHOR>
 */

public enum IdEnum {

    TB_ORG_CUSTOMER_RELATION("R","tb_org_customer_relation"),
    TB_ORG_ITEM_TEST("T","tb_org_item_test"),
    TB_ORG_ITEM_MAPPING_RELATION("M","tb_org_item_mapping_relation"),
    TB_ORG_ITEM_OUTSIDE("O","tb_org_item_outside"),
    TB_ORG_APPLY_SAMPLE_MAIN_ITEM_MAPPING("M","tb_org_apply_sample_main_item_mapping"),
    TB_ORG_APPLY_SAMPLE_MAIN("M","tb_org_apply_sample_main"),
    TB_ORG_APPLY_SAMPLE_MAIN_ITEM("I","tb_org_apply_sample_main_item"),
    TB_ORG_APPLY_SAMPLE_MAIN_ITEM_ADDITIONAL("A","tb_org_apply_sample_main_item_additional"),
    TB_ORG_APPLY_SAMPLE_MAIN_ITEM_RESULT("R","tb_org_apply_sample_main_item_result"),
    TB_ORG_APPLY_FORM("F","tb_org_apply_form"),
    TB_ORG_APPLY_FORM_CODE("C","tb_org_apply_form"),
    TB_ORG_APPLY_SAMPLE_MAIN_OPT_FLOW("F","tb_org_apply_sample_main_opt_flow"),
    TB_LIS_CUSTOMER_INFO("L","tb_lis_customer_info"),
    TB_LIS_CUSTOMER_MAPPING("M","tb_lis_customer_mapping"),
    TB_ORG_ITEM_REVERT("R","tb_org_item_revert"),
    TB_ORG_ITEM_REVERT_MAPPING("M","tb_org_item_revert_mapping"),
    TB_ORG_APPLY_SAMPLE_LIS("SL","tb_org_apply_sample_lis"),
    TB_ORG_BARCODE_RELATION("R","tb_org_barcode_relation"),
    ;


    private String value;
    private String desc;

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    IdEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }
}
