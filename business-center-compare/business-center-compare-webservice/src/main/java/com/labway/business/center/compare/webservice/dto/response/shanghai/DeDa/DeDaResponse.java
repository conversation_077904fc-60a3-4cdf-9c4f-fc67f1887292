package com.labway.business.center.compare.webservice.dto.response.shanghai.DeDa;

import com.labway.business.center.compare.webservice.enums.ResultCode;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

@Data
@AllArgsConstructor
@NoArgsConstructor
@XmlRootElement(name = "Response")
@XmlAccessorType(XmlAccessType.FIELD)
public class DeDaResponse {

    @XmlElement(name = "ResultCode")
    private Integer resultCode;

    @XmlElement(name = "ResultContent")
    private String resultContent;

    public DeDaResponse(ResultCode result){
        this.resultCode = result.getCode();
        this.resultContent = result.getMsg();
    }
}
