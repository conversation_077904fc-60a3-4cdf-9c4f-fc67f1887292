package com.labway.business.center.compare.webservice.server.diAn;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.labway.business.center.compare.dto.ItemMappingRelationsDto;
import com.labway.business.center.compare.dto.diAn.DiAnPdfResultDto;
import com.labway.business.center.compare.dto.diAn.DiAnSampleResultDto;
import com.labway.business.center.compare.dto.diAn.GetOutSideApplyByBarcodeDto;
import com.labway.business.center.compare.dto.diAn.GetOutSideApplyByBarcodeDto2;
import com.labway.business.center.compare.dto.diAn.param.ReqInfo;
import com.labway.business.center.compare.dto.diAn.response.DiAnSampleResultResponse;
import com.labway.business.center.compare.dto.diAn.response.QueryReports2Result;
import com.labway.business.center.compare.webservice.enums.ResultCode;
import com.labway.business.center.compare.webservice.enums.WebResult;
import com.labway.business.center.compare.webservice.persistence.entity.TbOrgApplySampleMain;
import com.labway.business.center.compare.webservice.persistence.entity.TbOrgApplySampleMainItem;
import com.labway.business.center.compare.webservice.persistence.entity.TbOrgApplySampleMainItemResult;
import com.labway.business.center.compare.webservice.persistence.entity.TbOrgItemRevertMapping;
import com.labway.business.center.compare.webservice.persistence.repository.TbOrgApplySampleMainItemRepository;
import com.labway.business.center.compare.webservice.persistence.repository.TbOrgApplySampleMainItemResultRepository;
import com.labway.business.center.compare.webservice.persistence.repository.TbOrgApplySampleMainRepository;
import com.labway.business.center.compare.webservice.persistence.repository.TbOrgItemRevertMappingRepository;
import com.labway.business.center.compare.webservice.utils.WebserviceUtil;
import com.labway.business.center.compare.webservice.utils.XmlUtil;
import com.swak.frame.dto.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.cxf.common.util.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.jws.WebService;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@RefreshScope
@Component
@WebService(
        name = "diAnService",  // 与接口中指定的name一致
        targetNamespace = "http://diAn.server.webservice.compare.center.business.labway.com/", // 与接口中的命名空间一致,一般是接口的包名倒
        endpointInterface = "com.labway.business.center.compare.webservice.server.diAn.DiAnService"// 接口地址
)
public class DiAnServiceImpl implements DiAnService {

    @Value("#{${webservice.clientGUIDMap}}")
    private Map<String,String> clientGUIDMap;
    @Value("#{${webservice.orgNameMapping}}")
    private Map<String,String> orgNameMapping;

    // -客户认证密码(例：123456776F0D685BE0530BF000123456)
    @Value("${webservice.dian.sample.ClientGUID}")
    private String sampleClientGUID;
    @Value("${webservice.dian.sample.url}")
    private String sampledianUrl;
    @Value("${webservice.dian.sample.namespace}")
    private String sampledianNamespace;
    @Value("${webservice.dian.sample.methods.GetDetailDataByHospBarcode3}")
    private String GetDetailDataByHospBarcode3;

    @Value("${webservice.dian.pdf.ClientGUID}")
    private String pdfClientGUID;
    @Value("${webservice.dian.pdf.url}")
    private String pdfDianUrl;
    @Value("${webservice.dian.pdf.namespace}")
    private String pdfDianNamespace;
    @Value("${webservice.dian.pdf.methods.QueryReports2}")
    private String QueryReports2;

    @Resource
    private TbOrgApplySampleMainRepository tbOrgApplySampleMainRepository;
    @Resource
    private TbOrgApplySampleMainItemRepository tbOrgApplySampleMainItemRepository;
    @Resource
    private TbOrgItemRevertMappingRepository tbOrgItemRevertMappingRepository;
    @Resource
    private TbOrgApplySampleMainItemResultRepository tbOrgApplySampleMainItemResultRepository;


    /**
     * 根据条码号查询样本信息
     * @param hspOrgCode
     * @param barcode
     * @return
     */
    @Override
    public String GetOutSideApplyByBarcode(String ClientGUID, String hspOrgCode, String barcode) {

        log.info("迪安查询样本信息，样本号【{}】，样本机构：【{}】，凭证信息：【{}】",barcode,hspOrgCode,ClientGUID);
        hspOrgCode = orgNameMapping.get(hspOrgCode);

        // 查询样本主表信息
        TbOrgApplySampleMain tbOrgApplySampleMain = tbOrgApplySampleMainRepository.queryUnSignSampleInfo(hspOrgCode,barcode);
        if (tbOrgApplySampleMain==null){
            return XmlUtil.responseToXml(new WebResult(ResultCode.SAMPLE_NOT_EXIST));
//            return XmlUtil.responseToXml(new WebResult(ResultCode.SAMPLE_NOT_EXIST)).replaceFirst("^<\\?xml.*\\?>", "");
        }

        String targetOrgCode = tbOrgApplySampleMain.getTargetOrgCode();
        String guid = clientGUIDMap.get(targetOrgCode);
        if (StringUtils.isBlank(ClientGUID) || !ClientGUID.equals(guid)){
            return XmlUtil.responseToXml(new WebResult(ResultCode.USE_VAILD_FAIL));
//            return XmlUtil.responseToXml(new WebResult(ResultCode.USE_VAILD_FAIL)).replaceFirst("^<\\?xml.*\\?>", "");
        }


        // 查询样本的项目信息
        List<TbOrgApplySampleMainItem> mainItems = tbOrgApplySampleMainItemRepository.querySampleItemByMainId(tbOrgApplySampleMain.getMainId());

        // 组装迪安查询样本的数据格式
        GetOutSideApplyByBarcodeDto dto = getGetOutSideApplyByBarcodeDto(tbOrgApplySampleMain,mainItems);
        GetOutSideApplyByBarcodeDto2 dto2 = JSONObject.parseObject(JSONObject.toJSONString(dto), GetOutSideApplyByBarcodeDto2.class);
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
        dto2.setBIRTHDAY(dto.getBIRTHDAY()==null?null:simpleDateFormat.format(dto.getBIRTHDAY()));
        dto2.setAPP_DATE(dto.getAPP_DATE()==null?null:simpleDateFormat.format(dto.getAPP_DATE()));
        dto2.setEXTRACT_DATE(dto.getEXTRACT_DATE()==null?null:simpleDateFormat.format(dto.getEXTRACT_DATE()));

        String resultXml = XmlUtil.responseToXml(new WebResult(dto2));
        resultXml = resultXml.replace("<resultXmlString>","");
        resultXml = resultXml.replace("</resultXmlString>","");
//        resultXml = resultXml.replaceFirst("^<\\?xml.*\\?>", "");
        log.info("迪安查询样本【样本号：{}】信息，返回结果：{}",barcode,resultXml);
        return resultXml;
    }


    /**
     * 查询迪安系统获取样本检验结果信息
     */
    @DS("account")
    @Transactional(rollbackFor = Exception.class)
    @Override
    public String queryDiAnSampleResult(List<String> barcodes){
        log.info("开始执行迪安样本结果。。。查询参数：{}",JSONObject.toJSONString(barcodes));

        // 查询外送迪安的样本（未回传结果的样本信息）--只查询最近一个月的样本 因为有些样本是没有结果的 例如病理的样本
        Date queryDate = DateUtils.addDays(new Date(),-30);
        List<TbOrgApplySampleMain> sampleMains = tbOrgApplySampleMainRepository.queryUnResultDiAnSampleInfo(barcodes,queryDate);
        if (CollectionUtils.isEmpty(sampleMains)){
            log.info("没有需要同步结果的迪安外送样本信息！");
            return "没有需要同步结果的迪安外送样本信息哦！";
        }

        Date now = new Date();
        for (TbOrgApplySampleMain sampleMain : sampleMains) {
            log.info("开始同步样本{}的检验结果信息。。。",sampleMain.getBarcode());

            // 查询常规报告结果信息
            List<DiAnSampleResultDto> sampleDtos = getSampleResults(sampleMain);
            // 查询样本pdf结果信息
            List<DiAnPdfResultDto> pdfDtos = getPdfResults(sampleMain);

            // 构建文字结果信息（迪安的样本由于没有签收的动作，所以在同步完样本结果的时候需要更新样本的状态，同时添加样本的签收对照关系）
            List<TbOrgApplySampleMainItemResult> resultList = saveSampleResults(sampleDtos, sampleMain, now);
            // 构建pdf结果信息
            String pdfStr = savePdfResults(pdfDtos, sampleMain, now);


            // 删除旧的报告结果
            tbOrgApplySampleMainItemResultRepository.deleteByMainId(sampleMain.getMainId());
            // 落库保存
            tbOrgApplySampleMainItemResultRepository.saveSampleResults(resultList);
            tbOrgApplySampleMainRepository.updatePdfInfoById(sampleMain.getMainId(),pdfStr,sampleDtos.get(0),sampleMain);

            // 更新样本的外送状态以及报告状态--文字结果和pdf结果都获取到时，说明结果已经回传完毕
            if (!CollectionUtils.isEmpty(sampleDtos) && !CollectionUtils.isEmpty(pdfDtos)){
                tbOrgApplySampleMainRepository.updateStatusAndReportStatusForDiAn(sampleMain.getMainId());
                log.info("样本{}的检验结果同步完成！！！！",sampleMain.getBarcode());
            }else {
                log.warn("样本{}的检验结果同步未完成,文字结果：{}条,pdf结果：{}条",sampleMain.getBarcode(),sampleDtos.size(),pdfDtos.size());
            }

        }

        log.info("迪安报告结果同步任务执行结束,本次同步结果的样本数：{}",sampleMains.size());
        return "success";
    }



    //==================================================================================================================

    private GetOutSideApplyByBarcodeDto getGetOutSideApplyByBarcodeDto(TbOrgApplySampleMain tbOrgApplySampleMain, List<TbOrgApplySampleMainItem> mainItems) {

        GetOutSideApplyByBarcodeDto dto = new GetOutSideApplyByBarcodeDto();
        dto.setBARCODE(tbOrgApplySampleMain.getBarcode());
        dto.setOUTSIDE_ORG_CODE(tbOrgApplySampleMain.getTargetOrgName());
        dto.setHSP_ORG_CODE(tbOrgApplySampleMain.getHspOrgCode());
        dto.setHSP_ORG_NAME(tbOrgApplySampleMain.getHspOrgName());
        dto.setTUBE_TYPE(tbOrgApplySampleMain.getTubeType());
        dto.setSOURCE_TYPE(tbOrgApplySampleMain.getApplyType());
        dto.setSAMPLE_TYPE(tbOrgApplySampleMain.getSampleType());
        dto.setSAMPLE_STATUS("正常"); // todo待确认
        dto.setAPP_DEPT(tbOrgApplySampleMain.getDept());
        dto.setPATIENT_NO(tbOrgApplySampleMain.getVisitCardNo());// todo待确认
        dto.setPATIENT_NAME(tbOrgApplySampleMain.getPatientName());
        dto.setSEX(tbOrgApplySampleMain.getPatientSex()==2?"男":"女");
        dto.setAGE(String.valueOf(tbOrgApplySampleMain.getPatientAge()));
        dto.setBIRTHDAY(tbOrgApplySampleMain.getPatientBirthday());
        dto.setBED(tbOrgApplySampleMain.getPatientBed());
        dto.setDIAG(tbOrgApplySampleMain.getClinicalDiagnosis());
        dto.setID_NUMBER(tbOrgApplySampleMain.getPatientCard());
        dto.setADDRESS(tbOrgApplySampleMain.getPatientAsddress());
        dto.setTEL(tbOrgApplySampleMain.getPatientMobile());
        dto.setMEMO(tbOrgApplySampleMain.getRemark());
        dto.setAPP_USER_NAME(tbOrgApplySampleMain.getSendDoctor());
        dto.setAPP_DATE(tbOrgApplySampleMain.getApplyDate());
        dto.setEXTRACT_USER_NAME("");//todo待确认
        dto.setEXTRACT_DATE(tbOrgApplySampleMain.getSamplingDate());

        List<GetOutSideApplyByBarcodeDto.ApplyDetail> apply_Detail = new ArrayList<>();
        for (TbOrgApplySampleMainItem mainItem : mainItems) {
            GetOutSideApplyByBarcodeDto.ApplyDetail temp = new GetOutSideApplyByBarcodeDto.ApplyDetail();
            temp.setBARCODE(tbOrgApplySampleMain.getBarcode());
            temp.setTESTITEM_CODE(mainItem.getOutTestItemCode());
            temp.setTESTITEM_NAME(mainItem.getOutTestItemName());
            apply_Detail.add(temp);
        }
        dto.setApply_Detail(apply_Detail);

        return dto;
    }

    // 查询迪安pdf报告结果信息
    private List<DiAnPdfResultDto> getPdfResults(TbOrgApplySampleMain sampleMain) {

        ReqInfo reqInfo = new ReqInfo();
        reqInfo.setClientID(orgNameMapping.get(sampleMain.getHspOrgName()));
        reqInfo.setKey(pdfClientGUID);

        reqInfo.setModel("0");
        ReqInfo.Condition condition = new ReqInfo.Condition();
        condition.setClinicID(sampleMain.getBarcode());
        reqInfo.setCondition(condition);

        List<DiAnPdfResultDto> diAnPdfResultDtos = new ArrayList<>();

        int pageNo = 1;
        while (true){
            reqInfo.setPageNo(pageNo++);
            Response<QueryReports2Result> queryReports2ResultResponse = WebserviceUtil.queryDiAnPdfResult(pdfDianUrl, pdfDianNamespace, QueryReports2, reqInfo);
            if (!queryReports2ResultResponse.isSuccess()){
                log.warn("查询迪安pdf报告结果失败，查询样本条码：{}，返回结果：{}",sampleMain.getBarcode(), JSONObject.toJSONString(queryReports2ResultResponse));
                break;
            }

            QueryReports2Result data = queryReports2ResultResponse.getData();
            Boolean hasNextPage = data.getHasNextPage();

            // 处理获取pdf报告
            diAnPdfResultDtos.addAll(data.getDatas());

            if (!hasNextPage){
                break;
            }
            log.info("样本的pdf结果存在多页，开始查询第{}页的数据！",pageNo);
        }

        return diAnPdfResultDtos;
    }


    // 查询迪安文字结果信息
    private List<DiAnSampleResultDto> getSampleResults(TbOrgApplySampleMain sampleMain) {

        HashMap<String, Object> paramMap = new HashMap<>();
        paramMap.put("ClientID",orgNameMapping.get(sampleMain.getHspOrgName()));
        paramMap.put("ClientGUID", sampleClientGUID);
        paramMap.put("hospBarcode",sampleMain.getBarcode());
        /**
         * 1-只有该条码下所有检测项目的结果都出来了，才返回数据
         *  2-返回该条码下所有已出结果的数据
         *  3-返回该条码下所有数据，包含没出结果的
         */
        paramMap.put("SelectType","1");

        log.info("开始查询样本【{}】的检验结果信息！",sampleMain.getBarcode());
        Response<String> sampleResponse = WebserviceUtil.invoke(sampledianUrl, sampledianNamespace, GetDetailDataByHospBarcode3, paramMap);
        if (!sampleResponse.isSuccess()){
            log.error("调用迪安服务失败，查询样本号【{}】,失败信息：{}",sampleMain.getBarcode(),sampleResponse.getMsg());
            return Collections.emptyList();
        }
        log.info("迪安样本文字结果查询结束，响应信息：{}",JSONObject.toJSONString(sampleResponse.getData()));

        String data =  "<response>" + sampleResponse.getData() + "</response>";
        DiAnSampleResultResponse diAnSampleResultDto = XmlUtil.xml2Bean(data, DiAnSampleResultResponse.class);
        List<DiAnSampleResultDto> resultsDataSet = diAnSampleResultDto.getResultsDataSet();
        if (CollectionUtils.isEmpty(resultsDataSet)){
            log.info("样本{}未查询到检验结果信息，迪安返回结果：{}",sampleMain.getBarcode(), JSONObject.toJSONString(diAnSampleResultDto));
            return Collections.emptyList();
        }

        return resultsDataSet;
    }


    // 保存pdf报告结果
    private String savePdfResults(List<DiAnPdfResultDto> pdfDtos, TbOrgApplySampleMain sampleMain,Date now ) {
        if (CollectionUtils.isEmpty(pdfDtos)){
            log.info("样本条码{}暂未查询到pdf报告结果！",sampleMain.getBarcode());
            return null;
        }

        List<String> pdfs = pdfDtos.stream().map(e -> e.getReport().stream().map(p->p.getPdfReportUrl())).flatMap(o->o).collect(Collectors.toList());
        String pdfStr = pdfs.stream().collect(Collectors.joining(","));
        log.info("样本条码{}查询到pdf报告url{}",sampleMain.getBarcode(),pdfStr);
        return pdfStr;
    }


    // 保存文字报告结果
    private List<TbOrgApplySampleMainItemResult> saveSampleResults(List<DiAnSampleResultDto> sampleDtos,TbOrgApplySampleMain sampleMain,Date now) {

        // 查询报告项目的对照关系
        List<String> itemCodes = sampleDtos.stream().map(e -> e.getTestcode()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(itemCodes)){
            log.info("检验结果未包含任何报告项目编码信息！样本条码号：{}",sampleMain.getBarcode());
            return Collections.emptyList();
        }

        String hspOrgCode = sampleMain.getHspOrgCode();
        String targetOrgCode = sampleMain.getTargetOrgCode();

        // 查询报告项目对照关系(这里反查迪安的报告项目对照关系，所以送检机构和检验机构的位置对调了)
        List<TbOrgItemRevertMapping> mappings = tbOrgItemRevertMappingRepository.queryMappingByType(targetOrgCode,hspOrgCode,1);

        // 构建转换业务中台的样本结果数据
        List<TbOrgApplySampleMainItemResult> resultList = getBuildSampleResults(sampleDtos,mappings,sampleMain,now);


        // 处理检验项目的对照关系（迪安对照的实验室项目）
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(resultList)){
            doTestItemMapping(resultList,sampleMain);
        }

        return resultList;
    }

    // 构建样本结果表信息
    private List<TbOrgApplySampleMainItemResult> getBuildSampleResults(List<DiAnSampleResultDto> sampleDtos, List<TbOrgItemRevertMapping> mappings,TbOrgApplySampleMain sampleMain,Date now) {
        if (CollectionUtils.isEmpty(sampleDtos)){
            return Collections.emptyList();
        }

        // 迪安编码--对照关系
        Map<String, TbOrgItemRevertMapping> mappingMap = mappings.stream().collect(Collectors.toMap(e -> e.getCustomerItemCode(), p -> p, (o1, o2) -> o1));
        Integer patientSex = sampleMain.getPatientSex();

        List<TbOrgApplySampleMainItemResult> resultList = new ArrayList<>();
        for (DiAnSampleResultDto sampleDto : sampleDtos) {
            String testcode = sampleDto.getTestcode();
            String diAnReportCode = sampleDto.getS();
            String sinonym = sampleDto.getSinonym();
            TbOrgItemRevertMapping tbOrgItemRevertMapping = mappingMap.get(diAnReportCode);
            if (tbOrgItemRevertMapping == null){
                log.warn("迪安报告项目【编码{}】未做回传对照【机构名称编码：{}-{}】，结果不填充实验室报告项目信息！！！",diAnReportCode,sampleMain.getTargetOrgCode(),sampleMain.getTargetOrgName());
            }

            // 填充样本结果表信息
            TbOrgApplySampleMainItemResult sampleMainItemResult = new TbOrgApplySampleMainItemResult();
            sampleMainItemResult.setResultId("D" + IdWorker.getId());
            sampleMainItemResult.setMainId(sampleMain.getMainId());
            sampleMainItemResult.setItemId("");
            sampleMainItemResult.setBarcode(sampleMain.getBarcode());
            sampleMainItemResult.setOutTestItemCode("");
            sampleMainItemResult.setOutTestItemName("");
//            sampleMainItemResult.setItemTestCode(testcode);
//            sampleMainItemResult.setItemTestName(testcode);
            sampleMainItemResult.setItemTestCode("");
            sampleMainItemResult.setItemTestName("");
//            sampleMainItemResult.setItemReportCode(diAnReportCode);
//            sampleMainItemResult.setItemReportName(sinonym);
            sampleMainItemResult.setItemReportCode(tbOrgItemRevertMapping==null?"":tbOrgItemRevertMapping.getOrgItemCode());
            sampleMainItemResult.setItemReportName(tbOrgItemRevertMapping==null?"":tbOrgItemRevertMapping.getOrgItemName());
            sampleMainItemResult.setHspOrgCode(sampleMain.getHspOrgCode());
            sampleMainItemResult.setHspOrgName(sampleMain.getHspOrgName());
            sampleMainItemResult.setCreateBy("system");
            sampleMainItemResult.setCreateTime(now);
            sampleMainItemResult.setUpdateBy("system");
            sampleMainItemResult.setUpdateTime(now);
            sampleMainItemResult.setDeleteFlag(0);
            sampleMainItemResult.setSampleNo(sampleMain.getSampleNo());
            sampleMainItemResult.setFormId(sampleMain.getFormId());
            sampleMainItemResult.setFormCode(sampleMain.getFormCode());
            sampleMainItemResult.setQueryTimes(0);
            sampleMainItemResult.setIsReport(1);
            sampleMainItemResult.setIsDownload(0);
            // 上面的复制为预置结果字段，下面为检验结果字段
            sampleMainItemResult.setTestDate(sampleDto.getSubmitdate());
            sampleMainItemResult.setIsAdd(0);
            // 是否复测
            sampleMainItemResult.setIsRetest(0);
            sampleMainItemResult.setTestResult(sampleDto.getFINAL());
            sampleMainItemResult.setResultUnit(sampleDto.getUnits());
//            sampleMainItemResult.setReferenceValue(patientSex==1?sampleDto.getDisplowhigh_m():sampleDto.getDisplowhigh_f());
            sampleMainItemResult.setReferenceValue(sampleDto.getDisplowhigh());
            sampleMainItemResult.setTestJudge(sampleDto.getRn20());
            sampleMainItemResult.setInstrumentCode("");
            sampleMainItemResult.setMachineResult(sampleDto.getFINAL());
            sampleMainItemResult.setUpValue(sampleDto.getHIGHB());
            sampleMainItemResult.setDownValue(sampleDto.getLOWB());
            sampleMainItemResult.setRecordDate(sampleDto.getCollectddate());

            sampleMainItemResult.setTestUserCode(sampleDto.getUsrnam());
            sampleMainItemResult.setTestUserName(sampleDto.getUsrnam());

            sampleMainItemResult.setOtherItemReportCode(diAnReportCode);
            sampleMainItemResult.setOtherItemReportName(sinonym);
            sampleMainItemResult.setOtherItemTestCode(testcode);
            sampleMainItemResult.setOtherItemTestName("");

            resultList.add(sampleMainItemResult);
        }

        return resultList;
    }

    // 处理检验项目的对照关系
    private void doTestItemMapping(List<TbOrgApplySampleMainItemResult> resultList,TbOrgApplySampleMain sampleMain) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(resultList)){
            return;
        }

        // 实验室
        String hspOrgCode = sampleMain.getHspOrgCode();
        // 客商
        String targetOrgCode = sampleMain.getTargetOrgCode();

        // 客商检验项目编码
        List<String> testItemCodes = resultList.stream().map(e -> e.getOtherItemTestCode()).collect(Collectors.toList());
        List<ItemMappingRelationsDto> itemMappingRelations = tbOrgItemRevertMappingRepository.queryItemMappingRelation(targetOrgCode,hspOrgCode,testItemCodes);
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(itemMappingRelations)){
            log.info("未查询到检验项目对照，不填充客商检验项目编码，条码号：{}",sampleMain.getBarcode());
            return;
        }

        Map<String, List<ItemMappingRelationsDto>> mappingGroup = itemMappingRelations.stream().collect(Collectors.groupingBy(e -> e.getItemOutsideCode()));

        for (TbOrgApplySampleMainItemResult tbOrgApplySampleMainItemResult : resultList) {
            String otherItemTestCode = tbOrgApplySampleMainItemResult.getOtherItemTestCode();
            List<ItemMappingRelationsDto> itemMappingRelationsDtos = mappingGroup.get(otherItemTestCode);
            if (org.apache.commons.collections4.CollectionUtils.isEmpty(itemMappingRelationsDtos)){
                log.info("迪安检验项目编码没有对照，跳过处理，项目编码：{}",otherItemTestCode);
                continue;
            }
            ItemMappingRelationsDto itemMappingRelationsDto = itemMappingRelationsDtos.get(0);

            tbOrgApplySampleMainItemResult.setItemTestCode(itemMappingRelationsDto.getItemTestCode());
            tbOrgApplySampleMainItemResult.setItemTestName(itemMappingRelationsDto.getItemTestCode());
            tbOrgApplySampleMainItemResult.setOutTestItemCode(itemMappingRelationsDto.getItemOutsideCode());
            tbOrgApplySampleMainItemResult.setOutTestItemName(itemMappingRelationsDto.getItemOutsideName());
        }


    }

}
