package com.labway.business.center.compare.webservice.persistence.mapper;

import java.util.List;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import com.labway.business.center.compare.webservice.persistence.entity.TbOrgItemTestRelation;

/**
 * 机构下发的检验项目信息表(TbOrgItemTestRelation)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-03-14 19:29:11
 */
@DS("account")
@Mapper
public interface TbOrgItemTestRelationMapper extends BaseMapper<TbOrgItemTestRelation> {

/**
* 批量新增数据（MyBatis原生foreach方法）
*
* @param entities List<TbOrgItemTestRelation> 实例对象列表
* @return 影响行数
*/
int insertBatch(@Param("entities") List<TbOrgItemTestRelation> entities);

/**
* 批量新增或按主键更新数据（MyBatis原生foreach方法）
*
* @param entities List<TbOrgItemTestRelation> 实例对象列表
* @return 影响行数
* @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
*/
int insertOrUpdateBatch(@Param("entities") List<TbOrgItemTestRelation> entities);

}

