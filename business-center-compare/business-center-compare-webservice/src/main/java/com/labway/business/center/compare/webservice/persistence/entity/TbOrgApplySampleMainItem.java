package com.labway.business.center.compare.webservice.persistence.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 外送生清单-送检项目信息表(TbOrgApplySampleMainItem)表实体类
 *
 * <AUTHOR>
 * @since 2023-10-23 09:45:28
 */
@Data
@NoArgsConstructor
@SuppressWarnings("serial")
public class TbOrgApplySampleMainItem extends Model<TbOrgApplySampleMainItem> {
    //送检项目表id
    @TableId(type = IdType.INPUT)
    private String mainItemId;
    //申请单id
    private String mainId;
    //送检样本条码号
    private String barcode;
    //送检机构编码（对应中台客商编码）
    private String hspOrgCode;
    //送检机构名称（对应中台客商名称）
    private String hspOrgName;
    //外部项目编码
    private String outTestItemCode;
    //外部项目名称
    private String outTestItemName;
    //创建人id
    private String createBy;
    //创建时间
    private Date createTime;
    //更新人id
    private String updateBy;
    //更新时间
    private Date updateTime;
    //删除标识 0未删除 1删除
    @TableLogic(value = "0",delval = "1")
    private Integer deleteFlag;
    //申请单id
    private String formId;
    //申请单编码
    private String formCode;


}

