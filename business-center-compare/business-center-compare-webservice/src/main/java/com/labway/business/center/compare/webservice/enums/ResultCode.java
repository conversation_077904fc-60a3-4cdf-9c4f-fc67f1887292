package com.labway.business.center.compare.webservice.enums;


import com.swak.frame.enums.IResultCode;

/**
 * BasicErrCode 的扩展
 */
public enum ResultCode implements IResultCode {

    SUCCESS(0, "成功"),

    //-----------------通用错误信息--------------------
    INTERNAL_SERVER_ERROR(500, "内部服务异常，请稍后再试"),
    NO_DATA_PERMISSION(401,"数据权限不足"),
    NO_ROLE_PERMISSION(401,"角色权限不足"),
    FAIL_ROLE_PERMISSION(401,"用户角色校验失败"),
    DATA_NOT_EXITS(402,"所查询的数据不存在,请检查查询参数"),
    PARAMS_NOT_VALIDATE(403,"参数不合法"),
    FILE_TYPE_ERROR(404,"上传文件类型错误"),
    REPEAT_COMMIT(405,"请稍等，不要多次点击"),
    USE_VAILD_FAIL(601, "凭证信息校验失败！"),

    //------------------------------------10100-------------------------------------------------------------------------
    SAMPLE_NOT_EXIST(10010,"待签收样本信息不存在，请核实样本外送状态！"),
    SAMPLE_QUERY_ERROR(10011,"两癌样本信息查询失败，错误信息【%s】！"),
    SAMPLE_NOR_EXIST(10012,"两癌样本信息不存在！"),
    CALL_EXCEPTION(10013, "调用两癌接口异常！"),
    RESULT_NOT_SUCCESS(10014, "调用两癌接口返回失败【%s】！"),
    CALL_QPSLIMIT(10015, "调用两癌接口限流！"),
    HSP_ORG_CODE_NULL(10016,"送检机构不能为空！"),
    PATIENT_NO_NULL(10017,"病人编号不能为空！"),
    TEST_TIME_BEG(10018,"检验开始时间不能为空！"),
    TEST_TIME_END(10019,"检验结束时间不能为空！"),
    NO_DATA(10017, "未出结果");

    private final Integer code;
    private final  String msg;


    private ResultCode(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }


    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getMsg() {
        return msg;
    }
}
