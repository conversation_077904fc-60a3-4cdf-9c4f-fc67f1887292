package com.labway.business.center.compare.webservice.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.Map;

@Getter
@Setter
@Component
@RefreshScope
@ConfigurationProperties(prefix = "business")
public class BusinessConfig {

    /**
     * 送检机构code->名称
     */
    private Map<String, String> orgCodeNameMap;

    public String getOrgNameByCode(String orgCode) {
        return orgCodeNameMap.getOrDefault(orgCode, orgCode);
    }

}
