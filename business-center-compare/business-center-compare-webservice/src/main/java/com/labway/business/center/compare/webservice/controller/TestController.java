package com.labway.business.center.compare.webservice.controller;

import com.labway.business.center.compare.dto.OutApplyInfoDTO;
import com.labway.business.center.compare.dto.middlebase.OrgApplySampleMainDTO;
import com.labway.business.center.compare.request.QueryApplyRequest;
import com.labway.business.center.compare.request.SignApplyInfoRequest;
import com.labway.business.center.compare.service.la.LaDubboService;
import com.labway.business.center.core.enums.Region;
import com.labway.business.center.compare.webservice.server.diAn.DiAnService;
import com.swak.frame.dto.Response;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/test")
public class TestController {

    @Resource
    private DiAnService diAnService;
    @Resource
    private LaDubboService laDubboService;

    private static final String region = Region.changzhou.getDesc();


    // ************************************************** 外送迪安 **************************************************
    /**
     * 查询样本信息
     */
    @PostMapping("/querySampleInfo")
    public String querySampleInfo(@RequestParam(required = true) String ClientGUID, @RequestParam(required = true) String strHspOrgCode, @RequestParam(required = true) String strBarcode) {
        return diAnService.GetOutSideApplyByBarcode(ClientGUID, strHspOrgCode, strBarcode);
    }


    /**
     * 查询同步迪安外送样本结果信息
     *
     * @param barcodes
     * @return
     */
    @PostMapping("/testQuerySampleResult")
    public String testQuerySampleResult(@RequestBody List<String> barcodes) {
        return diAnService.queryDiAnSampleResult(barcodes);
    }

    // ************************************************** 外送迪安 **************************************************


    // ==================================================== 两癌 ===================================================
    /**
     * 丹阳两癌系统登录接口
     */
    @PostMapping("/DYLALogin")
    public Response<String> DYLALogin(@RequestParam String loginCode, @RequestParam String password) {
        return laDubboService.lALogin(region,loginCode, password);
    }

    /**
     * 丹阳两癌样本查询接口3.2
     */
    @PostMapping("/DYLAQuerySampleInfo3")
    public Response<String> DYLAQuerySampleInfo3(@RequestParam String sampleCode) {
        return laDubboService.laQuerySampleInfo3(region, sampleCode);
    }

    /**
     * 丹阳两癌样本查询接口1.2
     */
    @PostMapping("/DYLAQuerySampleInfo1")
    public Response<OutApplyInfoDTO> DYLAQuerySampleInfo1(@RequestBody QueryApplyRequest request) {
        request.setRegion(region);
        return laDubboService.laQuerySampleInfo1(request);
    }

    /**
     * 丹阳两癌样本签收接口1.2
     */
    @PostMapping("/DYLASignSampleInfo1")
    public Response<OrgApplySampleMainDTO> DYLASignSampleInfo1(@RequestBody SignApplyInfoRequest request) {
        request.setRegion(region);
        return laDubboService.laSignSampleInfo1(request);
    }

    /**
     * 丹阳两癌推送结果信息接口4.2
     */
    @PostMapping("/DYLAPushSampleResultInfo")
    public Response<String> dylaPushSampleResultInfo(@RequestBody List<String> sampleCodes) {
        return laDubboService.laPushSampleResultInfo(region, sampleCodes);
    }
    // ==================================================== 两癌 ===================================================

}
